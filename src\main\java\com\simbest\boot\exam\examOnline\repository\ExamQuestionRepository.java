/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.repository;/**
 * Created by KZH on 2019/10/8 15:08.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:08
 * @desc 题目
 **/
public interface ExamQuestionRepository extends LogicRepository<ExamQuestion,String> {

    /**
     * 根据 questionBankCode 获取题目
     * @param questionBankCode
     * @return
     */
    @Query(value =  "select t.*  from us_exam_question t  where t.question_Bank_Code=:questionBankCode  and t.enabled=1 order by t.question_order asc ",
            nativeQuery = true)
    List<ExamQuestion> findAllByQuestionBankCode(@Param("questionBankCode") String questionBankCode);

    /**
     * 根据 questionBankCode 获取题目
     * @param questionBankCode
     * @return
     */
    @Query(value =  "select *" +
            "  from (select t.*" +
            "          from us_exam_question t" +
            "         where t.question_Bank_Code = :questionBankCode" +
            "           and t.question_class = :questionClass" +
            "           and t.enabled = 1" +
            "         order by dbms_random.value)" +
            " where rownum <= 1",
            nativeQuery = true)
    List<ExamQuestion> findAllByQuestionBankCodeAndPowerBuildingExtr(@Param("questionBankCode") String questionBankCode,@Param("questionClass") String questionClass);

    /**
     * 根据 examAppCode 获取试卷属性
     * @param examAppCode
     * @return
     */
    @Query(value =  "select *" +
            "  from (select t.*" +
            "          from us_exam_question t" +
            "         where t.question_Bank_Code = :examAppCode" +
            "           and t.enabled = 1" +
            "            and t.question_type=:questionType" +
            "         order by dbms_random.value()) tt" +
            " where rownum <= :size " +
            "order by tt.question_order asc",
            nativeQuery = true)
    List<ExamQuestion> findAllByQuestionBankCodeRandom(@Param("examAppCode")String examAppCode,@Param("size")String size,@Param("questionType")String questionType);


    /**
     * 根据 questionCodeList 获取题目
     * @param questionCodeList
     * @return
     */
    @Query(value =  "select t.*  from us_exam_question t  where t.question_Code in :questionCodeList  and t.enabled=1 order by t.question_order asc ",
            nativeQuery = true)
    List<ExamQuestion> findAllByQuestionCode(@Param("questionCodeList") List<String> questionCodeList);



    /**
     * 根据 questionCodeList 获取题目
     * @param questionIdList
     * @return
     */
    @Query(value =  "select t.*  from us_exam_question t  where t.id in (:questionIdList)  and t.enabled=1 order by t.question_order asc ",
            nativeQuery = true)
    List<ExamQuestion> findAllByQuestionId(@Param("questionIdList") List<String> questionIdList);

    /**
     * 特殊--固定取前三条为问卷调查题
     * @param questionBankCode
     * @return
     */
    @Query(value =  "select * from (select t.*  from us_exam_question t  where t.question_Bank_Code='A-002' and t.enabled=1 order by t.question_order asc ) where rownum<=3;",
            nativeQuery = true)
    List<ExamQuestion> findAllThree(@Param("questionBankCode") String questionBankCode);


    /**
     * 根据 题目编码 获取题目
     * @param questionCode
     * @return
     */
    @Query(value =  "select t.*  from us_exam_question t  where t.question_Code=:questionCode   and t.enabled=1 ",
            nativeQuery = true)
    ExamQuestion findAllByQuestionCode(@Param("questionCode") String questionCode);

    @Query(value = "select concat(t.question_type , concat('#' , t.question_code)) as code_str , t.question_group_name from US_EXAM_QUESTION t where t.question_bank_code = :questionBankCode and t.enabled = 1 ",nativeQuery = true)
    List<Map<String, Object>> findGroupByQuestionBankCode(@Param("questionBankCode") String questionBankCode);

    @Query(value = "select t.question_code, CONCAT(CONCAT(t.question_group_name, '-'), t.question_type) AS type ,concat(t.question_type , concat('#' , t.question_code)) as code_str , t.question_group_name from US_EXAM_QUESTION t where t.question_bank_code = :questionBankCode and t.enabled = 1 ",nativeQuery = true)
    List<Map<String, Object>> findByQuestionBankCode(@Param("questionBankCode") String questionBankCode);

    @Query(value = " select t.*  from us_exam_question t  where  t.enabled=1  AND t.question_bank_code = :questionBankCode and t.id not in( :ids )  order by t.question_order asc    ",nativeQuery = true)
    List<ExamQuestion> findAllByQuestionBankCodeAndExamNotIn(@Param("questionBankCode") String questionBankCode, @Param("ids") List<String> ids);
}
