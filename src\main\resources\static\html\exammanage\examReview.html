<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>试卷评测</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <style>
        .scoreBox {
            font-size: 18px;
            margin-left: 13px;
        }
        #score {
            font-size: 22px;
            color: red;
        }
        .panel.combo-p {
            height: 100px;
            overflow-y: auto;
            border: 1px solid #e6e6e6;
        }
        .combo-panel.panel-body.panel-body-noheader {
            border: none;
        }
    </style>
    <script type="text/javascript">
        function loadMyGrid (params,paramsT) {
            var pageparam = {
                "listtable": {
                    "listname": "#examReviewTable",//table列表的id名称，需加#
                    "querycmd": "action/examInfo/findUserInfoByExamCode?examCode="+params+"&publishTruename="+paramsT,//table列表的查询命令
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "sortOrder": 'desc',
                    "columns": [[//列
                        {title: "答题人", field: "publishTruename", width: 150,sortable: true, tooltip: true,align:"center"},
                        {title: "部门名称", field: "departmentName", width: 300,sortable: true, tooltip: true,align:"center"},
                        {
                            field: "opt", title: "操作", width: 250, rowspan: 1,sortable: true, tooltip: true,align:"center",//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var id = row.id;
                                var username = row.publishUsername;
                                var url = "html/exammanage/xunchazhenggai.html"
                                var actionType = "secrecyJoin"; //待办
                                var g = "<a class='detail col_b' index='"+index+"'path='"+url+"?id="+id+"&examAppCode="+row.examAppCode+"&examCode="+row.examCode+"&username="+username+"&actionType="+actionType+ "&currentUserCode="+row.creator+"&titName="+encodeURI(row.companyName)+"&type=piyue&piyueId="+row.id+"'>【办理】</a>";
                                return g;
                            }
                        }

                    ]]
                }
            };
            loadGrid(pageparam)
        }
        $(function () {
            $(".searchAllPeople").click(function () {
                if (!getFormValue("examCodeForm").examCode) {
                    getparent().mesShow("温馨提示","请选择试卷", 1000,'red');
                } else {
                    loadMyGrid(getFormValue("examCodeForm").examCode, getFormValue("examCodeForm").publishTruename)
                }
            })
            //办理
            $(document).on("click","a.detail",function(){
                console.log($(this).attr("path"))
                top.dialogP($(this).attr("path") , window.name,'办理','detail',true,'maximized','maximized',listLoad);
            });
            //刷新页面
            function listLoad(){
                if(window.parent.document.getElementById('detailF').contentWindow.remainTimeT) clearInterval(window.parent.document.getElementById('detailF').contentWindow.remainTimeT);
                $(this).parent().siblings(".window-shadow").remove();
                $(this).parent().siblings(".window-mask").remove();
                $(this).parent().remove();
                $("#secrecyTask").datagrid("reload");
            }
        })
    </script>
</head>
<body class="body_page">
<form id="examCodeForm">
    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td width="150" align="right">请选择要批阅的考试：</td>
            <td width="280">
                <input  name="examCode" type="text" class="easyui-combobox" editable="false" style="width:100%;height: 32px"
                        data-options="valueField: 'examCode',
                             panelHeight:'auto',
                             textField: 'examName',
                             contentType:'application/json; charset=utf-8',
                             url: web.rootdir+'action/summary/findAllNoPage',
                             prompt:'--请选择--'"/>
            </td>
            <td width="150" align="right">用户名：</td>
            <td width="150">
                <input name="publishTruename" type="text" value=""/>
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl searchAllPeople "><font>查询</font></a>
                </div>
            </td>
            <td></td>
        </tr>
    </table>
</form>
<div class="examReviewTable">
    <table id="examReviewTable"></table>
</div>
</body>
</html>
