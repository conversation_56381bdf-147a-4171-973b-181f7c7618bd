/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;/**
 * Created by KZH on 2019/10/8 10:14.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @create 2019-10-08 10:14
 * @desc 题库
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_question_bank")
@ApiModel(value = "题库表")
public class ExamQuestionBank extends LogicModel {


    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EQB") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "题库编码",name = "questionBankCode",example = "A-001",required = true)
    private String questionBankCode;

    @Column(length = 250)
    @ApiModelProperty(value = "题库名称",name = "questionBankName",example = "反腐倡廉教育题库",required = true)
    private String questionBankName;

    @Column(length = 40)
    @ApiModelProperty(value = "题库数量",name = "questionBankSize",example = "15",required = true)
    private Integer questionBankSize;

    @Column(length = 40)
    @ApiModelProperty(value = "题库类型")
    private String type;

    @Column(length = 40)
    @ApiModelProperty(value = "创建者")
    private String truename;

}
