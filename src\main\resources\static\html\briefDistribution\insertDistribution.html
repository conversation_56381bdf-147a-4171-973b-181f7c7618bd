<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>简报派发列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/static/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/static/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/exam.js?v=svn.revision" th:src="@{/static/js/exam.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .panel.combo-p {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #e6e6e6;
        }
        .combo-panel.panel-body.panel-body-noheader {
            border: none;
        }
    </style>
    <script type="text/javascript">
        $(function () {
            var pageparam = {
                "listtable": {
                    "listname": "#insertDistributionTable",//table列表的id名称，需加#
                    "querycmd": "action/applyForm/findListByPage",//table列表的查询命令
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "sortOrder": 'desc',
                    "styleClass": "noScroll",
                    "columns": [[//列
                        {title: "标题", field: "title", width: 300,sortable: true, tooltip: true,align:"center"},
                        {title: "状态", field: "status", width: 80,sortable: true, tooltip: true,align:"center",
                            formatter:function (value,row,index) {
                                if(value == "0"){
                                    value = "立即发送"
                                }else if(value == "1"){
                                    value = "已发送"
                                }else if(value == "2"){
                                    value = "待发送"
                                }else if(value == "3"){
                                    value = "发送存在失败"
                                }else{
                                    value = ""
                                }
                                return value
                            }
                        },
                        {title: "创建人", field: "creator", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "创建时间", field: "createdTime", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "发送人", field: "senderTrueName", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "发送时间", field: "sendTime", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "接收人数", field: "receiveNumber", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "已阅人数", field: "viewedNumber", width: 100,sortable: true, tooltip: true,align:"center"},
                        {
                            field: "opt", title: "操作", width: 250,sortable: true, tooltip: true,align:"center", rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g=[];
                                if(row.status == "0" || row.status == "1"){
                                    g.push("<a href='#' class='infoDetail' id=" + row.id + ">【消息明细】</a>");
                                    // g.push("<a class='transpond' href='#'   id=" + row.id + ">【转发】</a>");
                                }else{
                                    g.push("<a class='edit' ptitle='修改'  path='html/briefDistribution/addDistribution.html?id=" + row.id + " '>【编辑】</a>");
                                    g.push("<a href='#' delete='action/applyForm/deleteById?id=" + row.id + "'>【删除】</a>");
                                    // g.push("<a class='sendForm' href='#' add='action/applyForm/send?id=" + row.id + "'>【发送】</a>");
                                }
                                return g.join("");
                            }
                        }

                    ]]
                },
            };
            loadGrid(pageparam);
        });

        function dialogClosed(){
            top.dialogClose('addDistributionFun');
            top.dialogClose('updateDistribution');
            // $('#insertDistributionTable').datagrid('reload')         
            setTimeout(function(){
                $('#insertDistributionTable').datagrid('reload')         
            },2000)
        }

        // 信息明细
        $(document).on("click", ".infoDetail", function () {
            var $t = $(this);
            var id = $t.attr("id");
            top.dialogP('html/message/infopage/infopageDetail.html?id=' + id, window.name, '信息明细', 'infopageDetailFun', true, 800,600,close);
        });
        // 修改简报
        $(document).on("click", ".edit", function () {
            var $t = $(this);
            var url = $t.attr("path") + '&mytype=edit';
            top.dialogP(url, 'insertDistribution', '修改简报', 'updateDistribution', true, 'maximized',close);
        });

        // 新增简报
        $(document).on("click", ".addInfo", function () {
            top.dialogP('html/briefDistribution/addDistribution.html?mytype=add', window.name, '新增简报', 'addDistributionFun', true, "maximized",close);
        });

        // 简报转发
        $(document).on("click", ".transpond", function () {
            var $t = $(this);
            var id = $t.attr("id");
            top.dialogP('html/briefDistribution/addDistribution.html?mytype=transpond'+'&id='+ id, window.name, '转发简报', 'addDistributionFun', true, "maximized",close);
        });
        function addDistributionFun(){}
        function close() {
            $("#insertDistributionTable").datagrid("reload");
        }
        function updateDistribution(data) {}
             //重置查询条件
      $(document).on('click','.reset',function (){
        formreset('insertDistributionTableQueryForm');
      });

    </script>
</head>
<body class="body_page">
<form id="insertDistributionTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td colspan="5" width="300">
            </td>
        </tr>
        <tr>
            <td width="90" align="right">标题：</td>
            <td width="150">
                <input name="title" type="text" value=""/>
            </td>
            <td width="90" align="right" >状态：</td>
            <td width="150">
                <input  name="status" type="text" class="easyui-combobox" editable="false" style="width:100%;height: 32px"
                        data-options="valueField: 'value',
                     panelHeight:'auto',
                     textField: 'label',
                     data:[{label:'已发送',value:'1'},{label:'待发送',value:'2'}],
                     prompt:'--请选择--'"/>
            </td>
            <td >
                <div class="w100">
                    <a class="btn fl searchtable"><span>查询</span></a>
                    <a class="btn ml10 reset"><span>重置</span></a>
                    <a class="btn a_green fr addInfo" >新建</a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--searchform-->

<!--table-->
<div class="insertDistributionTable">
    <table id="insertDistributionTable"></table>
</div>
</body>
</html>
