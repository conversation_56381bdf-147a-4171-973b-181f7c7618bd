package com.simbest.boot.exam.study.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.service.IExamQuestionService;
import com.simbest.boot.exam.study.model.TrainingAnswerDetail;
import com.simbest.boot.exam.study.model.WrongQuestionCollection;
import com.simbest.boot.exam.study.repository.WrongQuestionCollectionRepository;
import com.simbest.boot.exam.study.service.IWrongQuestionCollectionService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用途：WrongQuestionCollection领域对象名称服务层实现
 */
@Slf4j
@Service
public class WrongQuestionCollectionServiceImpl extends LogicService<WrongQuestionCollection, String> implements IWrongQuestionCollectionService {

    private WrongQuestionCollectionRepository repository;

    @Autowired
    public WrongQuestionCollectionServiceImpl(WrongQuestionCollectionRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private IExamQuestionService examQuestionService;
    
    /**
     * 异步保存错题信息
     * @param question 错题信息
     * @param answerDetail 答题信息
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveWrongQuestionAsync(ExamQuestion question , TrainingAnswerDetail answerDetail) {
        try {
            String username = answerDetail.getUsername();
            String questionCode = question.getQuestionCode();
            String questionId = question.getId();

            // 构造查询条件
            Specification<WrongQuestionCollection> specification = (root, query, criteriaBuilder) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(criteriaBuilder.equal(root.get("username"), username));
                predicates.add(criteriaBuilder.equal(root.get("questionCode"), questionCode));
                predicates.add(criteriaBuilder.equal(root.get("questionId"), questionId));
                return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
            };
            
            // 查询是否已存在该用户的该题目错题记录
            List<WrongQuestionCollection> existingRecords = repository.findAll(specification);
            
            if (existingRecords != null && !existingRecords.isEmpty()) {
                // 如果已存在，则更新记录
                WrongQuestionCollection existingRecord = existingRecords.get(0);
                existingRecord.setUserAnswer(answerDetail.getUserAnswer());
                existingRecord.setLastWrongTime(LocalDateTime.now());
                existingRecord.setWrongCount(existingRecord.getWrongCount() != null ? existingRecord.getWrongCount() + 1 : 1);
                // 保留其他不变的字段
                this.update(existingRecord);
                log.debug("更新错题记录: username={}, questionCode={}", username, questionCode);
            } else {
                WrongQuestionCollection wrongQuestionCollection = new WrongQuestionCollection();
                // 如果不存在，则新增记录
                wrongQuestionCollection.setUsername(username);
                wrongQuestionCollection.setQuestionId(questionId);
                wrongQuestionCollection.setQuestionCode(questionCode);
                wrongQuestionCollection.setQuestionType(question.getQuestionType());
                wrongQuestionCollection.setQuestionContent(question.getQuestionName());
                wrongQuestionCollection.setUserAnswer(answerDetail.getUserAnswer());
                wrongQuestionCollection.setCorrectAnswer(answerDetail.getQuestionAnswer());
                wrongQuestionCollection.setWrongCount(1);
                wrongQuestionCollection.setLastWrongTime(LocalDateTime.now());
                wrongQuestionCollection.setCreator(username);
                wrongQuestionCollection.setModifier(username);
                this.insert(wrongQuestionCollection);
                log.debug("新增错题记录: username={}, questionCode={}", username, questionCode);
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }

    /**
     * 根绝人员查询错题集
     *
     * @param page  页数
     * @param size  数量
     * @param model 查询条件
     * @return
     */
    @Override
    public JsonResponse findByUsername( String source , String currentUsername,  Integer page, Integer size, WrongQuestionCollection model) {
        if (StrUtil.equals(source, Constants.SOURCE_M) && StrUtil.isNotEmpty(currentUsername)) {
            loginUtils.manualLogin(currentUsername, Constants.APP_CODE);
        }
        Page<WrongQuestionCollection> resultPage = null;
        try {
            IUser iUser = SecurityUtils.getCurrentUser();
            //查询错题数据
            Specification<WrongQuestionCollection> build = Specifications.<WrongQuestionCollection>and()
                    .eq("enabled", Boolean.TRUE)
                    .eq("username", iUser.getUsername())
                    .like(StrUtil.isNotEmpty(model.getQuestionContent()), "questionContent", "%" + model.getQuestionContent() + "%")
                    .build();
            Pageable pageable = this.getPageable(page, size, Sort.Direction.DESC.toString(), "lastWrongTime");
            resultPage = this.findAll(build, pageable);
        } catch (Exception e ) {
            Exceptions.printException(e);
        }
        return JsonResponse.success(resultPage);
    }

    /**
     * 根据id查询错题信息
     *
     * @param id 错题记录id
     * @return
     */
    @Override
    public JsonResponse findQuestionById(String id) {
        Assert.notNull(id , "id不能为空" );
        JsonResponse jsonResponse = null;
        WrongQuestionCollection wrongQuestionCollection = null;
        try {
            wrongQuestionCollection = this.findById(id);
            Assert.notNull(wrongQuestionCollection , "查询错题信息失败");
            ExamQuestion question = examQuestionService.findQuestionAndAnswer(wrongQuestionCollection.getQuestionCode());
            wrongQuestionCollection.setQuestion(question);
            jsonResponse = JsonResponse.success(wrongQuestionCollection);
        } catch (Exception e ) {
            Exceptions.printException(e);
            jsonResponse = JsonResponse.fail("查询失败！");
        }
        return jsonResponse;
    }
}