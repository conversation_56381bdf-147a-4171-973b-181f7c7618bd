package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.web.controller.GenericController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamAnnualQuarterInfo;
import com.simbest.boot.exam.examOnline.service.IExamAnnualQuarterInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-06-8
 * @desc 考试年度季度信息
 **/

@Api(description = "获取考试的年度季度信息", tags = {"获取考试的年度季度信息控制器"})
@Slf4j
@RestController
@RequestMapping("/action/annualQuarter")
public class ExamAnnualQuarterInfoController extends GenericController<ExamAnnualQuarterInfo,String> {
    private IExamAnnualQuarterInfoService service;

    @Autowired
    public ExamAnnualQuarterInfoController(IExamAnnualQuarterInfoService service){
        super(service);
        this.service=service;
    }

    @ApiOperation(value = "根据考试编号获取考试的年度季度信息",notes = "考试年度季度信息")
    @PostMapping(value = {"/getAnnualQuarterInfo", "sso/getAnnualQuarterInfo", "api/getAnnualQuarterInfo"})
    public JsonResponse getAnnualQuarterInfo(@RequestParam(required = false) String annualQuarterCode,@RequestParam(required = false)String createdTime){
        ExamAnnualQuarterInfo annualQuarterInfo = service.findAnnualQuarterInfo(annualQuarterCode, createdTime);
        return   JsonResponse.success(annualQuarterInfo);
    }

    @ApiOperation(value = "获取所有考试的年度季度信息",notes = "考试年度季度信息")
    @PostMapping(value = {"/getAllAnnualQuarterInfo", "sso/getAllAnnualQuarterInfo", "api/getAllAnnualQuarterInfo"})
    public JsonResponse getAllAnnualQuarterInfo(@RequestParam(required = false) String annualQuarterCode){
        List<ExamAnnualQuarterInfo> allAnnualQuarterInfo = service.findAllAnnualQuarterInfo(annualQuarterCode);
        return   JsonResponse.success(allAnnualQuarterInfo);
    }
}
