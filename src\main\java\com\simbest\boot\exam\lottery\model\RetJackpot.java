/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.model;

import lombok.Data;

/**
 * <strong>Title : RetJackpot</strong><br>
 * <strong>Description : 返回对象 </strong><br>
 * <strong>Create on : 2021/4/10</strong><br>
 * <strong>Modify on : 2021/4/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Data
public class RetJackpot {

    // 几等奖
    private String getPrize;

    // 是否中奖
    private Boolean isJackpot;
}
