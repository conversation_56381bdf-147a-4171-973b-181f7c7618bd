package com.simbest.boot.exam.examOnline.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.examOnline.model.ExamInformation;
import com.simbest.boot.exam.examOnline.model.ExamSummary;
import com.simbest.boot.exam.examOnline.repository.ExamInformationRepository;
import com.simbest.boot.exam.examOnline.service.IExamInformationService;
import com.simbest.boot.exam.examOnline.service.IExamSummaryService;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.security.IPosition;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ExamInformationServiceImpl extends LogicService<ExamInformation, String> implements IExamInformationService {
    private ExamInformationRepository examInformationRepository;
    @Autowired
    private IExamSummaryService examSummaryService;

    @Autowired
    private IExamWorkService iExamWorkService;

    public ExamInformationServiceImpl(ExamInformationRepository repository) {
        super(repository);
        this.examInformationRepository = repository;
    }


    /**
     * 根据考试编码查询考试信息
     *
     * @param examCode
     * @param creator
     * @return
     */
    @Override
    public ExamInformation findExamInformationByExamCode(String examCode, String creator) {
        String currentUserName = SecurityUtils.getCurrentUserName();
        if (ObjectUtil.isNotEmpty(creator)) {
            currentUserName = creator;
        }
        Specification<ExamInformation> specification = Specifications.<ExamInformation>and()
                .eq("examCode", examCode)
                .eq("publishUsername", currentUserName)
                .eq("enabled", true)
                .build();
        return this.findOne(specification);
    }

    /**
     * 根据考试编码查询考试信息
     *
     * @param examCode
     * @param creator
     * @return
     */


    @Override
    public List<ExamInformation> submitExam(String currentUserCode, String source, ExamInformation examInformation) {
        ExamInformation result;
        //判断是否已过考试时间
        ExamSummary examSummaryByCode = examSummaryService.findExamSummaryByCode(examInformation.getExamCode());
        LocalDateTime examEndTime = examSummaryByCode.getExamEndTime();

        LocalDateTime nowTime = LocalDateTime.now();
        boolean before = nowTime.isBefore(examEndTime);
        Assert.isTrue(before, "考试时间已过！");
        //获取当前人的信息
        IUser currentUser = SecurityUtils.getCurrentUser();
//        String companyCode = currentUser.getBelongCompanyCode();
//        String companyName = currentUser.getBelongCompanyName();
        Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
        List<String> positionName = Lists.newArrayList();
        for (IPosition authPosition : authPositions) {
            positionName.add(authPosition.getPositionName());
        }
        List<ExamInformation> informationList = examInformation.getExamInformationList();
        List<ExamInformation> informations = Lists.newArrayList();
        for (ExamInformation information : informationList) {
            if("02".equals(currentUser.getBelongCompanyTypeDictValue())){
                information.setCompanyCode(currentUser.getBelongCompanyCode());
                information.setCompanyName(currentUser.getBelongCompanyName());
            }else {
                information.setCompanyCode(currentUser.getBelongCompanyCodeParent());
                information.setCompanyName(currentUser.getBelongCompanyNameParent());
            }
            information.setExamAppCode(examInformation.getExamAppCode());
            information.setExamCode(examInformation.getExamCode());
            information.setPublishUsername(examInformation.getPublishUsername());
            information.setPublishTruename(examInformation.getPublishTruename());
            information.setPositionName(StringUtils.join(positionName, ","));
            Integer questionIndex = information.getQuestionIndex();
            if (questionIndex < 6) {
                information.setQuestionCategory("单位有变化");
            }
            if (questionIndex < 11 && questionIndex > 5) {
                information.setQuestionCategory("员工有感知");
            }
            if (questionIndex < 16 && questionIndex > 10) {
                information.setQuestionCategory("各方有认同");
            }
            if (questionIndex < 21 && questionIndex > 15) {
                information.setQuestionCategory("生态有改善");
            }
            information.setIsFinishExam(true);
            information.setIsMarkingExam(false);
            result = this.insert(information);
            informations.add(result);
        }
//核销待办
// 将考试编码转换为待办类型
        ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode(examInformation.getExamCode());
        Assert.notNull(summaryInfo, "考试信息不存在!");
        String workType = summaryInfo.getWorkType();
        Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
        // 根据提交人和待办类型获取待办工作并进行核销操作
        List<ExamWork> workInfo = iExamWorkService.findByUsernameAndWorkType(examInformation.getPublishUsername(), workType);
        for (ExamWork examWork : workInfo) {
            if (null != examWork) {
                iExamWorkService.dealWith(source, currentUserCode, examWork.getId());
            } else {
                // 提交问卷操作时待办一定存在
                log.error("根据username【{}】、workType【{}】未能成功核销统一待办!", examInformation.getPublishUsername(), workType);
            }
        }
        return informations;
    }

    @Override
    public ExamInformation findExamInformation(String questionCode, String publishUsername) {
        ExamInformation examInformation = examInformationRepository.findAnswer(questionCode, publishUsername);
        return examInformation;
    }
}
