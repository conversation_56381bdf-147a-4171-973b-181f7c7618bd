<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
  <title>考试维护信息</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
  <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
    rel="stylesheet" />
  <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
  <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
  <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
  <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
  <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
    type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
    type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
    type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
  <style>
    input {
      width: 350px;
    }
  </style>
  <script type="text/javascript">
    var timu = []
    $(function () {
      //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
      var gps = getQueryString();
      loadForm("examBacklogTableUpdateForm");
      //判断是否是查看方式打开
      if (gps.type == 'read') {//如果是查看

        ajaxgeneral({
          url: "action/examAttribute/findById?id=" + gps.id,
          success: function (res) {
            formval(res.data, "examTableReadForm");
            if (res.data.topicStyle == 'random') {
              $('.topicNumber').show()
              $('.topicNumber input').show()
            } else {
              $('.topicNumber').hide()
              $('.topicNumber input').hide()
            }
          }
        });
        formReadonly("examTableReadForm");
        $(".noEdit").hide();

      } else if (gps.type == 'edit') {//如果是编辑

      // 出题方式下拉框的触发事件
      $('#topicStyle').combobox({
        onSelect: function (param) {
          if (param.value == "random") {
            $(".topicNumber").show()
            $(".topicNumber input").show()
          } else if (param.value == "fixed") {
            $(".topicNumber").hide()
            $(".topicNumber input").hide()
            top.dialogP("html/exam/examFixed.html", window.name, '固定出题', 'examImport', false, '700', '700');
          } else if (param.value == "original") {
            $(".topicNumber").hide()
            $(".topicNumber input").hide()

          }
        }
      });
        ajaxgeneral({
          url: "action/examAttribute/findById?id=" + gps.id,
          success: function (res) {
            formval(res.data, "examTableReadForm");
            localStorage.setItem('itemID', res.data.questionBankCode)
            if (res.data.topicStyle == 'random') {
              $('.topicNumber').show()
              $('.topicNumber input').show()
            } else {
              $('.topicNumber').hide()
              $('.topicNumber input').hide()
            }
          }
        });
        $(".noEdit").hide();

      }

      //动态获取input内容用提示框展示
      $('.tips').tooltip({
        content: '<span style="color:#000"></span>',
        onShow: function () {
          $(this).tooltip('tip').css({
            backgroundColor: '#fff',
            borderColor: '#000'
          });
          var txt = $('input', this).val();
          $(this).tooltip('tip').html(txt);
        }
      });


      //修改验证框样式
      $('.easyui-validatebox').validatebox({
        tipPosition: 'right',
        validateOnCreate: false
      });
      // $('.easyui-validatebox').validatebox('disableValidation');

      if(gps.type == "read") {
        idReadonly('examRemark')
      }




    });
    //   固定出题回调
    function examImport(param) {
      timu = param.data.arr
    }










    //表单校验
    window.fvalidate = function () {
      return $("#examBacklogTableUpdateForm").form("validate");
    };

    //重新加载table列表
    function loadList() {
      $('#examBacklogTableUpdateForm').datagrid('reload');
    }

    window.getchoosedata = function () {
      // var data = getFormValue("examTableReadForm");
      //console.log(data);
      // let params = {}
      // let examAttribute = {}
      // examAttribute.examName = data.examName
      // examAttribute.examAppCode = data.examAppCode
      // examAttribute.examRemark = data.examRemark
      // examAttribute.filling = data.filling
      // examAttribute.judge = data.judge
      // examAttribute.more = data.more
      // examAttribute.setTime = data.setTime
      // examAttribute.shortAnswer = data.shortAnswer
      // examAttribute.single = data.single
      // examAttribute.topicSum = data.topicSum
      // examAttribute.questionBankCode = data.questionBankCode
      // examAttribute.topicStyle = data.topicStyle
      // examAttribute.topicNumber = data.topicNumber
      // params.examAttribute = examAttribute
      // params.examAttributeQuestions = []
      // let a = params.examAttributeQuestions
      // for (var i = 0; i < timu.length; i++) {
      //   let params = {}
      //   params.examAppCode = data.examAppCode
      //   params.questionBankCode = data.questionBankCode
      //   params.questionCode = timu[i]
      //   a.push(params)
      // }
      // ajaxgeneral({
      //     url: "action/examAttribute/updateExamPaper",
      //     data:params,
      //     success: function (res) {
      //       //console.log(res);
      //     }
      //   });
      return { "data": 1, "state": 1 };
    }
  </script>
</head>

<body style="padding-top: 0;">
  <!--searchform-->
  <div class="table_searchD">
    <form id="examTableReadForm" method="post" contentType="application/json; charset=utf-8">
      <table border="0" cellpadding="0" cellspacing="10" width="100%">

        <tr>
          <td width="15%" align="left">试卷名称：</td>
          <td width="20%" length="100">
            <input id="examName" name="examName" type="text" class="examName" />
          </td>
        </tr>

        <tr>
          <td width="15%" align="left">系统编号：</td>
          <td width="20%" length="100">
            <input id="examAppCode" name="examAppCode" type="text" class="examAppCode" />
          </td>
        </tr>

        <tr>
          <td width="15%" align="left">总题目数：</td>
          <td width="20%" length="100">
            <input id="topicSum" name="topicSum" type="text" class="topicSum" />
          </td>
        </tr>

        <tr>
          <td width="10%" align="left">
            <font class="col_r"></font>题库编码：
          </td>
          <td width="20%" length="100">
            <input id="questionBankCode" name="questionBankCode" class="easyui-combobox"
              style="width:  350px; height: 32px;" data-options="
                       valueField: 'questionBankCode',
                       panelHeight:'auto',
                       textField: 'questionBankName',
                       contentType:'application/json; charset=utf-8',
                       url: web.rootdir+'action/examQuestionBank/findAllNoPage'" />
          </td>
        </tr>
        <tr>
          <td width="15%" align="left">出题方式：</td>
          <td width="20%" length="100">
            <input id="topicStyle" name="topicStyle" class="easyui-combobox" style="width:  350px; height: 32px;"
              data-options="
                valueField: 'value',
                panelHeight:'auto',
                textField: 'name',
                editable:false,
                data:[{value:'original',name:'全题库'},{value:'random',name:'随机出题'},{value:'fixed',name:'固定出题'}]" />
          </td>
        </tr>
        <tr class="topicNumber" style="display: none">
          <td width="15%" align="left">随机出题数量：</td>
          <td width="20%" length="100">
            <input id="topicNumber" name="topicNumber" type="text" value="" class="easyui-validatebox"
              style="width:  350px; height: 32px;display: none" />
            <!--                                        <input id="topicNumber" name="topicNumber" type="text" value="" class="easyui-validatebox" data-options="required:true"  style="width:  350px; height: 32px;display: none"/>-->
          </td>
        </tr>
        <tr>
          <td width="15%" align="left">单选题比例：</td>
          <td width="20%" length="100">
            <input id="single" name="single" type="text" class="single" />
          </td>
        </tr>

        <tr>
          <td width="15%" align="left">多选题比例：</td>
          <td width="20%" length="100">
            <input id="more" name="more" type="text" class="more" />
          </td>
        </tr>

        <tr>
          <td width="15%" align="left">判断题比例：</td>
          <td width="20%" length="100">
            <input id="judge" name="judge" type="text" class="judge" />
          </td>
        </tr>

        <tr>
          <td width="15%" align="left">填空题比例：</td>
          <td width="20%" length="100">
            <input id="filling" name="filling" type="text" class="filling" />
          </td>
        </tr>

        <tr>
          <td width="15%" align="left">简答题比例：</td>
          <td width="20%" length="100">
            <input id="shortAnswer" name="shortAnswer" type="text" class="shortAnswer" />
          </td>
        </tr>

        <tr>
          <td width="15%" align="left">试卷规定时间限制：</td>
          <td width="20%" length="100">
            <input id="setTime" name="setTime" type="text" class="setTime" />
          </td>
        </tr>
        <tr>
            <td width="10%" align="left">试卷简介：
            </td>
            <td width="20%" length="100">
                <textarea id="examRemark" name="examRemark" class="easyui-validatebox"
                    style="width:350px;height:100px;resize:both;"></textarea>

            </td>
        </tr>


      </table>

    </form>
  </div>

  <!-- <div class="appInfoTable" style="margin-top: 20px;"><table id="appInfoTable"></table></div> -->
</body>

</html>