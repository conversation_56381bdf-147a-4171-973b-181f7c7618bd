package com.simbest.boot.exam.appraise.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.appraise.model.AppraiseWork;
import com.simbest.boot.exam.appraise.service.IAppraiseWorkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@Slf4j
@RestController
@RequestMapping("/action/appraise/work")
public class AppraiseWorkController extends LogicController<AppraiseWork, String> {

    private final IAppraiseWorkService service;

    public AppraiseWorkController(IAppraiseWorkService service) {
        super(service);
        this.service = service;
    }

    /**
     * 获取评价布局信息
     *
     * @param username 当前登录人
     * @param workId   评价工单id
     * @return 评价封装信息
     */
    @RequestMapping(value = {"/getInfo", "/getInfo/sso", "/api/getInfo"})
    public JsonResponse getInfo(String username, String workId) {
        return JsonResponse.success(service.getInfo(username, workId));
    }

    /**
     * 提交用户评价信息
     *
     * @param source          当前操作来源
     * @param currentUserCode 当前操作人编码
     * @param map             用户评价信息（加密）
     * @return 用户评价信息保存结果
     */
    @RequestMapping(value = {"/submitInfoSalt", "/submitInfoSalt/sso", "/api/submitInfoSalt"})
    public JsonResponse submitInfoSalt(@RequestParam String source,
                                       @RequestParam String currentUserCode,
                                       @RequestBody Map<String, String> map) {
        String data = map.get("data");
        Assert.notNull(data, "用户评价信息不能为空, 检查参数！");
        return JsonResponse.success(service.submitInfoSalt(source, currentUserCode, data));
    }

//    @RequestMapping(value = {"/test", "/test/sso", "/api/test"})
//    public void test() {
//        String encrypt = aesEncryptor.encrypt("{\n" +
//                "    \"workId\": \"UAW694559070541901824\",\n" +
//                "    \"templateIds\": \"UAT693136368551694336,UAT693136368845295616,UAT693136369449275392,UAT693136370804035584,UAT693136371391238144,UAT693136373383532544,UAT693136373727465472,UAT693136374209810432,UAT693136375199666176,UAT693136375682011136,UAT693136375946252288\",\n" +
//                "    \"contents\": \"1,1,1,1,1,1,1,1,1,1,1234\"\n" +
//                "}");
//        System.out.println(encrypt);
//    }
//
//    @Autowired
//    private AesEncryptor aesEncryptor;

}
