/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamSummary;
import com.simbest.boot.exam.examOnline.model.ExamSurvey;
import com.simbest.boot.exam.examOnline.model.ExamTask;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ExamSurveyRepository extends LogicRepository<ExamSurvey,String> {

    //查询用户是否办理过
    @Query(value = "select * from us_exam_survey t where t.ENABLED=1 and t.USER_NAME=:userName",
            nativeQuery = true)
    ExamSurvey findExamSurvey(@Param("userName") String userName);



    @Query(value = "select * from us_exam_survey t where t.ENABLED=1 and t.PM_INS_ID=:pmInsId",
            nativeQuery = true)
    ExamSurvey findExamSurveyByPmInsId(@Param("pmInsId") String pmInsId);

}
