package com.simbest.boot.exam.appraise.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.exam.appraise.model.AppraiseItem;
import com.simbest.boot.exam.appraise.service.IAppraiseItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/action/appraise/item")
public class AppraiseItemController extends LogicController<AppraiseItem, String> {

    private final IAppraiseItemService service;

    public AppraiseItemController(IAppraiseItemService service) {
        super(service);
        this.service = service;
    }

}
