package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.SystemRepository;
import com.simbest.boot.exam.examOnline.model.ExamAnnualQuarterInfo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-06-07 11:09
 * @desc
 **/
public interface ExamAnnualQuarterInfoRepository extends SystemRepository<ExamAnnualQuarterInfo,String> {
    @Query(value = "select qi.* from us_exam_annual_quarter_info qi where qi.ANNUAL_QUARTER_CODE like :annualQuarterCode " ,
            nativeQuery = true)
    ExamAnnualQuarterInfo findExamAnnualQuarterInfoByAnnualQuarterCode(@Param("annualQuarterCode") String annualQuarterCode);

    //待办问卷考试中的试卷标题获取
    @Query(value = "select * from (select qi.* from us_exam_annual_quarter_info qi where qi.ANNUAL_QUARTER_CODE like :annualQuarterCode " +
            "order by qi.CREATED_TIME DESC ) where rownum=1 ",
            nativeQuery = true)
    ExamAnnualQuarterInfo findExamAnnualQuarterInfoByAnnualQuarterCode2(@Param("annualQuarterCode") String annualQuarterCode);

    @Query(value = "select qi.* from us_exam_annual_quarter_info qi where qi.ANNUAL_QUARTER_CODE like :annualQuarterCode " +
            "order by qi.CREATED_TIME DESC  ",
            nativeQuery = true)
    List<ExamAnnualQuarterInfo> findAllExamAnnualQuarterInfoByAnnualQuarterCode(@Param("annualQuarterCode") String annualQuarterCode);
}
