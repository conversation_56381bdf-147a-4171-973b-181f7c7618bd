package com.simbest.boot.exam.message.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.message.model.MessageModel;
import com.simbest.boot.exam.message.repository.MessageRepository;
import com.simbest.boot.exam.message.service.IMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MessageServiceImpl extends LogicService<MessageModel, String> implements IMessageService {
    public MessageRepository repository;

    public MessageServiceImpl(MessageRepository repository) {
        super(repository);
        this.repository = repository;
    }

    /**
     * 根据考试编码查询短信内容
     *
     * @param examCode 考试编码
     * @return 短信内容
     */
    @Override
    public String findMessageContentByExamCode(String examCode) {
        return repository.findMessageContentByExamCode(examCode);
    }
}
