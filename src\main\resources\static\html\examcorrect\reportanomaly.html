<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>题库页面</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
        type="text/javascript"></script>
    <script type="text/javascript">
        $(function () {
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    /*"idFiled":"ID", 用于自定义id的情况，当接口查出的id不为id，比如是ID时，在这里设置字段名 */
                    "listname": "#examQuestionBankTable",//table列表的id名称，需加# menuExpalinTable
                    "querycmd": "action/modifyReport/queryModifyReport",//table列表的查询命令
                    "checkboxall": false,
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "columns": [[//列
                        { title: "题目分组", field: "questionGroupName", width: 120, sortable: true, tooltip: true, align: "center" },
                        { title: "题目名称", field: "questionContent", width: 220, sortable: true, tooltip: true, align: "center" },
                        {
                            title: "题目类型", field: "questionType", width: 100, sortable: true, tooltip: true, align: "center", formatter: function (value, row, index) {
                                return value == 'single' ? '单选题' : value == 'more' ? '多选题' : '判断题'
                            }
                        },
                        { title: "上报人", field: "truename", width: 100, sortable: true, tooltip: true, align: "center" },
                        { title: "上报时间", field: "createdTime", width: 120, sortable: true, tooltip: true, align: "center" },
                        {
                            field: "opt", title: "操作", width: 110, sortable: true, tooltip: true, align: "center", rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【办理】</a>"
                                // +"<a href='#' delete='action/examQuestionBank/delExamQuestionBank' deleteid='"+row.id+"'>【删除】</a>";
                                return g;
                            }
                        }
                    ]],
                    // "pagerbar": [{
                    //     id: "deleteall",
                    //     iconCls: 'icon-remove',
                    //     text: "批量删除&nbsp;"
                    // }],
                    "deleteall": {//批量删除deleteall.id要与pagerbar.id相同
                        "id": "deleteall",
                        "url": "action/examQuestionBank/deleteAllByIds",
                        "contentType": "application/json; charset=utf-8"
                    }
                },
                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "formname": "#examQuestionBankTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/examQuestionBank/createExamQuestionBank",//新增命令
                    "updatacmd": "action/examQuestionBank/updateExamQuestionBank",//修改命令
                    "onSubmit": function (data) {
                        console.log('提交表单，调用save方法');
                        return save(); // 调用save方法而不是直接返回true
                    }
                }
            };
            loadGrid(pageparam);

            // 为查看详情链接添加点击事件
            $(document).on('click', '.showDialog', function () {
                var index = $(this).attr('showDialogindex');
                // 获取表格数据
                var row = $(pageparam.listtable.listname).datagrid('getData').rows[index];

                // 设置弹窗标题
                $('#buttons').dialog('setTitle', '题目详情');
                // 清空原有表单内容
                $('#examQuestionBankTableAddForm')[0].reset();
                ajaxgeneral({
                    url: "action/modifyReport/findQuestionById?id=" + row.id,
                    contentType: "application/json; charset=utf-8",
                    success: function (datas) {
                        console.log('datas', datas);
                        var list = datas.data.answerList;
                        // 渲染详情数据到弹窗
                        renderDetail(row, list);
                    }
                });

                // 重新配置对话框按钮
                $('#buttons').dialog({
                    buttons: [
                        {
                            text: '确认调整',
                            handler: function () {
                                save(1);
                            }
                        },
                        {
                            text: '无需调整',
                            handler: function () {
                                save(2);
                            }
                        }
                    ]
                });

                // 打开弹窗
                $('#buttons').dialog('open');

                return false;
            });
        });
        var questionTypes = ''
        // 渲染详情数据到弹窗
        function renderDetail(data, list) {
            // 清空弹窗内容
            var form = $('#examQuestionBankTableAddForm');
            form.find('table').html('');
            questionTypes = data.questionType
            // 创建详情内容
            // language=HTML
            var detailHtml = `
                <tr>
                    <td width="100" align="right" class="detail-label">题目名称:</td>
                    <td width="350" class="detail-value">${data.questionContent}</td>
                </tr>
                <tr>
                    <td width="100" align="right" class="detail-label">题目类型:</td>
                    <td width="350" class="detail-value">
                        <select id="questionTypeSelect" ${data.questionType == 'judge' ? 'disabled' : ''} style="width:200px;">
                            <option value="single" ${data.questionType == 'single' ? 'selected' : ''}>单选题</option>
                            <option value="more" ${data.questionType == 'more' ? 'selected' : ''}>多选题</option>
                            <option value="judge" ${data.questionType == 'judge' ? 'selected' : ''} ${(data.questionType == 'single' || data.questionType == 'more') ? 'disabled' : ''}>判断题</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="100" align="right" class="detail-label">选项:</td>
                    <td width="350" class="detail-value">
                        <div style="line-height:24px;">
                            ${generateOptions(data.questionType, list)}
                        </div>
                    </td>
                </tr>
            `;

            // 添加到表单
            form.find('table').html(detailHtml);

            // 为题目类型下拉框添加change事件监听
            $('#questionTypeSelect').on('change', function () {
                // 清空之前选择的答案
                window.selectedAnswer = [];

                // 获取新的题目类型
                var newQuestionType = $(this).val();

                // 重新生成选项HTML - 使用更精确的选择器
                var optionsTd = form.find('table tr:nth-child(3) td.detail-value');
                var optionsDiv = optionsTd.find('div');
                var newOptionsHtml = generateOptions(newQuestionType, list);
                optionsDiv.html(newOptionsHtml);
            });
        }

        // 根据题目类型和选项数据生成选项HTML
        function generateOptions(questionType, optionsList) {
            var html = '';
            // 存储选择结果的变量
            var result = [];

            // 判断是单选/判断还是多选
            var isSingleOrJudge = questionType === 'single' || questionType === 'judge';
            var inputType = isSingleOrJudge ? 'radio' : 'checkbox';
            var inputName = isSingleOrJudge ? 'answerOption' : 'answerOptions[]';

            if (optionsList && optionsList.length > 0) {
                optionsList.forEach(function (option, index) {
                    html += `
                    <div>
                        <input type="${inputType}" class="exampleOption" name="${inputName}" value="${option.answerCode}" />
                        ${option.answerContent}
                    </div>`;
                });
            }

            // 添加选择事件监听
            setTimeout(function () {
                $(document).off('change', '[name="' + inputName + '"]').on('change', '[name="' + inputName + '"]', function () {
                    updateSelectedOptions(inputType, inputName);
                });
            }, 10);

            return html;
        }

        // 更新选择的选项并存储到result变量
        function updateSelectedOptions(type, name) {
            var result = [];

            if (type === 'radio') {
                // 单选/判断类型
                var selectedVal = $('[name="' + name + '"]:checked').val();
                if (selectedVal) {
                    result.push(selectedVal);
                }
            } else {
                // 多选类型
                $('[name="' + name + '"]:checked').each(function () {
                    result.push($(this).val());
                });
            }

            console.log('选择的答案:', result);
            // 存储选择结果到全局变量，供保存时使用
            window.selectedAnswer = result;
        }

        // 保存选择的答案
        function save(type) {
            // 获取题目ID
            var questionId = $('#id').val();

            // 获取选择的答案
            var selectedAnswers = window.selectedAnswer || [];
            if (selectedAnswers.length == 0 && type == 1) {
                $.messager.alert('提示', '请选择答案！', 'error');
                return false
            }
            // 构建保存数据
            var saveData = {
                id: questionId,
                status: type,
                questionType: $('#questionTypeSelect').val(), // 从下拉框获取题目类型
                answerCode: selectedAnswers.join(',')
            };

            // 调用保存接口
            ajaxgeneral({
                url: "action/modifyReport/updateQuestionInfo",
                type: "POST",
                contentType: "application/json; charset=utf-8",
                data: saveData,
                success: function (response) {
                    if (response.errcode === 0) {
                        // $.messager.alert('提示', '保存成功！', 'info');
                        // 关闭对话框
                        $('#buttons').dialog('close');
                        // 刷新表格 - 使用直接的选择器代替pageparam变量
                        $('#examQuestionBankTable').datagrid('reload');
                    } else {
                        $.messager.alert('错误', response.msg || '保存失败！', 'error');
                    }
                },
                error: function () {
                    $.messager.alert('错误', '网络错误，请重试！', 'error');
                }
            });

            return false; // 阻止表单默认提交
        }
    </script>
</head>

<body class="body_page">
    <!--searchform-->
    <form id="examQuestionBankTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <input id="programaCode" name="programaCode" type="hidden" />
            <tr>
                <td width="100" align="right">题目分组</td>
                <td width="150"><input name="questionGroupName" type="text" value="" />
                </td>
                <td width="100" align="right">题目名称</td>
                <td width="150"><input name="questionContent" type="text" value="" />
                </td>
                <td>
                    <div class="w100">
                        <a class="btn a_primary fl searchtable"><span>查询</span></a>
                    </div>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
    <div class="examQuestionBankTable">
        <table id="examQuestionBankTable">
        </table>
    </div>
    <!--新增修改的dialog页面-->
    <div id="buttons" title="题目详情" class="easyui-dialog" style="width:500px;height:400px;" closed="true">
        <style>
            .exampleOption {
                display: inline-block;
                width: 30px;
            }

            .detail-table {
                border-collapse: collapse;
                width: 100%;
            }

            .detail-table td {
                padding: 8px;
                border: 1px solid #ddd;
            }

            .detail-label {
                font-weight: bold;
                background-color: #f5f5f5;
                color: #333;
            }

            .detail-value {
                background-color: #fff;
                vertical-align: top;
                color: #666;
            }
        </style>
        <form id="examQuestionBankTableAddForm" method="post" contentType="application/json; charset=utf-8"
            onSubmit="save()">
            <input id="id" name="id" type="hidden" />
            <table class="detail-table" width="100%">
                <tr>
                    <td width="100" align="right" class="detail-label">题目分组:</td>
                    <td width="350" class="detail-value"></td>
                </tr>
                <tr>
                    <td width="100" align="right" class="detail-label">题目名称:</td>
                    <td width="350" class="detail-value"></td>
                </tr>
                <tr>
                    <td width="100" align="right" class="detail-label">题目类型:</td>
                    <td width="350" class="detail-value"></td>
                </tr>
                <tr>
                    <td width="100" align="right" class="detail-label">选项:</td>
                    <td width="350" class="detail-value"></td>
                </tr>
                <tr>
                    <td width="100" align="right" class="detail-label">上报人:</td>
                    <td width="350" class="detail-value"></td>
                </tr>
                <tr>
                    <td width="100" align="right" class="detail-label">上报时间:</td>
                    <td width="350" class="detail-value"></td>
                </tr>
            </table>
        </form>
    </div>
</body>

</html>