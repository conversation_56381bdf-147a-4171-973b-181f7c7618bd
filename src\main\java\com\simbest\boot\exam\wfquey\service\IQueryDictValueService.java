package com.simbest.boot.exam.wfquey.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.sys.model.SysDictValue;

import java.util.List;

/**
 * 用途：数据字典 service层
 * 作者：zhangshaofeng
 * 时间：2018/07/09
 */
public interface IQueryDictValueService extends ILogicService<SysDictValue, String> {

    /**
     * 数据字典查询
     *
     * @param dictType
     * @return
     */
    List<SysDictValue> queryByType(String dictType);

}
