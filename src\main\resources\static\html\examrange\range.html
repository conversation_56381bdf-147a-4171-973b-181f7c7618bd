<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
    xmlns:th="http://www.thymeleaf.org">
<head>
  <title>组织树</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
  <!--
  <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
  -->
  <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
  <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
  <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
  <style>
    *{margin: 0;border: 0;}
    /* 表单样式控制 */
    .tbc{
      text-align: right;
      margin: auto;
      border: 0;
      width: 400px;
    }
  </style>
  <script type="text/javascript">
    var gps=getQueryString();
    var examCode;
    var examName;
    getCurrent();
    $(function(){
      treeLoadSuccess();
      initTree('');
      $(window).resize(function() {//根据窗口高度设置树右侧实线
        $("#orgTree").css("height",$(document).height());
      });

      //table
      pageparam={
        "listtable":{
          "listname":"#groupListTable",//table列表的id名称，需加#
          // "querycmd":"action/rangeGroup/findByExamRangeGroupName",
          "contentType":"application/json; charset=utf-8",
          // "queryParams":{examCode:""},
          "data":{},
          "nowrap": true,//把数据显示在一行里,默认true
          "styleClass":"noScroll",
          "fitColumns":true,
          "frozenColumns":[],//固定在左侧的列
          "columns":[[//列
            { title: "群组名称", field: "groupName", width: 15, align:'center'},
            { title: "群组编码", field: "groupId", width: 15, align:'center'},
            { title: "考试编码", field: "examCode", width: 15, align:'center'},
            { title: "当前分组人数", field: "countGroupUser", width: 15, align:'center'},
            { title: "考试名称", field: "examName", width: 15, align:'center'},
            { title: "操作", field: "op", width: 15, align:'center',
              formatter:function (value,row,index) {
                var g="<a href='#' class='editGroup' id='"+ row.id +"'>【编辑】</a>"
                        +"<a href='#' class='editPerson operateBlue' id="+row.groupId+"'>【人员配置】</a>"
                        +"<a  href='#' class='del operateRed' delete='action/rangeGroup/deleteExamRangeGroupById?id="+row.id+"'"+">【删除】</a>";
                return g;
              }
            },
          ]],
        },
        "dialogform": {
          "dialogid":"#add",//对话框的id
          "formname": "#groupListTableAddForm",//新增或修改对话框的formid需加#
          "insertcmd":"action/rangeGroup/createGroup",//新增命令
          "onSubmit":function(data){
            $('.easyui-validatebox').validatebox('enableValidation');
            if (fvalidate()) {
              //..
            }else{
              var examAppCode = $('#paperName').combobox('getValue');
              var paperName = $(".paperName span > input").val();
              // var examCode =  $('#examName').combobox('getValue');
              // var examName =  $(".examName span > input").val();
              data.examAppCode=examAppCode;
              data.paperName=paperName;
              data.examCode=examCode;
              data.examName=examName;
              return true;
            }

          }
        },
      };
      loadGrid(pageparam);


      //重置查询条件
      $(document).on('click','.reset',function (){
        formreset('groupListTableQueryForm');
      });




      //编辑分组
      $(document).on('click','.editGroup',function (){
        var id = $(this).attr("id");
        top.dialogP('html/examrange/editGroup.html?id='+id,window.name,'编辑分组','editGroup',false,700,600);
      });


      //编辑分组下人员
      $(document).on('click','.editPerson',function (){
        var id = $(this).attr("id");
        top.dialogP('html/examrange/editPerson.html?id='+id,window.name,'编辑分组人员','editPerson',false,900,600);
      });



      //编辑分组回调
      window.editGroup = function (data) {
        ajaxgeneral({
          url: "action/rangeGroup/update",
          data:data.data,
          contentType: "application/json; charset=utf-8",
          success: function (res) {

          }
        })
      }


      //编辑分组人员回调
      window.editPerson = function (data) {
    
      }


      //表单校验
      window.fvalidate = function () {
        // return $("#groupListTableAddForm").form("validate");
      };


      //查询试卷下拉框
      $('#paperName').combobox({
        url:'action/examAttribute/findAllNoPage',
        valueField: 'examAppCode',
        contentType: 'application/json; charset=utf-8',
        textField: 'examName',
        panelHeight: '400px',
        prompt: '-请选择-',
        editable: false,
        onSelect: function (record) {
          $('#examName').val();
        }
      });
      //查询考试下拉框
      $('#examName').combobox({
          url:'action/summary/findAllNoPage',
          valueField: 'examCode',
          contentType: 'application/json; charset=utf-8',
          textField: 'examName',
          panelHeight: '400px',
          prompt: '-请选择-',
          editable: false,
          onSelect: function (record) {

          }
        });

        if(web.currentUser.username == 'hadmin'){
          $('.searchBox').show()
        }

    });

    




    //初始化树
    function initTree(data) {
      $("#orgTree").css({"height": $(document).height(), "overflow":"auto"});
      var ajaxopts = {
        // url:"action/summary/findAllNoPage",
        url: "action/summary/findLeftTree?examCode=" + data,
        data:{},
        contentType: "application/json; charset=utf-8",
        success: function (data) {
          if(data.data.length!=0){
            for(var i in data.data){
              data.data[i].text=data.data[i].examName;
            }
            for(var i in data.data){
              data.data[i].text=data.data[i].GROUP_NAME;
            }
          }
          userIdzNodes = data.data;
          userTree(userIdzNodes);
          $("#orgTree>ul").eq(0).trigger("click");
        }
      };

      // if(data!==''){
      //   ajaxopts.url = "action/summary/findLeftTree?examCode=" + data
      // }
      ajaxgeneral(ajaxopts);
    }

    //树
    function userTree(userIdzNodes) {
      var datas=toTreeData(userIdzNodes,"examName","examCode","examCode|id,examName|text,examCode,examName");
      $("#orgTree").tree({
        lines:true,//是否显示树控件上的虚线
        treePid:'examCode',
        contentType: "application/json; charset=utf-8",
        cascadeCheck:false,
        data: datas,
        fileds:'examCode|id,examName,text|text,GROUP_NAME,GROUP_ID',
        animate:true,//节点在展开或折叠的时候是否显示动画效果
        onClick:function(node){
          examCode = node.examCode;
          examName = node.examName;
          pageparam.listtable.querycmd = "action/rangeGroup/findByExamRangeGroupName";
          pageparam.listtable.queryParams = {examCode:node.examCode};
          loadGrid(pageparam);
        },

        onDblClick:function(node){
        },
        onSelect:function(node){
        },

        onLoadSuccess:function(node,data){

        },
        //选择之前
        onBeforeSelect:function(node){

        }
      });
    }



    //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
    function beforerender(){

    };
    //点击查询
    function chooseuser() {
      initTree($('#examCode').val());
    };


    //数据加载成功
    function treeLoadSuccess(){
    }



  </script>
</head>
<style type="text/css">
  /* .ztree li ul.line{height:auto;} */
  #orgTree{width: 280px; border-right:1px solid;}
  .bodyBox{position: relative;}
  .left{position: absolute;left: 0;top: 0;}
  .right{position: absolute;left: 285px;right:0;bottom: 0; top: 0;}
  .table_searchD{position: relative;}
</style>
<body class="page_body">
  <div class="bodyBox">
    <div class="left">
      <div class="searchBox hide">
        <input type="text" id="examCode" name="examCode"  style="width: calc(100% - 64px);"/>
        <a class="btn fr" onclick="chooseuser()">搜索</a>
      </div>
      <ul id="orgTree"></ul>
    </div>

    <div class="right">
      <div class="table_searchD">
        <form id="groupListTableQueryForm" >
          <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
              <td width="90">群组名称：</td>
              <td width="190"><input id="groupName" name="groupName" type="text" /></td>
              <td width="90">群组编码：</td>
              <td width="190"><input id="groupId" name="groupId" type="text" /></td>
              <td >
                <div class="w100">
                  <a class="btn fl searchtable"><span>查询</span></a>
                  <a class="btn ml10 reset"><span>重置</span></a>
                  <span style="line-height: 32px;display: inline-block;margin-left:10px;color: red;font-size: 16px;">新增前请选择左侧考试，注意一个考试下对应一个群组！</span>
                  <a class="btn a_green fr showDialog" >新建</a>
                  <!-- <a class="btn a_green fr showDialogTop"  openlayer="html/examrange/editGroup.html" listPageName="Range" width="680" height="400">新建</a> -->
                </div>
              </td>
            </tr>
          </table>
        </form>
      </div>

      <div class="groupListTable">
        <table id="groupListTable"></table>
      </div>
    </div>

    <div id="add" title="新增或修改" class="easyui-dialog" style="width:700px;height:400px;">
      <form id="groupListTableAddForm" method="post" contentType="application/json; charset=utf-8"  onSubmit="onSubmit()" >

        <table class="tbc" border="0" cellpadding="0" cellspacing="16" >


          <tr >
              <td width="80" ><span class="col_r">*</span>群组名称:</td>
            <td width="160" ><input name="groupName" class="easyui-validatebox groupName" required="ture"  id="groupName"  type="text"  /></td>
          </tr>
    
    
          <tr >
            <td width="80" ><span class="col_r">*</span>群组编码:</td>
            <td width="160" ><input name="groupId" class="easyui-validatebox groupId" required="ture"  id="groupId"  type="text"  /></td>
          </tr>
    
          <tr >
            <td width="80" ><span class="col_r">*</span>试卷名称:</td>
            <td width="160"  class=" paperName" >
              <!-- <input id="examCode" name="examCode" class="examCode" style="width:  300px; height: 32px;"/> -->
              <input id="paperName" name="paperName" style="width:  300px; height: 32px;"/>
            </td>
          </tr>

          <!-- <tr >
            <td width="80" ><span class="col_r">*</span>考试名称:</td>
            <td width="160" class=" examName">
              <input id="examName" name="examName"   style="width:  300px; height: 32px;"/>
            </td>
          </tr> -->
    
    
        </table>

      </form>
    </div>


  </div>
</body>
</html>
