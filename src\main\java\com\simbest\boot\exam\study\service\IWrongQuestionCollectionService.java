package com.simbest.boot.exam.study.service;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.study.model.TrainingAnswerDetail;
import com.simbest.boot.exam.study.model.WrongQuestionCollection;
import com.simbest.boot.base.service.ILogicService;

/**
 * 用途：WrongQuestionCollection领域对象名称接口
 */
public interface IWrongQuestionCollectionService extends ILogicService<WrongQuestionCollection, String> {
    
    /**
     * 异步保存错题信息
     * @param question 错题信息
     * @param answerDetail 答案信息
     */
    void saveWrongQuestionAsync(ExamQuestion question , TrainingAnswerDetail answerDetail);

    /**
     * 根绝人员查询错题集
     * @param page  页数
     * @param size  数量
     * @param model 查询条件
     * @return
     */
    JsonResponse findByUsername( String source , String currentUsername, Integer page, Integer size, WrongQuestionCollection model);

    /**
     * 根据id查询错题信息
     * @param id 错题记录id
     * @return
     */
    JsonResponse findQuestionById(String id);
}