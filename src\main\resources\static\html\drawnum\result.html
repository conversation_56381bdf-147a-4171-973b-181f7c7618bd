<!-- 公开摇号结果 -->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>公开摇号结果</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/static/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../js/public.css?v=svn.revision" th:href="@{/static/js/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../js/pages.css?v=svn.revision" th:href="@{/static/js/pages.css?v=svn.revision}" rel="stylesheet"/>
    <!-- <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/> -->
    <!-- <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/> -->
    <!-- <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/> -->
    <!-- <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/> -->
    <!-- <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script> -->
    <!-- <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script> -->
    <!-- <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script> -->
    <!-- <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script> -->
    <!-- <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script> -->
    <!-- <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script> -->
    <script src="../../js/jquery.min.js?v=svn.revision" th:src="@{/static/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/static/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.aduq.js?v=svn.revision" th:src="@{/static/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    
    <script type="text/javascript">
      var allData = []
        $(function(){
          window.addEventListener('resize', function updateWidth() {
              var width = $('.contents')[0].offsetWidth; // 获取元素的宽度
              var height = $('.contents')[0].offsetHeight; // 获取元素的宽度
              $('.contents .card').css('width', (width-20)/24.3 +'px')
              $('.contents .card').css('margin-right', (width-20)/115 +'px') 
              $('.contents .carded .checked').css('width', (height-20)/40 +'px')

              // $('.contents .card').css('height', (height-20)/35 +'px')
              // $('.contents .card').css('line-height', (height-20)/35 +'px')
              // // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
              // $('.contents .card').css('margin-bottom', (height-20)/100 +'px')
              // if(height<582){
              //   $('.contents .card').css('height', (height-20)/35 +'px')
              //   $('.contents .card').css('line-height', (height-20)/35 +'px')
              //   // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
              //   $('.contents .card').css('margin-bottom', (height-20)/120 +'px')
              // }
              // if(height<510){
              //   $('.contents .card').css('height', (height-20)/35 +'px')
              //   $('.contents .card').css('line-height', (height-20)/35 +'px')
              //   // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
              //   $('.contents .card').css('margin-bottom', (height-20)/160 +'px')
              // }

              if(height<582){
                $('.contents .card').css('height', (height-20)/35 +'px')
                $('.contents .card').css('line-height', (height-20)/35 +'px')
                // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
                $('.contents .card').css('margin-bottom', (height-20)/120 +'px')
              }else if(height<510){
                $('.contents .card').css('height', (height-20)/35 +'px')
                $('.contents .card').css('line-height', (height-20)/35 +'px')
                // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
                $('.contents .card').css('margin-bottom', (height-20)/160 +'px')
              }else if(height>647){
                $('.contents .card').css('height', (height-20)/35 +'px')
                $('.contents .card').css('line-height', (height-20)/35 +'px')
                // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
                $('.contents .card').css('margin-bottom', (height-20)/90 +'px')
              }else{
                $('.contents .card').css('height', (height-20)/35 +'px')
                $('.contents .card').css('line-height', (height-20)/35 +'px')
                // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
                $('.contents .card').css('margin-bottom', (height-20)/100 +'px')
              }



          });
          render()
          setInterval(function(){
            render2()
          },1000)
        });

  
        
        function render2(){
          ajaxgeneral({
                url: 'action/publicLottery/getLotteryList/anonymous',
                contentType: 'application/json; charset=utf-8',
                success: function (res) {
                  var changeArr = []//选中
                  var concleArr = []//取消选中
                  // var date1 = + new Date()
                  for(var i in res.data){


                      //  res.data[i].username = null
                      // if(res.data[i].id == allData[i].id && res.data[i].username !== allData[i].username && res.data[i].username){
                      //   changeArr.push(res.data[i])
                      //   $('#block'+ res.data[i].num).addClass('card carded')
                      //   $('#block'+ res.data[i].num).find('img').removeClass('hide')
                      // } 
                      // if(res.data[i].id == allData[i].id && res.data[i].username !== allData[i].username && !res.data[i].username){
                      //   console.log(!res.data[i].username);
                      //   concleArr.push(res.data[i])
                      //   $('#block'+ concleArr[k].num).removeClass('carded')
                      //   $('#block'+ concleArr[k].num).find('img').addClass('hide')
                      // } 

                      if(res.data[i].username){
                        changeArr.push(res.data[i])
                        $('#block'+ res.data[i].num).addClass('card carded')
                        $('#block'+ res.data[i].num).find('img').removeClass('hide')
                      } 
                      if(!res.data[i].username){
                        concleArr.push(res.data[i])
                        $('#block'+ res.data[i].num).removeClass('carded')
                        $('#block'+ res.data[i].num).find('img').addClass('hide')
                      } 
                  }

                // var date2 = + new Date()
                  
                  // console.log(date2-date1);

                  // console.log(res.data.length,changeArr.length,concleArr.length,'concleArr');
                  // for(var o in changeArr){
                  //   $('#block'+ changeArr[o].num).addClass('card carded')
                  //   $('#block'+ changeArr[o].num).find('img').removeClass('hide')

                  //   // var i  = changeArr[o].num
                  //   // var g = ''
                  //   // g ='<span>'+i+'</span><img class="checked" src="../../images/drawnum/checked.png" >'
                  //   // $('#block'+ changeArr[o].num).empty()
                  //   // $('#block'+ changeArr[o].num).append(g)
                  // }

                  // for(var k in concleArr){
                  //   $('#block'+ concleArr[k].num).removeClass('carded')
                  //   $('#block'+ concleArr[k].num).find('img').addClass('hide')

                  //   // var i  = concleArr[k].num
                  //   // var g = ''
                  //   // g ='<span>'+i+'</span>'
                  //   // $('#block'+ concleArr[k].num).empty()
                  //   // $('#block'+ concleArr[k].num).append(g)
                  // }

                  allData = res.data
                  // 计算数量
                  // var isselect =[]
                  // for(var k in allData){
                  //   if(allData[k].username){
                  //     isselect.push(allData[k])
                  //   }
                  // }
                  $('.all').text(res.data.length)
                  $('.yes').text(changeArr.length)
                  $('.no').text(res.data.length - changeArr.length)
                }
            });
        }


        function render(){
          ajaxgeneral({
                url: 'action/publicLottery/getLotteryList/anonymous',
                contentType: 'application/json; charset=utf-8',
                success: function (res) {
                  allData = res.data
                  var gg = ''
                  var g = ''
                  var len = res.data.length
                  for(var i=1; i<=len; i++){
                      for(var j in res.data){
                        if(res.data[i-1].username){
                            if(i % 20 === 0){
                              g ='<div class="card carded"  id= block'+i+'><span>'+i+'</span><img class="checked" src="../../images/drawnum/checked.png" /></div></br>'
                            }else{
                              g ='<div class="card carded"  id= block'+i+'><span>'+i+'</span><img class="checked" src="../../images/drawnum/checked.png" /></div>'
                            }
                            break
                        }else{
                            if(i % 20 === 0){
                              g ='<div class="card" id= block'+i+'><span>'+i+'</span><img class="checked hide" src="../../images/drawnum/checked.png"/></div></br>'
                            }else{
                              g ='<div class="card" id= block'+i+'><span>'+i+'</span><img class="checked hide" src="../../images/drawnum/checked.png" /></div>'
                            }
                        }
                      }
                    gg +=g 
                  }
                  $('.contents').append(gg)
                  // 计算数量
                  var isselect =[]
                  for(var k in allData){
                    if(allData[k].username){
                      isselect.push(allData[k])
                    }
                  }
                  $('.all').text(allData.length)
                  $('.yes').text(isselect.length)
                  $('.no').text(res.data.length - isselect.length)

                  var width = $('.contents')[0].offsetWidth; // 获取元素的宽度
                  var height = $('.contents')[0].offsetHeight; // 获取元素的宽度

                  // console.log(height,'height');

                  $('.contents .card').css('width', (width-20)/24.3 +'px')
                  $('.contents .card').css('margin-right', (width-20)/115 +'px')
                  $('.contents .carded .checked').css('width', (height-20)/40 +'px')

                  if(height<582){
                    $('.contents .card').css('height', (height-20)/35 +'px')
                    $('.contents .card').css('line-height', (height-20)/35 +'px')
                    // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
                    $('.contents .card').css('margin-bottom', (height-20)/120 +'px')
                  }else if(height<510){
                    $('.contents .card').css('height', (height-20)/35 +'px')
                    $('.contents .card').css('line-height', (height-20)/35 +'px')
                    // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
                    $('.contents .card').css('margin-bottom', (height-20)/160 +'px')
                  }else if(height>647){
                    $('.contents .card').css('height', (height-20)/35 +'px')
                    $('.contents .card').css('line-height', (height-20)/35 +'px')
                    // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
                    $('.contents .card').css('margin-bottom', (height-20)/90 +'px')
                  }else{
                    $('.contents .card').css('height', (height-20)/35 +'px')
                    $('.contents .card').css('line-height', (height-20)/35 +'px')
                    // $('.contents .card').css('margin-bottom', (height-20-(height-20)/35)/90 +'px')
                    $('.contents .card').css('margin-bottom', (height-20)/100 +'px')
                  }

                }
            });
        }
    </script>
    <style> 
      .body_page{
        background: #306aef;
        /* padding-bottom: 50px; */
        overflow-y: hidden;
      }
      .top_box{
        width: 100%;
        display: flex;
        align-items: center;
        /* justify-content: space-between; */
        /* flex-wrap: wrap; */
        height: 90px;

      }
      .flexa_box{
        display: flex;
        align-items: center;
        justify-content: space-around;
        flex-wrap: nowrap;
      }
      .flexa_box .sum{
        padding: 10px 20px;
        background: #fff;
        border-radius: 30px;
        color: #366ff0;
        font-size: 20px;
      }
      .contents{
        background: #fff;
        width: 100%;
        padding: 10px 0px 10px 20px;
        border-radius: 10px;
        height: calc(100% - 110px);
        overflow-y: hidden;
      }
      .contents .card{
        width: 4%;
        /* height: 20px; */
        text-align: center;
        /* line-height: 20px; */
        border: 1px solid #aaa;
        /* margin-bottom: 10px; */
        border-radius: 3px;
        display: inline-block;
        font-size: 12px;
      }

      .contents .carded{
        position: relative;
        border: 1px solid #306aef;
        background: #d9ebff;
      }
      .contents .carded .checked{
        /* width: 30%; */
        position: absolute;
        bottom: -1px;
        right: -1px;
      }
      .num{
        font-weight: 700;
      }
      .hide{
        display: none;
      }
    </style>
</head>
<body class="body_page">
  <div class="top_box">
    <div class="logo">
      <img style="width: 80%;" src="../../images/drawnum/yidong.png" alt=""/>
    </div>
    <div class="flexa_box">
      <div class="tit">
        <img style="width: 80%;" src="../../images/drawnum/title.png" alt=""/>
      </div>
      <div class="sum">共<span class="num all">500</span>个号码&nbsp;&nbsp;已选中<span class="yes num">0</span>个&nbsp;&nbsp;剩余<span class="no num">0</span>个</div>
    </div>
   
  </div>

  <div class="contents">
   <!-- <div class="card carded">
      <span>1</span>
      <img class="checked" src="../../images/drawnum/checked.png" >
    </div>
    <div class="card">2</div> -->
  </div>
</body>
</html>
