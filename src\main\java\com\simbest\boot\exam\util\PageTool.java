/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.util;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.List;

/**
 * <strong>Title : PageTool</strong><br>
 * <strong>Description : 分页工具 </strong><br>
 * <strong>Create on : 2020/7/16</strong><br>
 * <strong>Modify on : 2020/7/16</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR> kou<PERSON><EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public class PageTool {

    /**
     * 对返回后的列表进行处理后进行分页
     *
     * @param objList   处理后的数据
     * @param pageindex 页码
     * @param pagesize  每页数量
     */
    public static <T> List<T> pagination(List<T> objList, int pageindex, int pagesize) {
        List<T> list = new ArrayList<>();
        int count = 0;
        for (int i = 0; i < objList.size(); i++) {
            if (i >= (pageindex - 1) * pagesize) {
                list.add(objList.get(i));
                count++;
            }
            if (count == pagesize) {
                break;
            }
        }
        return list;
    }

    /**
     * 对返回后的列表进行处理后进行分页 返回分页对象
     *
     * @param source   处理后的数据
     * @param pageable 分页参数
     */
    public static <T> Page<T> getPage(List<T> source, Pageable pageable) {
        List<T> list = PageTool.pagination(source, pageable.getPageNumber() + 1, pageable.getPageSize());
        return new PageImpl<>(list, pageable, source.size());
    }

}
