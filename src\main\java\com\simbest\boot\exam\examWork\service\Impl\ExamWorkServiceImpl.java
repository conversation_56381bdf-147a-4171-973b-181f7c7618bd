/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examWork.service.Impl;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.mzlion.core.lang.Assert;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfo;
import com.simbest.boot.exam.examOnline.model.ExamSummary;
import com.simbest.boot.exam.examOnline.service.IExamRangeUserInfoService;
import com.simbest.boot.exam.examOnline.service.IExamSummaryService;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.repository.ExamWorkRepository;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.mainbills.model.UsPmInstence;
import com.simbest.boot.exam.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.exam.todo.service.IUsTodoModelService;
import com.simbest.boot.exam.util.*;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class ExamWorkServiceImpl extends LogicService<ExamWork, String> implements IExamWorkService {

    private ExamWorkRepository examWorkRepository;

    private String param1 = "/acton/examWork";

    @Autowired
    private IUsPmInstenceService iUsPmInstenceService;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private LoginUtils loginUtils;
    @Autowired
    private IUsTodoModelService todoModelService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private ISysDictValueService sysDictValueService;

    @Autowired
    private IExamSummaryService examSummaryService;

    @Autowired
    private SMSTool smsTool;

    @Autowired
    private IExamRangeUserInfoService iExamRangeUserInfoService;

    @Autowired
    public ExamWorkServiceImpl(ExamWorkRepository repository) {
        super(repository);
        this.examWorkRepository = repository;

    }

    @Override
    public JsonResponse queryMyTask(Integer page, Integer size, String source, String currentUserCode, String title) {
        /**存放待办**/
        Page<ExamWork> newPage = null;
        /**操作日志记录参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyTask";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**锁定当前人code**/
            currentUserCode = SecurityUtils.getCurrentUserName();
            Pageable pageable = getPageable(page, size, "desc", "createdTime");
            Specification<ExamWork> specification = Specifications.<ExamWork>and()
                    .eq(StringUtils.isNotEmpty(currentUserCode), "transactorCode", currentUserCode)
                    .eq(StringUtils.isNotEmpty(title), "title", title)
                    .eq("enabled", true)
                    .build();
            List<ExamWork> works = super.findAllNoPage(specification, pageable.getSort());

            works = works.stream().filter(v-> {
                UsPmInstence byPmInsId = iUsPmInstenceService.findByPmInsId(v.getPmInsId());
                v.setSign(byPmInsId.getSign());
                return Objects.equals("0",v.getSign());
            }).collect(Collectors.toList());
            newPage = PageTool.getPage(works, pageable);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(newPage);
    }


    /**
     * 获取2023年明纪守法考试待办已办接口
     * <br/> params [page, size, source, currentUserCode, title]
     *
     * @return {@link JsonResponse}
     * <AUTHOR>
     * @since 2023/8/14 15:08
     */
    @Override
    public JsonResponse queryMyTaskMJSF(Integer page, Integer size, String source, String currentUserCode, String title) {
//        明纪守法初赛 特殊处理
//        IUser user = SecurityUtils.getCurrentUser();
//        Specification<ExamWork> specification = Specifications.<ExamWork>and()
//                .eq("transactorCode", user.getUsername())
//                .eq("examAppCode", MJSF_PFZ_SJ)
//                .eq("enabled", true)
//                .build();
//        long count = this.count(specification);
//        // 创建普法组考试 全体正式员工可以参与考试
//        if (count == 0 && user.getUserType() == 1) {
//            syncTool.createExamWork(MJSF_PFZ_KS, user.getUsername(), user.getTruename(), user.getBelongCompanyName(), MJSF_PFZ_KS_TITLE, "H", MJSF_PFZ_SJ);
//        }

        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyTask";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
        if (returnObj != null) return returnObj;

        currentUserCode = SecurityUtils.getCurrentUserName();
        Pageable pageable = getPageable(page, size, "desc", "createdTime");
        Specification<ExamWork> specification = Specifications.<ExamWork>and()
                .eq(StringUtils.isNotEmpty(currentUserCode), "transactorCode", currentUserCode)
                .eq(StringUtils.isNotEmpty(title), "title", title)
                .eq("enabled", true)
                .build();
        List<ExamWork> works = super.findAllNoPage(specification, pageable.getSort());

        // 过滤掉非明纪守法考试
        List<ExamWork> collect = works.stream().filter(v -> v.getExamCode().contains(Constants.MJSF)).collect(Collectors.toList());
        return JsonResponse.success(PageTool.getPage(collect, pageable));
    }

    /**
     * 获取2023年明纪守法考试已办接口
     * <br/> params [page, size, source, currentUserCode, title]
     *
     * @return {@link JsonResponse}
     * <AUTHOR>
     * @since 2023/8/14 15:08
     */
    @Override
    public JsonResponse queryMyJoinMJSF(Integer page, Integer size, String source, String currentUserCode, String title) {
        JsonResponse response = this.queryMyJoin(page, size, source, currentUserCode, title);
        Object data = response.getData();
        if (Objects.isNull(data)) return response;

        Page<ExamWork> workPage = (Page<ExamWork>) data;
        Pageable pageable = getPageable(page, size, "desc", "createdTime");
        // 过滤掉非明纪守法考试
        List<ExamWork> collect = workPage.stream().filter(v -> v.getExamCode().contains(Constants.MJSF)).collect(Collectors.toList());
        return JsonResponse.success(PageTool.getPage(collect, pageable));
    }

    /**
     * 获取特定考试待办和已办接口
     * <br/> params [page, size, source, currentUserCode, title]
     *
     * @return {@link JsonResponse}
     * <AUTHOR>
     * @since 2023/8/14 15:08
     */
    @Override
    public JsonResponse queryMyTask2023(Integer page, Integer size, String source, String currentUserCode, String title, String examCode) {
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyTask";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
        if (returnObj != null) return returnObj;

        currentUserCode = SecurityUtils.getCurrentUserName();
        Pageable pageable = getPageable(page, size, "desc", "createdTime");
        Specification<ExamWork> specification = Specifications.<ExamWork>and()
                .eq(StringUtils.isNotEmpty(currentUserCode), "transactorCode", currentUserCode)
                .eq(StringUtils.isNotEmpty(title), "title", title)
                .eq("enabled", true)
                .build();
        List<ExamWork> works = super.findAllNoPage(specification, pageable.getSort());

        // 过滤掉其他考试
        List<ExamWork> collect = works.stream().filter(v -> examCode.contains(v.getExamCode())).collect(Collectors.toList());
        return JsonResponse.success(PageTool.getPage(collect, pageable));
    }

    @Override
    public JsonResponse queryMyJoin(Integer page, Integer size, String source, String currentUserCode, String title) {
        /**存放已办**/
        Page<ExamWork> newPage = null;
        /**操作日志记录参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyJoin";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**锁定当前人code**/
            currentUserCode = SecurityUtils.getCurrentUserName();
            Pageable pageable = getPageable(page, size, "desc", "createdTime");
            Specification<ExamWork> specification = Specifications.<ExamWork>and()
                    .eq(StringUtils.isNotEmpty(currentUserCode), "transactorCode", currentUserCode)
                    .eq(StringUtils.isNotEmpty(title), "title", title)
                    .eq("enabled", true)
                    .build();
            List<ExamWork> works = super.findAllNoPage(specification, pageable.getSort());

            works = works.stream().filter(v-> {
                UsPmInstence byPmInsId = iUsPmInstenceService.findByPmInsId(v.getPmInsId());
                v.setSign(byPmInsId.getSign());
                return Objects.equals("1",v.getSign());
            }).collect(Collectors.toList());
            newPage = PageTool.getPage(works, pageable);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(newPage);
    }

    @Override
    public JsonResponse dealWith(String source, String currentUserCode, String id) {
        ExamWork examWork = null;
        UsPmInstence update = null;
        /**操作日志记录参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/dealWith";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**办理操作**/
            ExamWork work = examWorkRepository.findByIdActive(id);
            if (work != null) {//当该待办存在时进行更新操作
                UsPmInstence byPmInsId = iUsPmInstenceService.findByPmInsId(work.getPmInsId());
                byPmInsId.setSign(Constants.SIGN_O);
                update = iUsPmInstenceService.update(byPmInsId);
                work.setModifiedTime(LocalDateTime.now());
                examWork = this.update(work);
                /**注销统一待办**/
                todoModelService.closeTodo(work);
            } else {
                log.debug(id + "-------办理失败,未找到有效数据");
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return examWork != null & update != null ? JsonResponse.success(examWork, "办理成功") : JsonResponse.fail(examWork, "办理失败");
    }

    @Override
    public boolean createToDo(String company, String title, String questionName, String workType) {
        boolean flag = false;
        /**先将主数据信息转成待办插入业务表中，再推送同意待办和短信**/
        List<Map<String, Object>> userList = queryBscUser(company);
        if (userList != null && !userList.isEmpty()) {//人员不为空时进行插入操作
            System.out.println("创建待办开始--------------------userList=" + userList.size());
            int sum = 0;
            for (Map<String, Object> map : userList) {
                sum++;
                try {
                    synchronized (this) {
                        /**获取当前时间**/
                        LocalDateTime time = LocalDateTime.now();
                        /**创建当前人员待办信息**/
                        if (map != null && !map.isEmpty()) {
                            /**准备当前人待办参数**/
                            //获取主单据ID
                            String pmInsId = NumRuleUtil.getPmInsId(questionName);
                            String username = map.get("USERNAME") != null ? map.get("USERNAME").toString() : "";
                            ExamWork work = new ExamWork();
                            work.setCreator(username);
                            work.setModifier(username);
                            work.setCreateYear("2021");
                            work.setCreatedTime(time);
                            work.setModifiedTime(time);
                            work.setTransactor(map.get("TRUENAME") != null ? map.get("TRUENAME").toString() : "");
                            work.setTransactorCode(username);
//                            work.setCompanyCode( map.get("BELONGCOMPANYCODE") != null ? map.get("BELONGCOMPANYCODE").toString(): "");
                            work.setCompanyName(company);
//                            work.setDepartmentCode( map.get("BELONGDEPARTMENTCODE") != null ? map.get("BELONGDEPARTMENTCODE").toString(): "");
//                            work.setDepartmentName( map.get("DEPARTMENTNAME") != null ? map.get("DEPARTMENTNAME").toString(): "");
//                            work.setOrgCode( map.get("ORGCODE") != null ? map.get("ORGCODE").toString(): "");
//                            work.setOrgName( map.get("ORGNAME") != null ? map.get("ORGNAME").toString(): "");
                            work.setTitle(title);
                            //work.setDisplayName( map.get("DISPLAYNAME") != null ? map.get("DISPLAYNAME").toString(): "" );
                            //work.setPositionLevel( map.get("POSITIONLEVEL") != null ? Integer.parseInt(map.get("POSITIONLEVEL").toString()): 0 );
                            work.setPositionLevel(0);
                            //work.setParentCompanyCode( map.get("BELONGCOMPANYCODEPARENTCODE") != null ? map.get("BELONGCOMPANYCODEPARENTCODE").toString(): "" );
                            work.setParentCompanyCode("null");
                            //work.setSign( Constants.SIGN_Z );
                            work.setIsPostMsg(Constants.SIGN_Z);
                            work.setIsTodoFlag(Constants.SIGN_Z);
                            work.setWorkType(workType);//由页面控制待办打开页面
                            work.setPmInsId(pmInsId);
                            /**保存主单据**/
                            UsPmInstence usPmInstence = new UsPmInstence();
                            usPmInstence.setSign(Constants.SIGN_Z);
                            usPmInstence.setPmInsTitle(title);
                            usPmInstence.setPmInsId(pmInsId);
                            usPmInstence.setPmInsType("考试管理系统");//传入自己模块名称
                            iUsPmInstenceService.insert(usPmInstence);
                            /**保存当前人待办**/
                            ExamWork examWork = insert(work);
                            if (examWork != null) {
                                flag = true;
                            }

                        }
                    }
                } catch (Exception e) {
                    Exceptions.printException(e);
                }
            }
            System.out.println("创建待办结束--------------------循环次数sum=" + sum);
        }
        return flag;
    }

    /**
     * 批量推送待办
     *
     * @param list
     * @return
     */
    @Override
    public JsonResponse sendUnifiedToDo(List<ExamWork> list) {
        int count = 0;
        log.warn(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>开始执行方法sendUnifiedToDo>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        try {

            /**获取到公司人员当不为空时处理数据**/
            if (list != null && !list.isEmpty()) {
                log.warn(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>获取到公司人员当不为空时处理数据>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");

                for (ExamWork work : list) {
                    log.warn(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>推送统一待办和短信>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");

                    //推送统一待办和短信
                    /**如果推送待办成功，更新系统待办中标识和发送短信**/
                    if (todoModelService.openTodo(work)) {
                        count++;
                        log.warn(">>>>>>>>>>>>>>>>>>>>>>>>>>如果推送待办成功，更新系统待办中标识和发送短信>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>sendUnifiedToDo中work【{}】-----START", work);
//                        smsTool.sendShortMessage( work );
                        work.setIsTodoFlag(Constants.SIGN_O);
                        work.setIsPostMsg(Constants.SIGN_O);
                        work.setLastPostTime(DateUtil.getDate(new Date()));
                        this.update(work);
                        log.warn(">>>>>>>>>>>>>>>>>>>>>>>>结束>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>sendUnifiedToDo中work【{}】-----END", work);
                    }
                    //延缓两秒钟减轻统一待办压力T
                    //Thread.sleep(1*500);

                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        log.warn(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>共执行【{}】次>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", count);

        return null;
    }

    /**
     * 自定义查询全部 带条件 分页
     *
     * @param pageable
     * @param examWork
     * @return
     */
    @Override
    public JsonResponse findAllCustom(Pageable pageable, ExamWork examWork) {

        //办理人
        String transactor = examWork.getTransactor();
        //办理人oa账号
        String transactorCode = examWork.getTransactorCode();
        //办理人所在部门
        String departmentName = examWork.getDepartmentName();
        //考试标题
        String title = examWork.getTitle();

        String isTodoFlag = examWork.getIsTodoFlag();

        String isPostMsg = examWork.getIsPostMsg();

        //公司
        String companyName = examWork.getCompanyName();

        String workType = examWork.getWorkType();

        Specification<ExamWork> specification = Specifications.<ExamWork>and()
                .like(StringUtils.isNotEmpty(transactor), "transactor", "%" + transactor + "%")
                .like(StringUtils.isNotEmpty(transactorCode), "transactorCode", "%" + transactorCode + "%")
                .like(StringUtils.isNotEmpty(departmentName), "departmentName", "%" + departmentName + "%")
                .like(StringUtils.isNotEmpty(title), "title", "%" + title + "%")
                .eq(StringUtils.isNotEmpty(isTodoFlag), "isTodoFlag", isTodoFlag)
                .eq(StringUtils.isNotEmpty(isPostMsg), "isPostMsg", isPostMsg)
                .eq(StringUtils.isNotEmpty(workType), "workType", workType)
                .eq(StringUtils.isNotEmpty(companyName), "companyName", companyName)
                .build();
        // 获取查询结果
        Page<ExamWork> pages = this.findAll(specification, pageable);
        return JsonResponse.success(pages);
    }


    /**
     * 自定义查询全部 带条件 不分页
     *
     * @param examWork
     * @return
     */
    @Override
    public List<ExamWork> findAllCustom(ExamWork examWork) {
        //办理人
        String transactor = examWork.getTransactor();
        //办理人oa账号
        String transactorCode = examWork.getTransactorCode();
        //办理人所在部门
        String departmentName = examWork.getDepartmentName();
        //考试标题
        String title = examWork.getTitle();
        //公司
        String companyName = examWork.getCompanyName();

        String isTodoFlag = examWork.getIsTodoFlag();
//        if(isTodoFlag==null){
//            isTodoFlag=Constants.SIGN_Z;
//        }

        String isPostMsg = examWork.getIsPostMsg();


        String workType = examWork.getWorkType();


        Specification<ExamWork> specification = Specifications.<ExamWork>and()
                .like(StringUtils.isNotEmpty(transactor), "transactor", "%" + transactor + "%")
                .like(StringUtils.isNotEmpty(transactorCode), "transactorCode", "%" + transactorCode + "%")
                .like(StringUtils.isNotEmpty(departmentName), "departmentName", "%" + departmentName + "%")
                .like(StringUtils.isNotEmpty(title), "title", "%" + title + "%")
                .eq(StringUtils.isNotEmpty(isTodoFlag), "isTodoFlag", isTodoFlag)
                .eq(StringUtils.isNotEmpty(isPostMsg), "isPostMsg", isPostMsg)
                .eq(StringUtils.isNotEmpty(companyName), "companyName", companyName)
                .eq(StringUtils.isNotEmpty(workType), "workType", workType)
                .build();
        // 获取查询结果
        List<ExamWork> allNoPage = this.findAllNoPage(specification);
        if (allNoPage != null && allNoPage.size() > 0) {

            return allNoPage;
        }
        return null;

    }

    /**
     * 短信催办
     *
     * @param list
     * @return
     */
    @Override
    public JsonResponse urgedToDo(List<ExamWork> list) {
        try {
            /**获取到公司人员当不为空时处理数据**/
            if (list != null && !list.isEmpty()) {
                for (ExamWork work : list) {
                    //推送短信
                    /**如果短信发送成功，更新最后短信发送时间**/
                    if (smsTool.sendShortMessage(work)) {
                        work.setLastPostTime(DateUtil.getDate(new Date()));
                        this.update(work);
                    }
                    //延缓两秒钟减轻统一待办压力T
                    Thread.sleep(1 * 500);

                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return null;
    }

    /**
     * 根据username 获取本地待办
     *
     * @param username
     * @return
     */
    @Override
    public ExamWork findByUsername(String username, String examCode) {

        return examWorkRepository.findAllByUsername(username, examCode);
    }

    /**
     * 根据用户名、待办类型获取考试工作信息
     *
     * @param username 用户名
     * @param workType 考试类型
     * @return 返回考试工作信息
     */
    @Override
    public List<ExamWork> findByUsernameAndWorkType(String username, String workType) {
        // 构筑查询条件
        Specification<ExamWork> spec = Specifications
                .<ExamWork>and()
                .eq("transactorCode", username)
                .eq("workType", workType)
                .eq("enabled", true)
                .build();
        // 根据工单待办类型和用户的用户名可获取到唯一待办信息
        return findAllNoPage(spec);
    }

    /**
     * 获取未完成的办理工单
     *
     * @param examCode 考试编码
     * @return
     */
    @Override
    public List<Map<String, Object>> findUnfinishedExamList(String examCode) {
        // 将考试编码转换为待办工作可以识别的workType
        ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode(examCode);
        Assert.notNull(summaryInfo, "考试信息不存在!");
        String workType = summaryInfo.getWorkType();
        Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
        // 查询为办理完成的工单信息
        String sql = " select DISTINCT ew.ID, ew.TRANSACTOR_CODE ,ew.TITLE from us_exam_work ew, us_pm_instence pi " +
                "where ew.enabled = 1 " +
                "and ew.pm_ins_id = pi.pm_ins_id " +
                "and ew.work_type = :workType " +
                "and pi.enabled = 1 " +
                "and pi.sign = 0 ";
        Map<String, Object> map = Maps.newHashMap();
        map.put("workType", workType);
        List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql, map);
        return list;
    }


    /**
     * Business support center 生成待办人员
     *
     * @return
     */
    private List<Map<String, Object>> queryBscUser() {
        /**存放公司人员**/
        List<Map<String, Object>> bscUser = null;
        try {

            SysDictValue sysDictValue = new SysDictValue();
            sysDictValue.setDictType("sqlType");
            List<SysDictValue> sysDictValueList = sysDictValueService.findDictValue(sysDictValue);

            /**生成待办人员**/

            if (sysDictValueList.size() > 0) {
                for (SysDictValue sysDictValueNew : sysDictValueList) {
                    bscUser = customDynamicWhere.queryNamedParameterForList(sysDictValueNew.getValue(), null);

                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return bscUser;
    }

    /**
     * Business support center 生成待办人员
     *
     * @return
     */
    private List<Map<String, Object>> queryBscUser(String company) {
        /**存放公司人员**/
        List<Map<String, Object>> bscUser = null;
        try {

            /**生成待办人员**/

            Map<String, Object> map = Maps.newHashMap();

            map.put("companyName", company);
            String sql = " SELECT distinct up.truename        TRUENAME," +
                    "                      up.username        USERNAME" +
                    "        FROM uums.V_USER_ORG_POSITION up" +
                    "            WHERE  up.displayName LIKE concat(:companyName,'%')" +
                    "                AND up.positionName != '一线员工'" +
                    "                AND up.userType = '1'" +
                    "                AND up.username not in(select username from TEMP)";
//            String sql = " SELECT distinct up.truename        TRUENAME," +
//                    "                      up.username        USERNAME" +
//                    "        FROM uums.V_USER_ORG_POSITION up" +
//                    "            WHERE  up.displayName LIKE concat(:companyName,'%')" +
//                    "                AND up.username = 'hadmin'" ;// 测试用sql
            bscUser = customDynamicWhere.queryNamedParameterForList(sql, map);

        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return bscUser;
    }


    /**
     * 功能描述 获取洛阳满意度县分公司人员的待办信息
     *
     * @param
     * @return java.util.List<com.simbest.boot.exam.examWork.model.ExamWork>
     * <AUTHOR>
     * @date 2021/6/23
     */
    @Override
    public List<ExamWork> findExamWorkByWorkTypeLYXF() {
        String workType = "lyxf";
        List<ExamRangeUserInfo> byGroupIdLY = iExamRangeUserInfoService.findByGroupIdLY(Constants.LYJGCODE);
        return examWorkRepository.findAllByEXamCode(workType, byGroupIdLY.size());
    }

    /**
     * 功能描述 获取洛阳满意度机关部门人员的待办信息
     *
     * @param
     * @return java.util.List<com.simbest.boot.exam.examWork.model.ExamWork>
     * <AUTHOR>
     * @date 2021/6/23
     */
    @Override
    public List<ExamWork> findExamWorkByWorkTypeLYJG() {
        String workType = "lyjg";
        List<ExamRangeUserInfo> byGroupIdLY = iExamRangeUserInfoService.findByGroupIdLY(Constants.LYJGCODE);
        return examWorkRepository.findAllByEXamCode(workType, byGroupIdLY.size());
    }

    @Override
    public String findByUsernameApp(String username) {
        List<ExamWork> allByUsernameApp = examWorkRepository.findAllByUsernameApp(username);
        for (ExamWork examWork : allByUsernameApp) {
            String workType = examWork.getWorkType();
            if (workType.equals("E") || workType.equals("F")) {
                return workType;
            }
        }
        return null;
    }

    /**
     * 根据用户名，待办类型查询待办信息
     *
     * <AUTHOR>
     * @date 2021/6/24
     */
    @Override
    public ExamWork findByTransactorCodeAndWorkType(String transactorCode, String workType) {
        return examWorkRepository.findByTransactorCodeAndWorkType(transactorCode, workType);
    }

    /**
     * 根据用户名，考试编码判断是否有待办
     *
     * @param username
     * @param examCode
     * @return
     * <AUTHOR>
     * @date 2021/12/27
     */
    @Override
    public boolean findByTransactorCodeAndExamCode(String username, String examCode) {
        String currentUserName = SecurityUtils.getCurrentUserName();
        boolean flag = false;
        Specification<ExamWork> spec = Specifications
                .<ExamWork>and()
                .eq("transactorCode", currentUserName)
                .eq("examCode", examCode)
                .eq("enabled", true)
                .build();
        List<ExamWork> allNoPage = findAllNoPage(spec);
        if (CollUtil.isNotEmpty(allNoPage)) {
            flag = true;
        }
        return flag;
    }

    /**
     * 检测是否存在待办
     *
     * @param username
     * @param examCode
     * @return
     */
    @Override
    public boolean checkIsNotDone(String username, String examCode) {
        List<Map<String , Object>> todos =  examWorkRepository.checkIsNotDone(username , examCode);
        if (CollectionUtil.isNotEmpty(todos)) {
            return true;
        }
        return false;
    }
}
