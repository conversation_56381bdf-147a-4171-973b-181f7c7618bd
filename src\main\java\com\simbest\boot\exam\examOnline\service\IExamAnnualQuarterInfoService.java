package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ISystemService;
import com.simbest.boot.exam.examOnline.model.ExamAnnualQuarterInfo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface IExamAnnualQuarterInfoService extends ISystemService<ExamAnnualQuarterInfo,String> {

    /**
     * 保存考试的年度季度信息
     * @param examCode
     */
    void saveAnnualQuarterInfo(String examCode);

    /**
     * 根据模糊的考试编码查询最新的考试年度季度信息
     */
    ExamAnnualQuarterInfo findAnnualQuarterInfo(String annualQuarterCode, String createdTime);

    /**
     * 根据模糊的考试编码查询考试年度季度信息
     */
    ExamAnnualQuarterInfo findAnnualQuarterInfo3(String annualQuarterCode);


    /**
     * 查询所有考试的年度季度信息
     */
    List<ExamAnnualQuarterInfo> findAllAnnualQuarterInfo(String annualQuarterCode);
}
