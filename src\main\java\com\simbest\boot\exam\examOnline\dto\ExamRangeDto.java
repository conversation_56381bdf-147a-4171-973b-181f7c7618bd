package com.simbest.boot.exam.examOnline.dto;

import com.simbest.boot.exam.examOnline.model.ExamRange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用途：考试汇总模块-考试范围信息数据传输实体
 * 作者：gy
 * 时间: 2021-02-19 10:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "考试汇总模块-考试范围信息数据传输实体")
public class ExamRangeDto {
    @ApiModelProperty(value = "学期外键")
    private String summaryId;

    @ApiModelProperty
    private List<ExamRange> rangeList;
}
