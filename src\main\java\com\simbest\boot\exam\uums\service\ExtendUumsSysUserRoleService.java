package com.simbest.boot.exam.uums.service;

import com.simbest.boot.base.web.response.JsonResponse;

/**
 * <AUTHOR>
 * @desc:
 * @date 2021/7/3  14:56
 */
public interface ExtendUumsSysUserRoleService {

   /**
     * @desc 查询当前角色下的人员
     * <AUTHOR>
     */
   JsonResponse findUserByRole(int page,int size,String roleId,String username);

   /**
     * @desc 关联用户角色
     * <AUTHOR>
     */
   JsonResponse createRoleUsers(String roleId,String usernames);

   /**
     * @desc 根据id删除用户角色信息
     * <AUTHOR>
     */
   JsonResponse  deleteById(String roleId,String[] usernames);
}
