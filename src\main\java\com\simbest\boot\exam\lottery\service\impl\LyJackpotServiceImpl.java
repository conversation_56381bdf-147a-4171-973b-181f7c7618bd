package com.simbest.boot.exam.lottery.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.lottery.model.LyJackpot;
import com.simbest.boot.exam.lottery.repository.LyJackpotRepository;
import com.simbest.boot.exam.lottery.service.ILyJackpotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
public class LyJackpotServiceImpl  extends LogicService<LyJackpot,String> implements ILyJackpotService {
    private LyJackpotRepository lyJackpotRepository;


    @Autowired
    public LyJackpotServiceImpl(LyJackpotRepository repository){
        super(repository);
        this.lyJackpotRepository=repository;

    }

    @Override
    public LyJackpot findLyJackpotIsResidue(String type) {
        LyJackpot retLyJackpot = new LyJackpot();
        List<LyJackpot> lyJackpotList = lyJackpotRepository.findAllOrderByCreateTime(type);
        if(!CollectionUtils.isEmpty(lyJackpotList)){
            retLyJackpot = lyJackpotList.get(0);
        }
        return retLyJackpot;
    }
}
