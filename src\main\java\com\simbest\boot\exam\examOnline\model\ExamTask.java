package com.simbest.boot.exam.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * 洛阳分公司满意度调查问卷定时器状态
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_task")
@ApiModel(value = "定时任务")
public class ExamTask extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ET") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 11)
    @ApiModelProperty(value = "考试类型")
    private String examCode;

    @Column(length = 11)
    @ApiModelProperty(value = "定时器状态 0->关闭 1->打开")
    private Integer taskStatus;
}
