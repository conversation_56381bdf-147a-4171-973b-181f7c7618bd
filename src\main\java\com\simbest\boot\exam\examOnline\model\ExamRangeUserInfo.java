package com.simbest.boot.exam.examOnline.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.SystemModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Map;

/**
 * 用途：考试管理--考试范围模块
 * 当前实体控制控制参与考试人员得群组
 * 作者：sws
 * 时间: 2021-04-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_range_user_info")
@ApiModel(value = "考试范围群组人员信息")
public class ExamRangeUserInfo extends SystemModel {


    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ERU") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "人员id")
    private String userName;

    @Column(length = 40)
    @ApiModelProperty(value = "人员姓名")
    private String userTrueName;

    @Column(length = 40)
    @ApiModelProperty(value = "人员所属部门名称")
    private String userOrgName;

    @Column(length = 40)
    @ApiModelProperty(value = "人员所属部门编号")
    private String userBelongDepartmentCode;

    @Column(length = 40)
    @ApiModelProperty(value = "人员所属公司")
    private String userBelongCompanyName;

    @Column(length = 40)
    @ApiModelProperty(value = "人员所属公司编号")
    private String userBelongCompanyCode;

    @Column(length = 40)
    @ApiModelProperty(value = "试卷名称")
    private String examName;

    @Column(length = 40)
    @ApiModelProperty(value = "试卷编码")
    private String examAppCode;

    @Column(length = 40)
    @ApiModelProperty(value = "群组编号")
    private String groupId;

    @Column(length = 100)
    @ApiModelProperty(value = "群组名称")
    private String groupName;

    @Transient
    private Map<String,Object> paramMap;




}
