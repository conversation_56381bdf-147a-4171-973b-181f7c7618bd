/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examStatistics.model;/**
 * Created by KZH on 2019/11/26 11:18.
 */

import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2019-11-26 11:18
 * @desc
 **/
@Data
public class StatisticExam {

    @Setter
    @Getter
    @ExcelVOAttribute(name = "公司", column = "A")
    @ApiModelProperty(value = "公司")
    private String company;

    @Setter
    @Getter
    @ExcelVOAttribute(name = "参加人数", column = "B")
    @ApiModelProperty(value = "参加人数")
    private String peopleNumber;

    @Setter
    @Getter
    @ExcelVOAttribute(name = "未参加人数", column = "C")
    @ApiModelProperty(value = "未参加人数")
    private String NoPeopleNumber;

    @Setter
    @Getter
    @ExcelVOAttribute(name = "参加人数百分比", column = "D")
    @ApiModelProperty(value = "参加人数百分比")
    private String percentage;

    @Setter
    @Getter
    @ExcelVOAttribute(name = "未参加人数百分比", column = "E")
    @ApiModelProperty(value = "未参加人数百分比")
    private String NoPercentage;

    @Setter
    @Getter
    @ExcelVOAttribute(name = "参加的三级经理占比", column = "F")
    @ApiModelProperty(value = "参加的三级经理占比")
    private String manager;

    @Setter
    @Getter
    @ExcelVOAttribute(name = "未参加的三级经理占比", column = "G")
    @ApiModelProperty(value = "未参加的三级经理占比")
    private String NoManager;

    @Setter
    @Getter
    @ExcelVOAttribute(name = "参加的管理层占比", column = "H")
    @ApiModelProperty(value = "参加的管理层占比")
    private String lead;

    @Setter
    @Getter
    @ExcelVOAttribute(name = "未参加的管理层占比", column = "I")
    @ApiModelProperty(value = "未参加的管理层占比")
    private String NoLead;
}
