package com.simbest.boot.exam.examOnline.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: IExamStatisticsExamService
 * @projectName exam
 * @description:
 * @date 2021/6/10  14:26
 */
public interface IExamStatisticsExcelService {
    /**
     * 统计机关各部门对机关各部门的满意度评价结果
     *
     * @return
     */
    void statisticsDepartments(String annualQuarterCode, HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 统计县分公司对机关各部门的满意度评价结果
     *
     * @param
     * @return
     */
    void countyBranchStatistics(String annualQuarterCode, HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 统计机关各部门对机关各部门的满意度评价总平均分以及百分制分值
     *
     * @return
     */
    void statisticsDepartmentsAvg(String annualQuarterCode, HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 县分公司对机关各部门的满意度评价总平均分以及百分制分值
     *
     * @return
     */
    void countyBranchStatisticsDepartmentsAvg(String annualQuarterCode, HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 统计汇总表
     * <AUTHOR>
     * @date 2021/6/29
     */
    void statisticalSummary(String annualQuarterCode, HttpServletRequest request, HttpServletResponse response) throws IOException;

}
