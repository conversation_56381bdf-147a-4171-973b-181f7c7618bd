<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
  <title>考试维护信息</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
  <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
    rel="stylesheet" />
  <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
  <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
  <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
  <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
  <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
    type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
    type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
    type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
    th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
  <style>
      .meitucrop {
        width: 130px;
      }
      .uploadImage_del {
        right: -2px;
      }
  </style>
</head>
<script type="text/javascript">
  $(function () {
    //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
    var gps = getQueryString();
    loadForm("examImgForm");
    //判断是否是查看方式打开

    pageparam = {
      "listtable": {
        "listname": "#examImgTable",//table列表的id名称，需加#
        "querycmd": "action/image/findImagePathByExamAppCode?examAppCode=" + gps.id,
        "contentType": "application/json; charset=utf-8",
        // "queryParams":{examCode:""},
        "data": {},
        "nowrap": true,//把数据显示在一行里,默认true
        "styleClass": "noScroll",
        "fitColumns": true,
        "frozenColumns": [],//固定在左侧的列
        "columns": [[//列
          { title: "试卷编码", field: "examAppCode", width: 15, align: 'center' },
          {
            title: "使用状态", field: "useNow", width: 10, align: 'center',
            formatter: function (value, row, index) {
              if (row.useNow) {
                return "使用中";
              } else {
                return "未使用";
              }
            }
          },
          {
            title: "操作", field: "op", width: 15, align: 'center',
            formatter: function (value, row, index) {
              var g = "<a href='#' class='read' id=" + row.downLoadUrl + " id2=" + row.downLoadUrl +">【查看】</a>"
                      + "<a href='#' onclick='start(this)' class='usedItem' paperBackgroundPath=" + row.paperBackgroundPath + " id=" + row.id + " examAppCode=" + row.examAppCode + ">【启用】</a>"
                      + "<a  href='#' class='del operateRed' delete='action/image/deleteById" + "' deleteid='" + row.id + "'>【删除】</a>";
              return g;
            }
          },
        ]],
      },
      "dialogform": {
        "dialogid": "#add",//对话框的id
        "formname": "#examImgTableAddForm",//新增或修改对话框的formid需加#
         "insertcmd": "action/image/saveImagePath",//新增命令
        // "dialogNoClose":true,
        "onSubmit": function (data) {
          // delete data.fileimg_cform_images1628576230143
          // fileimg_cform_images1628576691968
          let arr = []
          for (let i = 0; i < data.images.length; i++) {
            let obj = {}
            const e = data.images[0];
            obj.examAppCode = gps.id
            obj.useNow = false
            obj.mobileFilePath= e.mobileFilePath
            obj.id = e.id
            arr.push(obj)
          }
          data.imagePaths = arr;
          delete data.images;
          // data = data.imagePaths;
         /* ajaxgeneral({
            url: "action/image/saveImagePath",
            data: arr,
            contentType: "application/json; charset=utf-8",
            success: function (res) {
              $("#add").dialog('close')
              // loadGrid(pageparam);
            }
          })*/
          return true;

          // data.imagePaths = data.images
        }
      },
    };
    loadGrid(pageparam);


    //修改验证框样式
    $('.easyui-validatebox').validatebox({
      tipPosition: 'right',
      validateOnCreate: false
    });
  });

  //查看图片
  $(document).on('click', '.read', function (e) {
    //ie8不兼容event、target；以下写法用来兼容ie8
    var event = e || window.event;
    var target = event.target || event.srcElement;
    localStorage.setItem('id',target.id)
    top.dialogP('html/exam/readBackgroundImg.html?id=' + target.id, window.name, '试卷背景', 'read', false, '700', '600');
  })
  window.read = function (data) {

  }

  // $(document).on('click', '.usedItem', function (e) {
  //   top.mesConfirm("提示", '是否启用该图片为这张试卷的背景图片？',
  //           function () {
  //             var data = {};
  //             data.id = $(".usedItem").attr("id");
  //             data.useNow = true
  //             data.examAppCode = $(".usedItem").attr("examAppCode")
  //             ajaxgeneral({
  //               url: "action/image/saveUseImagePath",
  //               data: data,
  //               contentType: "application/json; charset=utf-8",
  //               success: function (res) {
  //                 getparent().mesShow("温馨提示", "启用成功", 1000);
  //                 loadGrid(pageparam);
  //                 $('.searchtable').click()
  //               }
  //             })
  //           }, function () {
  //           });
  // })
  function start(param) {
    top.mesConfirm("提示", '是否启用该图片为这张试卷的背景图片？',
            function () {
              var data = {};
              data.id = $(param).attr("id");
              data.useNow = true
              data.examAppCode = $(".usedItem").attr("examAppCode")
              ajaxgeneral({
                url: "action/image/saveUseImagePath",
                data: data,
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                  getparent().mesShow("温馨提示", "启用成功", 1000);
                  loadGrid(pageparam);
                  $('.searchtable').click()
                }
              })
            }, function () {
            });
  }


  //表单校验
  window.fvalidate = function () {
    return $("#examImgForm").form("validate");
  };

  //重新加载table列表
  function loadList() {
    $('#examImgForm').datagrid('reload');
  }


  window.getchoosedata = function () {
    $('.easyui-validatebox').validatebox('enableValidation');
    if (fvalidate()) {
      //..
    } else {
      return;
    }
    return { "data": datas, "state": 1, "mod": gps.mod ? gps.mod : -1 };//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
  };





</script>

<body style="padding-top: 0;">
  <div class="table_searchD">
    <form id="examImgTableQueryForm">
      <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
          <td style="color: red;font-size: 16px;">新建时请务必保障图片名称非中文</td>
          <td>
            <div class="w100">
<!--              <a class="btn fl searchtable"><span>查询</span></a>-->
<!--              <a class="btn ml10 reset"><span>重置</span></a>-->
              <a class="btn a_green fr showDialog">新建</a>
            </div>
          </td>
        </tr>
      </table>
    </form>
  </div>
  <div class="examImgTable">
    <table id="examImgTable"></table>
  </div>


  <div id="add" title="新增或修改" class="easyui-dialog" style="width:700px;height:400px;">
    <form id="examImgTableAddForm" method="post" contentType="application/json; charset=utf-8"   onSubmit="onSubmit()">

      <table border="0" cellpadding="10" cellspacing="16" width="100%">
        <!-- <tr>
          <td width="200" align="right">是否当前图片为背景：</td>
          <td width="300">
            <select id="isBackGround" name="isBackGround" class="easyui-combobox"
              style="width: 100%;height: 32px;" data-options="editable:false,panelHeight:'auto'">
              <option value="true">是</option>
              <option value="false">否</option>
            </select>
        </tr> -->
        <tr>
          <td align="right" width="200" valign="top" class="lh32">上传图片：</td>
          <td colspan="3" valign="top">
            <input id="images" name="images" type="text"  mulaccept="image/*" isDisk="true" class="cselectorImageUpload"
              extension="gif,jpg,jpeg,png" sizelength="1" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
              href="action/uploadFile/uploadProcessFiles" />
          </td>
        </tr>

      </table>

    </form>
  </div>
</body>

</html>