package com.simbest.boot.exam.examOnline.task;

import cn.hutool.core.comparator.CompareUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.simbest.boot.component.distributed.lock.AppRuntimeMaster;
import com.simbest.boot.component.task.AbstractTaskSchedule;
import com.simbest.boot.exam.examOnline.model.ExamInfoSave;
import com.simbest.boot.exam.examOnline.model.ExamSummary;
import com.simbest.boot.exam.examOnline.service.IExamInfoSaveService;
import com.simbest.boot.exam.examOnline.service.IExamSummaryService;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.wfquey.service.IQueryDictValueService;
import com.simbest.boot.sys.model.SysDict;
import com.simbest.boot.sys.repository.SysTaskExecutedLogRepository;
import com.simbest.boot.sys.service.ISysDictService;
import com.simbest.boot.util.security.LoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MJSFExamTask extends AbstractTaskSchedule {

    private final LoginUtils loginUtils;
    private final ISysDictService sysDictService;
    private final IExamWorkService examWorkService;
    private final IExamSummaryService examSummaryService;
    private final IExamInfoSaveService examInfoSaveService;


    @Autowired
    public MJSFExamTask(AppRuntimeMaster master, SysTaskExecutedLogRepository repository, LoginUtils loginUtils, IQueryDictValueService queryDictValueService, ISysDictService sysDictService, IExamWorkService examWorkService, IExamSummaryService examSummaryService, IExamInfoSaveService examInfoSaveService) {
        super(master, repository);
        this.loginUtils = loginUtils;
        this.sysDictService = sysDictService;
        this.examWorkService = examWorkService;
        this.examSummaryService = examSummaryService;
        this.examInfoSaveService = examInfoSaveService;
    }

    /**
     * 每十分钟执行一次
     */
//    @Scheduled(cron = "0 0/10 * * * ?")
    public void checkAndExecute() {
        super.checkAndExecute(false);
    }

    @Override
    public String execute() {
        loginUtils.adminLogin();

        // 查看定时任务状态
        SysDict one = sysDictService.findOne(Specifications.<SysDict>and().eq("dictType", "Task_" + Constants.MJSF).build());
        if (Objects.isNull(one) || !Objects.equals(one.getSpare1(), "1")) return null;

        // 过滤超时的考试工单
        // 获取超过考试时间的考试
        List<String> summaryList = examSummaryService.findAllNoPage(Specifications.<ExamSummary>and().like("examCode", String.format("%%%s%%", Constants.MJSF)).build())
                .stream().filter(v -> CompareUtil.compare(v.getExamStartTime().plusMinutes(Long.parseLong(one.getSpare2())), LocalDateTime.now()) <= 0).map(ExamSummary::getExamCode).collect(Collectors.toList());

        // 获取已答过题的人员信息
        List<Map<String, String>> infoSaves = examInfoSaveService.findAllNoPage(Specifications.<ExamInfoSave>and().like("examCode", String.format("%%%s%%", Constants.MJSF)).build())
                .stream().filter(v -> summaryList.contains(v.getExamCode())).map(v -> {
                    Map<String, String> map = Maps.newHashMap();
                    map.put("creator", v.getCreator());
                    map.put("examCode", v.getExamCode());
                    return map;
                }).distinct().collect(Collectors.toList());

        // 获取所有考试工单
        List<ExamWork> list = examWorkService.findAllNoPage(Specifications.<ExamWork>and().like("examCode", String.format("%%%s%%", Constants.MJSF)).build())
                .stream().filter(v -> summaryList.contains(v.getExamCode())).collect(Collectors.toList());

        // 过滤未考试人员工单
        List<ExamWork> collect = list.stream().filter(v -> infoSaves.stream().noneMatch(v1 -> Objects.equals(v1.get("creator"), v.getTransactorCode()) && Objects.equals(v1.get("examCode"), v.getExamCode()))).collect(Collectors.toList());

        // 添加弃权考试记录
        collect.forEach(v -> {
            ExamInfoSave infoSave = ExamInfoSave.builder()
                    .hasDrop(true)
                    .residueTime("0")
                    .examCode(v.getExamCode())
                    .examAppCode(v.getExamAppCode())
                    .examRecord("")
                    .examAnswer("")
                    .timestamp(new Date().getTime())
                    .build();

            infoSave.setCreator(v.getTransactorCode());
            infoSave.setModifier(v.getTransactorCode());
            examInfoSaveService.insert(infoSave);
        });

        return null;
    }
}
