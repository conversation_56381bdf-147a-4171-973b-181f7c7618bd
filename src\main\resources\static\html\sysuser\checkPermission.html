<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>权限查询</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="https://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            var gps=getQueryString();
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称

            var pageparam1={
                "listtable":{
                    "listname":"#sysUserSearch1Table",//table列表的id名称，需加#
                    "querycmd":"action/permission/permission/findAllByUsername?username="+gps.username,//table列表的查询命令
                    "checkboxall":true,
                    "queryParams":{"username":gps.username},
                    //"pagination":false,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "所属应用编码", field: "appCode", width: 40,
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return row.appId.appCode;
                            }
                        },
                        { title: "所属应用名称", field: "appName", width: 90,
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return row.appId.appName;
                            }
                        },
                        { title: "资源路径", field: "url", width: 100,tooltip:true},
                        { title: "资源图标", field: "icon", width: 40},
                        { title: "资源描述", field: "description", width: 80 },
                        { title: "菜单级别", field: "menuLevel", width: 40 },//排序sortable: true
                        { title: "上级菜单", field: "parentId", width: 40 }//排序sortable: true
                    ] ],
                    // "pagerbar": [{
                    //     id:"deleteall",
                    //     iconCls: 'icon-remove',
                    //     text:"批量删除&nbsp;"
                    // }],
                    // "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                    //     "id":"deleteall",
                    //     "url":"action/user/org/deleteAllByIds",
                    //     "contentType":"application/json; charset=utf-8"
                    // }
                },
                // "dialogform":{
                //     "dialogid":"#buttons",//对话框的id
                //     "formname":"#sysUserOrgsTableAddForm",//新增或修改对话框的formid需加#
                //     "insertcmd":"action/user/org/create",//新增命令
                //     "updatacmd":"action/user/org/update"//修改命令
                // }
            };
            loadGrid(pageparam1);
            var pageparam2={
                "listtable":{
                    "listname":"#sysUserSearch2Table",//table列表的id名称，需加#
                    "querycmd":"action/sys/role/findRoleByUsernamePage?username="+gps.username,//table列表的查询命令
                    "checkboxall":true,
                    "queryParams":{"username":gps.username},
                    //"pagination":false,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "角色ID", field: "id", width: 60,sortable:true,
                            styler:function (value,row,index) {
                                if(row.id=="40"){
                                    return 'color:red;';
                                }
                            }
                        },
                        { title: "角色编码", field: "roleCode", width: 60,sortable:true,
                            styler:function (value,row,index) {
                                if(row.id=="40"){
                                    return 'color:red;';
                                }
                            }
                            },
                        { title: "角色名称", field: "roleName", width: 100,sortable:true,
                            styler:function (value,row,index) {
                                if(row.id=="40"){
                                    return 'color:red;';
                                }
                            }
                        },

                        { title: "是否是业务角色", field: "isApplicationRole", width: 40,sortable:true,
                            formatter:function(value,row,index){
                                return value?"是":"否";
                            },
                            styler:function (value,row,index) {
                                if(row.id=="40"){
                                    return 'color:red;';
                                }
                            }
                        }
                        // {
                        //     field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                        //     formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                        //         var gps=getQueryString();
                        //         var g = "<a href='#' class='showDialogTop' listPageName='userOrgsF' width='700' height='400' showDialogindex='" + index + "' openlayer='html/sysuser/sysUserOrgsInfo.html?id="+row.id+"&username="+gps.username+"' title='修改'>【修改】</a>"
                        //             +"<a href='#' delete='action/user/org/deleteById' deleteid='"+row.id+"'>【删除】</a>";
                        //         return g;
                        //     }
                        // }
                    ] ],
                    // "pagerbar": [{
                    //     id:"deleteall",
                    //     iconCls: 'icon-remove',
                    //     text:"批量删除&nbsp;"
                    // }],
                    // "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                    //     "id":"deleteall",
                    //     "url":"action/user/org/deleteAllByIds",
                    //     "contentType":"application/json; charset=utf-8"
                    // }
                },
                // "dialogform":{
                //     "dialogid":"#buttons",//对话框的id
                //     "formname":"#sysUserOrgsTableAddForm",//新增或修改对话框的formid需加#
                //     "insertcmd":"action/user/org/create",//新增命令
                //     "updatacmd":"action/user/org/update"//修改命令
                // }
            };
            loadGrid(pageparam2);
            var pageparam3={
                "listtable":{
                    "listname":"#sysUserSearch3Table",//table列表的id名称，需加#
                    "querycmd":"action/app/app/findPermissionByUsername?username="+gps.username,//table列表的查询命令
                    "checkboxall":true,
                    "queryParams":{"username":gps.username},
                    //"pagination":false,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "所属应用编码", field: "appCode", width: 40,},
                        { title: "所属应用名称", field: "appName", width: 90,}

                        // {
                        //     field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                        //     formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                        //         var gps=getQueryString();
                        //         var g = "<a href='#' class='showDialogTop' listPageName='userOrgsF' width='700' height='400' showDialogindex='" + index + "' openlayer='html/sysuser/sysUserOrgsInfo.html?id="+row.id+"&username="+gps.username+"' title='修改'>【修改】</a>"
                        //             +"<a href='#' delete='action/user/org/deleteById' deleteid='"+row.id+"'>【删除】</a>";
                        //         return g;
                        //     }
                        // }
                    ] ],
                    // "pagerbar": [{
                    //     id:"deleteall",
                    //     iconCls: 'icon-remove',
                    //     text:"批量删除&nbsp;"
                    // }],
                    // "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                    //     "id":"deleteall",
                    //     "url":"action/user/org/deleteAllByIds",
                    //     "contentType":"application/json; charset=utf-8"
                    // }
                },
                // "dialogform":{
                //     "dialogid":"#buttons",//对话框的id
                //     "formname":"#sysUserOrgsTableAddForm",//新增或修改对话框的formid需加#
                //     "insertcmd":"action/user/org/create",//新增命令
                //     "updatacmd":"action/user/org/update"//修改命令
                // }
            };
            loadGrid(pageparam3);

        });
    </script>
    <style>
        h3{
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            border-bottom: 1px dashed #ccc;
        }
    </style>
</head>
<body>
<!--searchform-->
<!--table-->
<h3>用户角色权限</h3>
<div class="sysUserSearch2Table" style="margin-top: 15px;border-top: 1px solid #ccc;"><table id="sysUserSearch2Table"></table></div>
<h3>应用访问权限</h3>
<div class="sysUserSearch3Table" style="margin-top: 15px;border-top: 1px solid #ccc;"><table id="sysUserSearch3Table"></table></div>
<h3>功能菜单权限</h3>
<div class="sysUserSearch1Table" style="margin-top: 15px;border-top: 1px solid #ccc;"><table id="sysUserSearch1Table"></table></div>


</body>
</html>
