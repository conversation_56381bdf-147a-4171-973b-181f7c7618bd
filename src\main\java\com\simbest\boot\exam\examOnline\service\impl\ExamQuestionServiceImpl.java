/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service.impl;/**
 * Created by KZH on 2019/10/8 15:14.
 */

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.exam.examOnline.model.ExamQuestionBank;
import com.simbest.boot.exam.examOnline.model.ExamQuestionImport;
import com.simbest.boot.exam.examOnline.repository.ExamQuestionRepository;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionBankService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.repository.SysDictValueRepository;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:14
 * @desc 题目
 **/
@SuppressWarnings("all")
@Slf4j
@Service
public class ExamQuestionServiceImpl extends LogicService<ExamQuestion, String> implements IExamQuestionService {

    private ExamQuestionRepository examQuestionBankRepository;

    @Autowired
    public ExamQuestionServiceImpl(ExamQuestionRepository repository) {
        super(repository);
        this.examQuestionBankRepository = repository;


    }

    @Autowired
    private ISysDictValueService iSysDictValueService;

    @Autowired
    private IExamQuestionBankService iExamQuestionBankService;

    @Autowired
    private IExamQuestionAnswerService iExamQuestionAnswerService;

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private SysDictValueRepository sysDictValueRepository;


    @Override
    public List<SysDictValue> findDictValue(String dictType) {
        log.debug("数据字典查询>>>>>>>>>>>>>>dictType=" + dictType);
        return sysDictValueRepository.findDictValue(dictType);
    }



    /**
     * @desc 根据题库编码获取试题
     * <AUTHOR>
     */
    @Override
    public List<ExamQuestion> findAllByQuestionBankCode(String questionBankCode) {
        return examQuestionBankRepository.findAllByQuestionBankCode(questionBankCode);
    }

    @Override
    public List<ExamQuestion> findAllByQuestionBankCodeAndPowerBuildingExtr(String questionBankCode) {
        List<ExamQuestion> retLists = Lists.newArrayList();

        // 力量大厦问卷从十类题中随机抽取一到小题
        for (int i = 1; i <= 10; i++) {
            List<ExamQuestion> allByQuestionBankCodeAndPowerBuildingExtr = examQuestionBankRepository.findAllByQuestionBankCodeAndPowerBuildingExtr(questionBankCode, String.valueOf(i));
            retLists.addAll(allByQuestionBankCodeAndPowerBuildingExtr);
        }

        return retLists;
    }

    @Override
    public List<ExamQuestion> findAllByQuestionBankCodeRandom(String questionBankCode, String size, String questionType) {
        return examQuestionBankRepository.findAllByQuestionBankCodeRandom(questionBankCode, size, questionType);
    }


    @Override
    public List<ExamQuestion> findAllByQuestionBankCode(List<String> questionCodeList) {
        return examQuestionBankRepository.findAllByQuestionCode(questionCodeList);
    }

    @Override
    public List<ExamQuestion> findAllByQuestionId(List<String> questionidList) {
        return examQuestionBankRepository.findAllByQuestionId(questionidList);
    }

    /**
     * 新增试题
     *
     * @param sumMaps
     * @return
     */
    @Override
    public JsonResponse importQuestion(Map<String, Object> sumMaps) {
        LinkedHashMap<String, ArrayList<String>> examQuestionList = (LinkedHashMap<String, ArrayList<String>>) sumMaps.get("examQuestionList");
        String questionBankCode = String.valueOf(sumMaps.get("questionBank"));

        List<ExamQuestion> examQuestionListSave = Lists.newArrayList();

        ExamQuestionBank allByQuestionBankCode = iExamQuestionBankService.findAllByQuestionBankCode(questionBankCode);
        Assert.notNull(allByQuestionBankCode, "获取题库为空，请检查是否存在题库");

        Integer questionBankSize = allByQuestionBankCode.getQuestionBankSize();

        for (Map.Entry<String, ArrayList<String>> entry : examQuestionList.entrySet()) {
            //log.debug("key:" + entry.getKey() + "   value:" + entry.getValue());
            if (questionBankSize == null) {
                questionBankSize = 0;
            }
            questionBankSize++;
            String key = entry.getKey();//key 代表题目
            if (key.equals("")) return JsonResponse.fail("题目信息不能为空");

            String questionCode = questionBankCode + "-" + questionBankSize;
            ExamQuestion examQuestion = new ExamQuestion();
            examQuestion.setQuestionCode(questionCode);
            examQuestion.setQuestionName(key);
            examQuestion.setQuestionBankCode(questionBankCode);
            examQuestion.setQuestionOrder(Integer.valueOf(questionBankSize));

            ArrayList<String> answerList = entry.getValue();//前四个代表答案 最后一个是正确的答案

            String questionType = answerList.get(0);
            if (questionType == null) {
                return JsonResponse.fail(-1, "题目类型获取失败");
            }
            examQuestion.setQuestionType(questionType);

            // 设置填空题的问题和答案
            if (Constants.FILLING.equals(questionType)) {
                String reg = "\\[\\[(.+?)]]";
                Matcher matcher = Pattern.compile(reg).matcher(key);
                List<String> l = new ArrayList<>();
                while (matcher.find()) {
                    l.add(matcher.group(1));
                }
                answerList.add(l.stream().reduce((a, b) -> a + " " + b).get());
                key = key.replaceAll(reg, "[[]]");
                examQuestion.setQuestionName(key);
            }

            /*处理答案*/
            List<ExamQuestionAnswer> examQuestionAnswerList = Lists.newArrayList();
            switch (questionType) {
                case Constants.SINGLE:
                    examQuestionAnswerList = disposeSingleData(answerList, questionCode);
                    break;
                case Constants.INDEFINITE:
                case Constants.MORE:
                    examQuestionAnswerList = disposeSingleData(answerList, questionCode);
                    break;
                case Constants.JUDGE:
                    examQuestionAnswerList = disposeJudgeData(answerList, questionCode);
                    break;
                case Constants.SHORTANSWER:
                    examQuestionAnswerList = disposeShortAnswerData(answerList, questionCode);
                    break;
                case Constants.FILLING:
                    examQuestionAnswerList = disposeFillingData(answerList, questionCode);
                    break;
            }

            int count = 0;//用于记 是否为多选
            for (ExamQuestionAnswer examQuestionAnswer : examQuestionAnswerList) {

                Boolean isCorrect = examQuestionAnswer.getIsCorrect();
                if (isCorrect != null) {
                    if (isCorrect) {
                        count++;
                    }
                }
            }
            if (count >= 2) {
                examQuestion.setQuestionType("more");
            }
            examQuestionListSave.add(examQuestion);
            iExamQuestionAnswerService.saveAll(examQuestionAnswerList);

        }
        this.saveAll(examQuestionListSave);
        allByQuestionBankCode.setQuestionBankSize(questionBankSize);
        iExamQuestionBankService.update(allByQuestionBankCode);

        return JsonResponse.success(1, "添加完成本次新增" + examQuestionListSave.size() + "道题");
    }

    /**
     * 根据题目编码获取试题
     *
     * @param questionCode
     * @return
     */
    @Override
    public ExamQuestion findAllByQuestionCode(String questionCode) {
        return examQuestionBankRepository.findAllByQuestionCode(questionCode);
    }

    /**
     * 重写父类删除 自定义删除题目同时去删除答案
     *
     * @param id 主键
     */
    @Override
    public void deleteById(String id) {

        ExamQuestion examQuestion = this.findById(id);

        String questionCode = examQuestion.getQuestionCode();

        List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(questionCode);

        for (ExamQuestionAnswer examQuestionAnswer : examQuestionAnswerList) {
            iExamQuestionAnswerService.deleteById(examQuestionAnswer.getId());
        }
        super.deleteById(id);
    }


    /**
     * 单选 多选数据处理
     *
     * @param answerList
     * @param questionCode
     * @return
     */
    private List<ExamQuestionAnswer> disposeSingleData(ArrayList<String> answerList, String questionCode) {
        List<ExamQuestionAnswer> examQuestionAnswerList = Lists.newArrayList();

        for (int j = 1; j < answerList.size(); j++) {

            String answerContent = answerList.get(j);
            if (Constants.A.equals(answerContent) || Constants.B.equals(answerContent) || Constants.C.equals(answerContent)
                    || Constants.D.equals(answerContent) || Constants.E.equals(answerContent) || Constants.F.equals(answerContent)
                    || Constants.G.equals(answerContent) || Constants.H.equals(answerContent) || Constants.I.equals(answerContent)
                    || Constants.J.equals(answerContent) || Constants.K.equals(answerContent) || Constants.L.equals(answerContent)) {
                String str = answerList.get(j);
                switch (str) {
                    case "A":
                        ExamQuestionAnswer examQuestionAnswer1 = examQuestionAnswerList.get(0);
                        examQuestionAnswer1.setIsCorrect(true);
                        break;
                    case "B":
                        ExamQuestionAnswer examQuestionAnswer2 = examQuestionAnswerList.get(1);
                        examQuestionAnswer2.setIsCorrect(true);
                        break;
                    case "C":
                        ExamQuestionAnswer examQuestionAnswer3 = examQuestionAnswerList.get(2);
                        examQuestionAnswer3.setIsCorrect(true);
                        break;
                    case "D":
                        ExamQuestionAnswer examQuestionAnswer4 = examQuestionAnswerList.get(3);
                        examQuestionAnswer4.setIsCorrect(true);
                        break;
                    case "E":
                        ExamQuestionAnswer examQuestionAnswer5 = examQuestionAnswerList.get(4);
                        examQuestionAnswer5.setIsCorrect(true);
                        break;
                    case "F":
                        ExamQuestionAnswer examQuestionAnswer6 = examQuestionAnswerList.get(5);
                        examQuestionAnswer6.setIsCorrect(true);
                        break;
                    case "G":
                        ExamQuestionAnswer examQuestionAnswer7 = examQuestionAnswerList.get(6);
                        examQuestionAnswer7.setIsCorrect(true);
                        break;
                    case "H":
                        ExamQuestionAnswer examQuestionAnswer8 = examQuestionAnswerList.get(7);
                        examQuestionAnswer8.setIsCorrect(true);
                        break;
                    case "I":
                        ExamQuestionAnswer examQuestionAnswer9 = examQuestionAnswerList.get(8);
                        examQuestionAnswer9.setIsCorrect(true);
                        break;
                    case "J":
                        ExamQuestionAnswer examQuestionAnswer10 = examQuestionAnswerList.get(9);
                        examQuestionAnswer10.setIsCorrect(true);
                        break;
                    case "K":
                        ExamQuestionAnswer examQuestionAnswer11 = examQuestionAnswerList.get(10);
                        examQuestionAnswer11.setIsCorrect(true);
                        break;
                    case "L":
                        ExamQuestionAnswer examQuestionAnswer12 = examQuestionAnswerList.get(11);
                        examQuestionAnswer12.setIsCorrect(true);
                        break;
                }
            } else {
                ExamQuestionAnswer examQuestionAnswer = new ExamQuestionAnswer(); //题目正确答案

                switch (j) {
                    case 1:
                        examQuestionAnswer.setAnswerCode(Constants.A);
                        break;
                    case 2:
                        examQuestionAnswer.setAnswerCode(Constants.B);
                        break;
                    case 3:
                        examQuestionAnswer.setAnswerCode(Constants.C);
                        break;
                    case 4:
                        examQuestionAnswer.setAnswerCode(Constants.D);
                        break;
                    case 5:
                        examQuestionAnswer.setAnswerCode(Constants.E);
                        break;
                    case 6:
                        examQuestionAnswer.setAnswerCode(Constants.F);
                        break;
                    case 7:
                        examQuestionAnswer.setAnswerCode(Constants.G);
                        break;
                    case 8:
                        examQuestionAnswer.setAnswerCode(Constants.H);
                        break;
                    case 9:
                        examQuestionAnswer.setAnswerCode(Constants.I);
                        break;
                    case 10:
                        examQuestionAnswer.setAnswerCode(Constants.J);
                        break;
                    case 11:
                        examQuestionAnswer.setAnswerCode(Constants.K);
                        break;
                    case 12:
                        examQuestionAnswer.setAnswerCode(Constants.L);
                        break;

                }

                examQuestionAnswer.setQuestionCode(questionCode);
                examQuestionAnswer.setAnswerContent(answerContent);

                examQuestionAnswerList.add(examQuestionAnswer);
            }

        }
        return examQuestionAnswerList;
    }

    /**
     * 判断 数据处理
     *
     * @param answerList
     * @param questionCode
     * @return
     */
    private List<ExamQuestionAnswer> disposeJudgeData(ArrayList<String> answerList, String questionCode) {
        List<ExamQuestionAnswer> examQuestionAnswerList = Lists.newArrayList();
        String s = answerList.get(1);

        ExamQuestionAnswer examQuestionAnswer = new ExamQuestionAnswer();
        ExamQuestionAnswer examQuestionAnswer2 = new ExamQuestionAnswer();

        examQuestionAnswer.setQuestionCode(questionCode);
        examQuestionAnswer2.setQuestionCode(questionCode);
        examQuestionAnswer.setAnswerCode("true");
        examQuestionAnswer2.setAnswerCode("false");
        if (Constants.TRUE.equals(s)) {
            examQuestionAnswer.setIsCorrect(true);
        } else {
            examQuestionAnswer2.setIsCorrect(true);
        }

        examQuestionAnswerList.add(examQuestionAnswer);
        examQuestionAnswerList.add(examQuestionAnswer2);
        return examQuestionAnswerList;
    }

    /**
     * 简答 处理答案
     *
     * @param answerList
     * @param questionCode
     * @return
     */
    private List<ExamQuestionAnswer> disposeShortAnswerData(ArrayList<String> answerList, String questionCode) {
        List<ExamQuestionAnswer> examQuestionAnswerList = Lists.newArrayList();
        String s = answerList.get(1);
        ExamQuestionAnswer examQuestionAnswer = new ExamQuestionAnswer();
        examQuestionAnswer.setQuestionCode(questionCode);
        examQuestionAnswer.setAnswerContent(s);
        examQuestionAnswer.setIsCorrect(true);
        examQuestionAnswerList.add(examQuestionAnswer);
        return examQuestionAnswerList;
    }

    /**
     * 填空题 处理答案
     *
     * @param answerList
     * @param questionCode
     * @return
     */
    private List<ExamQuestionAnswer> disposeFillingData(ArrayList<String> answerList, String questionCode) {
        List<ExamQuestionAnswer> examQuestionAnswerList = Lists.newArrayList();
        String s = answerList.get(1);
        String s1 = answerList.get(2);
        ExamQuestionAnswer examQuestionAnswer = new ExamQuestionAnswer();
        examQuestionAnswer.setQuestionCode(questionCode);
        examQuestionAnswer.setAnswerContent(s1);
        // 填空题是否需要按顺序判题
        examQuestionAnswer.setIdentification(s);
        examQuestionAnswer.setIsCorrect(true);
        examQuestionAnswerList.add(examQuestionAnswer);
        return examQuestionAnswerList;
    }


    @Override
    public List<ExamQuestionImport> importExcel(HttpServletRequest request, HttpServletResponse response) {
        JsonResponse jsonResponse = JsonResponse.defaultSuccessResponse();

        //取出当前登录人对象
        IUser currentUser = SecurityUtils.getCurrentUser();
        MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
        PrintWriter out;
        try {
            response.setContentType("text/html; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            out = response.getWriter();
            List<ExamQuestionImport> examQuestionList = null;
            UploadFileResponse uploadFileResponse = null;
            for (MultipartFile uploadfile : multipartFiles.values()) {
                // 先上传至sys_file表,注意sheetName名要与excel保持一致
                uploadFileResponse = sysFileService.importExcel(uploadfile, null, null, null, ExamQuestionImport.class, "试题数据");
                // 获取excel表格
                examQuestionList = uploadFileResponse.getListData();
                for (ExamQuestionImport examQuestion : examQuestionList) {

                }
            }
            uploadFileResponse.setListData(examQuestionList);
            jsonResponse.setData(uploadFileResponse);

            String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
            out.println(result);
            out.close();
            return examQuestionList;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public JsonResponse saveExamQuestionList(ExamQuestion examQuestion) {
        JsonResponse jsonResponse = new JsonResponse();
        //获取导入的每一行的数据
        List<ExamQuestionImport> answerListImport = examQuestion.getAnswerListImport();
        //先分别取出题目类型相关list
        List<ExamQuestionImport> answerListImportSingle = new ArrayList<>();
        List<ExamQuestionImport> answerListImportMore = new ArrayList<>();
        List<ExamQuestionImport> answerListImportJudge = new ArrayList<>();
        List<ExamQuestionImport> answerListImportShortAnswer = new ArrayList<>();
        List<ExamQuestionImport> answerListImportFilling = new ArrayList<>();
        List<ExamQuestionImport> answerListImportIndefinite = new ArrayList<>();
        try {
            for (ExamQuestionImport examQuestionImport : answerListImport) {
                switch (examQuestionImport.getQuestionType()) {
                    case Constants.SINGLE:
                        answerListImportSingle.add(examQuestionImport);
                        break;
                    case Constants.MORE:
                        answerListImportMore.add(examQuestionImport);
                        break;
                    case Constants.JUDGE:
                        answerListImportJudge.add(examQuestionImport);
                        break;
                    case Constants.SHORTANSWER:
                        answerListImportShortAnswer.add(examQuestionImport);
                        break;
                    case Constants.FILLING:
                        answerListImportFilling.add(examQuestionImport);
                        break;
                    case Constants.INDEFINITE:
                        answerListImportIndefinite.add(examQuestionImport);
                        break;
                }
            }
            //处理单选题
            for (ExamQuestionImport examQuestionImport : answerListImportSingle) {
                //保存每一条问题以及答案
                ExamQuestionBank allByQuestionBankCode = iExamQuestionBankService.findAllByQuestionBankCode(examQuestionImport.getQuestionBankCode());
                if (null == allByQuestionBankCode) {
                    return JsonResponse.fail("获取题库为空，请检查是否存在题库编码是否正确！");
                }
                Integer questionBankSize = allByQuestionBankCode.getQuestionBankSize();

                if (questionBankSize == null) {
                    questionBankSize = 0;
                }
                questionBankSize++;
                String questionCode = examQuestionImport.getQuestionBankCode() + "-" + questionBankSize;
                ExamQuestion examQuestionI = new ExamQuestion();
                examQuestionI.setQuestionBankCode(examQuestionImport.getQuestionBankCode());
                examQuestionI.setQuestionCode(questionCode);
                examQuestionI.setQuestionName(examQuestionImport.getQuestionName());
                examQuestionI.setQuestionType(examQuestionImport.getQuestionType());
                examQuestionI.setQuestionScore(examQuestionImport.getQuestionScore());
                examQuestionI.setQuestionOrder(Integer.valueOf(questionBankSize));
                examQuestionI.setQuestionGroupName(examQuestionImport.getQuestionGroupName());
                examQuestionI.setMaxChooseNum(examQuestionImport.getMaxChooseNum());
                ExamQuestion insert = this.insert(examQuestionI);
                List<ExamQuestionAnswer> answerList = saveAnswerList(examQuestionImport, insert);
                iExamQuestionAnswerService.saveAll(answerList);

                allByQuestionBankCode.setQuestionBankSize(questionBankSize);
                iExamQuestionBankService.update(allByQuestionBankCode);

            }
            //处理多选题
            for (ExamQuestionImport examQuestionImport : answerListImportMore) {
                //保存每一条问题以及答案
                ExamQuestionBank allByQuestionBankCode = iExamQuestionBankService.findAllByQuestionBankCode(examQuestionImport.getQuestionBankCode());
                Integer questionBankSize = allByQuestionBankCode.getQuestionBankSize();
                if (null == allByQuestionBankCode) {
                    return JsonResponse.fail("获取题库为空，请检查是否存在题库编码是否正确！");
                }
                if (questionBankSize == null) {
                    questionBankSize = 0;
                }
                questionBankSize++;
                String questionCode = examQuestionImport.getQuestionBankCode() + "-" + questionBankSize;
                ExamQuestion examQuestionI = new ExamQuestion();
                examQuestionI.setQuestionBankCode(examQuestionImport.getQuestionBankCode());
                examQuestionI.setQuestionCode(questionCode);
                examQuestionI.setQuestionName(examQuestionImport.getQuestionName());
                examQuestionI.setQuestionType(examQuestionImport.getQuestionType());
                examQuestionI.setQuestionScore(examQuestionImport.getQuestionScore());
                examQuestionI.setQuestionOrder(Integer.valueOf(questionBankSize));
                examQuestionI.setQuestionGroupName(examQuestionImport.getQuestionGroupName());
                examQuestionI.setMaxChooseNum(examQuestionImport.getMaxChooseNum());
                ExamQuestion insert = this.insert(examQuestionI);
                List<ExamQuestionAnswer> answerList = saveAnswerList(examQuestionImport, insert);
                iExamQuestionAnswerService.saveAll(answerList);

                allByQuestionBankCode.setQuestionBankSize(questionBankSize);
                iExamQuestionBankService.update(allByQuestionBankCode);

            }
            //处理不定项题
            for (ExamQuestionImport examQuestionImport : answerListImportIndefinite) {
                //保存每一条问题以及答案
                ExamQuestionBank allByQuestionBankCode = iExamQuestionBankService.findAllByQuestionBankCode(examQuestionImport.getQuestionBankCode());
                Integer questionBankSize = allByQuestionBankCode.getQuestionBankSize();
                if (null == allByQuestionBankCode) {
                    return JsonResponse.fail("获取题库为空，请检查是否存在题库编码是否正确！");
                }
                if (questionBankSize == null) {
                    questionBankSize = 0;
                }
                questionBankSize++;
                String questionCode = examQuestionImport.getQuestionBankCode() + "-" + questionBankSize;
                ExamQuestion examQuestionI = new ExamQuestion();
                examQuestionI.setQuestionBankCode(examQuestionImport.getQuestionBankCode());
                examQuestionI.setQuestionCode(questionCode);
                examQuestionI.setQuestionName(examQuestionImport.getQuestionName());
                examQuestionI.setQuestionType(examQuestionImport.getQuestionType());
                examQuestionI.setQuestionScore(examQuestionImport.getQuestionScore());
                examQuestionI.setQuestionOrder(Integer.valueOf(questionBankSize));
                examQuestionI.setQuestionGroupName(examQuestionImport.getQuestionGroupName());
                ExamQuestion insert = this.insert(examQuestionI);
                List<ExamQuestionAnswer> answerList = saveAnswerList(examQuestionImport, insert);
                iExamQuestionAnswerService.saveAll(answerList);

                allByQuestionBankCode.setQuestionBankSize(questionBankSize);
                iExamQuestionBankService.update(allByQuestionBankCode);

            }
            //处理判断题
            for (ExamQuestionImport examQuestionImport : answerListImportJudge) {
                //保存每一条问题以及答案
                ExamQuestionBank allByQuestionBankCode = iExamQuestionBankService.findAllByQuestionBankCode(examQuestionImport.getQuestionBankCode());
                Integer questionBankSize = allByQuestionBankCode.getQuestionBankSize();
                if (null == allByQuestionBankCode) {
                    return JsonResponse.fail("获取题库为空，请检查是否存在题库编码是否正确！");
                }
                if (questionBankSize == null) {
                    questionBankSize = 0;
                }
                questionBankSize++;
                String questionCode = examQuestionImport.getQuestionBankCode() + "-" + questionBankSize;
                ExamQuestion examQuestionI = new ExamQuestion();
                examQuestionI.setQuestionBankCode(examQuestionImport.getQuestionBankCode());
                examQuestionI.setQuestionCode(questionCode);
                examQuestionI.setQuestionName(examQuestionImport.getQuestionName());
                examQuestionI.setQuestionType(examQuestionImport.getQuestionType());
                examQuestionI.setQuestionScore(examQuestionImport.getQuestionScore());
                examQuestionI.setQuestionOrder(Integer.valueOf(questionBankSize));
                examQuestionI.setQuestionGroupName(examQuestionImport.getQuestionGroupName());
                ExamQuestion insert = this.insert(examQuestionI);
                List<ExamQuestionAnswer> answerList = saveAnswerList(examQuestionImport, insert);
                iExamQuestionAnswerService.saveAll(answerList);

                allByQuestionBankCode.setQuestionBankSize(questionBankSize);
                iExamQuestionBankService.update(allByQuestionBankCode);
            }
            //处理填空题
            for (ExamQuestionImport examQuestionImport : answerListImportFilling) {
                //保存每一条问题以及答案
                ExamQuestionBank allByQuestionBankCode = iExamQuestionBankService.findAllByQuestionBankCode(examQuestionImport.getQuestionBankCode());
                Integer questionBankSize = allByQuestionBankCode.getQuestionBankSize();
                if (null == allByQuestionBankCode) {
                    return JsonResponse.fail("获取题库为空，请检查是否存在题库编码是否正确！");
                }
                if (questionBankSize == null) {
                    questionBankSize = 0;
                }
                questionBankSize++;
                String questionCode = examQuestionImport.getQuestionBankCode() + "-" + questionBankSize;
                ExamQuestion examQuestionI = new ExamQuestion();
                examQuestionI.setQuestionBankCode(examQuestionImport.getQuestionBankCode());
                examQuestionI.setQuestionCode(questionCode);
                examQuestionI.setQuestionName(examQuestionImport.getQuestionName());
                examQuestionI.setQuestionType(examQuestionImport.getQuestionType());
                examQuestionI.setQuestionScore(examQuestionImport.getQuestionScore());
                examQuestionI.setQuestionOrder(Integer.valueOf(questionBankSize));
                examQuestionI.setQuestionGroupName(examQuestionImport.getQuestionGroupName());
                ExamQuestion insert = this.insert(examQuestionI);
                List<ExamQuestionAnswer> answerList = saveAnswerList(examQuestionImport, insert);
                iExamQuestionAnswerService.saveAll(answerList);

                allByQuestionBankCode.setQuestionBankSize(questionBankSize);
                iExamQuestionBankService.update(allByQuestionBankCode);
            }
            //处理简答题
            for (ExamQuestionImport examQuestionImport : answerListImportShortAnswer) {
                //保存每一条问题以及答案
                ExamQuestionBank allByQuestionBankCode = iExamQuestionBankService.findAllByQuestionBankCode(examQuestionImport.getQuestionBankCode());
                Integer questionBankSize = allByQuestionBankCode.getQuestionBankSize();
                if (null == allByQuestionBankCode) {
                    return JsonResponse.fail("获取题库为空，请检查是否存在题库编码是否正确！");
                }
                if (questionBankSize == null) {
                    questionBankSize = 0;
                }
                questionBankSize++;
                String questionCode = examQuestionImport.getQuestionBankCode() + "-" + questionBankSize;
                ExamQuestion examQuestionI = new ExamQuestion();
                examQuestionI.setQuestionBankCode(examQuestionImport.getQuestionBankCode());
                examQuestionI.setQuestionCode(questionCode);
                examQuestionI.setQuestionName(examQuestionImport.getQuestionName());
                examQuestionI.setQuestionType(examQuestionImport.getQuestionType());
                examQuestionI.setQuestionScore(examQuestionImport.getQuestionScore());
                examQuestionI.setQuestionOrder(Integer.valueOf(questionBankSize));
                examQuestionI.setQuestionGroupName(examQuestionImport.getQuestionGroupName());
                ExamQuestion insert = this.insert(examQuestionI);
                List<ExamQuestionAnswer> answerList = saveAnswerList(examQuestionImport, insert);
                iExamQuestionAnswerService.saveAll(answerList);

                allByQuestionBankCode.setQuestionBankSize(questionBankSize);
                iExamQuestionBankService.update(allByQuestionBankCode);
            }
            return JsonResponse.success(1, "添加完成本次新增" + answerListImport.size() + "道题");
        } catch (Exception e) {
            e.printStackTrace();
        }


        return jsonResponse;
    }

    /**
     * 保存答案
     * <br/> params [examQuestionImport, insert]
     *
     * @return {@link List< ExamQuestionAnswer>}
     */
    private List<ExamQuestionAnswer> saveAnswerList(ExamQuestionImport examQuestionImport, ExamQuestion insert) {

        List<ExamQuestionAnswer> answerList = new ArrayList<>();
        String answerIsTrue = examQuestionImport.getAnswerIsTrue();
        String type = examQuestionImport.getQuestionType();
        //保存答案以及正确答案
        if (Objects.equals(type, Constants.SINGLE)
                || Objects.equals(type, Constants.MORE)
                || Objects.equals(type, Constants.INDEFINITE)) {
            if (StringUtils.isEmpty(answerIsTrue)) {
                answerIsTrue = "";
            }
            // 注意！！！ 此代码不是我写的。。。
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerA())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("A")
                        .answerContent(examQuestionImport.getAnswerA()).build();
                if (answerIsTrue.contains("A")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerB())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("B")
                        .answerContent(examQuestionImport.getAnswerB()).build();
                if (answerIsTrue.contains("B")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerC())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("C")
                        .answerContent(examQuestionImport.getAnswerC()).build();
                if (answerIsTrue.contains("C")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerD())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("D")
                        .answerContent(examQuestionImport.getAnswerD()).build();
                if (answerIsTrue.contains("D")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerE())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("E")
                        .answerContent(examQuestionImport.getAnswerE()).build();
                if (answerIsTrue.contains("E")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerF())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("F")
                        .answerContent(examQuestionImport.getAnswerF()).build();
                if (answerIsTrue.contains("F")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerG())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("G")
                        .answerContent(examQuestionImport.getAnswerG()).build();
                if (answerIsTrue.contains("G")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerH())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("H")
                        .answerContent(examQuestionImport.getAnswerH()).build();
                if (answerIsTrue.contains("H")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerI())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("I")
                        .answerContent(examQuestionImport.getAnswerI()).build();
                if (answerIsTrue.contains("I")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerJ())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("J")
                        .answerContent(examQuestionImport.getAnswerJ()).build();
                if (answerIsTrue.contains("J")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerK())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("K")
                        .answerContent(examQuestionImport.getAnswerK()).build();
                if (answerIsTrue.contains("K")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
            if (!StringUtils.isEmpty(examQuestionImport.getAnswerL())) {
                ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                        .questionCode(insert.getQuestionCode())
                        .answerCode("L")
                        .answerContent(examQuestionImport.getAnswerL()).build();
                if (answerIsTrue.contains("L")) {
                    answer.setIsCorrect(true);
                }
                answerList.add(answer);
            }
        } else if (Objects.equals(type, Constants.FILLING) || Objects.equals(type, Constants.SHORTANSWER)) {
            ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                    .questionCode(insert.getQuestionCode())
                    .answerContent(answerIsTrue)
                    .isCorrect(true).build();
            answerList.add(answer);
        } else if (Objects.equals(type, Constants.JUDGE)) {
            ExamQuestionAnswer answer = ExamQuestionAnswer.builder()
                    .questionCode(insert.getQuestionCode())
                    .answerContent("对")
                    .answerCode("true")
                    .build();
            if (BooleanUtils.isTrue(new Boolean(answerIsTrue)))
                answer.setIsCorrect(true);
            answerList.add(answer);
            answer = ExamQuestionAnswer.builder()
                    .questionCode(insert.getQuestionCode())
                    .answerContent("错")
                    .answerCode("false")
                    .build();
            if (BooleanUtils.isFalse(new Boolean(answerIsTrue)))
                answer.setIsCorrect(true);
            answerList.add(answer);
        }
        return answerList;
    }

    /**
     * 查询所有试题
     *
     * @param examQuestion
     * @return
     */
    @Override
    public JsonResponse findAllExamQuestion(ExamQuestion examQuestion, Pageable pageable) {
        Page<ExamQuestion> all = null;
        try {
            IUser currentUser = SecurityUtils.getCurrentUser();

            if (currentUser.getUsername().equals("hadmin")) {
                Specification<ExamQuestion> questionSpecification = Specifications.<ExamQuestion>and()
                        .like(!StringUtils.isEmpty(examQuestion.getQuestionName()), "questionName", "%" + examQuestion.getQuestionName() + "%")
                        .eq(!StringUtils.isEmpty(examQuestion.getQuestionBankCode()), "questionBankCode", examQuestion.getQuestionBankCode())
                        .eq(!StringUtils.isEmpty(examQuestion.getQuestionType()), "questionType", examQuestion.getQuestionType())
                        .build();
                all = this.findAll(questionSpecification, pageable);
                return JsonResponse.success(all);
            }

            Specification<ExamQuestion> questionSpecification = Specifications.<ExamQuestion>and()
                    .like(!StringUtils.isEmpty(examQuestion.getQuestionName()), "questionName", "%" + examQuestion.getQuestionName() + "%")
                    .eq(!StringUtils.isEmpty(examQuestion.getQuestionBankCode()), "questionBankCode", examQuestion.getQuestionBankCode())
                    .eq(!StringUtils.isEmpty(examQuestion.getQuestionType()), "questionType", examQuestion.getQuestionType())
                    .eq("creator", currentUser.getUsername())
                    .build();
            all = this.findAll(questionSpecification, pageable);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return JsonResponse.success(all);
    }

    /**
     * 根据题库编码获取所有题目编号及分组信息
     *
     * @param questionBankCode
     * @return
     */
    @Override
    public List<Map<String, Object>> findGroupByQuestionBankCode(String questionBankCode) {
        return examQuestionBankRepository.findGroupByQuestionBankCode(questionBankCode);
    }

    @Override
    public List<Map<String, Object>> findByQuestionBankCode(String questionBankCode) {
        return examQuestionBankRepository.findByQuestionBankCode(questionBankCode);
    }

    /**
     * 根据题目编号获取题目
     *
     * @param finallyCodeList 题目编号
     * @return
     */
    @Override
    public List<ExamQuestion> findAllByQuestionCodes(List<String> finallyCodeList) {
        Specification<ExamQuestion> build = Specifications.<ExamQuestion>and()
                .eq("enabled", Boolean.TRUE)
                .in("questionCode", finallyCodeList)
                .build();
        List<ExamQuestion> examQuestions = this.findAllNoPage(build, Sort.by(Sort.Direction.ASC, "questionOrder"));
        return examQuestions;
    }



    /**
     * 根据题库编码和随机提取个数来抽取题目信息
     *
     * @param questionBankCode 题库编码
     * @param count
     * @return
     */
    @Override
    public List<ExamQuestion> getRandomQuestionsByCategory(String questionBankCode, Integer count) {
        // 获取指定类别的所有题目
        List<ExamQuestion> allQuestions = examQuestionBankRepository.findAllByQuestionBankCode(questionBankCode);

        if (allQuestions == null || allQuestions.isEmpty()) {
            throw new IllegalArgumentException("No questions found for category: " + questionBankCode);
        }

        // 如果请求的题目数量大于总题目数量，返回所有题目
        if (count >= allQuestions.size()) {
            return allQuestions;
        }

       // 随机抽取题目
        List<String> questionCodes = allQuestions.stream().map(ExamQuestion::getId).collect(Collectors.toList());
        TreeSet<String> treeSet = new TreeSet<>();
        while (treeSet.size() < count) {
            int i = RandomUtil.randomInt(0, questionCodes.size());
            treeSet.add(questionCodes.get(i));
        }
        List<ExamQuestion> examQuestions = allQuestions.stream().filter(question -> treeSet.stream().anyMatch(questionCode -> StrUtil.equals(questionCode, question.getId()))).collect(Collectors.toList());

        /* List<ExamQuestion> selectedQuestions = new ArrayList<>(allQuestions);
        Collections.shuffle(selectedQuestions);
        List<ExamQuestion> examQuestions = CollectionUtil.sub(selectedQuestions , 0 , count);*/
        log.warn("当前随机出题的数量为{}个" , examQuestions.size());
        /*List<ExamQuestion> examQuestions = selectedQuestions.subList(0, count);
        int i = 0;
        while (examQuestions.size() < count && i < count) {
            i++;

        }*/

        //补充题目选项
        for (ExamQuestion examQuestion : examQuestions) {
            List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
            examQuestion.setAnswerList(examQuestionAnswerList);
        }
        return examQuestions;
    }

    @Override
    public List<ExamQuestion> getRandomQuestionsByCategoryAndExamNotIn(String questionBankCode, Integer count,List<String> ids) {
        // 获取指定类别的所有题目
        List<ExamQuestion> allQuestions = examQuestionBankRepository.findAllByQuestionBankCodeAndExamNotIn(questionBankCode,ids);
        if (allQuestions == null || allQuestions.isEmpty()) {
            throw new IllegalArgumentException("No questions found for category: " + questionBankCode);
        }

        // 如果请求的题目数量大于总题目数量，返回所有题目
        if (count >= allQuestions.size()) {
            return allQuestions;
        }

        // 随机抽取题目
        List<ExamQuestion> selectedQuestions = new ArrayList<>(allQuestions);
        Collections.shuffle(selectedQuestions);
        List<ExamQuestion> examQuestions = selectedQuestions.subList(0, count);
        //补充题目选项
        for (ExamQuestion examQuestion : examQuestions) {
            List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
            examQuestion.setAnswerList(examQuestionAnswerList);
        }
        return examQuestions;
    }

    @Override
    public List<ExamQuestion> getRandomQuestionsByCategoryWithOutAnswer(String questionBankCode, Integer count) {
        // 获取指定类别的所有题目
        List<ExamQuestion> allQuestions = examQuestionBankRepository.findAllByQuestionBankCode(questionBankCode);

        if (allQuestions == null || allQuestions.isEmpty()) {
            throw new IllegalArgumentException("No questions found for category: " + questionBankCode);
        }

        // 如果请求的题目数量大于总题目数量，返回所有题目
        if (count >= allQuestions.size()) {
            return allQuestions;
        }

        // 随机抽取题目
        List<String> questionCodes = allQuestions.stream().map(ExamQuestion::getId).collect(Collectors.toList());
        TreeSet<String> treeSet = new TreeSet<>();
        while (treeSet.size() < count) {
            int i = RandomUtil.randomInt(0, questionCodes.size());
            treeSet.add(questionCodes.get(i));
        }
        List<ExamQuestion> examQuestions = allQuestions.stream().filter(question -> treeSet.stream().anyMatch(questionCode -> StrUtil.equals(questionCode, question.getId()))).collect(Collectors.toList());
        log.warn("当前随机出题的数量为{}个" , examQuestions.size());


        //补充题目选项
        for (ExamQuestion examQuestion : examQuestions) {
            List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
            examQuestionAnswerList.forEach(examQuestionAnswer -> examQuestionAnswer.setIsCorrect(null));
            examQuestion.setAnswerList(examQuestionAnswerList);
        }
        return examQuestions;
    }


    /**
     * 根据问题编码查询问题信息
     * @param questionCode  问题编码
     * @return
     */
    public ExamQuestion findByQuestionCode(String questionCode) {
        Assert.notNull(questionCode , "问题编码不能为空！");
        Specification<ExamQuestion> build = Specifications.<ExamQuestion>and()
                .eq("enabled", Boolean.TRUE)
                .eq("questionCode", questionCode)
                .build();
        List<ExamQuestion> questions = this.findAllNoPage(build);
        if (CollectionUtil.isNotEmpty(questions)) {
            return questions.get(0);
        }
        return null;
    }

    /**
     * 根据题目编码获取题目和答案信息
     * @param questionCode 题目编码
     * @return
     */
    @Override
    public ExamQuestion findQuestionAndAnswer(String questionCode) {
        ExamQuestion question = this.findByQuestionCode(questionCode);
        Assert.notNull(question , "查询问题信息失败！");
        if (!StrUtil.equals(question.getQuestionType() , Constants.FILLING) && !StrUtil.equals(question.getQuestionType() , Constants.SHORTANSWER)) {
            List<ExamQuestionAnswer> answerList = iExamQuestionAnswerService.findAllByQuestionCode(questionCode);
            if (CollectionUtil.isNotEmpty(answerList)) {
                question.setAnswerList(answerList);
            }
        }
        return question;
    }

}
