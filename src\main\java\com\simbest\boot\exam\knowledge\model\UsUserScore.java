/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.model;/**
 * Created by KZH on 2019/10/8 15:55.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:55
 * @desc 考试业务单据
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_user_score")
@ApiModel(value = "用户得分总分表")
public class UsUserScore extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "W") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 50, nullable = false)
    @ApiModelProperty(value = "主单据Id", name = "pmInsId", example = "2019678301426913763328", required = true)
    private String pmInsId;

    @Column(length = 50, nullable = false)
    @ApiModelProperty(value = "办理人", name = "transactor", example = "超级管理员", required = true)
    private String transactor;

    @Column(length = 50, nullable = false)
    @ApiModelProperty(value = "办理人oa账号", name = "transactorCode", example = "hadmin", required = true)
    private String transactorCode;

    @Column(length = 20, nullable = false)
    @ApiModelProperty(value = "人员级别", name = "positionLevel", example = "5", required = true)
    private String positionLevel;

    @Column(length = 200, nullable = false)
    @ApiModelProperty(value = "办理人所在部门", name = "departmentCode", example = "2700526267653981965", required = true)
    private String departmentCode;

    @Column(length = 200)
    @ApiModelProperty(value = "办理人所在部门", name = "departmentName", example = "业务支撑中心")
    private String departmentName;

    @Column(length = 200, nullable = false)
    @ApiModelProperty(value = "办理人所在公司", name = "companyCode", example = "4772338661636601428", required = true)
    private String companyCode;

    @Column(length = 200, nullable = false)
    @ApiModelProperty(value = "父级值", name = "parentCompanyCode", example = "00000000000000000000", required = true)
    private String parentCompanyCode;

    @Column(length = 200)
    @ApiModelProperty(value = "办理人所在公司", name = "companyName", example = "省公司")
    private String companyName;

    @Column(length = 200)
    @ApiModelProperty(value = "办理人所在组织", name = "orgCode", example = "2709048922982171940", required = true)
    private String orgCode;

    @Column(length = 200)
    @ApiModelProperty(value = "办理人所在组织", name = "orgName", example = "经分支撑室")
    private String orgName;

    @Column(length = 200)
    @ApiModelProperty(value = "部门全路径", name = "displayName", example = "省公司-业务支撑中心-经分支撑室")
    private String displayName;

    @Column(length = 50)
    @ApiModelProperty(value = "创建年份", name = "createYear", example = "2019")
    private String createYear;

    @Column(length = 200)
    @ApiModelProperty(value = "考试标题", name = "title", example = "2019年河南公司业务支撑中心反腐倡廉教育考试", required = true)
    private String title;

    @Column(length = 20)
    @ApiModelProperty(value = "是否推送过统一待办", name = "isTodoFlag", example = "0")
    private String isTodoFlag;

    @Column(length = 20)
    @ApiModelProperty(value = "推送短信", name = "isPostMsg", example = "0")
    private String isPostMsg;

    @Column(length = 20)
    @ApiModelProperty(value = "待办类型", name = "workType", example = "A")
    private String workType;

    @Column(length = 50)
    @ApiModelProperty(value = "推送短信最后时间", name = "lastPostTime", example = "2019-08-12 09:36:48")
    private String lastPostTime;

    @Column(length = 20)
    @ApiModelProperty(value = "试卷编码", name = "examAppCode", example = "lyxf")
    private String examAppCode;

    @Column(length = 20)
    @ApiModelProperty(value = "考试编码", name = "examCode", example = "lyxf")
    private String examCode;

    @Column(length = 20)
    @ApiModelProperty(value = "答题总分", name = "examCode", example = "lyxf")
    private String allScoure;

    @Column(length = 20)
    @ApiModelProperty(value = "排序", name = "examCode", example = "lyxf")
    private String sort;


    @Transient
    @ApiModelProperty(value = "是否待办已办 1已办 0待办")
    private String sign;

}
