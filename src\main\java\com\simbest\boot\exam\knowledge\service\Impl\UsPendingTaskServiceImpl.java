/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service.Impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.cmcc.a4.response.JsonResponse;
import com.simbest.boot.exam.examOnline.util.SyncTool;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.knowledge.model.UsInvitations;
import com.simbest.boot.exam.knowledge.model.UsPendingTask;
import com.simbest.boot.exam.knowledge.repository.UsInvitationsRepository;
import com.simbest.boot.exam.knowledge.repository.UsPendingTaskRepository;
import com.simbest.boot.exam.knowledge.service.IUsInvitationsService;
import com.simbest.boot.exam.knowledge.service.IUsPendingTaskService;
import com.simbest.boot.exam.util.DateUtil;
import com.simbest.boot.security.IAuthService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class UsPendingTaskServiceImpl extends LogicService<UsPendingTask, String> implements IUsPendingTaskService {

    private final UsPendingTaskRepository usPendingTaskRepository;

    private final String param1 = "/acton/usPendingTask";

    @Autowired
    public UsPendingTaskServiceImpl(UsPendingTaskRepository usPendingTaskRepository) {
        super(usPendingTaskRepository);
        this.usPendingTaskRepository = usPendingTaskRepository;
    }

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IExamWorkService iExamWorkService;

    @Setter(onMethod_ = {@Autowired})
    private SyncTool syncTool;

    /**
     * 生成并推送统一待办
     *
     * @param usPendingTask 待办任务
     * @return JsonResponse
     */
    @Override
    public JsonResponse saveOpenTodo(UsPendingTask usPendingTask) {
        ExamWork examWork = new ExamWork();

        // 获取当前时间
        Date now = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String nowStr = formatter.format(now);

        // 设置主单据Id
        examWork.setPmInsId(usPendingTask.getPmInsId());

        // 设置办理人
        examWork.setTransactor(usPendingTask.getPendTrueName());

        // 设置办理人OA账号
        examWork.setTransactorCode(usPendingTask.getPendUserName());

        // 查询用户信息
        SimpleUser user = uumsSysUserinfoApi.findByKey(usPendingTask.getPendUserName(), IAuthService.KeyType.username, "exam");

        // 设置人员级别
        examWork.setPositionLevel(user.getPositionLevel());

        // 设置办理人所在部门
        examWork.setDepartmentCode(user.getBelongDepartmentCode());
        examWork.setDepartmentName(user.getBelongDepartmentName());

        // 设置办理人所在公司
        examWork.setCompanyCode(user.getBelongCompanyCode());
        examWork.setParentCompanyCode(user.getBelongCompanyCodeParent());
        examWork.setCompanyName(user.getBelongCompanyName());

        // 设置办理人所在组织
        examWork.setOrgCode(user.getBelongOrgCode());
        examWork.setOrgName(user.getBelongOrgName());

        // 设置部门全路径
        examWork.setDisplayName(user.getBelongOrgName());

        // 设置创建年份
        examWork.setCreateYear(DateUtil.getCurrYear());

        // 设置考试标题
        examWork.setTitle(usPendingTask.getTitle());

        // 设置是否推送过统一待办
        examWork.setIsTodoFlag("0");

        // 设置推送短信
        examWork.setIsPostMsg("0");

        // 设置待办类型
        examWork.setWorkType("Z");

        // 设置推送短信最后时间
        examWork.setLastPostTime(nowStr);

        // 设置试卷编码
        examWork.setExamAppCode("lyxf");

        // 设置考试编码
        examWork.setExamCode(usPendingTask.getQuestionBankCode());

        // 设置是否待办已办
        examWork.setSign("0");
        ExamWork examWork1 = iExamWorkService.insert(examWork);

        // 输出设置后的对象信息
        System.out.println("Exam Work: " + examWork);
        // 推发统一待办
        List<ExamWork> todoWorkList=new ArrayList<>();
        if(null!=examWork1){
            todoWorkList.add(examWork1);
        }
        iExamWorkService.sendUnifiedToDo(todoWorkList);
        log.warn("推送待办完成，总计推送" + todoWorkList.size() + "条");
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 核销统一待办
     *
     * @param usPendingTask
     * @return
     */
    @Override
    public JsonResponse cancleOpenTodo(UsPendingTask usPendingTask) {
        ExamWork examWork = null;
        Specification<ExamWork> build = Specifications.<ExamWork>and()
                .eq("pmInsId", usPendingTask.getPmInsId())
                .eq("transactorCode", usPendingTask.getPendUserName()).build();
        List<ExamWork> examWorkList = iExamWorkService.findAllNoPage(build);
        if (CollectionUtil.isNotEmpty(examWorkList)) {
            examWork = examWorkList.get(0);
        } else {
            return JsonResponse.fail(-1 , "未查询到待办信息！");
        }
        /*ExamWork examWork = new ExamWork();

        // 获取当前时间
        Date now = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String nowStr = formatter.format(now);

        // 设置主单据Id
        examWork.setPmInsId(usPendingTask.getPmInsId());

        // 设置办理人
        examWork.setTransactor(usPendingTask.getPendTrueName());

        // 设置办理人OA账号
        examWork.setTransactorCode(usPendingTask.getPendUserName());

        // 查询用户信息
        SimpleUser user = uumsSysUserinfoApi.findByKey(usPendingTask.getPendUserName(), IAuthService.KeyType.username, "exam");

        // 设置人员级别
        examWork.setPositionLevel(user.getPositionLevel());

        // 设置办理人所在部门
        examWork.setDepartmentCode(user.getBelongDepartmentCode());
        examWork.setDepartmentName(user.getBelongDepartmentName());

        // 设置办理人所在公司
        examWork.setCompanyCode(user.getBelongCompanyCode());
        examWork.setParentCompanyCode(user.getBelongCompanyCodeParent());
        examWork.setCompanyName(user.getBelongCompanyName());

        // 设置办理人所在组织
        examWork.setOrgCode(user.getBelongOrgCode());
        examWork.setOrgName(user.getBelongOrgName());

        // 设置部门全路径
        examWork.setDisplayName(user.getBelongOrgName());

        // 设置创建年份
        examWork.setCreateYear(DateUtil.getCurrYear());

        // 设置考试标题
        examWork.setTitle(usPendingTask.getTitle());

        // 设置是否推送过统一待办
        examWork.setIsTodoFlag("0");

        // 设置推送短信
        examWork.setIsPostMsg("0");

        // 设置待办类型
        examWork.setWorkType("Z");

        // 设置推送短信最后时间
        examWork.setLastPostTime(nowStr);

        // 设置试卷编码
        examWork.setExamAppCode("lyxf");

        // 设置考试编码
        examWork.setExamCode(usPendingTask.getQuestionBankCode());

        // 设置是否待办已办
        examWork.setSign("0");
        ExamWork examWork1 = iExamWorkService.insert(examWork);*/
        examWork.setSign("0");
        ExamWork examWork1 = iExamWorkService.update(examWork);
        // 输出设置后的对象信息
        System.out.println("Exam Work: " + examWork);
        // 推发统一待办
        List<ExamWork> todoWorkList=new ArrayList<>();
        if(null!=examWork1){
            todoWorkList.add(examWork1);
        }
        // 异步核销统一待办
        syncTool.asyncDealWith(todoWorkList);
        log.warn("推送待办完成，总计推送" + todoWorkList.size() + "条");
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 查询当日所有待办信息
     * 用于核销待办
     *
     * @param currentDay
     * @return
     */
    @Override
    public List<UsPendingTask> cancleTask(String currentDay) {
        return usPendingTaskRepository.cancleTask(currentDay);
    }

    /**
     * 根据pmInsId查询待办信息
     *
     * @param pmInsId
     * @return
     */
    @Override
    public UsPendingTask findPendingTaskByPmInsId(String pmInsId) {
        return usPendingTaskRepository.findPendingTaskByPmInsId(pmInsId);
    }

    /**
     * 用于调整答题状态
     *
     * @param invitaitonId
     * @param status
     * @return
     */
    @Override
    public List<UsPendingTask> findPendingTaskByStatue(String invitaitonId, String status) {
        return usPendingTaskRepository.findPendingTaskByStatue(invitaitonId,status);
    }

    /**
     * 根据邀请信息查询
     *
     * @param invitaitonId
     * @return
     */
    @Override
    public List<UsPendingTask> findPendingTaskByInvitaitonId(String invitaitonId) {
        return usPendingTaskRepository.findPendingTaskByInvitaitonId(invitaitonId);
    }

    /**
     * 查询被邀请人列表信息
     *
     * @param currentDay
     * @param recUserName
     * @return
     */
    @Override
    public List<UsPendingTask> InvitedTask(String currentDay, String recUserName) {
        return usPendingTaskRepository.InvitedTask(currentDay,recUserName);
    }

    @Override
    public UsPendingTask getUsPendingTaskByPmInsId(String pmInsId) {
        return usPendingTaskRepository.findPendingTaskByPmInsId(pmInsId);
    }
}