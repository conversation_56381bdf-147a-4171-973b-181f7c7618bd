package com.simbest.boot.exam.briefDistribution.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.briefDistribution.model.OrganizationValue;
import com.simbest.boot.exam.briefDistribution.model.OrganizationValueDTO;
import com.simbest.boot.exam.briefDistribution.repository.OrganizationValueRepository;
import com.simbest.boot.exam.briefDistribution.service.OrganizationValueService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.util.json.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class OrganizationValueServiceImpl extends LogicService<OrganizationValue, String> implements OrganizationValueService {

    private final OrganizationValueRepository repository;

    public OrganizationValueServiceImpl(OrganizationValueRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    ISysFileService sysFileService;

    @Override
    public List<OrganizationValue> findList() {
        List<OrganizationValue> organizationValueList = repository.findOrganizationValuesByGroupTypeAndStatus(Constants.JBPFGROUPTYPE, "1");
        return organizationValueList;
    }

    @Override
    public void importExcel(HttpServletRequest request, HttpServletResponse response) {
        JsonResponse jsonResponse = JsonResponse.defaultSuccessResponse();
        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        boolean flag = true;
        List<OrganizationValue> organizationValueList = new ArrayList<>();
        try {
            out = response.getWriter();
            MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
            //获取所有车的信息
            for (MultipartFile multipartFile : multipartFiles.values()) {
                UploadFileResponse uploadFileResponse = sysFileService.importExcel(multipartFile, null, null, null, OrganizationValueDTO.class, "人员信息");
                List<OrganizationValueDTO> organizationValueDTOS = uploadFileResponse.getListData();
//                List<OrganizationValueDTO> organizationValueDTOS = ExcelUtils.importExcel(multipartFile, 0, 0, OrganizationValueDTO.class);
                for (OrganizationValueDTO organizationValueDTO : organizationValueDTOS) {
                    OrganizationValue organizationValue = new OrganizationValue();
                    organizationValue.setGroupType("jianbaoPF");
                    organizationValue.setGroupName("简报派发推送人员");
                    organizationValue.setUserName(organizationValueDTO.getUserName());
                    organizationValue.setUserTrueName(organizationValueDTO.getUserTrueName());
                    organizationValue.setPhone(organizationValueDTO.getPhone());
                    organizationValue.setStatus("1");
                    organizationValueList.add(organizationValue);
                }
                if (organizationValueList.size()==organizationValueDTOS.size()){
                    this.saveAll(organizationValueList);
                }
            }
        } catch (Exception e) {
            log.error("--->>> importExcel 组织人员信息导入异常");
        }
        String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
        out.println(result);
        out.close();
    }


}
