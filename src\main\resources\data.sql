INSERT INTO SYS_ORG_LEVEL (ID, ORG_LEVEL,ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (1, '默认级别',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;
INSERT INTO SYS_ORG_LEVEL (ID, ORG_LEVEL,ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (2, '一级',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;
INSERT INTO SYS_ORG_LEVEL (ID, ORG_LEVEL,ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (3, '二级',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;
INSERT INTO SYS_ORG_LEVEL (ID, ORG_LEVEL,ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (4, '三级',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;
INSERT INTO SYS_ORG_LEVEL (ID, ORG_LEVEL,ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (5, '四级',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;

INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (1, '默认类型',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (2, '公司管理层',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (3, '其他公司领导',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (4, '省公司部门',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));; 
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (5, '省公司一级中心',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (6, '省公司二级中心/办公室/临时机构',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (7, '地市分公司管理层',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (8, '地市分公司资深经理',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (9, '地市分公司总经理助理',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (10, '地市分公司部门',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (11, '县/市区分公司领导',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (12, '县/市区分公司部门/中心',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ORG_STYLE (ID, ORG_STYLE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (13, '县/市区分公司营业厅/班组',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  

INSERT INTO SYS_ROLE_TYPE (ID, ROLE_TYPE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (1, '默认职务',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE_TYPE (ID, ROLE_TYPE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (2, '管理职务',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));; 
INSERT INTO SYS_ROLE_TYPE (ID, ROLE_TYPE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (3, '技术职务',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE_TYPE (ID, ROLE_TYPE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (4, '党内职务',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE_TYPE (ID, ROLE_TYPE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (5, '团内职务',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));; 
INSERT INTO SYS_ROLE_TYPE (ID, ROLE_TYPE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (6, '工会',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  

INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (1,'总经理',2,2,2,21,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (2,'副总经理',2,2,2,22,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (3,'党组书记',4,2,2,21,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (4,'党组成员',4,2,2,22,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (5,'纪检组长',4,2,2,22,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (6,'工会主席',6,2,2,22,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (7,'总经理助理',2,3,2,23,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (8,'副总会计师',2,3,2,23,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (9,'副总工程师',2,3,2,23,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (10,'总经理',2,4,3,31,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (11,'副总经理',2,4,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (12,'主任',2,4,3,31,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (13,'副主任',2,4,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (14,'资深经理',2,4,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (15,'三级经理',2,4,4,41,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (16,'三级副经理',2,4,4,42,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (17,'一般员工',2,4,5,52,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (18,'党组秘书',4,4,3,31,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (19,'巡视组组长',4,4,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (20,'机关党委副书记',4,4,3,31,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (21,'工会副主席',6,4,3,31,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (22,'工会办公室主任',6,4,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (23,'机关工会主席',6,4,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (24,'团工委书记',5,4,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (25,'机关团委书记',5,4,4,41,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (26,'二级专家',3,4,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (27,'三级专家',3,4,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (28,'高级主管',3,4,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (29,'主管',3,4,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (30,'总经理',2,5,3,31,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (31,'副总经理',2,5,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (32,'资深经理',2,5,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (33,'三级经理',2,5,4,41,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (34,'三级副经理',2,5,4,42,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (35,'班组长',2,5,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (36,'副班组长',2,5,5,52,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (37,'一般员工',2,5,5,52,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (38,'党总支书记',4,5,3,31,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (39,'二级专家',3,5,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (40,'三级专家',3,5,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (41,'高级主管',3,5,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (42,'主管',3,5,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (43,'主任',2,6,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (44,'一般员工',2,6,5,52,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (45,'二级专家',3,6,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (46,'三级专家',3,6,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (47,'高级主管',3,6,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (48,'主管',3,6,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (49,'总经理',2,7,3,31,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (50,'副总经理',2,7,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (51,'党委书记',4,7,3,31,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (52,'纪检书记',4,7,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (53,'工会主席',6,7,3,32,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (54,'资深经理',2,8,3,33,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (55,'总经理助理',2,9,3,34,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (56,'主任',2,10,4,41,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (57,'经理',2,10,4,41,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (58,'资深经理',2,10,4,42,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (59,'副主任',2,10,4,42,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (60,'副经理',2,10,4,42,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (61,'经理助理',2,10,4,43,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (62,'班组长',2,10,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (63,'副班组长',2,10,5,52,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (64,'管理员',2,10,6,61,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (65,'一般员工',2,10,7,71,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (66,'纪委副书记',4,10,4,41,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (67,'党支部书记',4,10,4,41,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (68,'工会副主席',6,10,4,41,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (69,'经理',2,11,4,41,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (70,'副经理',2,11,4,42,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (71,'经理助理',2,11,4,43,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (72,'党支部书记',4,11,4,41,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (73,'主任',2,12,5,51,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (74,'副主任',2,12,5,52,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (75,'管理员',2,12,6,61,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (76,'一般员工',2,12,7,71,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (77,'主任',2,13,6,61,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (78,'副主任',2,13,6,62,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (79,'班组长',2,13,6,61,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (80,'副班组长',2,13,6,62,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_POSITION (ID, POSITION_NAME,ROLE_TYPE_ID,ORG_STYLE_ID,CORP_LEVEL,INNER_LEVEL, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (81,'一般员工',2,13,7,71,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  

INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (1,1,'超级管理员','ROLE_SUPER',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (2,1,'应用管理员','ROLE_ADMIN',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (3,1,'安全管理员','ROLE_SAFE',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (4,1,'普通用户','ROLE_USER',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (5,1,'匿名用户','ROLE_ANONYMOUS',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (101,2,'公司总经理','ROLE_GSZJL_2',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (102,2,'公司副总经理','ROLE_GSFZJL_2',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (103,3,'部门经理','ROLE_BMJL_3',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (104,4,'部门经理','ROLE_BMJL_4',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (105,4,'部门副经理','ROLE_BMFJL_4',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (106,4,'部门三级经理','ROLE_BMSJJL_4',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (107,4,'一般员工','ROLE_YBYG_4',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (108,5,'中心经理','ROLE_ZXJL_5',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (109,5,'中心副经理','ROLE_ZXFJL_5',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (110,5,'中心三级经理','ROLE_ZXSJJL_5',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (111,5,'中心班组长','ROLE_ZXBZZ_5',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (112,5,'一般员工','ROLE_YBYG_5',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (113,6,'中心经理','ROLE_ZXJL_6',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (114,6,'中心副经理','ROLE_ZXFJL_6',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (115,6,'中心三级经理','ROLE_ZXSJJL_6',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (116,6,'一般员工','ROLE_YBYG_6',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (117,7,'公司总经理','ROLE_GSZJL_7',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (118,7,'公司副总经理','ROLE_GSFZJL_7',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (119,8,'公司副总经理','ROLE_GSFZJL_8',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (120,9,'公司副总经理','ROLE_GSFZJL_9',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (121,10,'部门经理','ROLE_BMJL_10',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (122,10,'部门副经理','ROLE_BMFJL_10',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (123,10,'部门三级经理','ROLE_BMSJJL_10',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (124,10,'一般员工','ROLE_YBYG_10',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (125,11,'部门经理','ROLE_BMJL_11',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (126,11,'部门副经理','ROLE_BMFJL_11',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (127,12,'中心主任','ROLE_ZXZR_12',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (128,12,'中心副主任','ROLE_ZXFZR_12',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (129,12,'一般员工','ROLE_YBYG_12',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (130,13,'组长','ROLE_ZZ_13',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (131,13,'副组长','ROLE_FZZ_13',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_ROLE (ID, ORG_STYLE_ID,ROLE_NAME,ROLE_CODE, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (132,13,'一般员工','ROLE_YBYG_13',1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  

INSERT INTO SYS_ORG_INFO_FULL (ID, ORG_NAME,ORG_CODE,DISPLAY_NAME,ORG_LEVEL_ID,STATUS,ORG_STYLE_ID,PARENT_ORG_ID, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME) VALUES (1, '河南移动', '0000','中国移动通信集团河南有限公司',1,0,1,null,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));


INSERT INTO SYS_USER_INFO_FULL (ID, USERNAME, PASSWORD,TRUENAME,NICKNAME, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME, ORG_CODE,ORG_NAME,STATUS,MOBILE) VALUES (1, 'hadmin', '$2a$10$Pi1N9b4x/5Yh6UOBvl7oleS6JlncmVxrmyRxNCQkY8vNTPuN5sUG.', '超级管理员','管总', 1, 1,1,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),'0000','河南移动',0,'***********');

INSERT INTO SYS_USER_ROLE (ID, USERNAME, ROLE_ID, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME)  VALUES (1, 'hadmin', 1,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_USER_ROLE (ID, USERNAME, ROLE_ID, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME)  VALUES (2, 'hadmin', 2,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_USER_ROLE (ID, USERNAME, ROLE_ID, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME)  VALUES (3, 'hadmin', 3,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  
INSERT INTO SYS_USER_ROLE (ID, USERNAME, ROLE_ID, ENABLED, REMOVED, CREATOR, MODIFIER, CREATED_TIME, MODIFIED_TIME)  VALUES (4, 'hadmin', 4,1,0,'hadmin','hadmin',to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'),to_date('2018-02-05 00:00:00','yyyy-mm-dd hh24:mi:ss'));;  

