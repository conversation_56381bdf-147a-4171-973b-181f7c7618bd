 <!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>我的待办</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/static/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/static/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">
        var reloadUrl = '';
        $(function () {
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#secrecyTask",//table列表的id名称，需加#
                    "querycmd": "action/examWork/queryMyTask?source=PC",//table列表的查询命令
                    //"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns": [],//固定在左侧的列
                    "columns": [[//列
                        {title: "标题", field: "title", width: 80, sortable: true, tooltip: true, align: "center"},
                        {
                            title: "创建时间",
                            field: "createdTime",
                            width: 40,
                            sortable: true,
                            tooltip: true,
                            align: "center"
                        },
                        {
                            title: "状态", field: "sign", width: 40, sortable: true, tooltip: true, align: "center",
                            formatter: function (value, row, index) {
                                return "未办";
                            }
                        },
                        {
                            field: "opt", title: "操作", width: 40, rowspan: 1, sortable: true, tooltip: true,
                            align: "center",//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var id = row.id;
                                var username = row.transactorCode;
                                var workType = row.workType;
                                var appHtml = web.appHtml;
                                var url;
                                switch (workType) {
                                    case "A":
                                        url = appHtml.A;
                                        break;
                                    case "B":
                                        url = appHtml.B;
                                        break;
                                    case "C":
                                        url = appHtml.C;
                                        break;
                                    case "D":
                                        url = appHtml.D;
                                        break;
                                    case "E":
                                        url = appHtml.E;
                                        break;
                                    case "F":
                                        url = appHtml.F;
                                        break;
                                    case "G":
                                        url = appHtml.G;
                                        break;
                                    case "H":
                                        url = appHtml.H;
                                        break;
                                    case "W":
                                        url = appHtml.W;
                                        break;
                                    case "I":
                                        url = appHtml.I;
                                        break;
                                    case "J":
                                        url = appHtml.J;
                                        break;
                                    case "K":
                                        url = appHtml.K;
                                        break;
                                    case "L":
                                        url = appHtml.L;
                                        break;
                                    case "M":
                                        url = appHtml.M;
                                        break;
                                    case "Z":
                                        url = appHtml.Z;
                                        break;
                                    case "N":
                                        url = appHtml.N;
                                        break;
                                    case "Q":
                                        url = appHtml.Q;
                                        break;
                                    case "T":
                                        url = appHtml.T;
                                        break;
                                    case "S":
                                        url = appHtml.S;
                                        break;
                                    case "X":
                                        url = appHtml.X;
                                        break;
                                    case "R":
                                        url = appHtml.R;
                                        break;
                                    case "O":
                                        url = appHtml.O;
                                        break;
                                }
                                var actionType = "secrecyTask"; //待办
                                var g = "<a class='detail col_b' index='" + index + "'   username='" + row.transactorCode + "'  id='" + row.id + "'  examAppCode='" + row.examAppCode + "'  examCode='" + row.examCode + "'      path='" + url + "?id=" + id + "&examAppCode=" + row.examAppCode + "&examCode=" + row.examCode + "&username=" + username + "&actionType=" + actionType + "&type=task" + "&titName=" + encodeURI(row.companyName) + "'>【办理】</a>";
                                return g;
                            }
                        }
                    ]]
                }
            };
            loadGrid(pageparam);


            var pageparam2 = {
                "listtable": {
                    "listname": "#briefTask",//table列表的id名称，需加#
                    "querycmd": "action/task/myTaskToDo?source=PC",//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass": "noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns": [],//固定在左侧的列
                    "columns": [[//列
                        {title: "标题", field: "title", width: 40, sortable: true, tooltip: true, align: "center"},
                        {
                            title: "创建时间",
                            field: "createdTime",
                            width: 80,
                            sortable: true,
                            tooltip: true,
                            align: "center"
                        },
                        {
                            title: "状态", field: "sign", width: 40, sortable: true, tooltip: true, align: "center",
                            formatter: function (value, row, index) {
                                return "未办";
                            }
                        },
                        {
                            field: "opt", title: "操作", width: 40, rowspan: 1, sortable: true, tooltip: true,
                            align: "center",//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var id = row.id;
                                var businessId = row.businessId;
                                var username = row.transactorCode;
                                var pmInsType = row.pmInsType;
                                var appHtml = web.appHtml;
                                var url;
                                switch (pmInsType) {
                                    case "P":
                                        url = appHtml.P;
                                        break;
                                }
                                var actionType = "secrecyTask"; //待办
                                var g = "<a class='detail2 col_b' tit='" + row.title + "'  index='" + index + "'   username='" + row.transactorCode + "'  id='" + row.id + "'  examAppCode='" + row.examAppCode + "'  examCode='" + row.examCode + "'      path='" + url + "?id=" + id + "&examAppCode=" + row.examAppCode + "&examCode=" + row.examCode + "&username=" + username + "&actionType=" + actionType + "&titName=" + encodeURI(row.companyName) + "'>【办理】</a>";
                                return g;
                            }
                        }
                    ]]
                }
            };
            loadGrid(pageparam2);
            //办理
            $(document).on("click", "a.detail", function () {
               var path = $(this).attr("path")
               var examCode = $(this).attr("examCode")
               var examAppCode = $(this).attr("examAppCode")
               var id = $(this).attr("id")
               var username = $(this).attr("username")


               console.log(path,'123456');

               if(path.indexOf('html/home/<USER>') !== -1 ){
                   window.open('/exam/html/home/<USER>'+ id +'&examAppCode='+ examAppCode +'&examCode='+ examCode +'&username='+ username +'&actionType=secrecyTask&titName=null&name=detailVal')
               }else{
                   top.dialogP($(this).attr("path"), window.name, '办理', 'detail', true, 'maximized', 'maximized', listLoad);
               }
            });
            $(document).on("click", "a.detail2", function () {
               var path = $(this).attr("path")
               var examCode = $(this).attr("examCode")
               var examAppCode = $(this).attr("examAppCode")
               var id = $(this).attr("id")
               var username = $(this).attr("username")
               var tit = $(this).attr("tit")

               if(path.indexOf('html/home/<USER>') !== -1 ){
                   window.open('/exam/html/home/<USER>'+ id +'&examAppCode='+ examAppCode +'&examCode='+ examCode +'&username='+ username +'&actionType=secrecyTask&titName=null&name=detailVal')
               }else{
                   top.dialogP($(this).attr("path"), window.name, tit, 'detail', true, 'maximized', 'maximized', listLoad);
               }
            });
            setTimeout(function(){
                $('.ks').trigger('click')
            },500)


            $(".myTabs div").on("click", function () {
                var index  = $(this).attr('index')
                $('.ks').removeClass('active')
                $('.jb').removeClass('active')
                if(index == '0'){
                    $('.ks').addClass('active')
                    $('.secrecyTask').show()
                    $('.briefTask').hide()
                    loadGrid(pageparam);
                }
                if(index == '1'){
                    $('.jb').addClass('active')
                    $('.briefTask').show()
                    $('.secrecyTask').hide()
                    loadGrid(pageparam2);
                }
            })
        });

        //刷新页面
        function listLoad() {
            if (window.parent.document.getElementById('detailF').contentWindow.remainTimeT) clearInterval(window.parent.document.getElementById('detailF').contentWindow.remainTimeT);
            $(this).parent().siblings(".window-shadow").remove();
            $(this).parent().siblings(".window-mask").remove();
            $(this).parent().remove();
            $("#secrecyTask").datagrid("reload");
            $("#briefTask").datagrid("reload");
        }

        // function open(){
        //     window.open('/exam/html/home/<USER>')
        // }

        //跳转答题
        $(document).on("click", "a.jumpDS", function () {
            window.open('/exam/html/home/<USER>')
        });

        // $(document).on("click", ".ks", function () {
        //     console.log(123456789);
        // });
    </script>
</head>
<body class="body_page">
<!--searchform-->


<div class="myTabs">
    <div class="ks" index="0">考试待办</div>
    <div class="jb" index="1">简报待办</div>
</div>


<!--table-->
<div class="secrecyTask hide">
    <table id="secrecyTask"></table>
</div>

<div class="briefTask hide">
    <table id="briefTask"></table>
</div>
<a class="jumpDS btn hide">测试答题</a>
</body>
</html>

<style>
    .myTabs{
        display: flex;
        margin-bottom: 20px;
    }
    .myTabs div{
        width: 120px;
        height: 32px;
        border: 1px solid #95bfe6;
        font-size: 16px;
        text-align: center;
        cursor: pointer;
        line-height: 32px;
        
    }
    .myTabs div.active{
        color: #333;
        font-weight: 700;
        color: #356885;
        border: 3px solid 95bfe6;
    }
</style>
