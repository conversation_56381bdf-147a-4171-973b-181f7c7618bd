package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.exam.examOnline.model.ExamInfoSave;
import com.simbest.boot.exam.examOnline.service.IExamInfoSaveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/action/examInfoSave")
public class ExamInfoSaveController extends LogicController<ExamInfoSave, String> {

    private final IExamInfoSaveService service;

    public ExamInfoSaveController(IExamInfoSaveService service) {
        super(service);
        this.service = service;
    }

}
