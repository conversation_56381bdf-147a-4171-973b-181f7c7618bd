/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service.impl;
/**
 * Created by KZH on 2019/10/8 15:13.
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.exam.examOnline.dto.ExamAttributeDto;
import com.simbest.boot.exam.examOnline.model.*;
import com.simbest.boot.exam.examOnline.repository.ExamAttributeRepository;
import com.simbest.boot.exam.examOnline.service.*;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.OperateLogTool;
import com.simbest.boot.exam.wfquey.service.IQueryDictValueService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.IntFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:13
 * @desc 试卷属性
 **/
@Slf4j
@Service
public class ExamAttributeServiceImpl extends LogicService<ExamAttribute, String> implements IExamAttributeService {

    private ExamAttributeRepository examAttributeRepository;

    @Autowired
    private IExamQuestionService iExamQuestionService;

    @Autowired
    private IExamQuestionAnswerService iExamQuestionAnswerService;

    @Autowired
    private IExamUserService iExamUserService;

    @Autowired
    private IExamInformationService iExamInformationService;
    @Autowired
    private IExamQuestionUserService iExamQuestionUserService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private IExamRangeService rangeService;

    @Autowired
    private IExamInfoService iExamInfoService;

    @Autowired
    private IExamAttributeQuestionService iExamAttributeQuestionService;

    @Autowired
    private IExamRangeGroupService iExamRangeGroupService;
    @Autowired
    @Lazy
    private IExamWorkService examWorkService;
    @Autowired
    private IQueryDictValueService queryDictValueService;

    @Autowired
    ISysDictValueService sysDictValueService;

    @Autowired
    public ExamAttributeServiceImpl(ExamAttributeRepository repository) {
        super(repository);
        this.examAttributeRepository = repository;
    }

    private static final String MJSF_NZS = "2023mjsf_nsz_sj";

    /**
     * 获取试卷模板
     *
     * @param condition
     * @return
     */
    @Override
    public JsonResponse constructExamLayout(String currentUserCode, String source, Map<String, Object> condition) {
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/constructExamLayout";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examAttribute", "constructExamLayout", operateLog);
        if (returnObj != null) {
            return returnObj;
        }
        String examAppCode = String.valueOf(condition.get("examAppCode"));
        Assert.notNull(examAppCode, "appCode不可为空");
        //获取到试卷属性
        ExamAttribute allByExamAppCode = examAttributeRepository.findAllByExamAppCode(examAppCode);
        Assert.notNull(allByExamAppCode, "未查询到系统相关试卷");
        //获取到题库编码
        String questionBankCode = allByExamAppCode.getQuestionBankCode();
        Assert.notNull(questionBankCode, "题库编码不可为空");
        // 答题限制
        String userName = SecurityUtils.getCurrentUserName();
        ExamInfo info = iExamInfoService.findExamInfo1(userName, examAppCode);
        if (Objects.nonNull(info) && !info.isHasDrop()) {
            if (!this.checkExamNumber(info.getExamNumber(), examAppCode)) {
                iExamInfoService.submitExam1(userName, Constants.SOURCE_P, info);
            }
        }
        //根据试卷的出题方式出试卷的试题
        String topicStyle = allByExamAppCode.getTopicStyle();
        //出题方式为题库全题目  为兼容原来的考试
        if (topicStyle == null || topicStyle.equals(Constants.TOPIC_STYLE_ORIGINAL)) {
            List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);//获取到题目
            return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, examQuestionList));
        }
        //出题方式为随机出题
         else if (topicStyle.equals(Constants.TOPIC_STYLE_RANDOM)) {
            String currentUserName = SecurityUtils.getCurrentUserName();
            if (StrUtil.isEmpty(currentUserCode)) {
                currentUserCode = currentUserName;
            }
            //查询当前人是否已参与此考试 ，因为是随机出题方式，所以在点击已办中查看时需要获取试卷的题目信息
            Specification<ExamRangeGroup> spec = Specifications.<ExamRangeGroup>and()
                    .eq("examAppCode", examAppCode)
                    .build();
            ExamInfo examInfo = null;
            ExamRangeGroup one = iExamRangeGroupService.findOne(spec);
            if (one != null) {
                String examCode = one.getExamCode();
                if (examCode != null) {
                    examInfo = iExamInfoService.findExamInfo(currentUserCode, examCode, examAppCode);
                }
            }

            //如果该用户已答过此考试的随机题，则通过答题记录表查询属于该用户的答题的所属题目
            if (examInfo != null) {
                String examRecord = examInfo.getExamRecord(); //此人的答题的题目编号
                Assert.notNull(examRecord, "此人交白卷");
                String[] split = examRecord.split(",");//截取题目编号
                ArrayList<ExamQuestion> newExamQuestions = new ArrayList<>();
                //根据题目编码查询对应的信息
                for (String s : split) {
                    ExamQuestion question = iExamQuestionService.findAllByQuestionCode(s);
                    newExamQuestions.add(question);
                }
                return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, newExamQuestions));
            }

            // 随机出题
            //出题方式为随机出题,占比都要按照小数填写
            String randomNumber = allByExamAppCode.getTopicNumber();//获取随机题目数量
            int number1 = Integer.parseInt(randomNumber);
            //获取到题目该题库所有题目
            List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);
            //用于存储随机出来的题目信息
            List<ExamQuestion> newExamQuestions = new ArrayList<>();
            //该题库题目数量
            int size = examQuestionList.size();
            String message = "题库题目数少于" + number1 + "题";
            Assert.isTrue(size >= number1, message);
            if (size == number1) {
                List<ExamQuestion> examQuestionList2 = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);//获取到题目
                return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, examQuestionList2));
            }

            // 试卷出题比例合法判断
            Assert.notNull(allByExamAppCode.getSingle(), "单选题出题比例为null");
            Assert.notNull(allByExamAppCode.getMore(), "多选题出题比例为null");
            Assert.notNull(allByExamAppCode.getFilling(), "填空题出题比例为null");
            Assert.notNull(allByExamAppCode.getJudge(), "判断题出题比例为null");
            Assert.notNull(allByExamAppCode.getShortAnswer(), "简答题出题比例为null");
            int sum = Integer.parseInt(allByExamAppCode.getSingle())
                    + Integer.parseInt(allByExamAppCode.getMore())
                    + Integer.parseInt(allByExamAppCode.getFilling())
                    + Integer.parseInt(allByExamAppCode.getJudge())
                    + Integer.parseInt(allByExamAppCode.getShortAnswer());
            if (number1 != sum) {
                throw new IllegalStateException("出题总数和出题比例不相等！");
            }

            // 2023明纪守法内审组考试 采用新的随机出题方法
            if (Objects.equals(examAppCode, MJSF_NZS)) {
                List<ExamQuestion> list = this.randomProduceExamQuestion2023NSZ(examQuestionList);
                return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, list));
            }

            List<ExamQuestion> examQuestionsSingle = this.randomProduceExamQuestion(examQuestionList, Constants.SINGLE, Integer.parseInt(allByExamAppCode.getSingle()));
            List<ExamQuestion> examQuestionsMore = this.randomProduceExamQuestion(examQuestionList, Constants.MORE, Integer.parseInt(allByExamAppCode.getMore()));
            List<ExamQuestion> examQuestionsFilling = this.randomProduceExamQuestion(examQuestionList, Constants.FILLING, Integer.parseInt(allByExamAppCode.getFilling()));
            List<ExamQuestion> examQuestionsJudge = this.randomProduceExamQuestion(examQuestionList, Constants.JUDGE, Integer.parseInt(allByExamAppCode.getJudge()));
            List<ExamQuestion> examQuestionsShortAnswer = this.randomProduceExamQuestion(examQuestionList, Constants.SHORTANSWER, Integer.parseInt(allByExamAppCode.getShortAnswer()));
            for (List<ExamQuestion> list : Arrays.asList(examQuestionsSingle, examQuestionsMore, examQuestionsFilling, examQuestionsJudge, examQuestionsShortAnswer)) {
                newExamQuestions.addAll(list);
            }


        }
         //随机出题特殊版本， 必须将每个分组中都选中一个题目
        else if (topicStyle.equals(Constants.TOPIC_STYLE_RANDOM_SPECIAL)) {
            //获取基础配置信息
            Assert.notNull(allByExamAppCode.getTopicNumber() , "题目总数不能为空！");
            Integer singleNumber = StrUtil.isNotEmpty(allByExamAppCode.getSingle()) ? Integer.parseInt(allByExamAppCode.getSingle()) : 0;
            Integer moreNumber = StrUtil.isNotEmpty(allByExamAppCode.getMore()) ? Integer.parseInt(allByExamAppCode.getMore()) : 0;
            Integer judgeNumber = StrUtil.isNotEmpty(allByExamAppCode.getJudge()) ? Integer.parseInt(allByExamAppCode.getJudge()) : 0;
            List<ExamQuestion>  newExamQuestions = null;
            try {
                newExamQuestions =  this.randomGroup(allByExamAppCode.getQuestionBankCode(), singleNumber, moreNumber, judgeNumber);
            } catch (Exception e ) {
                Exceptions.printException(e);
                return JsonResponse.fail("获取试卷失败");
            }
            if (newExamQuestions == null) {
                return JsonResponse.fail("获取试卷失败");
            } else {
                return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, newExamQuestions));
            }
        }
        //随机出题特殊版本， 必须将每个分组都按照特定配置进行随机出题
        else if (topicStyle.equals(Constants.TOPIC_STYLE_RANDOM_GROUP_SPECIAL)) {
            Assert.notNull(allByExamAppCode.getSpecialRuleConfig() , "定制化规则不能为空！");
            try {
                Map<String, Integer> groupRules = JacksonUtils.json2obj(allByExamAppCode.getSpecialRuleConfig(), Map.class);

                Integer allSingleNum = StrUtil.isNotEmpty(allByExamAppCode.getSingle()) ? Integer.parseInt(allByExamAppCode.getSingle()) : 0;
                Integer allMoreNum = StrUtil.isNotEmpty(allByExamAppCode.getMore()) ? Integer.parseInt(allByExamAppCode.getMore()) : 0;
                Integer allJudgeNum = StrUtil.isNotEmpty(allByExamAppCode.getJudge()) ? Integer.parseInt(allByExamAppCode.getJudge()) : 0;

                Integer singleNum = 0;
                Integer moreNum = 0;
                Integer judgeNum = 0;
                // 查询题库中的题目信息
                List<Map<String, Object>> questionCodeInfos = iExamQuestionService.findGroupByQuestionBankCode(questionBankCode);
                if (CollectionUtil.isNotEmpty(questionCodeInfos)) {
                    // 将所有题目编号信息按分组分类
                    Map<String, List<String>> questionCodeInfoMap = new HashMap<>();
                    for (Map<String, Object> questionCodeInfo : questionCodeInfos) {
                        String groupName = MapUtil.getStr(questionCodeInfo, "QUESTION_GROUP_NAME");
                        String codeStr = MapUtil.getStr(questionCodeInfo, "CODE_STR");

                        List<String> codeList = questionCodeInfoMap.computeIfAbsent(groupName, k -> new ArrayList<>());
                        codeList.add(codeStr);
                    }
                    List<String> resultQuestionCodes = Lists.newArrayList();
                    // 先按分组规则抽取题目，并确保每个分组的题目数量正确
                    for (Map.Entry<String, Integer> groupRule : groupRules.entrySet()) {
                        String groupName = groupRule.getKey();
                        Integer requiredCount = groupRule.getValue();

                        List<String> groupQuestions = questionCodeInfoMap.get(groupName);
                        if (groupQuestions != null && !groupQuestions.isEmpty()) {
                            // 从该分组中随机抽取指定数量的题目，确保抽取的题目符合要求
                            Integer flag = 0;
                            while (flag < requiredCount && groupQuestions.size() > 0) {
                                int randomIndex = RandomUtil.randomInt(0, groupQuestions.size());
                                String selectedQuestion = groupQuestions.remove(randomIndex);
                                if (selectedQuestion.startsWith("single#") && singleNum < allSingleNum) {
                                    resultQuestionCodes.add(selectedQuestion.substring(7));
                                    singleNum++;
                                    flag++;
                                } else if (selectedQuestion.startsWith("more#") && moreNum < allMoreNum) {
                                    resultQuestionCodes.add(selectedQuestion.substring(5)); // 去掉前缀"more#"
                                    moreNum++;
                                    flag++;
                                } else if (selectedQuestion.startsWith("judge#") && judgeNum < allJudgeNum) {
                                    resultQuestionCodes.add(selectedQuestion.substring(6)); // 去掉前缀"judge#"
                                    judgeNum++;
                                    flag++;
                                }
                            }
                        }
                    }

                    if (CollectionUtil.isNotEmpty(resultQuestionCodes)) {
                        List<ExamQuestion>questionList = iExamQuestionService.findAllByQuestionCodes(resultQuestionCodes);
                        if (CollectionUtil.isNotEmpty(questionList)) {
                            return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, questionList));
                        }
                    }

                }
            } catch (Exception e) {
                Exceptions.printException(e);
            }
            return JsonResponse.fail(-1 , "获取试卷试题失败！");
        }
        //固定出题方式
        else if (topicStyle.equals(Constants.TOPIC_STYLE_FIXED)) {
            //获取对应试卷的固定题目信息
            List<ExamAttributeQuestion> examAttributeQuestions = iExamAttributeQuestionService.findAllExamAttributeQuestion(examAppCode);
            ArrayList<ExamQuestion> newExamQuestions = new ArrayList<>();
            //遍历查询对应题目
            for (ExamAttributeQuestion examAttributeQuestion : examAttributeQuestions) {
                ExamQuestion question = iExamQuestionService.findAllByQuestionCode(examAttributeQuestion.getQuestionCode());
                newExamQuestions.add(question);
            }
            return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, newExamQuestions));
        }
        return JsonResponse.fail("获取试卷失败");
    }

    /**
     * 随机出题
     * <br/> params [list, type, sum]
     *
     * @param list 题库
     * @param type 类型
     * @param sum  数量
     * @return {@link List< ExamQuestion>}
     * <AUTHOR>
     * @since 2023/8/4 11:30
     */
    private List<ExamQuestion> randomProduceExamQuestion(List<ExamQuestion> list, String type, Integer sum) {
        list = list.stream().filter(v -> Objects.equals(type, v.getQuestionType())).collect(Collectors.toList());
        // 打乱list
        Collections.shuffle(list);
        return list.stream().limit(sum).collect(Collectors.toList());
    }

    /**
     * 2023明纪守法内审组考试 采用特定随机出题方式
     */
    private List<ExamQuestion> randomProduceExamQuestion2023NSZ(List<ExamQuestion> list) {
        // 题目分40组 ，从每组随机出一题
        List<ExamQuestion> list1 = new ArrayList<>();
        int step = (int) Math.ceil(list.size() / 40.0);
        for (int i = 0; i < 40; i++) {
            List<ExamQuestion> sub = CollectionUtil.sub(list, i * step, (i + 1) * step);
            List<ExamQuestion> list2 = this.randomProduceExamQuestion(sub, Constants.SINGLE, 1);
            list1.addAll(list2);
        }
        return list1;
    }

    @Override
    public JsonResponse constructWindowsExamLayout(Map<String, Object> condition) {
        String examAppCode = String.valueOf(condition.get("examAppCode"));
        Assert.notNull(examAppCode, "appCode不可为空");

        ExamAttribute allByExamAppCode = examAttributeRepository.findAllByExamAppCode(examAppCode);//获取到试卷属性
        Assert.notNull(allByExamAppCode, "未查询到系统相关试卷");

        String questionBankCode = allByExamAppCode.getQuestionBankCode();//获取到题库编码
        Assert.notNull(questionBankCode, "题库编码不可为空");

//        List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCodeRandom("A-008","35","single");//获取到题目
//        List<ExamQuestion> examQuestionList2 = iExamQuestionService.findAllByQuestionBankCodeRandom("A-009","10","more");//获取到题目
        List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCodeRandom(allByExamAppCode.getQuestionBankCode(), allByExamAppCode.getTopicSum(), "single");//获取到题目
        List<ExamQuestion> examQuestionList2 = iExamQuestionService.findAllByQuestionBankCodeRandom(allByExamAppCode.getQuestionBankCode(), allByExamAppCode.getTopicSum(), "more");//获取到题目
//        List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCodeRandom("A-006","1","single");//获取到题目
//        List<ExamQuestion> examQuestionList2 = iExamQuestionService.findAllByQuestionBankCodeRandom("A-007","1","more");//获取到题目


        examQuestionList.addAll(examQuestionList2);

        return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, examQuestionList));
    }

    /**
     * 获取试卷模板和答案
     *
     * @param currentUserCode
     * @param source
     * @param condition
     * @return
     */
    @Override
    public JsonResponse constructExamLayoutAndAnswer(String currentUserCode, String source, Map<String, Object> condition) {
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/constructExamLayoutAndAnswer";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examAttribute", "constructExamLayoutAndAnswer", operateLog);
        if (returnObj != null) {
            return returnObj;
        }
        String examAppCode = String.valueOf(condition.get("examAppCode"));
        Assert.notNull(examAppCode, "appCode不可为空");
        //获取到试卷属性
        ExamAttribute allByExamAppCode = examAttributeRepository.findAllByExamAppCode(examAppCode);
        Assert.notNull(allByExamAppCode, "未查询到系统相关试卷");
        //获取到题库编码
        String questionBankCode = allByExamAppCode.getQuestionBankCode();
        Assert.notNull(questionBankCode, "题库编码不可为空");
        //根据试卷的出题方式出试卷的试题
        String topicStyle = allByExamAppCode.getTopicStyle();
        if (topicStyle == null || topicStyle.equals(Constants.TOPIC_STYLE_ORIGINAL)) {
            //出题方式为题库全题目  为兼容原来的考试
            List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);//获取到题目
            return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, examQuestionList));
        }
        //出题方式为随机出题
        if (topicStyle.equals(Constants.TOPIC_STYLE_RANDOM)) {
            String currentUserName = SecurityUtils.getCurrentUserName();
            if (StrUtil.isEmpty(currentUserCode)) {
                currentUserCode = currentUserName;
            }
            //查询当前人是否已参与此考试 ，因为是随机出题方式，所以在点击已办中查看时需要获取试卷的题目信息
            Specification<ExamRangeGroup> spec = Specifications.<ExamRangeGroup>and()
                    .eq("examAppCode", examAppCode)
                    .build();
            ExamInfo examInfo = null;
            ExamRangeGroup one = iExamRangeGroupService.findOne(spec);
            if (one != null) {
                String examCode = one.getExamCode();
                if (examCode != null) {
                    examInfo = iExamInfoService.findExamInfo(currentUserCode, examCode, examAppCode);
                }
            }

            //如果该用户已答过此考试的随机题，则通过答题记录表查询属于该用户的答题的所属题目
            if (examInfo != null) {
                String examRecord = examInfo.getExamRecord(); //此人的答题的题目编号
                Assert.notNull(examRecord, "此人交白卷");
                String[] split = examRecord.split(",");//截取题目编号
                ArrayList<ExamQuestion> newExamQuestions = new ArrayList<>();
                //根据题目编码查询对应的信息
                for (String s : split) {
                    ExamQuestion question = iExamQuestionService.findAllByQuestionCode(s);
                    newExamQuestions.add(question);
                }
                return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, newExamQuestions));
            }

            //出题方式为随机出题,占比都要按照小数填写
            String randomNumber = allByExamAppCode.getTopicNumber();//获取随机题目数量
            int number1 = Integer.parseInt(randomNumber);
            //获取到题目该题库所有题目
            List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);
            //用于存储随机出来的题目信息
            List<ExamQuestion> newExamQuestions = new ArrayList<>();
            //该题库题目数量
            int size = examQuestionList.size();
            String message = "题库题目数少于" + number1 + "题";
            Assert.isTrue(size >= number1, message);
            if (size == number1) {
                List<ExamQuestion> examQuestionList2 = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);//获取到题目
                return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, examQuestionList2));
            }

            // 试卷出题比例合法判断
            Assert.notNull(allByExamAppCode.getSingle(), "单选题出题比例为null");
            Assert.notNull(allByExamAppCode.getMore(), "多选题出题比例为null");
            Assert.notNull(allByExamAppCode.getFilling(), "填空题出题比例为null");
            Assert.notNull(allByExamAppCode.getJudge(), "判断题出题比例为null");
            Assert.notNull(allByExamAppCode.getShortAnswer(), "简答题出题比例为null");
            int sum = Integer.parseInt(allByExamAppCode.getSingle())
                    + Integer.parseInt(allByExamAppCode.getMore())
                    + Integer.parseInt(allByExamAppCode.getFilling())
                    + Integer.parseInt(allByExamAppCode.getJudge())
                    + Integer.parseInt(allByExamAppCode.getShortAnswer());
            if (number1 != sum) {
                throw new IllegalStateException("出题总数和出题比例不相等！");
            }

            List<ExamQuestion> examQuestionsSingle = this.randomProduceExamQuestion(examQuestionList, Constants.SINGLE, Integer.parseInt(allByExamAppCode.getSingle()));
            List<ExamQuestion> examQuestionsMore = this.randomProduceExamQuestion(examQuestionList, Constants.MORE, Integer.parseInt(allByExamAppCode.getMore()));
            List<ExamQuestion> examQuestionsFilling = this.randomProduceExamQuestion(examQuestionList, Constants.FILLING, Integer.parseInt(allByExamAppCode.getFilling()));
            List<ExamQuestion> examQuestionsJudge = this.randomProduceExamQuestion(examQuestionList, Constants.JUDGE, Integer.parseInt(allByExamAppCode.getJudge()));
            List<ExamQuestion> examQuestionsShortAnswer = this.randomProduceExamQuestion(examQuestionList, Constants.SHORTANSWER, Integer.parseInt(allByExamAppCode.getShortAnswer()));
            for (List<ExamQuestion> list : Arrays.asList(examQuestionsSingle, examQuestionsMore, examQuestionsFilling, examQuestionsJudge, examQuestionsShortAnswer)) {
                newExamQuestions.addAll(list);
            }

            return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, newExamQuestions));
        }

        //固定出题方式
        if (topicStyle.equals(Constants.TOPIC_STYLE_FIXED)) {
            //获取对应试卷的固定题目信息
            List<ExamAttributeQuestion> examAttributeQuestions = iExamAttributeQuestionService.findAllExamAttributeQuestion(examAppCode);
            ArrayList<ExamQuestion> newExamQuestions = new ArrayList<>();
            //遍历查询对应题目
            for (ExamAttributeQuestion examAttributeQuestion : examAttributeQuestions) {
                ExamQuestion question = iExamQuestionService.findAllByQuestionCode(examAttributeQuestion.getQuestionCode());
                newExamQuestions.add(question);
            }
            return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, newExamQuestions));
        }
        return JsonResponse.fail("获取试卷失败");
    }

    @Override
    public JsonResponse constructPowerBuildingLayout(Map<String, Object> condition) {
        String examAppCode = String.valueOf(condition.get("examAppCode"));
        Assert.notNull(examAppCode, "appCode不可为空");

        ExamAttribute allByExamAppCode = examAttributeRepository.findAllByExamAppCode(examAppCode);//获取到试卷属性
        Assert.notNull(allByExamAppCode, "未查询到系统相关试卷");

        String questionBankCode = allByExamAppCode.getQuestionBankCode();//获取到题库编码
        Assert.notNull(questionBankCode, "题库编码不可为空");

        IUser user = SecurityUtils.getCurrentUser();
        String username = user.getUsername();
        ExamInfo examInfo = iExamInfoService.findExamInfo(username, examAppCode);

        List<ExamQuestion> examQuestionList = null;
        if (examInfo == null) {
            examQuestionList = iExamQuestionService.findAllByQuestionBankCodeAndPowerBuildingExtr(allByExamAppCode.getQuestionBankCode());//获取到题目
        } else {
            String examRecord = examInfo.getExamRecord();
            examQuestionList = iExamQuestionService.findAllByQuestionBankCode(Lists.newArrayList(examRecord.split(",")));
        }

        return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, examQuestionList));

    }

    @Override
    public JsonResponse constructSpecialExamLayout(Map<String, Object> condition) {

        String questionBankCode = MapUtil.getStr(condition, "questionBankCode");
        Assert.notNull(questionBankCode, "questionBankCode不可为空");

        IUser currentUser = SecurityUtils.getCurrentUser();
        List<ExamUser> examUserByQuestionBankCode = iExamUserService.getExamUserByQuestionBankCode(questionBankCode, currentUser.getUsername());

        if (CollUtil.isEmpty(examUserByQuestionBankCode)) {
            // 生成试卷
            List<ExamUser> examUserList = this.generateExamAttribute(questionBankCode, currentUser.getUsername());
            // 填充试卷内题目
            this.fillExamAttributeQuestion(examUserList, questionBankCode);

            examUserByQuestionBankCode = examUserList;
        }

        // 试卷已生成直接返回试卷
        ExamAttribute examAttribute = this.AssociatedExamAttributeQuestionFinishing(examUserByQuestionBankCode, currentUser.getUsername());

        return examAttribute != null ? JsonResponse.success(examAttribute) : JsonResponse.success(true);

    }

    @Override
    public ExamAttribute getExamAttributeByExamAppCode(String examAppCode) {
        return examAttributeRepository.findAllByExamAppCode(examAppCode);
    }

    /**
     * 根据examCode考试编码获取试卷信息
     *
     * @param currentUserCode 当前用户编码
     * @param source          来源
     * @param condition       表单数据
     * @return 返回试卷信息
     */
    @Override
    public ExamAttribute findExamPaper(String currentUserCode, String source, Map<String, Object> condition) {
        String examCode = String.valueOf(condition.get("examCode"));
        Assert.isTrue(StringUtils.isNotEmpty(examCode), "examCode不能为空!");
        /*---------------------------------1、根据考试编码获取到试卷编码---------------------------------------*/
        String examPaperCode = rangeService.findPaperCodeByExamCode(examCode);
        Assert.isTrue(StringUtils.isNotEmpty(examPaperCode), "未获取到对应试卷编码");
        /*---------------------------------2、根据试卷编码获取试卷基本信息及试卷题目信息-----------------------*/
        // 获取到试卷属性
        ExamAttribute examPaper = examAttributeRepository.findAllByExamAppCode(examPaperCode);
        Assert.notNull(examPaper, "未查询到系统相关试卷");
        // 获取到题库编码
        String questionBankCode = examPaper.getQuestionBankCode();
        Assert.notNull(questionBankCode, "题库编码不可为空");
        // 获取试卷题目信息
        List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);//获取到题目
        this.generalExamAttributeQuestionFinishing(examPaper, examQuestionList);
        return examPaper;
    }

    private List<ExamUser> generateExamAttribute(String questionBankCode, String username) {
        // 获取到试卷属性
        List<ExamAttribute> allByExamAppCode = examAttributeRepository.findAllByQuestionBankCode(questionBankCode);
        Assert.notNull(allByExamAppCode, "未查询到系统相关试卷");

        List<ExamUser> examUserList = Lists.newArrayList();

        int i = 0;
        for (ExamAttribute examAttribute : allByExamAppCode) {
            String examAppCode = examAttribute.getExamAppCode();

            ExamUser insert = iExamUserService.insert(ExamUser.builder()
                    .questionBankCode(questionBankCode)
                    .bankCode(examAppCode)
                    .username(username)
                    .isFinish(ApplicationConstants.ZERO)
                    .examinationSort(i)
                    .build());
            examUserList.add(insert);
            i++;

        }

        return examUserList;
    }

    private void fillExamAttributeQuestion(List<ExamUser> examUserList, String questionBankCode) {

        // 获取到题库的全部数据
        List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);

        // 根据题目类型分类整理
        List<ExamQuestion> singleQuestionList = Lists.newArrayList();

        List<ExamQuestion> moreQuestionList = Lists.newArrayList();

        List<ExamQuestion> judgeQuestionList = Lists.newArrayList();

        for (ExamQuestion examQuestion : examQuestionList) {
            String questionType = examQuestion.getQuestionType();
            switch (questionType) {
                case "single":
                    singleQuestionList.add(examQuestion);
                    break;
                case "more":
                    moreQuestionList.add(examQuestion);
                    break;
                case "judge":
                    judgeQuestionList.add(examQuestion);
                    break;
                default:
                    break;
            }
        }


        for (ExamUser examUser : examUserList) {
            String bankCode = examUser.getBankCode();
            // 获取试卷属性
            ExamAttribute examAttribute = this.getExamAttributeByExamAppCode(bankCode);

            int single = Integer.parseInt(examAttribute.getSingle());
            int more = Integer.parseInt(examAttribute.getMore());
            int judge = Integer.parseInt(examAttribute.getJudge());

            List<ExamQuestion> singleRandomList = this.createRandomList(singleQuestionList, single);
            if (CollUtil.isNotEmpty(singleRandomList)) {
                for (ExamQuestion examQuestion : singleRandomList) {
                    String questionCode = examQuestion.getQuestionCode();
                    ExamQuestionUser examQuestionUser = ExamQuestionUser.builder()
                            .bankCode(bankCode)
                            .questionType("single")
                            .questionCode(questionCode)
                            .build();
                    // 关联题目和人员数据
                    iExamQuestionUserService.insert(examQuestionUser);
                }
                // 移除掉已绑定的题目
                singleQuestionList.removeAll(singleRandomList);
            }
            List<ExamQuestion> moreRandomList = this.createRandomList(moreQuestionList, more);
            if (CollUtil.isNotEmpty(moreRandomList)) {
                for (ExamQuestion examQuestion : moreRandomList) {
                    String questionCode = examQuestion.getQuestionCode();
                    ExamQuestionUser examQuestionUser = ExamQuestionUser.builder()
                            .bankCode(bankCode)
                            .questionType("more")
                            .questionCode(questionCode)
                            .build();
                    // 关联题目和人员数据
                    iExamQuestionUserService.insert(examQuestionUser);
                }
                // 移除掉已绑定的题目
                moreQuestionList.removeAll(moreRandomList);
            }

            List<ExamQuestion> judgeRandomList = this.createRandomList(judgeQuestionList, judge);
            if (CollUtil.isNotEmpty(judgeRandomList)) {
                for (ExamQuestion examQuestion : judgeRandomList) {
                    String questionCode = examQuestion.getQuestionCode();
                    ExamQuestionUser examQuestionUser = ExamQuestionUser.builder()
                            .bankCode(bankCode)
                            .questionType("judge")
                            .questionCode(questionCode)
                            .build();
                    // 关联题目和人员数据
                    iExamQuestionUserService.insert(examQuestionUser);
                }
                // 移除掉已绑定的题目
                judgeQuestionList.removeAll(judgeRandomList);
            }


        }

    }

    private ExamAttribute AssociatedExamAttributeQuestionFinishing(List<ExamUser> examUserByQuestionBankCode, String username) {
        ExamUser waitExamUser = null;
        for (ExamUser examUser : examUserByQuestionBankCode) {
            // 获取未完成试卷关联
            if (ApplicationConstants.ZERO == examUser.getIsFinish()) {
                waitExamUser = examUser;
                break;
            }
        }
        if (waitExamUser == null) {
            return null;
        }

        String bankCode = waitExamUser.getBankCode();

        List<String> questionCodeList = Lists.newArrayList();
        List<ExamQuestionUser> examQuestionUserList = iExamQuestionUserService.getExamQuestionUserByBankCode(bankCode, username);

        for (ExamQuestionUser examQuestionUser : examQuestionUserList) {
            questionCodeList.add(examQuestionUser.getQuestionCode());
        }

        // 获取到题目详细数据
        List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionCodeList);
        // 获取到试卷属性
        ExamAttribute allByExamAppCode = examAttributeRepository.findAllByExamAppCode(bankCode);

        return this.generalExamAttributeQuestionFinishing(allByExamAppCode, examQuestionList);
    }


    private ExamAttribute generalExamAttributeQuestionFinishing(ExamAttribute allByExamAppCode, List<ExamQuestion> examQuestionList) {
        String userName = SecurityUtils.getCurrentUserName();
        List<ExamQuestion> singleQuestionList = Lists.newArrayList();

        List<ExamQuestion> moreQuestionList = Lists.newArrayList();

        List<ExamQuestion> judgeQuestionList = Lists.newArrayList();

        List<ExamQuestion> fillingQuestionList = Lists.newArrayList();

        List<ExamQuestion> shortAnswerQuestionList = Lists.newArrayList();

        List<ExamQuestion> indefiniteQuestionList = Lists.newArrayList();

        List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllNoPage(
                Specifications.<ExamQuestionAnswer>and().in("questionCode", examQuestionList.stream().map(ExamQuestion::getQuestionCode).collect(Collectors.toList())).build(), Sort.by(Sort.Direction.ASC, "answerCode")
        );
        examQuestionList.forEach(v -> {
            List<ExamQuestionAnswer> collect = examQuestionAnswerList.stream().filter(v1 -> Objects.equals(v.getQuestionCode(), v1.getQuestionCode())).collect(Collectors.toList());
            v.setAnswerList(collect);
        });

        for (ExamQuestion examQuestion : examQuestionList) {
            String questionType = examQuestion.getQuestionType();

            switch (questionType) {
                case "single":
                    singleQuestionList.add(examQuestion);
                    break;
                case "more":
                    moreQuestionList.add(examQuestion);
                    break;
                case "judge":
                    judgeQuestionList.add(examQuestion);
                    break;
                case "filling":
                    fillingQuestionList.add(examQuestion);
                    break;
                case "shortAnswer":
                    shortAnswerQuestionList.add(examQuestion);
                    break;
                case "indefinite":
                    indefiniteQuestionList.add(examQuestion);
                    break;
                default:
                    break;

            }

        }
        if (examQuestionList.size() > 0) {
            ExamInfo info = iExamInfoService.findExamInfo(userName, allByExamAppCode.getExamAppCode());

            // 特殊的考试配置，答案不回显
            Optional<SysDictValue> first = queryDictValueService.queryByType("EXAM_RESULT_DISPLAY")
                    .stream().filter(v -> Objects.nonNull(v) && allByExamAppCode.getExamAppCode().contains(v.getName())).findFirst();
            if (first.isPresent() && Objects.equals(first.get().getValue(), "1")) {
                info = null;
            }

            // 未考完试去除答案
            if (Objects.isNull(info) || !info.getIsFinishExam()) {
                List<ExamQuestion> l = new ArrayList<>();
                l.addAll(singleQuestionList);
                l.addAll(moreQuestionList);
                l.addAll(judgeQuestionList);
                l.addAll(fillingQuestionList);
                l.addAll(shortAnswerQuestionList);
                l.addAll(indefiniteQuestionList);
                for (ExamQuestion question : l) {
                    if (Objects.equals(Constants.FILLING, question.getQuestionType()) ||
                            Objects.equals(Constants.SHORTANSWER, question.getQuestionType())) {
                        question.setAnswer(null);
                        question.setAnswerList(new ArrayList<>());
                    }
                    if (Objects.equals(Constants.SINGLE, question.getQuestionType()) ||
                            Objects.equals(Constants.MORE, question.getQuestionType()) ||
                            Objects.equals(Constants.JUDGE, question.getQuestionType()) ||
                            Objects.equals(Constants.INDEFINITE, question.getQuestionType())) {
                        for (ExamQuestionAnswer questionAnswer : question.getAnswerList()) {
                            if (!"2024_LDGBZWJDCP2".equals(allByExamAppCode.getExamAppCode())){//【2024年度领导干部自我监督应知应会测评（第二期）】考试回显正确答案
                                questionAnswer.setIsCorrect(false);
                            }
                        }
                    }
                }
            }

            allByExamAppCode.setSingleQuestionList(singleQuestionList);
            allByExamAppCode.setMoreQuestionList(moreQuestionList);
            allByExamAppCode.setJudgeQuestionList(judgeQuestionList);
            allByExamAppCode.setFillingQuestionList(fillingQuestionList);
            allByExamAppCode.setShortAnswerQuestionList(shortAnswerQuestionList);
            allByExamAppCode.setIndefiniteQuestionList(indefiniteQuestionList);
        }
        // 2023nbxc_02 内部巡查 额外特殊操作
        this.dataPlugin02(allByExamAppCode);
        // 2024_jjdc_ks 纪检调查 额外特殊操作 20240130
//        this.dataPlugin03(allByExamAppCode);
        return allByExamAppCode;
    }

    /**
     * 2024_jjdc_ks 2023年分公司全面从严治党监督责任履行及纪检干部作风建设情况测评问卷 额外特殊操作
     * 20240130
     * @param allByExamAppCode 试卷实体
     */
    private void dataPlugin03(ExamAttribute allByExamAppCode) {
        // 为特定顺序的题目加上标题
        allByExamAppCode.getSingleQuestionList().forEach(v -> {
            if (Objects.equals(v.getQuestionOrder(), 1)) {
                v.setType("一、基本信息");
            }
            if (Objects.equals(v.getQuestionOrder(), 4)) {
                v.setType("二、本单位纪委履职情况");
            }
            if (Objects.equals(v.getQuestionOrder(), 19)) {
                v.setType("三、纪检干部作风形象");
            }
        });
        allByExamAppCode.getMoreQuestionList().forEach(v -> {
            if (Objects.equals(v.getQuestionOrder(), 24)) {
                v.setType("四、其他");
            }
        });
    }

    /**
     * 2023nbxc_02 内部巡查 额外特殊操作
     */
    @Deprecated
    private void dataPlugin02(ExamAttribute allByExamAppCode) {
//        List<String> dictValues = queryDictValueService.queryByType("EXAM_NBXC_ZD")
//                .stream().map(SysDictValue::getName).collect(Collectors.toList());
        List<String> dictValues = queryDictValueService.queryByType("2024_MZCP")
                .stream().map(SysDictValue::getName).collect(Collectors.toList());
        if (!dictValues.contains(allByExamAppCode.getExamAppCode())) {
            return;
        }

        List<String> list =new ArrayList<>( Arrays.asList(
                "党的建设-政治建设",
                "党的建设-思想建设",
                "党的建设-干部队伍建设",
                "党的建设-基层组织建设",
                "党的建设-作风建设",
                "党的建设-纪律建设",
                "党的建设-党建工作责任制",
                "科学发展-治企有方",
                "科学发展-兴企有为",
                "科学发展-勇于创新",
                "科学发展-协调发展",
                "科学发展-整体合力",
                "对党忠诚_1",
                "勇于创新_1",
                "治企有方_1",
                "兴企有为_1",
                "清正廉洁_1",

                "对党忠诚_2",
                "勇于创新_2",
                "治企有方_2",
                "兴企有为_2",
                "清正廉洁_2",

                "对党忠诚_3",
                "勇于创新_3",
                "治企有方_3",
                "兴企有为_3",
                "清正廉洁_3",

                "对党忠诚_4",
                "勇于创新_4",
                "治企有方_4",
                "兴企有为_4",
                "清正廉洁_4",

                "对党忠诚_5",
                "勇于创新_5",
                "治企有方_5",
                "兴企有为_5",
                "清正廉洁_5"

        ));
        allByExamAppCode.getSingleQuestionList().forEach(v -> {
            String s = list.stream().filter(v1 -> v.getQuestionName().contains(v1)).findFirst().get();
            v.setType(s);
            v.setTypeHtml(this.dataPlugin1(allByExamAppCode.getExamAppCode(), s, list));
            String s1 = v.getQuestionName().replaceAll(String.format("【%s】", s), "");
            v.setQuestionName(s1);
        });
    }

    /**
     * 2023nbxc_02 内部巡查 额外特殊操作
     */
    @Deprecated
    private String dataPlugin1(String code, String s, List<String> list) {
        String[] split = s.split("-");
        List<String> num = Arrays.asList("一", "二", "三", "四", "五", "六", "七");

        List<String> name;
//        List<SysDictValue> dictValues = queryDictValueService.queryByType("EXAM_NBXC_ZD");
        List<SysDictValue> dictValues = queryDictValueService.queryByType("2024_MZCP");
//        name = Arrays.asList("","党委书记、总经理 白文博", "党委委员、纪委书记、工会主席、副总经理 </br>秦小新", "党委委员、副总经理 买叶明", "党委委员、副总经理 李利尊", "党委委员、副总经理 吴景志", "资深经理 马建方", "资深经理 朱东庆", "资深经理 徐国建");
        SysDictValue dictValue = dictValues.stream().filter(v -> Objects.equals(code, v.getName())).findFirst().get();
        name = Arrays.asList(dictValue.getValue().split("-"));

        IntFunction<String> function1 = (int o) -> String.format("<h1 style='font-size: 16px;font-weight:700;'>%s、%s</h1>", num.get(o), split[0]);
        IntFunction<String> function2 = (int o) -> String.format("<h2 style='font-size: 16px;font-weight:700;'>(%s)%s</h2>", num.get(o), split[1]);
        IntFunction<String> function3 = (int o) -> String.format("<h2 style='text-align: center;font-size: 17px;font-weight:700;margin:10px 0'>%s</h2>", name.get(o));

        if (split.length == 1) {
            String[] split1 = s.split("_");
            function1 = (int o) -> String.format("<h1 style='font-size: 16px;font-weight:700;'>(%s)%s</h1>", num.get(o), split1[0]);
            List<String> l1 = list.stream().filter(v -> v.contains(split1[1])).collect(Collectors.toList());
            int i = l1.indexOf(s);
            if (i == 0) {
                if (Objects.equals(split1[1], "1")) {
                    return "<h1 style='text-align: center;font-size: 18px;font-weight:700'>第二部分：对领导人员的民主测评<h1/>"
                            + function3.apply(Integer.parseInt(split1[1])) + function1.apply(i);
                }
                return function3.apply(Integer.parseInt(split1[1])) + function1.apply(i);
            }
            return function1.apply(i);
        } else {
            List<String> l1 = list.stream().filter(v -> v.contains(split[0])).collect(Collectors.toList());
            int i1 = l1.indexOf(s);
            String str = "";
            if (i1 != 0) {
                return str + function2.apply(i1);
            }
            if (Objects.equals(split[0], "党的建设")) {
                str = "<h1 style='text-align: center;font-size: 18px;font-weight:700'>第一部分：对领导班子民主测评</h1>" + function1.apply(0);
            } else {
                str = function1.apply(1);
            }
            return str + function2.apply(i1);
        }
    }

    private ExamAttribute generalExamAttributeQuestionFinished(ExamAttribute allByExamAppCode, List<ExamQuestion> examQuestionList) {
        List<ExamQuestion> singleQuestionList = Lists.newArrayList();

        List<ExamQuestion> moreQuestionList = Lists.newArrayList();

        List<ExamQuestion> judgeQuestionList = Lists.newArrayList();

        List<ExamQuestion> fillingQuestionList = Lists.newArrayList();

        List<ExamQuestion> shortAnswerQuestionList = Lists.newArrayList();

        for (ExamQuestion examQuestion : examQuestionList) {
            List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
            examQuestion.setAnswerList(examQuestionAnswerList);
            String questionType = examQuestion.getQuestionType();


            switch (questionType) {
                case "single":
                    singleQuestionList.add(examQuestion);
                    break;
                case "more":
                    moreQuestionList.add(examQuestion);
                    break;
                case "judge":
                    judgeQuestionList.add(examQuestion);
                    break;
                case "filling":
                    fillingQuestionList.add(examQuestion);
                    break;
                case "shortAnswer":
                    shortAnswerQuestionList.add(examQuestion);
                    break;
                default:
                    break;

            }

        }
        if (examQuestionList.size() > 0) {
            allByExamAppCode.setSingleQuestionList(singleQuestionList);
            allByExamAppCode.setMoreQuestionList(moreQuestionList);
            allByExamAppCode.setJudgeQuestionList(judgeQuestionList);
            allByExamAppCode.setFillingQuestionList(fillingQuestionList);
            allByExamAppCode.setShortAnswerQuestionList(shortAnswerQuestionList);
        }

        return allByExamAppCode;
    }

    private List<ExamQuestion> createRandomList(List<ExamQuestion> list, int n) {
        HashMap<Integer, String> map = Maps.newHashMap();
        List<ExamQuestion> listNew = Lists.newArrayList();
        if (list.size() < n) {
            return null;
        } else {
            while (map.size() < n) {
                int random = (int) (Math.random() * list.size());
                if (!map.containsKey(random)) {
                    map.put(random, "");
                    listNew.add(list.get(random));
                }
            }
            return listNew;
        }
    }

    /**
     * @desc 根据登录人查询对应试卷信息不分页
     * <AUTHOR>
     */
    @Override
    public List<ExamAttribute> findExamPaper(ExamAttribute examAttribute) {
        String currentUserName = SecurityUtils.getCurrentUserName();
        //如果当前登录人为超级管理员则查询所有试卷信息
        if (currentUserName.equals(Constants.HADMIN)) {
            Specification<ExamAttribute> creator = Specifications.<ExamAttribute>and()
                    .like(StringUtils.isNotEmpty(examAttribute.getExamAppCode()), "examAppCode", "%" + examAttribute.getExamAppCode() + "%")
                    .like(StringUtils.isNotEmpty(examAttribute.getExamName()), "examName", "%" + examAttribute.getExamName() + "%")
                    .like(StringUtils.isNotEmpty(examAttribute.getQuestionBankCode()), "questionBankCode", "%" + examAttribute.getQuestionBankCode() + "%")
                    .build();
            return this.findAllNoPage(creator);
        } else {
            Specification<ExamAttribute> creator = Specifications.<ExamAttribute>and()
                    .eq("creator", currentUserName)
                    .like(StringUtils.isNotEmpty(examAttribute.getExamAppCode()), "examAppCode", "%" + examAttribute.getExamAppCode() + "%")
                    .like(StringUtils.isNotEmpty(examAttribute.getExamName()), "examName", "%" + examAttribute.getExamName() + "%")
                    .like(StringUtils.isNotEmpty(examAttribute.getQuestionBankCode()), "questionBankCode", "%" + examAttribute.getQuestionBankCode() + "%")
                    .build();
            return this.findAllNoPage(creator);
        }
    }

    /**
     * @desc 根据登录人查询对应试卷信息分页
     * <AUTHOR>
     */
    @Override
    public Page<ExamAttribute> findExamPaperInfo(ExamAttribute examAttribute, Pageable pageable) {
        String currentUserName = SecurityUtils.getCurrentUserName();
        //如果当前登录人为超级管理员则查询所有试卷信息
        if (currentUserName.equals(Constants.HADMIN)) {
            Specification<ExamAttribute> creator = Specifications.<ExamAttribute>and()
                    .like(StringUtils.isNotEmpty(examAttribute.getExamAppCode()), "examAppCode", "%" + examAttribute.getExamAppCode() + "%")
                    .like(StringUtils.isNotEmpty(examAttribute.getExamName()), "examName", "%" + examAttribute.getExamName() + "%")
                    .like(StringUtils.isNotEmpty(examAttribute.getQuestionBankCode()), "questionBankCode", "%" + examAttribute.getQuestionBankCode() + "%")
                    .build();

            return this.findAll(creator, pageable);
        } else {
            Specification<ExamAttribute> creator = Specifications.<ExamAttribute>and()
                    .eq("creator", currentUserName)
                    .like(StringUtils.isNotEmpty(examAttribute.getExamAppCode()), "examAppCode", "%" + examAttribute.getExamAppCode() + "%")
                    .like(StringUtils.isNotEmpty(examAttribute.getExamName()), "examName", "%" + examAttribute.getExamName() + "%")
                    .like(StringUtils.isNotEmpty(examAttribute.getQuestionBankCode()), "questionBankCode", "%" + examAttribute.getQuestionBankCode() + "%")
                    .build();
            return this.findAll(creator, pageable);
        }
    }

    /**
     * @desc 新增试卷信息
     * <AUTHOR>
     */
    @Override
    public String createExamPaper(ExamAttributeDto examAttributeDto) {
        ExamAttribute examAttribute = examAttributeDto.getExamAttribute();
        String examAppCode = examAttribute.getExamAppCode();
        if (examAppCode != null) {
            Specification<ExamAttribute> spec = Specifications.<ExamAttribute>and()
                    .eq("examAppCode", examAppCode)
                    .build();
            List<ExamAttribute> allNoPage = this.findAllNoPage(spec);
            if (allNoPage.size() > 0) {
                return "创建试卷失败,试卷编码已存在!";
            }
        }

        List<ExamAttributeQuestion> examAttributeQuestions = examAttributeDto.getExamAttributeQuestions();
        String questionBankCode = examAttribute.getQuestionBankCode();
        //根据选择的出题方式判断如何记录试卷信息
        String topicStyle = examAttribute.getTopicStyle();
        if (topicStyle.equals(Constants.TOPIC_STYLE_FIXED)) {//选择题库中的某些题目作为此试卷题目
            ExamAttribute insert = this.insert(examAttribute);
            List<ExamAttributeQuestion> e = iExamAttributeQuestionService.saveExamAttributeQuestion(examAttributeQuestions);
            if (e.isEmpty()) {
                return "创建试卷失败";
            }
            return "创建试卷成功";
        }

        if (topicStyle.equals(Constants.TOPIC_STYLE_RANDOM)) {
            //查询该题库的题目数量
            Specification<ExamQuestion> spec = Specifications.<ExamQuestion>and().eq("questionBankCode", questionBankCode).build();
            List<ExamQuestion> allNoPage = (List<ExamQuestion>) iExamQuestionService.findAllNoPage(spec);
            int size = allNoPage.size();
            String topicNumber = examAttribute.getTopicNumber();
            int parseInt = Integer.parseInt(topicNumber);
            //如果随机数大于题库数目则无法创建试卷
            if (size < parseInt) {
                return "创建试卷失败！该随机数大于题库题目数量";
            }
            ;
            //随机出题方式创建试卷
            ExamAttribute insert = this.insert(examAttribute);
            if (insert == null) {
                return "创建试卷失败";
            }
            ;
            return "创建试卷成功";
          /*//获取随机题目数量
            String topicNumber = examAttribute.getTopicNumber();
            int questionNumber = Integer.parseInt(topicNumber);
            //获取到题目该题库所有题目
            List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);
            Random random = new Random();
            //该题库题目数量
            int size = examQuestionList.size();
            String message="题库题目数少于"+questionNumber+"题";
            Assert.isTrue(size>questionNumber,message);
            HashSet<Integer> questionNumber2 = new HashSet<>();
            //将生成的随机数存入set中，以达到过滤重复随机数,就不会出现重复题目的情况
            while (questionNumber2.size()<questionNumber){
                int i = random.nextInt(size-1);
                questionNumber2.add(i);
            }

            List<ExamAttributeQuestion> examAttributeQuestions1 = new ArrayList<>();
            for (Integer integer : questionNumber2) {
                //将随机获取的题目信息存入试卷题目关联表中
                ExamQuestion examQuestion = examQuestionList.get(integer);
                ExamAttributeQuestion examAttributeQuestion = new ExamAttributeQuestion();
                examAttributeQuestion.setQuestionCode(examQuestion.getQuestionCode());
                examAttributeQuestion.setExamAppCode(examAttribute.getExamAppCode());
                examAttributeQuestion.setQuestionBankCode(examQuestion.getQuestionBankCode());
                examAttributeQuestions1.add(examAttributeQuestion);
            }
            List<ExamAttributeQuestion> e = iExamAttributeQuestionService.saveExamAttributeQuestion(examAttributeQuestions1);
            ExamAttribute insert =  this.insert(examAttribute);
            if (e.isEmpty()) return false;
            return true;*/
        } else {
            //全题库出题方式
            ExamAttribute insert = this.insert(examAttribute);
            if (insert == null) {
                return "创建试卷失败";
            }
            return "创建试卷成功";
        }
    }

    /**
     * @desc 更改试卷信息
     * <AUTHOR>
     */
    @Override
    public boolean updateExamPaper(ExamAttributeDto examAttributeDto) {
        ExamAttribute examAttribute = examAttributeDto.getExamAttribute();
        List<ExamAttributeQuestion> examAttributeQuestions = examAttributeDto.getExamAttributeQuestions();
        //根据选择的出题方式判断如何记录试卷信息
        String topicStyle = examAttribute.getTopicStyle();
        if (topicStyle.equals(Constants.TOPIC_STYLE_FIXED)) {//选择题库中的某些题目作为此试卷题目
            ExamAttribute insert = this.insert(examAttribute);
            //在重新选择题目之后，先将之前选择的题目清空，避免出现重复的题目
            String examAppCode = examAttribute.getExamAppCode();
            iExamAttributeQuestionService.delByExamAppCode(examAppCode);
            List<ExamAttributeQuestion> e = iExamAttributeQuestionService.saveExamAttributeQuestion(examAttributeQuestions);
            if (e.isEmpty()) {
                return false;
            }
            return true;
        } else {
            ExamAttribute insert = this.insert(examAttribute);
            if (insert == null) {
                return false;
            }
            return true;
        }
    }

    /**
     * 获取试卷模板
     *
     * @param currentUserCode
     * @param source
     * @param condition
     * @return
     */
    @Override
    public JsonResponse constructExamLayoutTWO(String currentUserCode, String source, Map<String, Object> condition) {
        try {
            Map<String, Object> bobyParams = CollectionUtil.newHashMap();
            //todo 手机端没有传examAppCode,暂时手动写死,其他问卷请用constructExamLayout
          /*  bobyParams.put("examAppCode", "2023_CYZD");
            bobyParams.put("examCode", "2023_CYZD");*/
            return constructExamLayout(currentUserCode, source, bobyParams);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return JsonResponse.defaultSuccessResponse();

    }

    @Override
    public ExamInfo computeScore(String userName, String examCode, String examAppCode) {
        if (StringUtils.isBlank(userName)) {
            userName = SecurityUtils.getCurrentUserName();
        }

        // 获取答案
        ExamInfo examInfo = iExamInfoService.findExamInfo(userName, examCode, examAppCode);
        Assert.notNull(examInfo, "未查询到考试信息！");
        Assert.isTrue(examInfo.getIsFinishExam(), "考试未完成，无法计算分数");

        if (StringUtils.isNotBlank(examInfo.getScore())) {
            return examInfo;
        }

        // 获取试卷
        Map<String, Object> map = new HashMap<>();
        map.put("examAppCode", examAppCode);

        List<ExamQuestion> questions = this.getExamQuestion(Arrays.asList(examInfo.getExamRecord().split(",")));
        BigDecimal score = this.computeScore01(questions, examInfo);

        examInfo.setScore(score.toString());
        examInfo.setIsFinishExam(true);
        ExamInfo update = iExamInfoService.update(examInfo);
        return update;
    }

    /**
     * 2023卓越工程师页面跳转
     */
    @Override
    public JsonResponse pageJump() {
        String userName = SecurityUtils.getCurrentUserName();
        List<ExamWork> list = examWorkService.findAllNoPage(Specifications.<ExamWork>and().eq("transactorCode", userName).like("examCode", "%2023zygcs%").build());
        return JsonResponse.success(list);
    }

    /**
     * 定制化考试出题（IT运维知识测评）
     *
     * @param currentUserCode
     * @param source
     * @param condition
     * @return
     */
    @Override
    public JsonResponse specialExamLayout(String currentUserCode, String source, Map<String, Object> condition) {
        JsonResponse jsonResponse = null;
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/constructExamLayout";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examAttribute", "constructExamLayout", operateLog);
        if (returnObj != null) {
            return returnObj;
        }
        try {
            String examAppCode = String.valueOf(condition.get("examAppCode"));
            Assert.notNull(examAppCode, "appCode不可为空");
            //获取到试卷属性
            ExamAttribute allByExamAppCode = examAttributeRepository.findAllByExamAppCode(examAppCode);
            Assert.notNull(allByExamAppCode, "未查询到系统相关试卷");
            //获取到题库编码
            String questionBankCode = allByExamAppCode.getQuestionBankCode();
            Assert.notNull(questionBankCode, "题库编码不可为空");

            // 答题限制
            String userName = SecurityUtils.getCurrentUserName();
            ExamInfo info = iExamInfoService.findExamInfo1(userName, examAppCode);
            if (Objects.nonNull(info) && !info.isHasDrop()) {
                if (!this.checkExamNumber(info.getExamNumber(), examAppCode)) {
                    iExamInfoService.submitExam1(userName, Constants.SOURCE_P, info);
                }
            }

            //根据试卷的出题方式出试卷的试题
            String topicStyle = allByExamAppCode.getTopicStyle();

            //出题方式为随机出题
            if (topicStyle.equals(Constants.TOPIC_STYLE_RANDOM)) {
                String currentUserName = SecurityUtils.getCurrentUserName();
                if (StrUtil.isEmpty(currentUserCode)) {
                    currentUserCode = currentUserName;
                }
                //查询当前人是否已参与此考试 ，因为是随机出题方式，所以在点击已办中查看时需要获取试卷的题目信息
                Specification<ExamRangeGroup> spec = Specifications.<ExamRangeGroup>and()
                        .eq("examAppCode", examAppCode)
                        .build();
                ExamInfo examInfo = null;
                ExamRangeGroup one = iExamRangeGroupService.findOne(spec);
                if (one != null) {
                    String examCode = one.getExamCode();
                    if (examCode != null) {
                        examInfo = iExamInfoService.findExamInfo(currentUserCode, examCode, examAppCode);
                    }
                }

                //如果该用户已答过此考试的随机题，则通过答题记录表查询属于该用户的答题的所属题目
                if (examInfo != null) {
                    String examRecord = examInfo.getExamRecord(); //此人的答题的题目编号
                    Assert.notNull(examRecord, "此人交白卷");
                    String[] split = examRecord.split(",");//截取题目编号
                    ArrayList<ExamQuestion> newExamQuestions = new ArrayList<>();
                    //根据题目编码查询对应的信息
                    for (String s : split) {
                        ExamQuestion question = iExamQuestionService.findAllByQuestionCode(s);
                        newExamQuestions.add(question);
                    }
                    return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, newExamQuestions));
                }

                //查询所有数据的题目编号
                List<Map<String, Object>> questionCodeInfos = iExamQuestionService.findGroupByQuestionBankCode(questionBankCode);
                if (CollectionUtil.isNotEmpty(questionCodeInfos)) {
                    //将所有题目编号信息分组
                    Map<String, List<String>> questionCodeInfoMap = Maps.newHashMap();
                    for (Map<String, Object> questionCodeInfo : questionCodeInfos) {
                        List<String> codeList = questionCodeInfoMap.get(MapUtil.getStr(questionCodeInfo, "QUESTION_GROUP_NAME"));
                        if (CollectionUtil.isEmpty(codeList)) {
                            codeList = Lists.newArrayList();
                        }
                        codeList.add(MapUtil.getStr(questionCodeInfo, "CODE_STR"));
                        questionCodeInfoMap.put(MapUtil.getStr(questionCodeInfo, "QUESTION_GROUP_NAME") ,codeList );
                    }

                    /**
                     * 出题规则， 每隔群组至少选一个题目
                     */

                    Set<String> singleSet = Sets.newHashSet();
                    Set<String> moreSet = Sets.newHashSet();
                    String single = allByExamAppCode.getSingle();
                    String more = allByExamAppCode.getMore();
                    Integer singleNum = Integer.parseInt(single);
                    Integer moreNum = Integer.parseInt(more);
                    boolean singleIsFull = Boolean.FALSE;
                    boolean moreIsFull = Boolean.FALSE;


                    List<String> allCodeList = Lists.newArrayList();
                    //每隔分组随机选一题
                    for (Map.Entry<String, List<String>> entry : questionCodeInfoMap.entrySet()) {
                        List<String> value = entry.getValue();
                        String code = null;
                        Boolean isFinally = Boolean.FALSE;
                        while (!isFinally) {
                            code = randomCode(value);
                            if (code.contains("single") && !singleIsFull) {
                                singleSet.add(code.replace("single#" , ""));
                                singleIsFull = singleSet.size() == singleNum;
                                isFinally = Boolean.TRUE;
                            } else if (code.contains("more") && !moreIsFull) {
                                moreSet.add(code.replace("more#" , ""));
                                moreIsFull = moreSet.size() == moreNum;
                                isFinally= Boolean.TRUE;
                            }
                        }
                        value.remove(code);
                        allCodeList.addAll(value);
                    }

                    //循环继续抽题
                    while (!singleIsFull || !moreIsFull) {
                        String code = randomCode(allCodeList);
                        if (code.contains("single") && !singleIsFull) {
                            singleSet.add(code.replace("single#" , ""));
                            allCodeList.remove(code);
                            singleIsFull = singleSet.size() == singleNum;
                        } else if (code.contains("more") && !moreIsFull) {
                            moreSet.add(code.replace("more#" , ""));
                            allCodeList.remove(code);
                            moreIsFull = moreSet.size() == moreNum;
                        }
                    }

                    List<String> finallyCodeList = Lists.newArrayList();
                    finallyCodeList.addAll(moreSet);
                    finallyCodeList.addAll(singleSet);
                    //查询题目信息
                    log.warn("本次出题题目为:{}" , finallyCodeList );
                    List<ExamQuestion> questionList =  iExamQuestionService.findAllByQuestionCodes(finallyCodeList);
                    if (CollectionUtil.isNotEmpty(questionList)) {
                        ExamAttribute examAttribute = this.generalExamAttributeQuestionFinishing(allByExamAppCode, questionList);
                        jsonResponse = JsonResponse.success(examAttribute);
                    } else {
                        jsonResponse =  JsonResponse.fail("获取试卷失败!");
                    }
                } else {
                    jsonResponse = JsonResponse.fail("获取试卷失败!");
                }
            } else {
                jsonResponse = JsonResponse.fail("出题类型配置异常！");
            }
        } catch (Exception e ) {
            Exceptions.printException(e);
            jsonResponse = JsonResponse.fail("出题异常");
        }

        return jsonResponse;
    }

    @Override
    public JsonResponse specialExamPublic(String currentUserCode, String source, Map<String, Object> condition) {
        JsonResponse jsonResponse = null;
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/constructExamLayout";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examAttribute", "constructExamLayout", operateLog);
        if (returnObj != null) {
            return returnObj;
        }
        try {
            String examAppCode = String.valueOf(condition.get("examAppCode"));
            Assert.notNull(examAppCode, "appCode不可为空");
            //获取到试卷属性
            ExamAttribute allByExamAppCode = examAttributeRepository.findAllByExamAppCode(examAppCode);

            Assert.notNull(allByExamAppCode, "未查询到系统相关试卷");
            //获取到题库编码
            String questionBankCode = allByExamAppCode.getQuestionBankCode();
            Assert.notNull(questionBankCode, "题库编码不可为空");
            String  group="";
            if ("EXAM_20241125_gclv_ability_tx_exam".equals(allByExamAppCode.getExamAppCode())){
                group="20241125_gclv_ability_exam-group_tx";
            }else if ("EXAM_20241125_gclv_ability_gc_exam".equals(allByExamAppCode.getExamAppCode())){
                group="20241125_gclv_ability_exam-group_gc";
            }else {
                jsonResponse = JsonResponse.fail("未查询到对应试卷！");
            }
            String groupName=group;
            // 答题限制
            String userName = SecurityUtils.getCurrentUserName();
            ExamInfo info = iExamInfoService.findExamInfo1(userName, examAppCode);
            if (Objects.nonNull(info) && !info.isHasDrop()) {
                if (!this.checkExamNumber(info.getExamNumber(), examAppCode)) {
                    iExamInfoService.submitExam1(userName, Constants.SOURCE_P, info);
                }
            }

            //根据试卷的出题方式出试卷的试题
            String topicStyle = allByExamAppCode.getTopicStyle();

            //出题方式为随机出题
            if (topicStyle.equals(Constants.TOPIC_STYLE_RANDOM)) {
                String currentUserName = SecurityUtils.getCurrentUserName();
                if (StrUtil.isEmpty(currentUserCode)) {
                    currentUserCode = currentUserName;
                }
                //查询当前人是否已参与此考试 ，因为是随机出题方式，所以在点击已办中查看时需要获取试卷的题目信息
                Specification<ExamRangeGroup> spec = Specifications.<ExamRangeGroup>and()
                        .eq("examAppCode", examAppCode)
                        .build();
                ExamInfo examInfo = null;
                ExamRangeGroup one = iExamRangeGroupService.findOne(spec);
                if (one != null) {
                    String examCode = one.getExamCode();
                    if (examCode != null) {
                        examInfo = iExamInfoService.findExamInfo(currentUserCode, examCode, examAppCode);
                    }
                }

                //如果该用户已答过此考试的随机题，则通过答题记录表查询属于该用户的答题的所属题目
                if (examInfo != null) {
                    String examRecord = examInfo.getExamRecord(); //此人的答题的题目编号
                    Assert.notNull(examRecord, "此人交白卷");
                    String[] split = examRecord.split(",");//截取题目编号
                    ArrayList<ExamQuestion> newExamQuestions = new ArrayList<>();
                    //根据题目编码查询对应的信息
                    for (String s : split) {
                        ExamQuestion question = iExamQuestionService.findAllByQuestionCode(s);
                        newExamQuestions.add(question);
                    }
                    return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, newExamQuestions));
                }

                //获取 考试配置的字典
                List<SysDictValue> sysDictValueList = sysDictValueService.findByDictType("engineeFieldRandomSettings");
                String exam = sysDictValueList.get(0).getValue();
                Map<String,Object> map = JacksonUtils.json2obj(exam, Map.class);

                //获取  公共题目 单选、多选、判断  出题数字典配置
               Map<String,Object> groupMap=( Map<String,Object> )  map.get(groupName);
                Integer  singlePublic  = Integer.valueOf(groupMap.get("single").toString());
                Integer  morePublic  = Integer.valueOf(groupMap.get("more").toString());
                Integer judgePublic  = Integer.valueOf(groupMap.get("judge").toString());

                //获取  专业 单选、多选、判断  出题数字典配置
                List<SysDictValue> gcPublicList = sysDictValueService.findByDictType(groupName);
                String gcPublicStr = gcPublicList.get(0).getValue();
                Map<String,Object> gcPublicMap = JacksonUtils.json2obj(gcPublicStr, Map.class);
                Integer single = Integer.valueOf(gcPublicMap.get("single").toString());
                Integer more = Integer.valueOf(gcPublicMap.get("more").toString());
                Integer judge = Integer.valueOf(gcPublicMap.get("judge").toString());
                List<Map<String, Object>> questionCodeInfos = iExamQuestionService.findByQuestionBankCode(questionBankCode);

                //专业题目
                List<String> singleList = questionCodeInfos.stream().filter(questionCodeInfo -> (groupName + "-single").equals(questionCodeInfo.get("TYPE"))).map(questionCodeInfo -> questionCodeInfo.get("question_code").toString()).collect(Collectors.toList());
                List<String>  moreList = questionCodeInfos.stream().filter(questionCodeInfo -> (groupName+ "-more").equals(questionCodeInfo.get("TYPE"))).map(questionCodeInfo -> questionCodeInfo.get("question_code").toString()).collect(Collectors.toList());
                List<String>  judgeListr = questionCodeInfos.stream().filter(questionCodeInfo -> (groupName+ "-judge").equals(questionCodeInfo.get("TYPE"))).map(questionCodeInfo -> questionCodeInfo.get("question_code").toString()).collect(Collectors.toList());

                //公共题目
                List<String>  publicSingleList = questionCodeInfos.stream().filter(questionCodeInfo -> ("20241125_gclv_ability_exam-group_public-single").equals(questionCodeInfo.get("TYPE"))).map(questionCodeInfo -> questionCodeInfo.get("question_code").toString()).collect(Collectors.toList());
                List<String>   publicMoreList = questionCodeInfos.stream().filter(questionCodeInfo -> ("20241125_gclv_ability_exam-group_public-more").equals(questionCodeInfo.get("TYPE"))).map(questionCodeInfo -> questionCodeInfo.get("question_code").toString()).collect(Collectors.toList());
                List<String>   publicJudgeList = questionCodeInfos.stream().filter(questionCodeInfo -> ("20241125_gclv_ability_exam-group_public-judge").equals(questionCodeInfo.get("TYPE"))).map(questionCodeInfo -> questionCodeInfo.get("question_code").toString()).collect(Collectors.toList());

                TreeSet<String> treeSet = new TreeSet<>();
                //专业-单选
                while (treeSet.size() < single) {
                    int i = RandomUtil.randomInt(0, singleList.size());
                    treeSet.add(singleList.get(i));
                }
                //专业-多选
                while (treeSet.size()-single < more) {
                    int i = RandomUtil.randomInt(0, moreList.size());
                    treeSet.add(moreList.get(i));
                }
                //专业-判断
                while (treeSet.size()-single-more < judge) {
                    int i = RandomUtil.randomInt(0, judgeListr.size());
                    treeSet.add(judgeListr.get(i));
                }

                //公共-单选
                while (treeSet.size()-single-more-judge< singlePublic) {
                    int i = RandomUtil.randomInt(0, publicSingleList.size());
                    treeSet.add(publicSingleList.get(i));
                }
                //公共-多选
                while (treeSet.size()-single-more-judge- singlePublic< morePublic) {
                    int i = RandomUtil.randomInt(0, publicMoreList.size());
                    treeSet.add(publicMoreList.get(i));
                }
                //公共-判断
                while (treeSet.size()-single-more-judge- singlePublic-morePublic < judgePublic) {
                    int i = RandomUtil.randomInt(0, publicJudgeList.size());
                    treeSet.add(publicJudgeList.get(i));
                }

                List<ExamQuestion> questionList =  iExamQuestionService.findAllByQuestionCodes(new ArrayList<>(treeSet));
                if (CollectionUtil.isNotEmpty(questionList)) {
                    ExamAttribute examAttribute = this.generalExamAttributeQuestionFinishing(allByExamAppCode, questionList);
                    jsonResponse = JsonResponse.success(examAttribute);
                } else {
                    jsonResponse =  JsonResponse.fail("获取试卷失败!");
                }

            } else {
                jsonResponse = JsonResponse.fail("出题类型配置异常！");
            }
        }catch (Exception e){
            Exceptions.printException(e);
            jsonResponse = JsonResponse.fail("出题异常");
        }

        return jsonResponse;
    }

    @Override
    public JsonResponse specialExamLDGBZWJDCP2(String currentUserCode, String source, Map<String, Object> condition) {
        JsonResponse jsonResponse = null;
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/constructExamLayout";
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examAttribute", "constructExamLayout", operateLog);
        if (returnObj != null) {
            return returnObj;
        }
        try {
            String examAppCode = String.valueOf(condition.get("examAppCode"));
            Assert.notNull(examAppCode, "appCode不可为空");
            //获取到试卷属性
            ExamAttribute allByExamAppCode = examAttributeRepository.findAllByExamAppCode(examAppCode);
            Assert.notNull(allByExamAppCode, "未查询到系统相关试卷");
            //获取到题库编码
            String questionBankCode = allByExamAppCode.getQuestionBankCode();
            Assert.notNull(questionBankCode, "题库编码不可为空");

            // 答题限制
            String userName = SecurityUtils.getCurrentUserName();
            ExamInfo info = iExamInfoService.findExamInfo1(userName, examAppCode);
            if (Objects.nonNull(info) && !info.isHasDrop()) {
                if (!this.checkExamNumber(info.getExamNumber(), examAppCode)) {
                    iExamInfoService.submitExam1(userName, Constants.SOURCE_P, info);
                }
            }

            //根据试卷的出题方式出试卷的试题
            String topicStyle = allByExamAppCode.getTopicStyle();

            //出题方式为随机出题
            if (topicStyle.equals(Constants.TOPIC_STYLE_RANDOM)) {
                String currentUserName = SecurityUtils.getCurrentUserName();
                if (StrUtil.isEmpty(currentUserCode)) {
                    currentUserCode = currentUserName;
                }
                //查询当前人是否已参与此考试 ，因为是随机出题方式，所以在点击已办中查看时需要获取试卷的题目信息
                Specification<ExamRangeGroup> spec = Specifications.<ExamRangeGroup>and()
                        .eq("examAppCode", examAppCode)
                        .build();
                ExamInfo examInfo = null;
                ExamRangeGroup one = iExamRangeGroupService.findOne(spec);
                if (one != null) {
                    String examCode = one.getExamCode();
                    if (examCode != null) {
                        examInfo = iExamInfoService.findExamInfo(currentUserCode, examCode, examAppCode);
                    }
                }

                //如果该用户已答过此考试的随机题，则通过答题记录表查询属于该用户的答题的所属题目
                if (examInfo != null) {
                    String examRecord = examInfo.getExamRecord(); //此人的答题的题目编号
                    Assert.notNull(examRecord, "此人交白卷");
                    String[] split = examRecord.split(",");//截取题目编号
                    ArrayList<ExamQuestion> newExamQuestions = new ArrayList<>();
                    //根据题目编码查询对应的信息
                    for (String s : split) {
                        ExamQuestion question = iExamQuestionService.findAllByQuestionCode(s);
                        newExamQuestions.add(question);
                    }
                    return JsonResponse.success(this.generalExamAttributeQuestionFinishing(allByExamAppCode, newExamQuestions));
                }

                //查询所有数据的题目编号
                List<Map<String, Object>> questionCodeInfos = iExamQuestionService.findByQuestionBankCode(questionBankCode);
                if (CollectionUtil.isNotEmpty(questionCodeInfos)) {
                    List<String> finallyCodeList = Lists.newArrayList();

                    //根据随机处理数量随机出题
                    String topicNumberStr = allByExamAppCode.getTopicNumber();//总出题数
                    if (StringUtils.isNotEmpty(topicNumberStr)){
                        List<String> questionCodeList = questionCodeInfos.stream().map(questionCodeInfo -> questionCodeInfo.get("QUESTION_CODE").toString()).collect(Collectors.toList());
                        Integer topicNumber = Integer.valueOf(topicNumberStr);
                        if (questionCodeList.size()<topicNumber){
                            return  JsonResponse.fail("出题失败，题目数量不足以满足当前题库");
                        }
                        Set<Integer> numbers = new HashSet<>();
                        while (finallyCodeList.size()!=topicNumber){
                            int i =RandomUtil.randomInt(0, questionCodeList.size());//  [0,questionCodeList.size())
                            while (numbers.contains(i)){
                                i=RandomUtil.randomInt(0, questionCodeList.size());//  [0,questionCodeList.size())
                            }
                            String questionCode = questionCodeList.get(i);
                            numbers.add(i);
                            finallyCodeList.add(questionCode);
                        }
                    }
                    //查询题目信息
                    log.warn("本次出题题目为:{}" , finallyCodeList );
                    List<ExamQuestion> questionList =  iExamQuestionService.findAllByQuestionCodes(finallyCodeList);
                    Collections.shuffle(questionList);
                    if (CollectionUtil.isNotEmpty(questionList)) {
                        ExamAttribute examAttribute = this.generalExamAttributeQuestionFinishing(allByExamAppCode, questionList);
                        jsonResponse = JsonResponse.success(examAttribute);
                    } else {
                        jsonResponse =  JsonResponse.fail("获取试卷失败!");
                    }
                } else {
                    jsonResponse = JsonResponse.fail("获取试卷失败!");
                }
            } else {
                jsonResponse = JsonResponse.fail("出题类型配置异常！");
            }
        } catch (Exception e ) {
            Exceptions.printException(e);
            jsonResponse = JsonResponse.fail("出题异常");
        }

        return jsonResponse;
    }


    private String  randomCode(List<String> codeLis ) {
        int i = RandomUtil.randomInt(0, codeLis.size() - 1);
        return codeLis.get(i);
    }


    /***
     * 校验答题次数是否合法
     * <br/> params [number]
     *
     * @return {@link boolean}
     * <AUTHOR>
     * @since 2023/9/12 16:24
     */
    private boolean checkExamNumber(int number, String examAppCode) {
        Optional<SysDictValue> first = queryDictValueService.queryByType("EXAM_NUMBER_TYPE")
                .stream().filter(v -> examAppCode.contains(v.getName())).findFirst();
        if (!first.isPresent()) {
            return true;
        }
        return number < Integer.parseInt(first.get().getValue());
    }

    /***
     * 根据题号获取题目及选项
     * <br/> params [questionList]
     *
     * @return {@link List< ExamQuestion>}
     * <AUTHOR>
     * @since 2023/9/26 16:45
     */
    private List<ExamQuestion> getExamQuestion(List<String> questionList) {
        if (CollectionUtils.isEmpty(questionList)) {
            return new ArrayList<>();
        }

        List<ExamQuestion> questions = iExamQuestionService.findAllByQuestionBankCode(questionList);
        List<ExamQuestionAnswer> answers = iExamQuestionAnswerService.findAllNoPage(Specifications.<ExamQuestionAnswer>and().in("questionCode", questionList).build());
        Map<String, List<ExamQuestionAnswer>> collect = answers.stream().collect(Collectors.groupingBy(ExamQuestionAnswer::getQuestionCode));
        questions.forEach(v -> v.setAnswerList(collect.get(v.getQuestionCode())));

        return questions;
    }

    /**
     * 自动算分 单选 多选 判断 填空 不定项
     * <br/> params [sj, examInfo]
     *
     * @return {@link int}
     * <AUTHOR>
     * @since 2023/8/7 11:34
     */
    private BigDecimal computeScore01(List<ExamQuestion> questions, ExamInfo examInfo) {
        BigDecimal sum = BigDecimal.ZERO;
        String answer = examInfo.getExamAnswer();

        // 防null校验
        if (StringUtils.isBlank(answer)) {
            int length = examInfo.getExamRecord().split(",").length - 1;
            for (int i = 0; i < length; i++) {
                if (Objects.isNull(answer)) {
                    answer = "";
                }
                answer += ",";
            }
        }

        String[] answers = answer.split(",");
        String record = examInfo.getExamRecord();
        String[] records = record.split(",");
        List<String> recordsList = Arrays.asList(records);

        // 处理 答案数量不够的情况
        if (answers.length != records.length) {
            String[] strings = new String[records.length];
            for (int i = 0; i < strings.length; i++) {
                strings[i] = "";
                if (i < answers.length) {
                    strings[i] = answers[i];
                }
            }
            answers = strings;
        }

        // 计算
        for (ExamQuestion examQuestion : questions) {
            int index = recordsList.indexOf(examQuestion.getQuestionCode());
            if (index == -1) {
                continue;
            }
            List<ExamQuestionAnswer> list = examQuestion.getAnswerList().stream().filter(v -> BooleanUtils.isTrue(v.getIsCorrect())).collect(Collectors.toList());
            switch (examQuestion.getQuestionType()) {
                case Constants.JUDGE:
                case Constants.SINGLE: {
                    if (Objects.equals(answers[index], list.get(0).getAnswerCode())) {
                        sum = sum.add(this.computeScore01(examQuestion.getQuestionScore()));
                    }
                    break;
                }
                case Constants.INDEFINITE:
                case Constants.MORE: {
                    // 多选错选不得分，少选得一半分
                    String reduce = list.stream().map(ExamQuestionAnswer::getAnswerCode).sorted().reduce((a, b) -> a + b).get();
                    String reduce1 = Arrays.stream(answers[index].split("/")).sorted().reduce((a, b) -> a + b).get();
                    BigDecimal computed = BigDecimal.ZERO;
                    if (StringUtils.isBlank(reduce1)) {
                        computed = BigDecimal.ZERO;
                    }
                    else if (Objects.equals(reduce, reduce1)) {
                        computed = this.computeScore01(examQuestion.getQuestionScore());
                    }
                    else if (Arrays.stream(answers[index].split("/")).allMatch(reduce::contains)) {
                        computed = this.computeScore01(examQuestion.getQuestionScore()).divide(new BigDecimal(2));
                    }
                    sum = sum.add(computed);
                    break;
                }
                case Constants.FILLING: {
                    String[] split0 = answers[index].split(Constants.SEPARATOR);
                    String[] split1 = list.get(0).getAnswerContent().split(" ");
                    // 不按顺序判题
                    if (!Objects.equals(list.get(0).getIdentification(), "1")) {
                        Arrays.sort(split0);
                        Arrays.sort(split1);
                    }
                    if (Arrays.equals(split0, split1)) {
                        sum = sum.add(this.computeScore01(examQuestion.getQuestionScore()));
                    }
                    break;
                }
            }
        }
        return sum;
    }

    /**
     * 字符串转数字
     *
     * @param score score
     * @return 转换后的数字
     */
    private BigDecimal computeScore01(String score) {
        if (StringUtils.isBlank(score)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(score);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return BigDecimal.ZERO;
    }


    private List<ExamQuestion> randomGroup(String questionBankCode , Integer  singleNum  , Integer  moreNum , Integer judgeNum  ) throws Exception {
        List<ExamQuestion> randomQuestions = null;
        //查询所有数据的题目编号
        List<Map<String, Object>> questionCodeInfos = iExamQuestionService.findGroupByQuestionBankCode(questionBankCode);
        if (CollectionUtil.isNotEmpty(questionCodeInfos)) {
            //将所有题目编号信息分组
            Map<String, List<String>> questionCodeInfoMap = Maps.newHashMap();
            for (Map<String, Object> questionCodeInfo : questionCodeInfos) {
                List<String> codeList = questionCodeInfoMap.get(MapUtil.getStr(questionCodeInfo, "QUESTION_GROUP_NAME"));
                if (CollectionUtil.isEmpty(codeList)) {
                    codeList = Lists.newArrayList();
                }
                codeList.add(MapUtil.getStr(questionCodeInfo, "CODE_STR"));
                questionCodeInfoMap.put(MapUtil.getStr(questionCodeInfo, "QUESTION_GROUP_NAME"), codeList);
            }

            /**
             * 出题规则， 每隔群组至少选一个题目
             */
            Set<String> singleSet = Sets.newHashSet();
            Set<String> moreSet = Sets.newHashSet();
            Set<String> judgeSet = Sets.newHashSet();
            boolean singleIsFull = Boolean.FALSE;
            boolean moreIsFull = Boolean.FALSE;
            boolean judgeIsFull = Boolean.FALSE;

            List<String> allCodeList = Lists.newArrayList();
            //每隔分组随机选一题
            for (Map.Entry<String, List<String>> entry : questionCodeInfoMap.entrySet()) {
                List<String> value = entry.getValue();
                String code = null;
                Boolean isFinally = Boolean.FALSE;
                while (!isFinally) {
                    code = randomCode(value);
                    if (code.contains("single") && !singleIsFull) {
                        singleSet.add(code.replace("single#", ""));
                        singleIsFull = singleSet.size() == singleNum;
                        isFinally = Boolean.TRUE;
                    } else if (code.contains("more") && !moreIsFull) {
                        moreSet.add(code.replace("more#", ""));
                        moreIsFull = moreSet.size() == moreNum;
                        isFinally = Boolean.TRUE;
                    } else if (code.contains("judge") && !judgeIsFull ) {
                        judgeSet.add(code.replace("judge#", ""));
                        judgeIsFull = judgeSet.size() == judgeNum;
                        isFinally = Boolean.TRUE;
                    }
                }
                value.remove(code);
                allCodeList.addAll(value);
            }

            //循环继续抽题
            while (!singleIsFull || !moreIsFull || !judgeIsFull) {
                String code = randomCode(allCodeList);
                if (code.contains("single") && !singleIsFull) {
                    singleSet.add(code.replace("single#", ""));
                    allCodeList.remove(code);
                    singleIsFull = singleSet.size() == singleNum;
                } else if (code.contains("more") && !moreIsFull) {
                    moreSet.add(code.replace("more#", ""));
                    allCodeList.remove(code);
                    moreIsFull = moreSet.size() == moreNum;
                } else if (code.contains("judge") && !judgeIsFull ) {
                    judgeSet.add(code.replace("judge#", ""));
                    allCodeList.remove(code);
                    judgeIsFull = judgeSet.size() == judgeNum;
                }
            }
            List<String> finallyCodeList = Lists.newArrayList();
            finallyCodeList.addAll(moreSet);
            finallyCodeList.addAll(singleSet);
            finallyCodeList.addAll(judgeSet);
            //查询题目信息
            log.warn("本次出题题目为:{}", finallyCodeList);
            if (CollectionUtil.isNotEmpty(finallyCodeList)) {
                randomQuestions = iExamQuestionService.findAllByQuestionCodes(finallyCodeList);
            } else {
                throw new Exception("题目信息查询失败！");
            }
        } else {
            throw new Exception("题库信息查询失败！");
        }
        return randomQuestions;
    }

}
