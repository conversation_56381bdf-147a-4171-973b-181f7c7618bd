package com.simbest.boot.exam.uums.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.uums.service.ExtendUumsSysUserRoleService;
import com.simbest.boot.security.SimpleUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @desc:
 * @date 2021/7/2  18:17
 */
@Api(description = "考试管理系统对应角色下的人员信息",tags = "考试管理系统对应角色下的人员信息")
@Slf4j
@RestController
@RequestMapping(value = "/action/userRoleManage")
public class ExtendUumsSysUserRoleController {

    @Autowired
    private ExtendUumsSysUserRoleService extendUumsSysUserRoleService;

    @ApiOperation(value = "根据角色id查询对应角色下的人员",notes = "根据角色id查询对应角色下的人员")
    @PostMapping(value = {"/findUserByRoleId", "/api/findUserByRoleId", "/findUserByRoleId/sso", "/anonymous/findUserByRoleId"})
    public JsonResponse findUserByRole(@RequestParam(required = false,defaultValue = "1")int page,
                                       @RequestParam(required = false,defaultValue = "10") int size,
                                       @RequestParam String roleId,
                                       @RequestParam(required = false,defaultValue = "") String username){

      return   extendUumsSysUserRoleService.findUserByRole(page,size,roleId,username);
    }

    @ApiOperation(value = "关联用户角色信息",notes = "关联用户角色信息")
    @PostMapping(value = {"/createRoleUsers", "/api/createRoleUsers", "/createRoleUsers/sso", "/anonymous/createRoleUsers"})
    public JsonResponse createRoleUsers(@RequestParam String roleId,@RequestParam String userName){
        return   extendUumsSysUserRoleService.createRoleUsers(roleId,userName);
    }

    @ApiOperation(value = "根据id删除用户角色信息",notes = "根据id删除用户角色信息")
    @PostMapping(value = {"/deleteById", "/api/deleteById", "/deleteById/sso", "/anonymous/deleteById"})
    public JsonResponse deleteById(@RequestParam String id,@RequestBody String[] usernames){
        return   extendUumsSysUserRoleService.deleteById(id,usernames);
    }


}
