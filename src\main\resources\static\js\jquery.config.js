﻿//声明字典命名空间
if (!window.web) {
    web = {};
}
if (!window.dictionary) {
    dictionary = {};
}
if (!window.dictionarys) {
    dictionarys = {};
}
web.appCode = "exam";

web.appHtml = {
    "A": "html/template/examCommon.html",//考试模板（支持单选、多选、判断、填空，简答，有倒计时）
    "B": "html/template/questionnaireDdzw.html",//通用模板（支持单选、多选、判断、填空，简答,支持多选选项请选择,选项互斥）
    "C": "html/template/questionnaireNbxc.html",//内部巡察五套（支持单选、多选、判断、填空，简答,支持多选选项请选择,选项互斥）
    "D": "html/template/zwjdPublicExam.html", 
    // "D": "html/template/gcPublicExam.html", //倒计时，自动保存，单选、多选、判断，多终端打开
    // "H": "html/exammanage/questionnaireNew.html",//通用模板（支持单选、多选、判断、填空，简答,只支持最简单的问卷）
    "H": "html/template/questionnaireCYZD.html",//问卷从严治党0114（支持单选、多选、判断、填空，简答；支持多选选个数控制、选项请选择，”新增写死标题“）
    "T": "html/exammanage/itPublicExam.html", // it运维（分组出题）
    "O": "html/template/questionnaireNew.html",//问卷通用模板（支持单选、多选、判断、填空，简答；支持多选选个数控制、选项请选择）
    "P":"html/briefDistribution/trans.html" ,//简报  
    "Q":"html/template/questionnaireBQZM.html" ,//简报  
    "R":"html/template/questionnaireXCB.html", //巡察办  （滑块评分1分制）
    "S": "html/template/questionnaireCYZDNum.html",//（支持单选、多选、判断、填空，简答；支持多选选个数控制、选项请选择，评分，第一题是单选题题号是0）




};
//ABC代表的是流程类型,后边的是对应的是流程名，会显示在详情页面的头部

web.appName = {
    "A": "在线考试试卷",
    "B": "公司党委“担当作为”专项巡察调查问卷",
    "C": "内部巡察",
    "D": "2024年度领导干部自我监督测评(第二期)",
    // "D": "2024年工程领域所有人(含通信专业和土建专业)能力摸底考试功",
    "H": "问卷调查",
    "T": "IT运维知识测评",
    "O": "监督一点通”平台使用情况调查问卷",
    "P":"简报转发",
    "Q":"关于搬迁至中牟生产楼办公的意见调研问卷",
    "R":"经济责任审计调查问卷",
    "S": "问卷调查",




    
};
web.procesType = "pmInsType-流程类型";


