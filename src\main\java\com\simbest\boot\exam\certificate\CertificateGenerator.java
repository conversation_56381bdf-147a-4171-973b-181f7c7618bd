/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate;

import cn.hutool.core.map.MapUtil;
import com.simbest.boot.exam.certificate.doc.model.CertificateData;
import com.simbest.boot.exam.certificate.doc.model.CertificateField;
import com.simbest.boot.exam.certificate.doc.util.DocOperator;
import com.simbest.boot.exam.certificate.doc.util.DocxConverter;
import com.simbest.boot.exam.certificate.path.model.CertificateTempPaths;
import com.simbest.boot.exam.certificate.pdf.PdfConverter;
import com.simbest.boot.exam.examOnline.model.CertificateDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.xmlbeans.XmlException;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.IOException;
import java.util.Map;

/**
 * <strong>Title : CertificateGenerator</strong><br>
 * <strong>Description : 图片生成器 </strong><br>
 * <strong>Create on : 2020/11/10</strong><br>
 * <strong>Modify on : 2020/11/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
public class CertificateGenerator {

    public static File generate(CertificateData data) throws Exception {

        File file=null;

        String officePath = ConstantsOffice.LIBRE_OFFICE_PATH_WINDOWS;
        String imageSuffix = ConstantsOffice.IMAGE_SUFFIX;
        String imageScaleStr = ConstantsOffice.IMAGE_SCALE_STR;

        Float imageScale = null;
        imageScale = Float.valueOf(imageScaleStr);

        // 获取证书模板
        //File docxTemplate = new ClassPathResource(ConstantsOffice.TEMPLATE_FILE_PATH).getFile();
        File docxTemplate = new ClassPathResource("template/template.docx").getFile();

        // 生成临时文件路径
        CertificateTempPaths tempPaths = CertificateTempPaths.newInstance("docx", "pdf", imageSuffix);

        // 复制证书模板，将字段替换为自定义数据
        String tempDocxPath = tempPaths.getTempDocPathName();
        DocOperator.toCumstomDoc(docxTemplate, tempDocxPath, data);


        // 将doc模板转成pdf
        String tempPdfPath = tempPaths.getTempPdfPathName();
        DocxConverter.toPdfUsingLibreOffice(tempDocxPath, tempPdfPath, officePath);

        // 将pdf转成证书图片
        file = PdfConverter.toImageUsingPdfbox(tempPdfPath, tempPaths.getTempImagePathName(), imageSuffix, imageScale);

        File tempFile = tempPaths.getTempFile();
        if (tempFile.exists()) {
            FileUtils.deleteQuietly(tempFile);
            FileUtils.deleteQuietly(tempFile.getParentFile());
        }

        // 删除生成证书使用的临时文件
        if(tempPaths.deleteTempFiles()){
            log.info("临时文件清除完成");
        }
        else {
            log.info("临时文件清除失败");
        }
        return  file;
    }

    public static String addBlankSpace(String text) {
        StringBuffer sb = new StringBuffer();

        if (text == null) {
            return null;
        }

        char[] chars = text.toCharArray();

        String regex = "[\u4E00-\u9FA5]{1}";
        for (char aChar : chars) {
            String str = aChar + "";
            if (StringUtils.isBlank(str)) {
                continue;
            }

            sb.append(aChar);

            if (str.matches(regex)) {
                sb.append(" ");
            }
        }

        return sb.toString();
    }

    public static File generateExternal(CertificateDetail certificateDetail) throws Exception{

        CertificateData data = new CertificateData();
        data.put(new CertificateField("持证人", addBlankSpace(certificateDetail.getTruename()), 18));
        data.put(new CertificateField("证书中文信息", certificateDetail.getTitle(), 18));
        data.put(new CertificateField("证书编号", certificateDetail.getNumber(), 18));
        data.put(new CertificateField("证书日期", certificateDetail.getDate(), 15));
        return generate(data);
    }




    public static void main(String[] args) throws Exception {
        CertificateData data = new CertificateData();
        data.put(new CertificateField("持证人", addBlankSpace("张三"), 18));
        data.put(new CertificateField("证书中文信息", "纪检监察业务培训(第一期)线上培训班", 18));
//        data.put(new CertificateField("证书英文信息",
//                "Congratulations on completion of training program of \"Helicopter Pilot Qualification\"", 15));
        data.put(new CertificateField("证书编号", "00320200825001542", 18));
        //data.put(new CertificateField("证书签名", addBlankSpace("李四"), 25));
        data.put(new CertificateField("证书日期", "2020年11月11日", 15));
        generate(data);
    }
}
