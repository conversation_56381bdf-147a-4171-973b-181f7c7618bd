package com.simbest.boot.exam.uums.api;

import com.fasterxml.jackson.core.type.TypeReference;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.util.UumsUtil;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.user.UumsSysUserRoleApi;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用途：主数据人员-角色管理API
 * 作者: jingwenhao
 * 时间: 2019/3/14  18:11
 */
@Component
@Slf4j
public class ExtendUumsSysUserRoleApi extends UumsSysUserRoleApi {
    private static final String PATH_MAPPING = "/action/user/role/";
    private static final String SSO = "/sso";

    @Autowired
    private UumsUtil uumsUtil;

    /**
     * @desc: 新增用户角色
     * @author: jing<PERSON><PERSON>
     * @date: 2020/11/19
     */
    public Map<String, Object> create(Map<String, Object> userRole) {
        String url = PATH_MAPPING + "create" + SSO;
        JsonResponse jr = uumsUtil.postJson(url, null, userRole, SecurityUtils.getCurrentUserName());
        if (jr == null || jr.getErrcode() != 0) {
            log.error("新增用户角色失败，失败原因【{}】", jr.getMessage());
            return null;
        }
        String json = JacksonUtils.obj2json(jr.getData());
        return JacksonUtils.json2Type(json, new TypeReference<Map<String, Object>>() {
        });
    }

    /**
     * @desc: 根据主数据userRole主键【注意，是user_role主键】，删除用户角色
     * @author: jingwenhao
     * @date: 2020/11/19
     */
    public Boolean deleteById(String id) {
        String url = PATH_MAPPING + "deleteById" + SSO;
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        JsonResponse jr = uumsUtil.post(url, map, SecurityUtils.getCurrentUserName());
        if (jr == null || jr.getErrcode() != 0) {
            log.error("删除用户角色失败，失败原因【{}】", jr.getMessage());
            return null;
        }
        return true;
    }

    /**
     * @desc: 根据角色主键和用户账号 删除用户角色
     * @author: jingwenhao
     * @date: 2020/11/19
     */
    public Boolean deleteByRoleIdsAndUsername(String roleIds, String username) {
        String url = PATH_MAPPING + "deleteRoles" + SSO;
        Map<String, Object> map = new HashMap<>();
        map.put("roleIds", roleIds);
        map.put("username", username);
        JsonResponse jr = uumsUtil.post(url, map, SecurityUtils.getCurrentUserName());
        if (jr == null || jr.getErrcode() != 0) {
            log.error("删除用户角色失败，失败原因【{}】", jr.getMessage());
            return null;
        }
        return true;
    }

    /**
     * @desc: 查询用户-角色列表不分页
     * @author: jingwenhao
     * @date: 2020/11/19
     */
    public List<Map<String, Object>> findAllNoPage(Map<String, Object> userRole) {
        String url = PATH_MAPPING + "findAllNoPage" + SSO;
        JsonResponse jr = uumsUtil.postJson(url, null, userRole, SecurityUtils.getCurrentUserName());
        if (jr == null || jr.getErrcode() != 0) {
            log.error("查询用户-角色列表失败，失败原因【{}】", jr.getMessage());
            return null;
        }
        String json = JacksonUtils.obj2json(jr.getData());
        List<Map<String, Object>> page = JacksonUtils.json2Type(json, new TypeReference<List<Map<String, Object>>>() {
        });
        return page;
    }

    /**
     * @desc: 根据角色编码查询用户-角色列表不分页
     * @author: jingwenhao
     * @date: 2020/11/19
     */
    public List<Map<String, Object>> findUserRoleByRoleCode(String roleCode) {
        String url = PATH_MAPPING + "findUserRoleByRoleCode" + SSO;
        Map<String, Object> map = new HashMap<>();
        map.put("roleCode", roleCode);
        JsonResponse jr = uumsUtil.post(url, map, SecurityUtils.getCurrentUserName());
        if (jr == null || jr.getErrcode() != 0) {
            log.error("查询用户-角色列表失败，失败原因【{}】", jr.getMessage());
            return null;
        }
        String json = JacksonUtils.obj2json(jr.getData());
        List<Map<String, Object>> page = JacksonUtils.json2Type(json, new TypeReference<List<Map<String, Object>>>() {
        });
        return page;
    }

}
