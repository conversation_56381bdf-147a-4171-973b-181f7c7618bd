package com.simbest.boot.exam.util;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.distribution.id.IdGenerator;

/**
 * <strong>Title : NumRuleUtil</strong><br>
 * <strong>Description : 编号生成规则工具类</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>  IdGenerator
 */
public final class NumRuleUtil {

    private NumRuleUtil() {
    }

    /**
     * 获取当前数据库中保存的编号最大值
     *
     * @param maxNum 表中保存的最大值
     * @return
     */
    public synchronized static String getTitleNumRule(String maxNum) {
        String num = "";
        if (maxNum == null || "".equals(maxNum) || "null".equals(maxNum)) {
            num = DateUtil.getCurrYear() + "001";
        } else {
            num = String.valueOf(Integer.valueOf(maxNum).intValue() + 1);
        }
        return num;
    }

    /**
     * 获取主单据ID
     *
     * @return
     */
    public synchronized static String getPmInsId(String flowTypeCode) {
        String pmInsId = null;
        try {
            pmInsId = flowTypeCode + String.valueOf(IdGenerator.idWorker.nextId());
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return pmInsId;
    }
}
