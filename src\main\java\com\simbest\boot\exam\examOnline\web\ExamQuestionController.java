/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.web;/**
 * Created by KZH on 2019/10/8 15:18.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.service.IExamQuestionService;
import com.simbest.boot.exam.util.FileTool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:18
 * @desc 题目
 **/
@Api(description = "题目")
@Slf4j
@RestController
@RequestMapping(value = "/action/examQuestion")
public class ExamQuestionController extends LogicController<ExamQuestion, String> {

    private IExamQuestionService iExamQuestionService;

    @Autowired
    public ExamQuestionController(IExamQuestionService service) {
        super(service);
        this.iExamQuestionService = service;
    }

    @ApiOperation(value = "根据dictType获取数据字典值", notes = "根据dictType获取数据字典值")
    @PostMapping(value = "/findDictValue")
    public JsonResponse findDictValue(@RequestParam(required = false) String dictType) {
        return JsonResponse.success(iExamQuestionService.findDictValue(dictType));
    }

    @ApiOperation(value = "新增试题", notes = "新增试题")
    @PostMapping(value = {"/importQuestion", "sso/importQuestion"})
    public JsonResponse importQuestion(@RequestBody Map<String, Object> sumMaps) {
        return iExamQuestionService.importQuestion(sumMaps);
    }

    /**
     * 下载新增试题模板
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = {"/downloadTemplate", "/downloadTemplate/sso"}, method = RequestMethod.POST)
    public Map<String, Object> downloadTemplate(HttpServletResponse response, HttpServletRequest request) throws Exception {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("model/新增试题-模板.xls");
        FileTool.downloadIn(in, "新增试题-模板.xls", response);
        return null;
    }

    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping(value = "/importExcel")
    public JsonResponse importExcel(HttpServletRequest request, HttpServletResponse response) {
        return JsonResponse.success(iExamQuestionService.importExcel(request, response));
    }


    @ApiOperation(value = "保存导入数据", notes = "保存导入数据")
    @PostMapping(value = "/saveExamQuestionList")
    public JsonResponse saveExamQuestionList(@RequestBody ExamQuestion examQuestion) {

        return iExamQuestionService.saveExamQuestionList(examQuestion);
    }


    @ApiOperation(value = "查询所有试题", notes = "查询所有试题")
    @PostMapping(value = {"/findAllExamQuestion", "sso/findAllExamQuestion"})
    public JsonResponse findAllExamQuestion(
            @RequestParam(required = false, defaultValue = "1") int page,
            @RequestParam(required = false, defaultValue = "10") int size,
            @RequestParam(required = false) String direction,
            @RequestParam(required = false) String properties,
            @RequestBody ExamQuestion examQuestion) {

        Pageable pageable = iExamQuestionService.getPageable(page, size, direction, properties);
        return iExamQuestionService.findAllExamQuestion(examQuestion, pageable);
    }

    @ApiOperation(value = "根据题库编码查询所属题目", notes = "根据题库编码查询所属题目")
    @PostMapping(value = {"/findByQuestionBankCode", "sso/findByQuestionBankCode"})
    public JsonResponse findByQuestionBankCode(@RequestParam String questionBankCode) {
        return JsonResponse.success(iExamQuestionService.findAllByQuestionBankCode(questionBankCode));
    }


    /**
     *
     * 根据题库编码和随机提取个数来抽取题目信息
     * @param questionBankCode
     * @param count
     * @return
     */

    @ApiOperation(value = "根据题库编码和随机提取个数来抽取题目信息", notes = "根据题库编码和随机提取个数来抽取题目信息")
    @PostMapping(value = {"/getRandomQuestionsByCategory", "sso/getRandomQuestionsByCategory"})
    public JsonResponse getRandomQuestionsByCategory(
            @RequestParam(required = false) String questionBankCode,
            @RequestParam(required = false) Integer count) {
        List<ExamQuestion> randomQuestionsByCategory = iExamQuestionService.getRandomQuestionsByCategory(questionBankCode, count);
        return JsonResponse.success(randomQuestionsByCategory);
    }

}
