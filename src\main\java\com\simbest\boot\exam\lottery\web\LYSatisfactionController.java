package com.simbest.boot.exam.lottery.web;

import cn.hutool.core.map.MapUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.lottery.model.LYSatisfaction;
import com.simbest.boot.exam.lottery.service.ILYSatisfactionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@Api(description = "洛阳考试抽奖")
@RestController
@RequestMapping(value = "action/lysatisfaction")
public class LYSatisfactionController  extends LogicController<LYSatisfaction,String> {
    private ILYSatisfactionService service;
    @Autowired
    public LYSatisfactionController(ILYSatisfactionService service){
        super(service);
        this.service=service;

    }
    //类型和试题code
    @ApiOperation(value = "根据概率是否中奖", notes = "根据概率是否中奖")
    @PostMapping(value = {"/isLottery"})
    public JsonResponse isLottery( @RequestBody Map<String, String> params) {
        String type="";
        String examCode="";

        if(params !=null && params.size() > 0){
            type =  params.get("type");
            examCode =  params.get("examCode");
        }
        return service.isLySatisfaction(type,examCode);
    }

}
