package com.simbest.boot.exam.examOnline.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.model.ExamInfoDetail;
import com.simbest.boot.exam.examOnline.repository.ExamInfoDetailRepository;
import com.simbest.boot.exam.examOnline.service.IExamInfoDetailService;
import com.simbest.boot.exam.examOnline.service.IExamInfoService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.mq.util.Exceptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;

/**
 * 用途：考试管理模块--考试答题明细service
 * 作者：gy
 * 时间: 2021-02-23 20:01
 */
@Slf4j
@Service
public class ExamInfoDetailServiceImpl extends LogicService<ExamInfoDetail, String> implements IExamInfoDetailService {
    private ExamInfoDetailRepository repository;

    @Autowired
    public ExamInfoDetailServiceImpl(ExamInfoDetailRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private IExamInfoService examInfoService;

    @Autowired
    IExamQuestionAnswerService examQuestionAnswerService;

    @Override
    public void saverExamInfoDetails(String examCode) {
        /* 1、清空数据表现有考试明细数据 */
        Specification<ExamInfoDetail> spec = Specifications
                .<ExamInfoDetail>and()
                .eq("examCode", examCode)
                .build();
        List<ExamInfoDetail> detailList = findAllNoPage(spec);
        if (!detailList.isEmpty()) {
            repository.deleteAll(detailList);
        }
        /* 2、获取考试答题记录表信息 */
        Specification<ExamInfo> infoSpec = Specifications
                .<ExamInfo>and()
                .eq("examAppCode", examCode)
                .eq("isFinishExam", true)
                .build();
        List<ExamInfo> infoList = examInfoService.findAllNoPage(infoSpec);
        List<Map<String,Object>> correctMapList= examQuestionAnswerService.findAllCorrectByQuestionBankCode(examCode);

        /* 3、生成考试明细数据 */
        for (ExamInfo examInfo : infoList) {
            String examRecordStr = examInfo.getExamRecord();
            String examAnswerStr = examInfo.getExamAnswer();
            String[] examRecordSplit = examRecordStr.split(",");
            String[] examAnswerSplit = examAnswerStr.split(",",-1);
            for (int i = 0; i <= examRecordSplit.length - 1; i++) {
                String examRecord = examRecordSplit[i];
                String examAnswer = examAnswerSplit[i];
                ExamInfoDetail infoDetail = ExamInfoDetail
                        .builder()
                        .publishUsername(examInfo.getPublishUsername())
                        .publishTruename(examInfo.getPublishTruename())
                        .examInfoId(examInfo.getId())
                        .examCode(examInfo.getExamAppCode())
                        .examQuestionCode(examRecord)
                        .examAnswer(examAnswer)
                        .build();
                //answercode  trueanswer
                correctMapList.forEach(map -> {
                    String answercode = map.get("ANSWECODE").toString();
                    if (examRecord.equals(answercode)){
                        String trueanswer = map.get("TRUEANSWER").toString();
                        String totalSocre = map.get("SCORE").toString();
                        if (trueanswer.contains(",")){//多选
                            Double moreScore = getMoreScore(trueanswer, examAnswer, Double.valueOf(totalSocre));
                            infoDetail.setScore(moreScore.toString());
                        }else {
                            if (examAnswer.equals(trueanswer)){
                                infoDetail.setScore(totalSocre);
                            }else {
                                infoDetail.setScore("0");
                            }
                        }
                    }
                });

                //获取考试的正确答案
                insert(infoDetail);
            }
        }
    }

    /**
     * 异步保存数据信息
     *
     * @param examInfo  考试信息
     * @param allList 答题信息
     */
    @Override
    public void syncSaveInfo(ExamInfo examInfo, List<Map<String, Object>> allList) {
        Assert.notNull(examInfo , "考试保存记录信息不能为空！");
        if (CollectionUtil.isNotEmpty(allList)) {
            try {
                repository.deleteAllByExamInfoId(examInfo.getId());
                allList.forEach(info -> {
                    ExamInfoDetail infoDetail = ExamInfoDetail
                            .builder()
                            .publishUsername(examInfo.getPublishUsername())
                            .publishTruename(examInfo.getPublishTruename())
                            .examInfoId(examInfo.getId())
                            .examCode(examInfo.getExamAppCode())
                            .examQuestionCode(MapUtil.getStr(info , "questionCode"))
                            .examAnswer(MapUtil.getStr(info , "examAnswer"))
                            .build();
                    insert(infoDetail);
                });
            } catch (Exception e ) {
                Exceptions.printException(e);
            }

        }
    }


    /**
     * #####################判分规则##############################
     * 多选题得分计算：少选得一半，不选、选错得0,
     * ###########################################################
     *
     * @return
     */
    private  Double getMoreScore(String correctStr, String userStr,Double totalScore) {
        Set<String> correctSet = new HashSet<>(Arrays.asList(correctStr.split(",")));//正确答案set
        Set<String> userSet = new HashSet<>(Arrays.asList(userStr.split("/",-1)));//用户选择答案set

        //多选为错
        if (userSet.size() > correctSet.size()) {
            return Double.valueOf(0);
        }
        // 计算交集大小
        Set<String> intersection = new HashSet<>(correctSet);
        intersection.retainAll(userSet);
            // 检查是否完全匹配
            if (correctSet.equals(intersection)) {
                return totalScore; // 完全正确
            }
            // 计算缺失的数量
            int missingCount = correctSet.size() - intersection.size();
            int extraCount = userSet.size() - intersection.size();
            // 如果有多余选项或完全不匹配，则不得分
            if (extraCount > 0 || intersection.isEmpty()) {
                return Double.valueOf(0);
            }
            // 少选数小于（正确答案数-1），得一半
            if (missingCount > 0 && missingCount < correctSet.size() - 1 ) {
                return  totalScore/2;
            }
            return Double.valueOf(0); // 其他情况不得分


    }
}
