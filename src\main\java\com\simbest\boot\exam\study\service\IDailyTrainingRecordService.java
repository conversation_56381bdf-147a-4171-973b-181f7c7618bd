package com.simbest.boot.exam.study.service;

import com.simbest.boot.exam.study.model.DailyTrainingRecord;
import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.study.model.TrainingAnswerDetail;

import java.util.List;
import java.util.Map;

/**
 * 用途：DailyTrainingRecord领域对象名称接口
 */
public interface IDailyTrainingRecordService extends ILogicService<DailyTrainingRecord, String> {

    List<String> generateRandomQuestionsByBankCode(String questionBankCode);

    /**
     * 生成随机题目用于日常训练
     *
     * @param username         用户名
     * @param questionBankCode 题库编码
     * @return 包含训练记录和题目的结果
     */
    Map<String, Object> generateRandomQuestions(String username, String questionBankCode);


    /**
     * 每日答题
     * @param source        来源
     * @param currentUsername   登录人
     * @return
     */
    JsonResponse dailyQuestion(String source, String currentUsername);

    /**
     * 提交每一个答案
     * @param source
     * @param currentUsername
     * @param detail
     * @return
     */
    JsonResponse submitAnswer(String source, String currentUsername, TrainingAnswerDetail detail);
}