package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ISystemService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-04-23
 * @desc 考试范围群组 中 人员信息Service
 **/
public interface IExamRangeUserInfoService extends ISystemService<ExamRangeUserInfo, String> {


    /**
     * 将选出的人保存到人员信息表中
     *
     * @param examRangeUserInfos
     * @return List<ExamRangeUserInfo>
     */
    JsonResponse saveExamRangeUserInfo(String groupId, List<ExamRangeUserInfo> examRangeUserInfos);

    /**
     * 根据groupId删除人员信息
     *
     * @param groupId
     * @return
     */
    JsonResponse deleteByGroupId(String groupId);

    /**
     * 根据Id删除人员信息 洛阳满意度的删除考试管理系统的数据
     * uums的删除uums中的数据
     *
     * @param
     * @return
     */
    JsonResponse delById(String id);

    /**
     * 根据Id批量删除人员信息 洛阳满意度的删除考试管理系统的数据
     * uums的删除uums中的数据
     *
     * @param
     * @return
     */
    JsonResponse delByIds(String[] ids);

    /**
     * 根据groupId查询属于该群组的所有用户
     *
     * @param groupId
     * @return
     */
    JsonResponse findByGroupId(int page,int size,String groupId,String username,String truename,String displayName);


    /**
     * 根据groupId查询属于该群组的所有用户不分页
     *
     * @param groupId
     * @return
     */
    List<Map<String,Object>> findByGroupIdNoPage(String groupId);


    /**
     * 查询群组下的人数
     *
     * @return
     */
    Integer countGroupUser(String groupId);

    /**
     * 根据groupId查询属于该群组的所有用户
     *
     * @param groupId
     * @return
     */
    List<ExamRangeUserInfo> findByGroupIdLY(String groupId);


    ExamRangeUserInfo findByUserName(String userName,String examName);

    /**
      * @desc 根据uums的OA账号、群组编码模糊查询群组下的人员信息
      * <AUTHOR>
      */
    JsonResponse findBySidAndUserName(int page,int size,String groupId,Map<String,Object> map);


    JsonResponse importRangeUserInfo(HttpServletRequest request, HttpServletResponse response, String groupId);
}
