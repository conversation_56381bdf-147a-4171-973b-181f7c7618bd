/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <strong>Title : ExamUser</strong><br>
 * <strong>Description : 生成人员关联试卷 </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_user")
@ApiModel(value = "生成人员关联试卷")
@Builder
public class ExamUser extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EXAMUSER") //主键前缀，此为可选项注解
    private String id;
    @Column(length = 250)
    @ApiModelProperty(value = "试卷code",name = "questionBankCode",example = "A-006",required = true)
    private String questionBankCode;

    @Column(length = 250)
    @ApiModelProperty(value = "试卷code",name = "bankCode",example = "hnjjwz4-1",required = true)
    private String bankCode;

    @Column(length = 40)
    @ApiModelProperty(value = "username",name = "username",example = "hadmin",required = true)
    private String username;

    @Column
    @ApiModelProperty(value = "是否完成",name = "isFinish",example = "hadmin",required = true)
    private int isFinish;

    @Column(length = 250)
    @ApiModelProperty(value = "排序",name = "examinationSort",example = "hadmin",required = true)
    private int examinationSort;


}
