package com.simbest.boot.exam.examOnline.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.SystemService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.AuthoritiesConstants;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfo;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfoExcel;
import com.simbest.boot.exam.examOnline.repository.ExamRangeUserInfoRepository;
import com.simbest.boot.exam.examOnline.service.IExamRangeGroupService;
import com.simbest.boot.exam.examOnline.service.IExamRangeUserInfoService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.PageTool;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.util.PaginationHelp;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysUserGroupApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @create 2021-04-23
 * @desc 考试范围群组 中 的人员信息Service实现类
 **/
@Service
@Slf4j
public class ExamRangeUserInfoServiceImpl extends SystemService<ExamRangeUserInfo, String> implements IExamRangeUserInfoService {
    private ExamRangeUserInfoRepository repository;

    @Autowired
    public ExamRangeUserInfoServiceImpl(ExamRangeUserInfoRepository repository) {
        super(repository);
        this.repository = repository;
    }


    private static final String USER_MAPPING = "/action/user/user/";
    private static final String USER_MAPPING2 = "/action/user/group/";
    private static final String SSO = "/sso";
    private static final String SSO2 = "sso/";

    @Autowired
    private AppConfig config;
    //private String uumsAddress="http://localhost:8080/uums";
    @Autowired
    private RsaEncryptor encryptor;

    @Autowired
    private IExamRangeGroupService iExamRangeGroupService;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private UumsSysUserGroupApi  uumsSysUserGroupApi;

    @Autowired
    private PaginationHelp paginationHelp;
    @Autowired
    private IExamRangeUserInfoService examRangeUserInfoService;
    @Autowired
    private ISysFileService fileService;




    /**
     * 保存群组人员信息
     *
     * @param examRangeUserInfos
     * @return
     */
    @Override
    public JsonResponse saveExamRangeUserInfo(String groupId, List<ExamRangeUserInfo> examRangeUserInfos) {
        //洛阳满意度保存人员方式
        if (groupId.equals(Constants.GROUP_CODE_LYXF) || groupId.equals(Constants.GROUP_CODE_LYJG)){
            //保存之前先将之前添加的所有人员清除  避免人员重复
            try {
                deleteByGroupId(groupId);
                saveAll(examRangeUserInfos);
            } catch (Exception e) {
                Exceptions.printException(e);
            }
            return JsonResponse.success("添加成功");
        } else {
            //其它考试保存人员信息到uums中
            for (ExamRangeUserInfo examRangeUserInfo : examRangeUserInfos) {
                Map<String, Object> userGroupMap = new HashMap<>();
                userGroupMap.put("username",examRangeUserInfo.getUserName());
                userGroupMap.put("groupId",examRangeUserInfo.getGroupId());
                userGroupMap.put("enabled",true);
                //判断选择的人员中是否已存在本群组
                int userGroupCount = iExamRangeGroupService.findExamGroupInfoByUUMS(1, 10, userGroupMap);
                if (userGroupCount>0){
                    String message=examRangeUserInfo.getUserName()+"此用户已在群组内";
                    return JsonResponse.fail(message);
                }
                String groupId1 = examRangeUserInfo.getGroupId();
                String userName = examRangeUserInfo.getUserName();
                uumsSysUserGroupApi.create(userName, groupId1, Constants.APP_CODE);
            }
            return JsonResponse.success("操作成功");
            }
    }


    /**
     * 根据群组id删除人员信息
     *
     * @param groupId
     * @return
     */
    @Override
    public JsonResponse deleteByGroupId(String groupId) {
        try {
            repository.deleteByGroupId(groupId);
            return JsonResponse.success("删除人员信息成功");
        } catch (Exception e) {
            Exceptions.printException(e);

        }

        return JsonResponse.success("删除人员信息失败");
    }

    /**
     * 根据groupId查询属于该群组的人员信息
     *
     * @param groupId
     * @return
     */
    @Override
    public JsonResponse findByGroupId(int page,int size,String groupId,String username,String truename,String displayName ) {
        Page<List<Map<String, Object>>> resultData = null;
        //如果为洛阳满意度则查询本考试管理系统的数据库数据
        if (groupId.equals(Constants.GROUP_CODE_LYXF) || groupId.equals(Constants.GROUP_CODE_LYJG)){
            if (username==null) username="";
            if (truename==null) truename="";
            if (displayName==null) displayName="";
            Specification<ExamRangeUserInfo> spec = Specifications.<ExamRangeUserInfo>and()
                    .eq("groupId", groupId)
                    .like("userName","%"+username+"%")
                    .like("userTrueName","%"+truename+"%")
                    .like("userOrgName","%"+displayName+"%")
                    .build();

            Iterable<ExamRangeUserInfo> allGroupUser = examRangeUserInfoService.findAllNoPage(spec);

            List<Map <String,Object>> newUserInfos = new ArrayList<>();
            for (ExamRangeUserInfo examRangeUserInfo : allGroupUser) {
                String id = examRangeUserInfo.getId();
                Map<String, Object> map = new HashMap<>();
                map.put("ID",examRangeUserInfo.getId());
                map.put("TRUENAME",examRangeUserInfo.getUserTrueName());
                map.put("DISPLAY_NAME",examRangeUserInfo.getUserOrgName());
                map.put("USERNAME",examRangeUserInfo.getUserName());
                newUserInfos.add(map);

                examRangeUserInfo.setParamMap(map);
            }

            long listSize = newUserInfos.size();
            Pageable pageable1 = getPageable(page, size, null, null);
            List<Map<String, Object>> listPart = PageTool.pagination(newUserInfos, page, size);
            resultData = new PageImpl(listPart, pageable1, listSize);
            return JsonResponse.success(resultData);
        }else {
            //其它考试从uums中查询群组中人员信息
     //       JsonResponse userByGroup = uumsSysUserinfoApi.findUserByGroup(page, size, "desc","createdTime", Constants.APP_CODE, groupId);

            HashMap<String, Object> map = new HashMap<>();
            map.put("username",username);
            map.put("page",page);
            map.put("size",size);
            String username2 = SecurityUtils.getCurrentUserName();
            String json0= JacksonUtils.obj2json(map);
            log.debug("Http remote request user by username: {}", username2);
            JsonResponse response =  HttpClient.textBody(config.getUumsAddress() + USER_MAPPING + "findUserByGroup"+SSO + "?" + AuthoritiesConstants.SSO_API_USERNAME + "=" + encryptor.encrypt(username2)
                                    + "&" + AuthoritiesConstants.SSO_API_APP_CODE + "=" + Constants.APP_CODE +  "&page=" + page + "&size=" + size + "&direction=desc" + "&properties=created_time" + "&groupId=" + groupId)
                    .json(json0)
                    .asBean(JsonResponse.class);

       /*     HashMap<String, Object> userGroupMap = new HashMap<>();
            userGroupMap.put("groupId",groupId);
            String loginUser = SecurityUtils.getCurrentUserName();
            log.debug("Http remote request user by loginUser: {}", loginUser);
            String json0= JacksonUtils.obj2json(userGroupMap);
            String username1=encryptor.encrypt(loginUser);
            String username2=username1.replace("+","%2B");
            JsonResponse response= HttpClient.textBody(config.getUumsAddress() + USER_MAPPING + "findUserByGroup"+SSO+"?loginuser="+username2+"&appcode="+Constants.APP_CODE)
                    .json( json0 )
                    .param("page",page)
                    .param("size",size)
                    .param("direction","desc")
                    .param("properties","createdTime")
                    .asBean(JsonResponse.class );
            return response;*/
            return response;
        }

    }

    /**
      * 统计当前分组的人数  洛阳满意度相关考试的分组
      * <AUTHOR>
      * @date 2021/6/23

      */
    @Override
    public Integer countGroupUser(String groupId) {
        return repository.countByUserName(groupId);
    }


    /**
     * 根据groupId查询属于该群组的所有用户
     *
     * @param groupId
     * @return
     */
    @Override
    public List<ExamRangeUserInfo> findByGroupIdLY(String groupId) {
      return   repository.findByGroupId(groupId);
    }



    /**
     *根据用户名查询分组人员信息
     * <AUTHOR>
     * @date 2021/12/01
     */
    @Override
    public ExamRangeUserInfo findByUserName(String userName,String examName) {
        return repository.findByUserName(userName,examName);
    }

    /**
     * 根据Id删除人员信息 洛阳满意度的删除考试管理系统的数据
     * uums的删除uums中的数据
     *
     * @param id
     * @return
     */
    @Override
    public JsonResponse delById(String id) {
        //ERU为洛阳满意度考试人员信息的id前缀标识  则删除
        if (id.contains("ERU")){
            try {
                this.deleteById(id);
            } catch (Exception e) {
                Exceptions.printException(e);
            }
            return JsonResponse.success("删除成功");
        }else {
            String username = SecurityUtils.getCurrentUserName();
            log.debug("Http remote request user by username: {}", username);
            JsonResponse response =  HttpClient.post(config.getUumsAddress() + USER_MAPPING2 + SSO2 +"deleteById")
                    .param(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username))
                    .param(AuthoritiesConstants.SSO_API_APP_CODE,Constants.APP_CODE )
                    .param( "id",id)
                    .asBean(JsonResponse.class);
            return response;
        }
    }

    /**
     * 根据Id批量删除人员信息  洛阳满意度的删除考试管理系统的数据
     * uums的删除uums中的数据
     *
     * @param ids
     * @return
     */
    @Override
    public JsonResponse delByIds(String[] ids) {
        //ERU为洛阳满意度考试人员信息的id前缀标识  则删除
        if (ids[1].contains("ERU")) {
            try {
                for (String id : ids) {
                    repository.deleteById(id);
                }
            } catch (Exception e) {
                Exceptions.printException(e);
            }
            return JsonResponse.success("删除成功");
        }else {
            String loginUser = SecurityUtils.getCurrentUserName();
            log.debug("Http remote request user by loginUser: {}", loginUser);
            String json0= JacksonUtils.obj2json(ids);
            String username1=encryptor.encrypt(loginUser);
            String username2=username1.replace("+","%2B");
            JsonResponse response= HttpClient.textBody(config.getUumsAddress() + USER_MAPPING2 + SSO2+"deleteAllByIds"+"?loginuser="+username2+"&appcode="+Constants.APP_CODE)
                    .json( json0 )
                    .asBean(JsonResponse.class );
            return response;
        }

    }

    /**
      * @desc 根据uums的OA账号、群组编码模糊查询群组下的人员信息
      * <AUTHOR>
      */
    @Override
    public JsonResponse findBySidAndUserName(int page,int size,String groupId, Map<String, Object> map) {
        map.put("page",page);
        map.put("size",size);
        String username = SecurityUtils.getCurrentUserName();
        String json0= JacksonUtils.obj2json(map);
        log.debug("Http remote request user by username: {}", username);
        JsonResponse response =  HttpClient.textBody(config.getUumsAddress() + USER_MAPPING + "findUserByGroup"+SSO + "?" + AuthoritiesConstants.SSO_API_USERNAME + "=" + encryptor.encrypt(username)
                + "&" + AuthoritiesConstants.SSO_API_APP_CODE + "=" + Constants.APP_CODE +  "&page=" + page + "&size=" + size + "&direction=desc" + "&properties=created_time" + "&groupId=" + groupId)
                .json(json0)
                .asBean(JsonResponse.class);
        return response;
    }

    @Override
    public JsonResponse importRangeUserInfo(HttpServletRequest request, HttpServletResponse response, String groupId) {
        List<ExamRangeUserInfo> examRangeUserInfos= new ArrayList<>();
        MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
        StringBuilder stringBuilder = new StringBuilder();
        for (MultipartFile uploadfile : multipartFiles.values()) {
            UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, request.getParameter("pmInsType"), request.getParameter("pmInsId"), request.getParameter("pmInsTypePart"), ExamRangeUserInfoExcel.class, "Sheet1");
            List<ExamRangeUserInfoExcel> listData = uploadFileResponse.getListData();
            for (ExamRangeUserInfoExcel listDatum : listData) {
                SimpleUser simpleUser = uumsSysUserinfoApi.findByUsername(listDatum.getUserName(), Constants.APP_CODE);
                if (simpleUser==null){
                    stringBuilder.append(listDatum.getUserName()).append(";");
                }else {
                    ExamRangeUserInfo examRangeUserInfo = new ExamRangeUserInfo();
                    examRangeUserInfo.setGroupId(groupId);
                    examRangeUserInfo.setUserName(simpleUser.getUsername());
                    examRangeUserInfos.add(examRangeUserInfo);

                }
            }
        }
        if (StrUtil.isEmpty(stringBuilder.toString())){
            //洛阳满意度保存人员方式
            if (groupId.equals(Constants.GROUP_CODE_LYXF) || groupId.equals(Constants.GROUP_CODE_LYJG)){
                //保存之前先将之前添加的所有人员清除  避免人员重复
                try {
                    deleteByGroupId(groupId);
                    saveAll(examRangeUserInfos);
                    return JsonResponse.success("导入成功！");
                } catch (Exception e) {
                    Exceptions.printException(e);
                    return JsonResponse.success("导入异常！");

                }
            } else {
                //其它考试保存人员信息到uums中
                for (ExamRangeUserInfo examRangeUserInfo : examRangeUserInfos) {
                    Map<String, Object> userGroupMap = new HashMap<>();
                    userGroupMap.put("username",examRangeUserInfo.getUserName());
                    userGroupMap.put("groupId",examRangeUserInfo.getGroupId());
                    userGroupMap.put("enabled",true);
                    //判断选择的人员中是否已存在本群组
                    int userGroupCount = iExamRangeGroupService.findExamGroupInfoByUUMS(1, 10, userGroupMap);
                    if (userGroupCount>0){
                        String message=examRangeUserInfo.getUserName()+"此用户已在群组内";
                        return JsonResponse.fail(message);
                    }
                    String groupId1 = examRangeUserInfo.getGroupId();
                    String userName = examRangeUserInfo.getUserName();
                    uumsSysUserGroupApi.create(userName, groupId1, Constants.APP_CODE);
                }
                return JsonResponse.success("操作成功");
            }
        }else {
            return JsonResponse.fail(stringBuilder.toString());
        }

    }

    /**
      * @desc 根据群组ID查询uums群组所有人员不分页
      * <AUTHOR>
      */
    @Override
    public List<Map<String,Object>> findByGroupIdNoPage(String groupId) {
        Map<String, Object> map = new HashMap<>();
        map.put("groupId",groupId);
        String username = SecurityUtils.getCurrentUserName();
        String json0= JacksonUtils.obj2json(map);
        log.debug("Http remote request user by username: {}", username);
        JsonResponse response =  HttpClient.textBody(config.getUumsAddress() + USER_MAPPING2 +SSO2 +"findAllNoPage"+ "?" + AuthoritiesConstants.SSO_API_USERNAME + "=" + encryptor.encrypt(username)
                + "&" + AuthoritiesConstants.SSO_API_APP_CODE + "=" + Constants.APP_CODE )
                .json(json0)
                .asBean(JsonResponse.class);
        List<Map<String,Object>> data = (List<Map<String,Object>>) response.getData();
        return data;
    }
}
