package com.simbest.boot.exam.examStatistics.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.exam.examStatistics.model.Option;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfoVO extends Option implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "OA账户")
    private String username;

    @Excel(name = "姓名")
    private String truename;

    @Excel(name = "手机号")
    private String phone;

    @Excel(name = "公司")
    private String companyName;

    @Excel(name = "部门")
    private String departmentName;

    @Excel(name = "组织")
    private String orgName;

    @Excel(name = "职位")
    private String positionName;

    @Excel(name = "得分")
    private String score;

}
