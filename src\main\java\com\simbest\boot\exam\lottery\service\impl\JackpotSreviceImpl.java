/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.service.impl;/**
 * Created by KZH on 2019/12/9 16:20.
 */

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.lottery.model.Jackpot;
import com.simbest.boot.exam.lottery.repository.JackpotRepository;
import com.simbest.boot.exam.lottery.service.IJackpotSrevice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-12-09 16:20
 * @desc 奖池实现
 **/
@Service
@Slf4j
public class JackpotSreviceImpl  extends LogicService<Jackpot,String> implements IJackpotSrevice {

    private JackpotRepository jackpotRepository;


    @Autowired
    public JackpotSreviceImpl(JackpotRepository repository){
        super(repository);
        this.jackpotRepository=repository;

    }

    /**
     * 查询奖池中是否存在剩余奖项
     * @return 是或否
     */
    @Override
    public Jackpot findJackpotIsResidue() {
        List<Jackpot> allNoPage = this.findAllNoPage();
        Jackpot jackpot = allNoPage.get(0);

        return jackpot;
    }
}
