/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examStatistics.model;/**
 * Created by KZH on 2019/11/27 9:06.
 */

import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2019-11-27 9:06
 * @desc 以部门为单位统计信息
 **/
@Data
public class StatisticDepartmentExam {

    @Setter
    @Getter
    @ApiModelProperty(value = "公司")
    private String company;

    @Setter
    @Getter
    @ApiModelProperty(value = "部门")
    private String department;

    @Setter
    @Getter
    @ApiModelProperty(value = "参加人数")
    private String peopleNumber;

    @Setter
    @Getter
    @ApiModelProperty(value = "未参加人数")
    private String NoPeopleNumber;

    @Setter
    @Getter
    @ApiModelProperty(value = "参加人数百分比")
    private String percentage;

    @Setter
    @Getter
    @ApiModelProperty(value = "未参加人数百分比")
    private String NoPercentage;


}
