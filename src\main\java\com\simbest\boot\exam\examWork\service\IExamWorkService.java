/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examWork.service;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examWork.model.ExamWork;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
public interface IExamWorkService extends ILogicService<ExamWork,String> {

    /**
     * 查询待办
     * @param page 页码
     * @param size 数量
     * @param source 来源
     * @param currentUserCode 当前人OA账号
     * @param title 考试名称
     * @return
     */
    JsonResponse queryMyTask(Integer page, Integer size, String source, String currentUserCode, String title);


    JsonResponse queryMyTaskMJSF(Integer page, Integer size, String source, String currentUserCode, String title);

    JsonResponse queryMyJoinMJSF(Integer page, Integer size, String source, String currentUserCode, String title);

    JsonResponse queryMyTask2023(Integer page, Integer size, String source, String currentUserCode, String title, String examCode);

    /**
     * 查询已办
     * @param page 页码
     * @param size 数量
     * @param source 来源
     * @param currentUserCode 当前人OA账号
     * @param title 考试名称
     * @return
     */
    JsonResponse queryMyJoin( Integer page, Integer size, String source, String currentUserCode, String title);

    /**
     * 办理
     * @param source 来源
     * @param currentUserCode 当前人OA账号
     * @param id 待办id
     * @return
     */
    JsonResponse dealWith(  String source, String currentUserCode, String id);

    /**
     * 创建待办工作

     * @param title 代办标题
     * @return
     */
    boolean createToDo(String company,String title,String questionName,String workType);

    /**
     * 批量推送统一待办
     * @return
     */
    JsonResponse sendUnifiedToDo(List<ExamWork> list);

    /**
     * 自定义查询全部 带条件 分页
     * @param pageable
     * @param examWork
     * @return
     */
    JsonResponse findAllCustom(Pageable pageable,ExamWork examWork);

    /**
     * 自定义查询全部 带条件 不分页
     * @param examWork
     * @return
     */
    List<ExamWork> findAllCustom(ExamWork examWork);

    /**
     * 短信催办
     * @param list
     * @return
     */
    JsonResponse urgedToDo(List<ExamWork> list);


    /**
      *根据用户名查待办信息
      * <AUTHOR>
      * @date 2021/6/24
      */
    ExamWork findByUsername(String username,String examCode);

    /**
     * 根据用户名、待办类型获取考试工作信息
     */
    List<ExamWork> findByUsernameAndWorkType(String username, String workType);

    /**
     * 获取未完成的办理工单
     */
    List<Map<String, Object>> findUnfinishedExamList(String examCode);

    /**
      *获取洛阳满意度县分公司人员的待办信息
      */
    List<ExamWork> findExamWorkByWorkTypeLYXF();

    /**
     *获取洛阳满意度机关部门人员的待办信息
     */
    List<ExamWork> findExamWorkByWorkTypeLYJG();

    /**
     *根据用户名查待办信息
     * <AUTHOR>
     * @date 2021/6/24
     */
    String findByUsernameApp(String username);

    /**
     *根据用户名，待办类型查询待办信息
     * <AUTHOR>
     * @date 2021/6/24
     */
    ExamWork findByTransactorCodeAndWorkType(String transactorCode, String workType);

    /**
     * 根据用户名，考试编码判断是否有待办
     * <AUTHOR>
     * @date 2021/12/27
     * @param transactorCode
     * @param examCode
     * @return
     */
    boolean findByTransactorCodeAndExamCode(String transactorCode, String examCode);

    /**
     * 检测是否存在待办
     * @param username
     * @param examCode
     * @return
     */
    boolean checkIsNotDone(String username, String examCode);
}
