package com.simbest.boot.exam.flow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.PredicateBuilder;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.exam.briefDistribution.service.ISyncCommonService;
import com.simbest.boot.exam.flow.conf.TodoConfig;
import com.simbest.boot.exam.flow.model.SysTaskInfo;
import com.simbest.boot.exam.flow.repository.SysTaskInfoRepository;
import com.simbest.boot.exam.flow.service.ISysTaskInfoService;
import com.simbest.boot.exam.todo.model.UsTodoModel;
import com.simbest.boot.exam.todo.service.IUsTodoModelService;
import com.simbest.boot.exam.todo.service.TodoBusOperatorService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.OperateLogTool;
import com.simbest.boot.exam.util.ToDoEnum;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;

//import com.simbest.boot.wfdriver.task.model.TaskCallbackLog;
//import com.simbest.boot.wfdriver.task.service.ITaskCallBackLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @ClassName: SysTaskInfoServiceImpl
 * @description:
 * @author: ZHAOBO
 * @create: 2024-07-09 17:33
 */
@Service
@Slf4j
public class SysTaskInfoServiceImpl extends LogicService<SysTaskInfo, String> implements ISysTaskInfoService {

    private SysTaskInfoRepository repository;

    public SysTaskInfoServiceImpl(SysTaskInfoRepository repository) {
        super(repository);
        this.repository = repository;
    }

    String param1 = "/action/task";

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    IUsTodoModelService usTodoModelService;

    @Autowired
    ISyncCommonService syncCommonService;


    @Autowired
    TodoBusOperatorService todoBusOperatorService;

    @Autowired
    SysDictValueService sysDictValueService;

    @Autowired
    AppConfig appConfig;

    @Autowired
    ISysTaskInfoService iSysTaskInfoService;

    @Autowired
    TodoConfig todoConfig;


    @Autowired
    UumsSysAppApi uumsSysAppApi;
//    @Autowired
//    ITaskCallBackLogService taskCallBackLogService;

    @Autowired
    private MsgPostOperatorService msgPostOperatorService;
    public static final Map<String, String> todoHtml;

    static {
        Map<String, String> todoHtmlTmp = Maps.newConcurrentMap();
        todoHtmlTmp.put("A", "/html/exammanage/examCorrect.html");
        todoHtmlTmp.put("P", "/html/briefDistribution/trans.html");//豫起奋发-简报派发办理
        todoHtml = todoHtmlTmp;
    }

    /**
     * 查询待办
     *
     * @param page            页码
     * @param rows            数量
     * @param source          来源
     * @param title           标题
     * @param pmInsType       单据类型
     * @param currentUserCode 当前人
     * @return
     */
    @Override
    public JsonResponse myTaskToDo(Integer page, Integer rows, String source, String title, String pmInsType, String applyNumber, String currentUserCode, String pmInsId) {

        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/myTaskToDo";
        String params = "pageindex=" + page.toString() + ",pagesize=" + rows.toString() + ",title=" + title + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        Page<SysTaskInfo> taskPage = null;
        try {

            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }

            Pageable pageable = this.getPageable(page, rows, Sort.Direction.DESC.toString(), "startTime");
            Specification<SysTaskInfo> build = Specifications.<SysTaskInfo>and()
                    .eq("enabled", Boolean.TRUE)
                    .like(StrUtil.isNotEmpty(title), "title", "%" + title + "%")
                    .eq(StrUtil.isNotEmpty(pmInsType), pmInsType)
                    .like(StrUtil.isNotEmpty(applyNumber), "applyNumber", "%" + applyNumber + "%")
                    .eq("taskUsername", SecurityUtils.getCurrentUserName())
                    .eq(StrUtil.isNotEmpty(pmInsId), pmInsId)
                    .eq("status", 10)
                    .build();
            taskPage = this.findAll(build, pageable);

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.defaultErrorResponse();
        } finally {
            /**操作日志记录**/
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(taskPage);
    }

    /**
     * 查询已办
     *
     * @param page            页码
     * @param rows            数量
     * @param source          来源
     * @param title           标题
     * @param pmInsType       单据类型
     * @param currentUserCode 当前人
     * @return
     */
    @Override
    public JsonResponse myJoin(Integer page, Integer rows, String source, String title, String pmInsType, String applyNumber, String currentUserCode) {
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/myJoin";
        String params = "pageindex=" + page.toString() + ",pagesize=" + rows.toString() + ",title=" + title + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        Page<SysTaskInfo> taskPage = null;
        try {

            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            Pageable pageable = this.getPageable(page, rows, Sort.Direction.DESC.toString(), "startTime");
            Specification<SysTaskInfo> build = Specifications.<SysTaskInfo>and()
                    .eq("enabled", Boolean.TRUE)
                    .like(StrUtil.isNotEmpty(title), "title", "%" + title + "%")
                    .eq(StrUtil.isNotEmpty(pmInsType), pmInsType)
                    .like(StrUtil.isNotEmpty(applyNumber), "applyNumber", "%" + applyNumber + "%")
                    .eq("taskUsername", SecurityUtils.getCurrentUserName())
                    .eq("status", 12)
                    .build();
            taskPage = this.findAll(build, pageable);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.getMessage());
            Exceptions.printException(e);
            return JsonResponse.defaultErrorResponse();
        } finally {
            /**操作日志记录**/
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(taskPage);
    }

    /**
     * 流程启动
     *
     * @param map
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> startProcess(Map<String, Object> map) {

        String pmInsId = MapUtil.getStr(map, "pmInsId");
        String currentUserame = MapUtil.getStr(map, "currentUserame");
        String businessId = MapUtil.getStr(map, "businessId");
        String title = MapUtil.getStr(map, "title");
        String taskUsername = MapUtil.getStr(map, "taskUsername");
        Assert.notNull(pmInsId, "主单据编码不能为空！");
        Assert.notNull(taskUsername, "待办人不能为空！");
        Assert.notNull(currentUserame, "当前登录人不能为空！");
        Assert.notNull(businessId, "主数据id不能为空！");
        Assert.notNull(title, "工单标题不能为空！");
        SimpleUser createUser = uumsSysUserinfoApi.findByUsername(currentUserame, Constants.APP_CODE);
        SimpleUser taskUser = uumsSysUserinfoApi.findByUsername(taskUsername, Constants.APP_CODE);
        Assert.notNull(taskUser, "查询当前办理人信息失败！");
        Assert.notNull(createUser, "查询派发人办理人信息失败！");

        String pmInsType = pmInsId.replaceAll("[^(A-Za-z)]", "");
        SysTaskInfo taskInfo = SysTaskInfo.builder()
                .pmInsId(pmInsId)
                .pmInsType(pmInsType)
                .title(title)
                .businessId(businessId)
                .processDefId(Constants.PROCESS_DEF_ID_P)
                .processDefName(Constants.PROCESS_DEF_NAME_P)
                .activityDefId(Constants.ACTIVITY_START)
                .activityDefName(Constants.ACTIVITY_NAME_START)
                .formTaskId("-1")
                .status(10)
                .displayName(taskUser.getAuthOrgs().iterator().next().getDisplayName())
                .createUsername(createUser.getUsername())
                .createTrueName(createUser.getTruename())
                .taskUsername(taskUser.getUsername())
                .taskTrueName(taskUser.getTruename())
                .startTime(LocalDateTime.now())
                .processInstId(UUID.randomUUID().toString())
                .taskId(UUID.randomUUID().toString())
                .build();
        taskInfo.setApplyPhone(taskUser.getPreferredMobile());
        taskInfo.setBelongCompanyCode(taskUser.getBelongCompanyCode());
        taskInfo.setBelongCompanyName(taskUser.getBelongCompanyName());
        taskInfo.setBelongDepartmentName(taskUser.getBelongDepartmentName());
        taskInfo.setBelongDepartmentCode(taskUser.getBelongCompanyCode());
        taskInfo.setBelongCompanyTypeDictDesc(taskUser.getBelongCompanyTypeDictDesc());
        taskInfo.setBelongCompanyTypeDictValue(taskUser.getBelongCompanyTypeDictValue());
        taskInfo.setBelongOrgCode(taskUser.getBelongOrgCode());
        taskInfo.setBelongOrgName(taskUser.getBelongOrgName());

        taskInfo.setCreator(createUser.getUsername());
        taskInfo.setModifier(createUser.getUsername());
        taskInfo.setEnabled(Boolean.TRUE);
        this.insert(taskInfo);
        //推送统一待办
        //代办回调的路径
        String urlParams = null;

        String oaHtmlUrl = appConfig.getAppHostPort() + "/" + Constants.APP_CODE + todoHtml.get(pmInsType);
        urlParams = "?type=task&location=" + taskInfo.getActivityDefId()
                + "&pmInsId=" + taskInfo.getPmInsId()
                + "&taskId=" + taskInfo.getTaskId()
                + "&pmInsType=" + pmInsType
                + "&id=" + taskInfo.getId()
                + "&currentUserName=" + taskUsername
                + "&name=auditVal&appcode=exam&from=oa";
        UsTodoModel usTodoModel = new UsTodoModel();
        usTodoModel.setBusinessKey(taskInfo.getId());
        usTodoModel.setBusinessStatusId(taskInfo.getId());
        usTodoModel.setProcessInstanceId(taskInfo.getProcessInstId());
        usTodoModel.setWorkItemId(taskInfo.getTaskId());
        usTodoModel.setCreator(taskInfo.getCreator());
        usTodoModel.setCreatedTime(taskInfo.getCreatedTime());
        usTodoModel.setModifiedTime(taskInfo.getModifiedTime());
        usTodoModel.setModifier(taskInfo.getModifier());
        usTodoModel.setUserName(taskInfo.getTaskUsername());
        usTodoModel.setSender(taskInfo.getCreateUsername());
        usTodoModel.setTitle(taskInfo.getTitle());
        usTodoModel.setTypeStatus(ToDoEnum.open.getValue());
        usTodoModel.setOaHtmlUrl(oaHtmlUrl);
        usTodoModel.setUrlParams(urlParams);
        usTodoModel.setWorkFlag(true);
        usTodoModel.setEnabled(true);
        usTodoModel.setSendFlag(false);
        usTodoModel.setSendDate(LocalDateTime.now());
        //false代表不推送  true推送
        LocalDateTime callbackStartDate = LocalDateTime.now();

        Boolean isTodoFlag = false;
        Boolean isPostMsg = false;
        SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode( Constants.APP_CODE,taskInfo.getCreateUsername() );
        if ( simpleApp != null ){
            isTodoFlag = simpleApp.getTodoOpen();
            isPostMsg = simpleApp.getIsSendMsg();
        }
        //TODO 创建待办
        if ( isTodoFlag ) {
            log.warn( "TodoOpenServiceImpl>>>>>>>【{}】", JacksonUtils.obj2json( usTodoModel ) );
            //推送统一待办
            todoBusOperatorService.openTodo(usTodoModel);
        }
        if (isPostMsg){
            Boolean isPostMsgOK = false;
            if ( isPostMsg && StringUtils.isNotEmpty(taskInfo.getTaskUsername()) ){

                /**准备发送短息**/
                if ( StringUtils.isNotEmpty( taskInfo.getTaskUsername()) ){
                    /**准备审批短信模板数据**/
//                     Map<String, String> paramMap = Maps.newHashMap();
//                     paramMap.put("appName", Constants.APP_NAME);
//                     paramMap.put("fromUser", "hadmin");
//                     paramMap.put("itemSubject", businessStatus.getReceiptTitle());
//                     String msg = MessageEnum.MT000001.getMessage(paramMap);
                    String msg ="【"+Constants.APP_NAME+"】:您好！您已收到"+taskInfo.getTitle()+"，请审阅！";
                    isPostMsgOK = msgPostOperatorService.postMsg( readyParams( taskInfo.getTaskUsername(),msg) );
                }
            }
            /**记录发送短信日志操作**/
            if ( !isPostMsgOK ){//
                log.error("--->>>Message短信发送失败，用户：{}，手机号：{}",taskUser.getUsername(),taskInfo.getApplyPhone());
                taskInfo.setMessageStatus("2");
            }else {
                log.info("--->>>Message短信发送成功，用户：{}，手机号：{}",taskUser.getUsername(),taskInfo.getApplyPhone());
                taskInfo.setMessageStatus("1");
            }
            this.update(taskInfo);
            log.warn( "TodoOpenServiceImpl>>>>>>>【{}】", JacksonUtils.obj2json( usTodoModel ) );
        }
        Map<String, Object> resultMap = Dict.create()
                .set("processInstId", taskInfo.getProcessInstId())
                .set("id",taskInfo.getId())
                .set("taskId", taskInfo.getTaskId());

        return resultMap;
    }

    /**
     * 异步流转更新工单状态为办理中
     *
     * @param taskId
     * @return
     */
    @Override
    public int updateTaskStatus(String taskId) {
        int i = 0;
        SysTaskInfo sysTaskInfo = this.findByTaskId(taskId);
        Assert.notNull(sysTaskInfo, "未查询到待办信息！");
        Assert.isTrue(sysTaskInfo.getStatus() != 12, "当前工单已办理，请勿重复办理");
        sysTaskInfo.setStatus(100);
        this.update(sysTaskInfo);
        //核销统一待办
        boolean flag = false;
        UsTodoModel usTodoModel = usTodoModelService.getTodoByKey(sysTaskInfo.getId());
        if (usTodoModel != null) {
            usTodoModel.setModifier(sysTaskInfo.getModifier());
            usTodoModel.setModifiedTime(LocalDateTime.now());
            usTodoModel.setTypeStatus(ToDoEnum.close.getValue());
        }
        UsTodoModel resultModel = usTodoModelService.update(usTodoModel);
        if (resultModel != null) {
            flag = true;
            todoBusOperatorService.closeTodo(usTodoModel);
        }
        todoBusOperatorService.closeTodo(usTodoModel);
        i = 1;
        return i;
    }


    /**
     * 根据环节实例id查询环节办理信息
     *
     * @param taskId 任务办理id
     * @return
     */
    @Override
    public SysTaskInfo findByTaskId(String taskId) {
        Specification<SysTaskInfo> build = Specifications.<SysTaskInfo>and()
                .eq("enabled", Boolean.TRUE)
                .eq("taskId", taskId)
                .build();
        List<SysTaskInfo> sysTaskInfos = this.findAllNoPage(build, Sort.by(Sort.Direction.DESC, "createdTime"));
        return CollectionUtil.isNotEmpty(sysTaskInfos) ? sysTaskInfos.get(0) : null;
    }

    @Override
    public int finishTaskInfo(String taskId, String nextActivityDefId, String nextActivityDefName) {
        int ret = 0;
        try {
            Assert.notNull(taskId, "任务实例id不能为空！");
            SysTaskInfo taskInfo = this.findByTaskId(taskId);
            Assert.notNull(taskInfo, "获取待办信息失败！");
            Assert.isTrue(taskInfo.getStatus() != 12, "该工单已被办理，请勿重复办理！");
            //结束当前环节
            taskInfo.setNextActivityDefId(nextActivityDefId);
            taskInfo.setNextActivityDefName(nextActivityDefName);
            taskInfo.setEndTime(LocalDateTime.now());
            taskInfo.setStatus(12);
            this.update(taskInfo);
            ret = 1;
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return ret;
    }

    @Override
    public List<SysTaskInfo> queryTodoTaskInfoByParam(String applyNumber, String belongCompanyName, String belongDepartmentName) {
        List<SysTaskInfo> sysTaskInfoList = new ArrayList<>();
        try {

            Specification<SysTaskInfo> build = Specifications.<SysTaskInfo>and()
                    .eq("enabled", Boolean.TRUE)
                    .eq("applyNumber", applyNumber)
                    .eq("taskUsername", SecurityUtils.getCurrentUserName())
                    .eq(StrUtil.isNotEmpty(belongCompanyName), belongCompanyName, belongCompanyName)
                    .eq(StrUtil.isNotEmpty(belongCompanyName), belongCompanyName, belongCompanyName)
                    .eq("status", 10)
                    .build();
            sysTaskInfoList = this.findAllNoPage(build);
        } catch (Exception e) {
            Exceptions.printException(e);
            return new ArrayList<>();
        }
        return sysTaskInfoList;
    }

    @Override
    public JsonResponse done(String taskId) {
        //核销统一待办
        this.updateTaskStatus(taskId);
        //结束流程流转
        syncCommonService.completeTask(taskId);
        return JsonResponse.defaultSuccessResponse();
    }

    @Override
    public List<SysTaskInfo>  findByPmInsId(String pmInsId, String type) {
        PredicateBuilder<SysTaskInfo> predicateBuilder = Specifications.<SysTaskInfo>and()
                .eq("enabled", Boolean.TRUE)
                .eq("pmInsId", pmInsId);
        //type (taskToDo 待办 ,myDone 已办,null 全查 )
        if (StrUtil.isNotEmpty(type) && "taskToDo".equals(type)) {
            predicateBuilder.eq("status", 10);
        } else if (StrUtil.isNotEmpty(type) && "myDone".equals(type)) {
            predicateBuilder.eq("status", 12);
        }
        Specification<SysTaskInfo> build = predicateBuilder.build();
        List<SysTaskInfo> sysTaskInfos = this.findAllNoPage(build);
        return sysTaskInfos ;
    }

    @Override
    public Page<SysTaskInfo> findByPmInsIdPage(String pmInsId, String type, Integer page, Integer rows) {
        Pageable pageable = this.getPageable(page, rows, Sort.Direction.DESC.toString(), "startTime");
        PredicateBuilder<SysTaskInfo> predicateBuilder = Specifications.<SysTaskInfo>and()
                .eq("enabled", Boolean.TRUE)
                .eq("pmInsId", pmInsId);
        //type (taskToDo 待办 ,myDone 已办,null 全查 )
        if (StrUtil.isNotEmpty(type) && "taskToDo".equals(type)) {
            predicateBuilder.eq("status", 10);
        } else if (StrUtil.isNotEmpty(type) && "myDone".equals(type)) {
            predicateBuilder.eq("status", 12);
        }
        Specification<SysTaskInfo> build = predicateBuilder.build();
        Page<SysTaskInfo> sysTaskInfos = this.findAll(build, pageable);
        return sysTaskInfos;
    }

    @Override
    public List<SysTaskInfo> findTaskByUserName(String currentUserName) {
        Specification<SysTaskInfo> build = Specifications.<SysTaskInfo>and()
                .eq("enabled", Boolean.TRUE)
                .eq("taskUsername", currentUserName)
                .eq("status", "10")
                .build();
        List<SysTaskInfo> taskInfos = this.findAllNoPage(build, Sort.by(Sort.Direction.DESC, "createdTime"));
        return taskInfos;
    }

    /**
     * 准备短信对象
     * @param sendUser 发送人
     * @param msg 短信内容
     * @return
     */
    public static ShrotMsg readyParams(String sendUser, String msg){
        ShrotMsg shrotMsg = new ShrotMsg();
        Content content = new Content();
        Set<Content> contentSet = new HashSet<Content>();
        shrotMsg.setAppCode( Constants.APP_CODE );
        content.setUsername( sendUser );
        content.setMsgContent( msg );
        content.setImmediately( true );
        content.setSmsPriority( 1 );
        contentSet.add( content );
        shrotMsg.setContents( contentSet );
        return shrotMsg;
    }

}
