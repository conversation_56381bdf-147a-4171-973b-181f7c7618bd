/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service;/**
 * Created by KZH on 2019/10/8 15:12.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.model.ExamQuestionImport;
import com.simbest.boot.sys.model.SysDictValue;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:12
 * @desc 题目
 **/
public interface IExamQuestionService extends ILogicService<ExamQuestion,String> {

    /**
     * 根据dictType获取数据字典值 有特殊处理
     * @param dictType
     * @return
     */
    List<SysDictValue> findDictValue(String dictType);

    /**
     * 根据 questionBankCode 获取试题
     * @param questionBankCode
     * @return
     */
    List<ExamQuestion> findAllByQuestionBankCode(String questionBankCode);

    /**
     * 力量大厦问卷特殊抽取试题规则
     * @param questionBankCode
     * @return
     */
    List<ExamQuestion> findAllByQuestionBankCodeAndPowerBuildingExtr(String questionBankCode);

    List<ExamQuestion> findAllByQuestionBankCodeRandom(String questionBankCode,String size,String questionType);

    /**
     * 根据问题code集合获取题目详细数据
     * @param questionCodeList
     * @return
     */
    List<ExamQuestion> findAllByQuestionBankCode(List<String> questionCodeList);    /**
     * 根据问题ID集合获取题目详细数据
     * @param questionIdList
     * @return
     */
    List<ExamQuestion> findAllByQuestionId(List<String> questionIdList);

    /**
     * 新增试题
     * @param sumMaps
     * @return
     */
    JsonResponse importQuestion(Map<String, Object> sumMaps);

    /**
     * 根据题目编码获取试题
     * @param questionCode
     * @return
     */
    ExamQuestion findAllByQuestionCode(String questionCode);


    /**
     * 导入
     * @param request
     * @param response
     */
    List<ExamQuestionImport> importExcel(HttpServletRequest request, HttpServletResponse response);

    /**
     * 保存导入的信息
     * @param examQuestion
     * @return
     */
    JsonResponse saveExamQuestionList(ExamQuestion examQuestion);

    /**
     * 查询所有试题信息
     * @param examQuestion
     * @return
     */
    JsonResponse findAllExamQuestion(ExamQuestion examQuestion, Pageable pageable);

    /**
     * 根据题库编码获取所有题目编号及分组信息
     * @param questionBankCode
     * @return
     */
    List<Map<String, Object>> findGroupByQuestionBankCode(String questionBankCode);
    List<Map<String, Object>> findByQuestionBankCode(String questionBankCode);

    /**
     * 根据题目编号获取题目
     * @param finallyCodeList       题目编号
     * @return
     */
    List<ExamQuestion> findAllByQuestionCodes(List<String> finallyCodeList);


    /**
     * 根据题库编码和随机提取个数来抽取题目信息
     * @param questionBankCode 题库编码
     * @param count
     * @return
     */
     List<ExamQuestion> getRandomQuestionsByCategory(String questionBankCode, Integer count);

     List<ExamQuestion> getRandomQuestionsByCategoryAndExamNotIn(String questionBankCode, Integer count,List<String> ids);

    List<ExamQuestion> getRandomQuestionsByCategoryWithOutAnswer(String knowledgeQuestionBankCode, Integer challengeQuestionCount);

    /**
     * 根据题目编码获取题目和答案信息
     * @param questionCode  题目编码
     * @return
     */
    ExamQuestion findQuestionAndAnswer(String questionCode);
}
