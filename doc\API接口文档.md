# study.web包API接口文档

本文档记录了study.web包下的所有控制器接口信息，包括接口名称、路径、参数、测试用例和响应示例。

## 1. DailyTrainingRecordController (日常训练记录控制器)

标签: 学习管理-日常训练记录  
基础路径: `/study/dailyTrainingRecord`

### 1.1 每日答题接口

- **接口名称**: 每日答题
- **接口说明**: 每日答题（5次）
- **请求方式**: POST
- **请求路径**: 
  - `/study/dailyTrainingRecord/dailyQuestion`
  - `/study/dailyTrainingRecord/dailyQuestion/sso`
  - `/study/dailyTrainingRecord/dailyQuestion/api`
- **请求参数**:
  | 参数名 | 类型 | 是否必填 | 默认值 | 说明 |
  | --- | --- | --- | --- | --- |
  | source | String | 否 | PC | 来源 |
  | currentUsername | String | 否 | - | 当前用户名（手机端需传加密后的用户名） |

- **响应示例**:
  ```json
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "data": {
      "hasNum": "true" , // 是否有剩余答题次数
      "allNumber": "总答题次数" , // 是否有剩余答题次数  
      "nowNumber": "已答题次数" , // 是否有剩余答题次数
      "questionList": [{
          "questionName": "题目内容", 
          "id": "题目id", 
          "questionCode": "题目编码", 
          "titleDescription": "题目解析", 
          "answerList": [{
              "answerCode": "答案编码 A B C D ", 
              "answerContent": "答案内容", 
              "isCorrect": "是否是正确答案，布尔值1|0"
            }]
        }] ,//题目信息 
      "record": {
        "id": "主键" ,
        "username" : "答题人"
      }
    }
  }
  ```

### 1.2 提交每题信息接口

- **接口名称**: 提交每题信息
- **接口说明**: 提交用户答题详情
- **请求方式**: POST
- **请求路径**: 
  - `/study/dailyTrainingRecord/submitAnswer`
  - `/study/dailyTrainingRecord/submitAnswer/sso`
  - `/study/dailyTrainingRecord/submitAnswer/api`
- **请求参数**:
  | 参数名 | 类型 | 是否必填 | 默认值 | 说明 |
  | --- | --- | --- | --- | --- |
  | source | String | 否 | PC | 来源 |
  | currentUsername | String | 否 | - | 当前用户名 |
  | detail | TrainingAnswerDetail | 是 | - | 答题详情对象 |
  
- **请求体示例**:
  ```json
  {
  "questionId": "EQ888383665682833408" // 问题id,
  "recordId": "DTR892069679764439040" // 记录id,
  "userAnswer": "B" // 用户提交答案，多选用逗号分割 ,
  "seq":"1" //题目序号 
}
  ```

- **响应示例**:
  ```json
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "data": {}
  }
  ```

## 2. TrainingAnswerDetailController (答题详情控制器)

标签: 学习管理-答题详情  
基础路径: `/study/trainingAnswerDetail`

> 该控制器继承自LogicController，拥有基础的CRUD接口，但未定义额外的自定义接口。

## 3. WrongQuestionCollectionController (错题集控制器)

标签: 学习管理-错题集  
基础路径: `/study/wrongQuestionCollection`

### 3.1 根据人员查询错题集接口

- **接口名称**: 根据人员查询错题集
- **接口说明**: 根据用户名查询错题集合
- **请求方式**: POST
- **请求路径**: 
  - `/study/wrongQuestionCollection/findByUsername`
  - `/study/wrongQuestionCollection/findByUsername/sso`
  - `/study/wrongQuestionCollection/findByUsername/api`
- **请求参数**:
  | 参数名 | 类型 | 是否必填 | 默认值 | 说明 |
  | --- | --- | --- | --- | --- |
  | source | String | 否 | PC | 来源 |
  | currentUsername | String | 否 | - | 当前用户名 |
  | page | Integer | 否 | 1 | 页码 |
  | size | Integer | 否 | 10 | 每页大小 |
  | model | WrongQuestionCollection | 是 | - | 查询条件对象 |

- **响应示例**:
  ```json
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "data": {
      "content": [],
      "totalElements": 0,
      "totalPages": 0,
      "numberOfElements": 0
    }
  }
  ```

### 3.2 根据id查询错题信息接口

- **接口名称**: 根据id查询错题信息
- **接口说明**: 根据错题ID查询详细信息
- **请求方式**: POST
- **请求路径**: 
  - `/study/wrongQuestionCollection/findQuestionById`
  - `/study/wrongQuestionCollection/findQuestionById/sso`
  - `/study/wrongQuestionCollection/findQuestionById/api`
- **请求参数**:
  | 参数名 | 类型 | 是否必填 | 默认值 | 说明 |
  | --- | --- | --- | --- | --- |
  | id | String | 是 | - | 错题ID |

- **响应示例**:
  ```json
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "data": {}
  }
  ```

### 3.3 学会了，在错题集中移除

- **接口名称**: 根据id移除错题信息
- **接口说明**: 根据错题ID移除信息
- **请求方式**: POST
- **请求路径**:
  - `/study/wrongQuestionCollection/deleteById`
  - `/study/wrongQuestionCollection/deleteById/sso`
  - `/study/wrongQuestionCollection/deleteById/api`
- **请求参数**:
  | 参数名 | 类型 | 是否必填 | 默认值 | 说明 |
  | --- | --- | --- | --- | --- |
  | id | String | 是 | - | 错题ID |

- **响应示例**:
  ```json
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "data": {}
  }
  ```