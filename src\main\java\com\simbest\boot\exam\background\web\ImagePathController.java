package com.simbest.boot.exam.background.web;

import com.simbest.boot.base.web.controller.GenericController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.background.model.ImagePath;
import com.simbest.boot.exam.background.service.ImagePathService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc: 试卷背景图片控制器
 * @date 2021/7/15  15:42
 */
@Api(description = "试卷背景图片相关", tags = {"试卷背景图片相关全部接口"})
@Slf4j
@RestController
@RequestMapping("/action/image")
public class ImagePathController extends GenericController<ImagePath,String> {

    private ImagePathService service;

    @Autowired
    public ImagePathController(ImagePathService service) {
        super(service);
        this.service = service;
    }

    @ApiOperation(value = "根据试卷编码查询背景图片信息",notes = "根据试卷编码查询背景图片信息")
    @PostMapping(value = {"/findImagePathByExamAppCode","/sso/findImagePathByExamAppCode","/api/findImagePathByExamAppCode"})
    public JsonResponse findImagePathByExamAppCode(@RequestParam String examAppCode,
                                                   @RequestParam int page,
                                                   @RequestParam int size){
        Pageable pageable = service.getPageable(page, size);
        return JsonResponse.success(service.findImagePathByExamAppCode(examAppCode,pageable));
    }

    @ApiOperation(value = "根据试卷编码查询当前试卷使用的背景图片信息",notes = "根据试卷编码查询当前试卷使用的背景图片信息")
    @PostMapping(value = {"/findUseImagePathByExamAppCode","/sso/findUseImagePathByExamAppCode","/api/findUseImagePathByExamAppCode"})
    public JsonResponse findUseImagePathByExamAppCode(@RequestParam(required = false) String examAppCode){
        return JsonResponse.success(service.findUseImagePathByExamAppCode(examAppCode));
    }

    @ApiOperation(value = "保存试卷上传的图片信息",notes = "保存试卷上传的图片信息")
    @PostMapping(value = {"/saveImagePath","/sso/saveImagePath","/api/saveImagePath"})
    //public JsonResponse saveImagePath(@RequestBody List<ImagePath> imagePaths){
    public JsonResponse saveImagePath(@RequestBody Map<String,Object> imagePaths){
        List<Map<String,Object>> imagePaths2 = (List<Map<String, Object>>) imagePaths.get("imagePaths");
        ImagePath  imagePaths1= service.saveImagePath(imagePaths2);
        if (imagePaths1!=null){
            return JsonResponse.success(imagePaths1);
        }
        return JsonResponse.fail("图片上传失败");
    }

    @ApiOperation(value = "保存选择的试卷背景图片信息",notes = "保存选择的试卷背景图片信息")
    @PostMapping(value = {"/saveUseImagePath","/sso/saveUseImagePath","/api/saveUseImagePath"})
    public JsonResponse saveUseImagePath(@RequestBody ImagePath imagePath){
        ImagePath imagePath1 = service.saveUseImagePath(imagePath);
        if (imagePath1!=null){
            return JsonResponse.success("选择成功");
        }
        return JsonResponse.fail("不能启用多个");
    }


}
