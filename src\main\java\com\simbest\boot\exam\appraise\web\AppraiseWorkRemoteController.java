package com.simbest.boot.exam.appraise.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.appraise.model.AppraiseWork;
import com.simbest.boot.exam.appraise.model.dto.AppraiseInfoDTO;
import com.simbest.boot.exam.appraise.service.IAppraiseWorkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 远程接口调用专用 请避免复用接口
 */
@Slf4j
@RestController
@RequestMapping("/action/appraise/work/remote")
public class AppraiseWorkRemoteController extends LogicController<AppraiseWork, String> {

    private final IAppraiseWorkService service;

    public AppraiseWorkRemoteController(IAppraiseWorkService service) {
        super(service);
        this.service = service;
    }

    /**
     * 获取评价工单信息
     *
     * @param username       评价人
     * @param appraiseWorkId 评价工单id
     * @return 评价信息列表
     */
    @RequestMapping(value = {"/getWorkInfo", "/getWorkInfo/sso", "/api/getWorkInfo"})
    public JsonResponse getWorkInfo(@RequestParam("username") String username, @RequestParam("appraiseWorkId") String appraiseWorkId) {
        return JsonResponse.success(service.getWorkInfo(username, appraiseWorkId));
    }

    /**
     * 根据评价信息生成工单，并推送短消息
     *
     * @param dto 外部传入的评价信息
     * @return 返回评价工单信息
     */
    @RequestMapping(value = {"/pushWork", "/pushWork/sso", "/api/pushWork"})
    public JsonResponse pushWork(@RequestBody AppraiseInfoDTO dto) {
        return service.pushWork(dto);
    }

    /**
     * 对已有工单重推短信
     *
     * @param username       评价人
     * @param appraiseWorkId 评价工单id
     * @return true 发送成功 false 发送失败
     */
    @Deprecated
    @RequestMapping(value = {"/retrySendSmsMessage", "/retrySendSmsMessage/sso", "/api/retrySendSmsMessage"})
    public JsonResponse retrySendSmsMessage(@RequestParam("username") String username, @RequestParam("appraiseWorkId") String appraiseWorkId) {
        return JsonResponse.success(service.retrySendSmsMessage(username, appraiseWorkId));
    }

    /**
     * 获取评价模板信息
     *
     * @return 评价模板信息
     */
    @RequestMapping(value = {"/getTemplateInfo", "/getTemplateInfo/sso", "/api/getTemplateInfo"})
    public JsonResponse getTemplateInfo() {
        return JsonResponse.success(service.getTemplateInfo());
    }

    /**
     * 更新外部接口调用路径
     * @param appcode 应用编码
     * @param map 接口调用路径
     * @return 返回更新后的接口调用路径
     */
    @RequestMapping(value = {"/updateInterfaceUrl", "/updateInterfaceUrl/sso", "/api/updateInterfaceUrl"})
    public JsonResponse updateInterfaceUrl(String appcode, @RequestBody Map<String, String> map) {
        String url = map.get("url");
        Assert.notNull(url, "url不能为空, 检查参数！");
        return JsonResponse.success(service.updateInterfaceUrl(appcode, url),null);
    }



}
