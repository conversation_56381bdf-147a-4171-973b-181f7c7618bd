<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org"  style="height: 100%;">
<head>
    <title>OA河纪检调查问卷</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var timeFlag = 0; // 提交数据的标识
        var remainTimeT; // 保存记录的计时器
        $(function(){
            var userAgent = navigator.userAgent;
            /**单点配置**/
            var username = "";
            var gps = getQueryString();
            if(gps.actionType && gps.actionType=="secrecyJoin"){ // 已办
                $(".submitBtn").hide();
            }else{
                $(".submitBtn").show();
                //禁用鼠标右边
                document.oncontextmenu = function(){
                    getparent().mesShow("温馨提示","请手动答题",2000,'red');
                    return false;
                };
                //禁用ctrl+v功能
                document.onkeydown = function(){
                    if (event.ctrlKey && window.event.keyCode==86){

                        getparent().mesShow("温馨提示","请手动答题",2000,'red');
                        return false;
                    }
                };
            }

            // 页面认证问题
            if(gps.from=="oa"){
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.uid,
                    async: false,
                    success: function (ress) {
                        //console.log(ress);
                        username = ress.data.username;
                    }
                });
            }else {
                getCurrent();
                username = web.currentUser.username;
            }

            var questionBlankCode = ''; // 题库编码
            var singleLen = 0, multipleLen = 0, judgeLen = 0, shortLen = 0; // 题目数量
            var singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案
            var examData = {}; // 试卷模板
            var examRecordId = null; // 答题记录的id
            var currentAction = "";
            var isFinishExam = false; // 是否完成考试

            var totalSS = 0; // 考试时间

            // 秒数格式化为hh:mm:ss
            function countdown(totalSS){
                var hh = Math.floor(totalSS/3600).toString().length<2?'0'+Math.floor(totalSS/3600):Math.floor(totalSS/3600);
                var mm = Math.floor((totalSS%3600)/60).toString().length<2?'0'+Math.floor((totalSS%3600)/60):Math.floor((totalSS%3600)/60);
                var ss = Math.floor((totalSS%3600)%60).toString().length<2?'0'+Math.floor((totalSS%3600)%60):Math.floor((totalSS%3600)%60);

                var nowTime = hh+':'+mm+':'+ss;
                return nowTime
            }


            // 题目序号和答案序号格式化,0题目转为汉字，1选项转为大写英文字母
            function formatNumber(type,num) { // 0题目序号  1选项序号
                num = parseInt(num);
                var res = '';
                if (type == 0) {
                    var arr1 = new Array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九');
                    var arr2 = new Array('', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿');//可继续追加更高位转换值
                    if (!num || isNaN(num)) {
                        return "零";
                    }
                    var english = num.toString().split("")
                    var result = "";
                    for (var i = 0; i < english.length; i++) {
                        var des_i = english.length - 1 - i;//倒序排列设值
                        result = arr2[i] + result;
                        var arr1_index = english[des_i];
                        result = arr1[arr1_index] + result;
                    }
                    //将【零千、零百】换成【零】 【十零】换成【十】
                    result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');
                    //合并中间多个零为一个零
                    result = result.replace(/零+/g, '零');
                    //将【零亿】换成【亿】【零万】换成【万】
                    result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
                    //将【亿万】换成【亿】
                    result = result.replace(/亿万/g, '亿');
                    //移除末尾的零
                    result = result.replace(/零+$/, '')
                    //将【零一十】换成【零十】
                    //result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
                    //将【一十】换成【十】
                    result = result.replace(/^一十/g, '十');
                    res = result;

                } else if (type == 1) {
                    res = String.fromCharCode((num-1)+65);
                }
                return res;
            }

            // 获取试卷模板
            ajaxgeneral({
                url: 'action/examAttribute/constructExamLayout',
                data: {"examAppCode": "hnjjwz2"},
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    questionBlankCode = res.data.questionBankCode;
                    currentAction = "test";

                    // 当前用户是否有未完成试卷
                    ajaxgeneral({
                        url: 'action/examInfo/unfinishedExam',
                        type: "POST",
                        data:{
                            publishUsername: username,
                            examAppCode:"hnjjwz2"
                        },
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        success: function (result) {

                            //console.log(result.data!=null);
                            if(result.data!=null) {
                                examRecordId = result.data.id; // 未完成试卷id
                                totalSS = parseInt(result.data.residueTime);

                                var examRecord = result.data.examRecord.split(','); // 题目编号
                                var examAnswer = result.data.examAnswer.split(','); // 保存的答案

                                singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案

                                // 匹配已选择的答案
                                function matchAnswer(lists,mark){
                                    for(var i = 0; i < examRecord.length; i++){
                                        for(var n = 0; n < lists.length; n++){
                                            if(examRecord[i] == lists[n].questionCode){
                                                if(mark != 'shortAnswer'){
                                                    var examAnswerOptions = examAnswer[i].split('/');
                                                    for(var ii = 0; ii < examAnswerOptions.length; ii++){
                                                        for(var nn = 0; nn < lists[n].answerList.length; nn++){
                                                            if(examAnswerOptions[ii] == lists[n].answerList[nn].answerCode){
                                                                lists[n].answerList[nn].isSelected = true;
                                                            }else {
                                                                if (!lists[n].answerList[nn].isSelected) {
                                                                    lists[n].answerList[nn].isSelected = false;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }else{
                                                    lists[n].answerCode = examAnswer[i];
                                                }
                                            }
                                        }
                                    }
                                    return lists;
                                }
                                matchAnswer(res.data.singleQuestionList,'single');
                                matchAnswer(res.data.moreQuestionList,'multiple');
                                matchAnswer(res.data.judgeQuestionList,'judge');
                                matchAnswer(res.data.shortAnswerQuestionList,'shortAnswer');
                            }else{
                                totalSS = parseInt(res.data.setTime)*60;
                            }

                            if(res.data.singleQuestionList && res.data.singleQuestionList.length>0){
                                singleLen = res.data.singleQuestionList.length;
                                for(var i = 0;i < res.data.singleQuestionList.length; i++){
                                    for(var j = 0;j < res.data.singleQuestionList[i].answerList.length; j++){
                                        if(res.data.singleQuestionList[i].answerList[j].isSelected){
                                            singleData.push({
                                                questionCode: res.data.singleQuestionList[i].answerList[j].questionCode,
                                                examAnswer: res.data.singleQuestionList[i].answerList[j].answerCode
                                            });
                                        }
                                    }
                                }
                            }
                            if(res.data.moreQuestionList && res.data.moreQuestionList.length>0){
                                multipleLen = res.data.moreQuestionList.length;
                                for(var i = 0;i < res.data.moreQuestionList.length; i++){
                                    var options = [];
                                    for(var j = 0;j < res.data.moreQuestionList[i].answerList.length; j++){
                                        if(res.data.moreQuestionList[i].answerList[j].isSelected){
                                            options.push(res.data.moreQuestionList[i].answerList[j].answerCode);
                                        }
                                    }
                                    if(options.length > 0){
                                        multipleData.push({
                                            questionCode: res.data.moreQuestionList[i].questionCode,
                                            examAnswer: options.join("/")
                                        });
                                    }
                                }
                            }
                            if(res.data.judgeQuestionList && res.data.judgeQuestionList.length>0){
                                judgeLen = res.data.judgeQuestionList.length;
                                for(var i = 0; i < res.data.judgeQuestionList.length; i++){
                                    for(var j = 0; j < res.data.judgeQuestionList[i].answerList.length; j++){
                                        if(res.data.judgeQuestionList[i].answerList[j].isSelected){
                                            judgeData.push({
                                                questionCode: res.data.judgeQuestionList[i].answerList[j].questionCode,
                                                examAnswer: res.data.judgeQuestionList[i].answerList[j].answerCode
                                            });
                                        }
                                    }
                                }
                            }
                            if(res.data.shortAnswerQuestionList && res.data.shortAnswerQuestionList.length>0){
                                shortLen = res.data.shortAnswerQuestionList.length;
                                for(var i = 0;i < res.data.shortAnswerQuestionList.length; i++){
                                    if(res.data.shortAnswerQuestionList[i].answerCode){
                                        shortData.push({
                                            questionCode: res.data.shortAnswerQuestionList[i].questionCode,
                                            examAnswer: res.data.shortAnswerQuestionList[i].answerCode
                                        });
                                    }
                                }
                            }

                            examData = res.data;
                            if(result.data && result.data.isFinishExam){ //已完成考试
                                isFinishExam = true;
                            }else{  //未完成考试
                                isFinishExam = false;
                            }


                            $(".explain").html(res.data.examName);

                            if(result.data && result.data.isFinishExam){ //已完成考试
                                showQuestions('reTest', res.data);
                                $(".submitBtn").hide();
                                $("#closeDialog").dialog({closed:false});
                            }else{  //未完成考试
                                showQuestions('test', res.data);
                                $(".submitBtn").show();
                                // 设置提交按钮高亮是否显示
                                if(singleData.length+multipleData.length+judgeData.length+shortData.length == singleLen+multipleLen+judgeLen+shortLen){
                                    $("#submit").addClass(" canSubmit");
                                }else{
                                    $("#submit").removeClass("canSubmit");
                                }

                                // if(remainTimeT) clearInterval(remainTimeT);
                                // remainTimeT = setInterval(ajaxInterval,1000); //每隔5秒保存一次数据
                            }

                        }
                    })
                }
            })

            // 显示试卷
            function showQuestions(type,data){ // type的值：test测试；reTest重测
                if(data){
                    var titFlag = 0; // 标题序号
                    var qid=1;
                    var list=[];
                    var questions=list.concat(data.singleQuestionList,data.moreQuestionList,data.judgeQuestionList,data.shortAnswerQuestionList)
                    questions.sort(function (a,b) {return a.questionOrder-b.questionOrder })
                    //单选
                    for(var i=0;i<questions.length;i++){
                        if(questions[i].questionType=='single'){

                            var part = $("<div>").addClass("part singleQues").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、单选题").appendTo(part);
                            var main = $("<div>").addClass("main").appendTo(part);
                                var h6 = $("<h6>").html(qid+"、"+questions[i].questionName).appendTo(main);
                                var ul = $("<ul>").appendTo(main);
                                if(questions[i].answerList && questions[i].answerList.length>0){
                                    for(var j=0;j<questions[i].answerList.length;j++){
                                        if(type == "test"){ // 测试
                                            if(questions[i].answerList[j].isSelected){
                                                var li = $("<li>").addClass("active").appendTo(ul);
                                                var input = $("<input>").attr({
                                                    type:'radio',
                                                    id:questions[i].answerList[j].id,
                                                    name:questions[i].answerList[j].questionCode,
                                                    value:questions[i].answerList[j].answerCode,
                                                    checked:true
                                                }).appendTo(li);
                                            }else{
                                                var li = $("<li>").appendTo(ul);
                                                var input = $("<input>").attr({
                                                    type:'radio',
                                                    id:questions[i].answerList[j].id,
                                                    name: questions[i].answerList[j].questionCode,
                                                    value:questions[i].answerList[j].answerCode
                                                }).appendTo(li);
                                            }
                                        }else if(type == "reTest"){ // 重测
                                            var li = $("<li>");
                                            var input = $("<input>").attr({
                                                type:'radio',
                                                id:questions[i].answerList[j].id,
                                                name:questions[i].answerList[j].questionCode,
                                                value:questions[i].answerList[j].answerCode
                                            });
                                            if(questions[i].answerList[j].isSelected){ // 已填的答案   isSelected
                                                li = $("<li>").addClass("active red");
                                                input = $("<input>").attr({
                                                    type:'radio',
                                                    id:questions[i].answerList[j].id,
                                                    name:questions[i].answerList[j].questionCode,
                                                    value:questions[i].answerList[j].answerCode,
                                                    checked:true
                                                });
                                            }
                                            if(questions[i].answerList[j].isCorrect){ // 正确答案
                                                li = $("<li>").removeClass("red").addClass(" green");
                                            }

                                            $(input).appendTo(li);
                                            $(li).appendTo(ul);
                                        }
                                        var label = $("<label>").attr("for", questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode + "：" + questions[i].answerList[j].answerContent).appendTo(li);
                                    }
                                }
                                if(questions[i].questionOrder==4){
                                    var  inner= $("<p>").addClass('intext').text('（备注：大众市场和集团客户市场等部门均属于市场经营；企业发展、法律、财务、人力、信安、采购、综合、党廉、纪委等部门均为综合支撑）').appendTo(main);
                                }
                                qid++;
                        }
                        //多选
                        if(questions[i].questionType=='more'){
                                titFlag += 1;
                                var part = $("<div>").addClass("part multipleQues").appendTo($(".questions"));
                                //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、多选题").appendTo(part);
                                    var main = $("<div>").addClass("main").appendTo(part);
                                    var h6 = $("<h6>").html(qid+"、"+questions[i].questionName).appendTo(main);
                                    var ul = $("<ul>").addClass("clearfix").appendTo(main);

                                    for(var j=0;j<questions[i].answerList.length;j++){
                                        if(type == "test") { // 测试
                                            if (!questions[i].answerList[j].isSelected) {
                                                    var li = $("<li>").appendTo(ul);

                                                var input = $("<input>").attr({
                                                    type:'checkbox',
                                                    id:questions[i].answerList[j].id,
                                                    name:questions[i].answerList[j].questionCode,
                                                    value:questions[i].answerList[j].answerCode
                                                }).appendTo(li);
                                            }else{

                                                    var li = $("<li>").addClass(" active").appendTo(ul);

                                                var input = $("<input>").attr({
                                                    type:'checkbox',
                                                    id:questions[i].answerList[j].id,
                                                    name:questions[i].answerList[j].questionCode,
                                                    value:questions[i].answerList[j].answerCode,
                                                    checked:true
                                                }).appendTo(li);
                                            }
                                        }else if(type == "reTest") { // 重测



                                                var li = $("<li>")

                                            var input = $("<input>").attr({
                                                type:'checkbox',
                                                id:questions[i].answerList[j].id,
                                                name:questions[i].answerList[j].questionCode,
                                                value:questions[i].answerList[j].answerCode
                                            });
                                            if(questions[i].answerList[j].isSelected){ // 已填的答案



                                                    li = $("<li>").addClass("active red");

                                                input = $("<input>").attr({
                                                    type:'checkbox',
                                                    id:questions[i].answerList[j].id,
                                                    name:questions[i].answerList[j].questionCode,
                                                    value:questions[i].answerList[j].answerCode,
                                                    checked:true
                                                });
                                            }
                                            if(questions[i].answerList[j].isCorrect){ // 正确答案

                                                    li = $("<li>").removeClass(" red").addClass(" green");


                                            }
                                            $(input).appendTo(li);
                                            $(li).appendTo(ul);
                                        }
                                        var label = $("<label>").attr("for",questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode+"："+questions[i].answerList[j].answerContent).appendTo(li);
                                    }
                                    qid++;

                            }
                        if(questions[i].questionType=='shortAnswer'){
                            titFlag += 1;
                            var part = $("<div>").addClass("part shortAnswer").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"：简答题").appendTo(part);
                                var main = $("<div>").addClass("main shortAnswer").appendTo(part);
                                var h6 = $("<h6>").html(qid+"、"+questions[i].questionName).appendTo(main);
                                var pObj = $("<p>").appendTo(main);
                                if(type == "test"){ // 测试
                                    if(questions[i].answerCode){
                                        var textarea = $("<textarea>").attr({id:questions[i].questionCode}).html(questions[i].answerCode).appendTo(pObj);
                                    }else{
                                        var textarea = $("<textarea>").attr({id:questions[i].questionCode}).appendTo(pObj);
                                    }
                                }else if(type == "reTest") { // 重测
                                    var textarea = $("<textarea>").attr({id:questions[i].questionCode}).html(questions[i].answerCode).appendTo(pObj);
                                }
                                qid++;
                        }
                    }


                    // input和textarea的事件
                    $("input").on("click",function() {
                        // 单选和判断的高亮、isSelected字段控制
                        if ($(this).attr("type") && $(this).attr("type") == "radio") {
                            if (!$(this).parent("li").hasClass("active")) {
                                $(this).parent("li").addClass(" active");
                                $(this).parent("li").siblings().removeClass(" active");

                                // 重测时isSelected字段表示上次已选择的选项
                                var partClass = $(this).parents(".part").attr("class").split(" ")[1];

                                var big_index = $(this).parents(".main").index("." + partClass + " .main"); // 在大题中的索引
                                var small_index = $(this).parents().index(); // 在小题中的索引
                                if ($(this).parents(".part").hasClass("singleQues")) {
                                    for (var i = 0; i < examData.singleQuestionList.length; i++) {
                                        for (var j = 0; j < examData.singleQuestionList[i].answerList.length; j++) {
                                            examData.singleQuestionList[i].answerList[j].isSelected = false;
                                        }
                                    }
                                    examData.singleQuestionList[big_index].answerList[small_index].isSelected = true;
                                } else if ($(this).parents(".part").hasClass("judgeQues")) {
                                    for (var i = 0; i < examData.judgeQuestionList.length; i++) {
                                        for (var j = 0; j < examData.judgeQuestionList[i].answerList.length; j++) {
                                            examData.judgeQuestionList[i].answerList[j].isSelected = false;
                                        }
                                    }
                                    examData.judgeQuestionList[big_index].answerList[small_index].isSelected = true;
                                }
                            }
                        };

                        // 多选的高亮、isSelected控制
                        if ($(this).attr("type") && $(this).attr("type") == "checkbox") {
                            if (!$(this).parent("li").hasClass("active")) {
                                $(this).parent("li").addClass(" active");
                            } else {
                                $(this).parent("li").removeClass("active");
                            }
                            for (var i = 0; i < examData.moreQuestionList.length; i++) {
                                for (var j = 0; j < examData.moreQuestionList[i].answerList.length; j++) {
                                    if ($(".multipleQues .main").eq(i).find("li").eq(j).hasClass("active")) {
                                        examData.moreQuestionList[i].answerList[j].isSelected = true;
                                    } else {
                                        examData.moreQuestionList[i].answerList[j].isSelected = false;
                                    }
                                }
                            }
                        };

                        // 单选
                        if ($(this).parents(".part").hasClass("singleQues")) {
                            singleData = [];
                            for (var i = 0; i < $(".singleQues .main").length; i++) {
                                if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                    singleData.push({
                                        questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                        examAnswer: $(".singleQues .main").eq(i).find("input[type='radio']:checked").val()
                                    });
                                }
                            }
                        }
                        // 多选
                        if ($(this).parents(".part").hasClass("multipleQues")) {
                            multipleData = [];
                            for (var i = 0; i < $(".multipleQues .main").length; i++) {
                                var mulAnswer = []; // 每道多选题选中的答案
                                if ($(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
                                    $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
                                        mulAnswer.push($(this).val());
                                    });
                                    multipleData.push({
                                        questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
                                        examAnswer: mulAnswer.join('/')
                                    });
                                }
                            }
                        }
                        // 判断
                        if ($(this).parents(".part").hasClass("judgeQues")) {
                            judgeData = [];
                            for (var i = 0; i < $(".judgeQues .main").length; i++) {
                                if ($(".judgeQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                    judgeData.push({
                                        questionCode: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                        examAnswer: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").val()
                                    });
                                }
                            }
                        }

                        // 设置提交按钮高亮是否显示
                        if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen) {
                            $("#submit").addClass(" canSubmit");
                        } else {
                            $("#submit").removeClass("canSubmit");
                        }
                    });
                    $("textarea").on("input propertychange",function() {
                        // 简答
                        if ($(this).parents(".part").hasClass("shortAnswer")) {
                            shortData = [];
                            for (var i = 0; i < $(".shortAnswer .main").length; i++) {
                                if ($(".shortAnswer .main").eq(i).find("textarea").val() != "") {
                                    shortData.push({
                                        questionCode: $(".shortAnswer .main").eq(i).find("textarea").attr("id"),
                                        examAnswer: $(".shortAnswer .main").eq(i).find("textarea").val()
                                    });
                                }
                            }
                        }

                        // 设置提交按钮高亮是否显示
                        if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen) {
                            $("#submit").addClass(" canSubmit");
                        } else {
                            $("#submit").removeClass("canSubmit");
                        }
                    });

                    if(isFinishExam){ // 已完成考试禁用表单
                        $(".questions input,.questions textarea").attr("disabled",true);
                        $("#submit").hide();
                    }else{
                        $(".questions input,.questions textarea").attr("disabled",false);
                        $("#submit").show();
                    }

                }
                else{
                    getparent().mesShow("温馨提示","试卷获取失败,请联系系统管理员!!!",2000,'red');
                }
            }

            // 点“提交”
            $("#submit").click(function(){
                if(singleData.length+multipleData.length+judgeData.length+shortData.length <= 0){
                    getparent().mesShow("温馨提示","还未开始答题",2000,'red');
                }else if(singleData.length+multipleData.length+judgeData.length+shortData.length < singleLen+multipleLen+judgeLen+shortLen){
                    getparent().mesShow("温馨提示","试卷未答完，请继续答题！",2000,'red');
                }else{
                    $("#submitDialog").dialog({closed:false});
                }
            });

            $("#sureSubmit").click(function(){
                submitData();
            });

            // 提交答案
            function submitData(){
                clearInterval(remainTimeT);

                var questionCodeArry = [];
                var examAnswerArry = [];
                var totalData = singleData.concat(multipleData,judgeData,shortData);

                for(var i = 0; i < totalData.length; i++){
                    questionCodeArry.push(totalData[i].questionCode);
                    examAnswerArry.push(totalData[i].examAnswer);
                }
                ajaxgeneral({
                    url: 'action/examInfo/saveExamInfo',// 调用判断方法 实际不判断直接保存记录
                    data: {
                        examAppCode: "hnjjwz2",
                        publishUsername: username,
                        examRecord: questionCodeArry.join(','),
                        examAnswer: examAnswerArry.join(','),
                        questionBlankCode: questionBlankCode,
                        residueTime: totalSS
                    },
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        if(remainTimeT) clearInterval(remainTimeT);

                        $("#scoreDialog h5").html("提交成功！");
                        $("#yes").show();

                        $("#submitDialog").dialog({closed:true});
                        $("#scoreDialog").dialog({closed:false});
                    }
                })
            }

            // 全答对时关闭弹框
            $("#yes").click(function(){
                $("#scoreDialog").dialog({closed:true});
                if(gps.actionType && gps.actionType=="secrecyTask"){
                    top.dialogClose("detail");
                }else{
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                }
            });


            // 试卷已完成时，关闭页面
            $("#closeBtns button").click(function(){
                $('#closeDialog').dialog('close');

                if(gps.actionType && gps.actionType=="secrecyJoin"){
                    top.dialogClose("detail");
                }else{
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                }

            })
        })
    </script>
    <style type="text/css">
        /*公共样式*/
        .clearfix:after{content:'.';display:block;height:0;line-height:0;clear:both;visibility:hidden;}
        .clearfix{zoom:1;}
        .w15{width:15%;}
        /*页面样式*/
        /*背景颜色*/
        body {
            background-image: url("../../images/bg.jpg");
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover;
            opacity: 0.8;
            margin: 0px;
            padding: 0px;
        }
        .intext{
            text-indent: 15px;
            margin: 30px auto;
        }
        .wrapper{width:1000px;margin:0 auto;background-color:#fff;color:#000;}
        .header{text-align: center;}
        .header,.header img{width:100%;}
        .details{width:100%;padding:10px;font-size:16px;}
        .explain{line-height:34px;margin-top:10px;heigh:60px;line-height: 60px;
            font-weight: bolder;
            font-size: 24px;
            text-align: center;}
        .questions{padding-bottom: 20px}
        .questionType{font-size:20px;font-weight:bold;line-height:1.2;margin-top:20px;}
        .main,.main ul{padding:0 40px;}
        .shortAnswer .main ul{padding:0 10px;}
        .main h6{font-size:16px;line-height:1.5;margin:25px 0;font-weight: 600;}
        .main li{line-height:1.5;margin:15px 0;}
        .main li.fl{margin-top:0;}
        .main .active{color:orange;}
        .main .green{color:#09DB87;}
        .main .red{color:#E11414;}
        .main input{width:auto;}
        .main label{margin-left:10px;}
        .shortAnswer .main textarea{min-height:160px;font-size:14px;}
        .icon-duihao1{font-size:16px;margin-left:4px;}
        .icon-cuo1{font-size:14px;font-weight:bold;margin-left:4px;}
        .submitBtn{border:0;outline:0;width:90px;height:36px;background:#B4B4B4;border-radius:4px;font-size:14px;color:#fff;margin:10px 0 0 60px;letter-spacing:2px;}
        .submitBtn:active{opacity:.85;}
        .canSubmit{background-color:#E83333;}

        .dialog h5{font-size:15px;font-weight:bold;text-align:center;margin-top:10px;}
        .forceSubmitDialog p{font-size:14px;font-weight:bold;text-align:center;margin-top:20px;}
        .scoreDialog p{font-size:12px;text-align:center;}

        .submitBtns button{border:0;outline:0;padding:0;margin:0;height:32px;font-size:12px;color:#fff;text-align:center;border-radius:4px;padding:0 20px!important;}
        .submitBtns .gray{background-color:#B4B4B4;}
        .submitBtns .red{background-color:#E11414;}
        .remainTime{font-size:15px;font-weight:bold;margin-top:20px;text-align:right;}
    </style>
</head>
<body style="height: 100%;">
<div class="wrapper">
    <div class="details">
        <p class="explain"></p>
        <p style="text-align: center; font-size: 14px;"><font class="col_r">*</font>说明：本问卷为匿名调查，我们承诺对您的作答情况进行保密，请您实事求是作答。除注明多选外，其它为单选题。</p>

        <div id="remainTime" class="remainTime"></div>
        <div class="questions">

        </div>
        <!-- 提交 -->
        <button class="submitBtn" id="submit">提交</button>
        <!-- 提交对话框 -->
        <div id="submitDialog" class="easyui-dialog dialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#submitBtns'" style="width:400px;height:200px;padding:10px"><h5>您已答完所有题，确认提交？</h5></div>
        <div class="btns submitBtns" id="submitBtns" style="text-align:center;">
            <button class="easyui-linkbutton gray" onclick="$('#submitDialog').dialog('close')">取消</button>
            <button id="sureSubmit" class="easyui-linkbutton red">是的确认提交！</button>
        </div>

        <!-- 提交成功对话框 -->
        <div id="scoreDialog" class="easyui-dialog dialog scoreDialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#scoreBtns'" style="width:400px;height:200px;padding:10px">
            <h5></h5>
            <!--<p>（注：简答题不计入合格分数，只计入参考分值）</p>-->
        </div>
        <div class="submitBtns scoreBtns" id="scoreBtns" style="text-align:center;">
            <button id="yes" class="easyui-linkbutton red hide">确定</button>
        </div>

        <!-- 打开试卷时，试卷已完成，关闭页面 -->
        <div id="closeDialog" class="easyui-dialog dialog closeDialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#closeBtns'" style="width:400px;height:200px;padding:10px">
            <h5>您已完成调查问卷，感谢您的参与！</h5>
        </div>
        <div class="submitBtns" id="closeBtns" style="text-align:center;">
            <button class="easyui-linkbutton red">确定</button>
        </div>
    </div>
</div>

</body>
</html>
