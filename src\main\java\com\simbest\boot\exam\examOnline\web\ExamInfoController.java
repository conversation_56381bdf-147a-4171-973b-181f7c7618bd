/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.web;/**
 * Created by KZH on 2019/10/8 15:16.
 */

import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.service.IExamInfoDetailService;
import com.simbest.boot.exam.examOnline.service.IExamInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:16
 * @desc 答题记录
 **/
@Api(description = "答题记录")
@Slf4j
@RestController
@RequestMapping(value = "/action/examInfo")
public class ExamInfoController extends LogicController<ExamInfo, String> {

    private IExamInfoService iExamInfoService;

    @Autowired
    public ExamInfoController(IExamInfoService service) {
        super(service);
        this.iExamInfoService = service;
    }

    @Autowired
    private IExamInfoDetailService examInfoDetailService;

    @ApiOperation(value = "保存答题记录", notes = "保存答题记录")
    @PostMapping(value = {"/saveExamInfo", "sso/saveExamInfo", "api/saveExamInfo"})
    public JsonResponse saveExamInfo(@RequestParam(required = false) String currentUserCode,
                                     @RequestParam(required = false) String source,
                                     @RequestBody(required = false) ExamInfo examInfo) {
        //    todo 前端传值问题 暂时手动写死
       /* if (StrUtil.isEmpty(examInfo.getExamAppCode()) || StrUtil.isEmpty(examInfo.getExamCode())){
            examInfo.setExamAppCode("2023_CYZD");
            examInfo.setExamCode("2023_CYZD");
        }*/
        return iExamInfoService.saveExamInfo(currentUserCode, source, examInfo);
    }

    @ApiOperation(value = "保存答题记录", notes = "保存答题记录")
    @PostMapping(value = {"/saveExamInfoNoUpdate", "sso/saveExamInfoNoUpdate"})
    public JsonResponse saveExamInfoNoUpdate(@RequestBody(required = false) ExamInfo examInfo) {

        return iExamInfoService.saveExamInfoNoUpdate(examInfo);
    }

    @ApiOperation(value = "保存答题记录", notes = "保存答题记录")
    @PostMapping(value = {"/saveSpecialExamInfoNoUpdate", "sso/saveSpecialExamInfoNoUpdate"})
    public JsonResponse saveSpecialExamInfoNoUpdate(@RequestBody(required = false) ExamInfo examInfo) {

        return iExamInfoService.saveSpecialExamInfoNoUpdate(examInfo);
    }

    @ApiOperation(value = "判题-并生成证书", notes = "判题-并生成证书")
    @PostMapping(value = {"/judgeExamAndCertificate", "sso/judgeExamAndCertificate"})
    public JsonResponse judgeExamAndCertificate(@RequestBody(required = false) ExamInfo examInfo) {

        return iExamInfoService.judgeExamAndCertificate(examInfo);
    }

    @ApiOperation(value = "获取到人的未完成试卷", notes = "获取到人的未完成试卷")
    @PostMapping(value = {"/unfinishedExam", "sso/unfinishedExam"})
    public JsonResponse unfinishedExam(@RequestBody(required = false) ExamInfo examInfo) {
        return iExamInfoService.unfinishedExam(examInfo);
    }

    @ApiOperation(value = "判题", notes = "判题")
    @PostMapping(value = {"/judgeExam", "sso/judgeExam"})
    public JsonResponse judgeExam(@RequestBody(required = false) ExamInfo examInfo) {

        return iExamInfoService.judgeExam(examInfo);
    }

    @ApiOperation(value = "阅卷", notes = "阅卷")
    @PostMapping(value = {"/markingExam", "sso/markingExam"})
    public JsonResponse markingExam(@RequestParam(required = false) String publishUsername) {

        return iExamInfoService.markingExam(publishUsername);
    }

    @ApiOperation(value = "获取未阅卷人员", notes = "获取未阅卷人员")
    @ApiImplicitParams({ //
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称displayOrder）", dataType = "String", //
                    paramType = "query") //
    })
    @PostMapping(value = {"/findAllByIsMarkingExam", "sso/findAllByIsMarkingExam"})
    public JsonResponse findAllByIsMarkingExam(@RequestParam(required = false, defaultValue = "1") int page,
                                               @RequestParam(required = false, defaultValue = "10") int size,
                                               @RequestParam(required = false) String direction,
                                               @RequestParam(required = false) String properties,
                                               @RequestBody(required = false) ExamInfo examInfo) {
        Pageable pageable = iExamInfoService.getPageable(page, size, direction, properties);

        return iExamInfoService.findAllByIsMarkingExam(examInfo, pageable);

    }


    @ApiOperation(value = "初始化职务", notes = "初始化职务")
    @PostMapping(value = {"/manualInitPosition", "sso/manualInitPosition"})
    public JsonResponse manualInitPosition() {

        return iExamInfoService.manualInitPosition();
    }

    @ApiOperation(value = "获取到人员答题分数及证书文件-username为空获取到全部名单", notes = "获取到人员答题分数及证书文件-username为空获取到全部名单")
    @PostMapping(value = {"/getExamInfoByUsername", "getExamInfoByUsername/sso"})
    public JsonResponse getExamInfoByUsername(@RequestParam(required = false) String username) {

        return iExamInfoService.getExamInfoByUsername(username);
    }

    @ApiOperation(value = "保存试卷", notes = "保存试卷")
    @PostMapping(value = {"/saveExam", "/sso/saveExam", "/api/saveExam"})
    public JsonResponse saveExam(@RequestParam(required = false) String currentUserCode,
                                 @RequestParam(required = false, defaultValue = "PC") String source,
                                 @RequestBody(required = false) ExamInfo examInfo) {
        return JsonResponse.success(iExamInfoService.saveExam(currentUserCode, source, examInfo));
    }

    @ApiOperation(value = "提交试卷", notes = "提交试卷")
    @PostMapping(value = {"/submitExam", "sso/submitExam", "api/submitExam"})
    public JsonResponse submitExam(@RequestParam(required = false) String currentUserCode,
                                   @RequestParam(required = false, defaultValue = "PC") String source,
                                   @RequestBody(required = false) ExamInfo examInfo) {
        return JsonResponse.success(iExamInfoService.submitExam(currentUserCode, source, examInfo));
    }

    @ApiOperation(value = "提交试卷", notes = "新版-提交试卷")
    @PostMapping(value = {"/submitExamNew", "sso/submitExamNew", "api/submitExamNew"})
    public JsonResponse submitExamNew(@RequestParam(required = false) String currentUserCode,
                                   @RequestParam(required = false, defaultValue = "PC") String source,
                                   @RequestBody(required = false) ExamInfo examInfo) {
        return JsonResponse.success(iExamInfoService.submitExamNew(currentUserCode, source, examInfo));
    }

    @ApiOperation(value = "保存试卷_加密", notes = "保存试卷_加密")
    @PostMapping(value = {"/saveExamSalt", "/sso/saveExamSalt", "/api/saveExamSalt"})
    public JsonResponse saveExamSalt(@RequestParam(required = false) String currentUserCode,
                                     @RequestParam(required = false, defaultValue = "PC") String source,
                                     @RequestBody Map<String, String> bodyParam) {
        return JsonResponse.success(iExamInfoService.saveExamSalt(currentUserCode, source, bodyParam.get("str")));
    }

    @ApiOperation(value = "提交试卷_加密", notes = "提交试卷_加密")
    @PostMapping(value = {"/submitExamSalt", "sso/submitExamSalt", "api/submitExamSalt"})
    public JsonResponse submitExamSalt(@RequestParam(required = false) String currentUserCode,
                                       @RequestParam(required = false, defaultValue = "PC") String source,
                                       @RequestBody Map<String, String> bodyParam) {
        return JsonResponse.success(iExamInfoService.submitExamSalt(currentUserCode, source, bodyParam.get("str")));
    }

    @ApiOperation(value = "提交试卷", notes = "提交试卷")
    @PostMapping(value = {"/submitExam2", "sso/submitExam2", "api/submitExam2"})
    public JsonResponse submitExam2(@RequestParam(required = false) String currentUserCode,
                                    @RequestParam(required = false) String source,
                                    @RequestBody(required = false) ExamInfo examInfo) {
        return JsonResponse.success(iExamInfoService.submitExam(currentUserCode, source, examInfo));

    }


    @ApiOperation(value = "提交试卷", notes = "提交试卷")
    @PostMapping(value = {"/submitExamHonestRisk", "sso/submitExamHonestRisk", "api/submitExamHonestRisk"})
    public JsonResponse submitExamHonestRisk(@RequestParam(required = false) String currentUserCode,
                                             @RequestParam(required = false) String source,
                                             @RequestBody(required = false) ExamInfo examInfo) {
        String examRecord = examInfo.getExamRecord();
        String[] examRecord1 = examRecord.split(",");
        String examAnswer = examInfo.getExamAnswer();
        String[] examAnswer1 = examAnswer.split(",");
        if (examRecord1.length == examAnswer1.length) {
            for (String s : examAnswer1) {
                if (StrUtil.isEmpty(s)) {
                    return JsonResponse.fail("您还有未答完的题目，请继续作答！");
                }
            }
        } else {
            return JsonResponse.fail("您还有未答完的题目，请继续作答！");
        }
        return JsonResponse.success(iExamInfoService.submitExamHonestRisk(currentUserCode, source, examInfo));

    }

    @ApiOperation(value = "查询指定人的答题情况", notes = "查询指定人的答题情况")
    @PostMapping(value = {"/findExamInfo", "api/findExamInfo", "sso/findExamInfo"})
    public JsonResponse findExamInfo(@RequestBody ExamInfo o) {
        return JsonResponse.success(iExamInfoService.findExamInfo(o.getPublishUsername(), o.getExamCode(), o.getExamAppCode()));
    }

    @ApiOperation(value = "保存考试明细信息(巡察整改专用一次性接口)", notes = "保存考试明细信息(巡察整改专用一次性接口)")
    @PostMapping(value = {"/saverExamInfoDetails", "sso/saverExamInfoDetails", "api/saverExamInfoDetails"})
    public JsonResponse saverExamInfoDetails(@RequestParam String examCode) {
        examInfoDetailService.saverExamInfoDetails(examCode);
        return JsonResponse.success(null, "操作成功");
    }


    @ApiOperation(value = "判题", notes = "判题")
    @PostMapping(value = {"/judgeExamPowerBuilding", "sso/judgeExamPowerBuilding"})
    public JsonResponse judgeExamPowerBuilding(@RequestBody(required = false) ExamInfo examInfo) {

        return iExamInfoService.judgeExamPowerBuilding(examInfo);
    }

    @ApiOperation(value = "查询考试成绩", notes = "查询考试成绩")
    @PostMapping(value = {"/findExamInfoByExamCode", "api/findExamInfoByExamCode", "sso/findExamInfoByExamCode"})
    public JsonResponse findExamInfo(@RequestParam String examCode,
                                     @RequestParam(required = false) String creator) {
        ExamInfo examInfoByExamCode = iExamInfoService.findExamInfoByExamCode(examCode, creator);
        if (examInfoByExamCode != null) {
            return JsonResponse.success(examInfoByExamCode);
        }
        return JsonResponse.fail("您没有参加本次考试");
    }

    @ApiOperation(value = "查询考试性别是否填写", notes = "查询考试性别是否填写")
    @PostMapping(value = {"/findExamInfoByExamCodeSex", "api/findExamInfoByExamCodeSex", "sso/findExamInfoByExamCodeSex"})
    public JsonResponse findExamInfoSex(@RequestParam String examCode,
                                        @RequestParam(required = false) String creator) {
        ExamInfo examInfoByExamCode = iExamInfoService.findExamInfoByExamCode(examCode, creator);
        if (examInfoByExamCode != null) {
            return JsonResponse.success(true);
        }
        return JsonResponse.success(false);
    }

    @ApiOperation(value = "查询参与该考试的人员", notes = "查询参与该考试的人员")
    @PostMapping(value = {"/findUserInfoByExamCode", "api/findUserInfoByExamCode", "sso/findUserInfoByExamCode"})
    public JsonResponse findExamUserInfoByExamCode(@RequestParam String examCode,
                                                   @RequestParam(required = false) String publishTruename,
                                                   @RequestParam(required = false, defaultValue = "1") int page,
                                                   @RequestParam(required = false, defaultValue = "10") int size) {
        return JsonResponse.success(iExamInfoService.findExamUserInfoByExamCode(examCode, publishTruename, page, size));
    }

    @ApiOperation(value = "根据用户名模糊查询参与该考试的人员", notes = "根据用户名模糊查询参与该考试的人员")
    @PostMapping(value = {"/findUserByExamCodeTrueName", "api/findUserByExamCodeTrueName", "sso/findUserByExamCodeTrueName"})
    public JsonResponse findUserByExamCodeTrueName(@RequestBody ExamInfo examCode,
                                                   @RequestParam(required = false, defaultValue = "1") int page,
                                                   @RequestParam(required = false, defaultValue = "10") int size) {
        return JsonResponse.success(iExamInfoService.findUserByExamCodeTrueName(examCode, page, size));
    }

    @ApiOperation(value = "对参与考试的人员批阅其考试试卷", notes = "对参与考试的人员批阅其考试试卷")
    @PostMapping(value = {"/updateExamInfo", "api/updateExamInfo", "sso/updateExamInfo"})
    public JsonResponse updateExamInfo(@RequestBody ExamInfo examInfo) {
        ExamInfo examInfo1 = iExamInfoService.updateExamInfo(examInfo);
        if (examInfo1 != null) return JsonResponse.success(examInfo1, "评分成功");
        return JsonResponse.fail("评分失败");
    }

    @ApiOperation(value = "测评结果查询", notes = "测评结果查询")
    @PostMapping(value = {"/findTestResultSelect", "api/findTestResultSelect", "sso/findTestResultSelect"})
    public JsonResponse findTestResultSelect(@RequestBody(required = false) Map<String, Object> paramMap,
                                             @RequestParam(required = false, defaultValue = "1") int page,
                                             @RequestParam(required = false, defaultValue = "10") int size) {
        return JsonResponse.success(iExamInfoService.findTestResultSelect(paramMap, page, size));
    }

    @ApiOperation(value = "导出测评结果查询", notes = "导出测评结果查询")
    @PostMapping(value = "/exportTestResultSelect")
    public void exportTestResultSelect(HttpServletRequest request,
                                       HttpServletResponse response,
                                       @RequestParam(required = false) Map<String, Object> paramMap) {
        iExamInfoService.exportTestResultSelect(request, response, paramMap);
    }

    @ApiOperation(value = "保存试卷2", notes = "保存试卷2")
    @PostMapping(value = {"/saveExam2", "/sso/saveExam2", "/api/saveExam2"})
    public JsonResponse saveExam2(@RequestParam(required = false) String currentUserCode,
                                  @RequestParam(required = false) String source,
                                  @RequestBody(required = false) ExamInfo examInfo) {
        return JsonResponse.success(iExamInfoService.saveExam2(currentUserCode, source, examInfo));
    }
}
