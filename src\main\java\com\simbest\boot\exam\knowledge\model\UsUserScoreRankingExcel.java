/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.model;/**
 * Created by KZH on 2019/10/8 15:55.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:55
 * @desc 考试排名导出实体
 **/
@Data
public class UsUserScoreRankingExcel {
    @ExcelVOAttribute(name = "排名", column = "A")
    private String rank;

    @ExcelVOAttribute(name = "姓名", column = "B")
    private String ansewersTrueName;

    @ExcelVOAttribute(name = "所在组织", column = "C")
    private String belongOrgName;

    @ExcelVOAttribute(name = "累计得分", column = "D")
    private String totalScore;

    @ExcelVOAttribute(name = "累计答题用时(分钟.秒)", column = "E")
    private String timeSpentMinutes;
}
