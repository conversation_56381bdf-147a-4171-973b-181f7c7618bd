<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>统计</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/static/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/static/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var reloadUrl='';
        $(function(){
            //获取最近的考试信息
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#atisticsDepartments",//table列表的id名称，需加#
                    // "querycmd":"action/examAnswerNotes/statisticsDepartments",//table列表的查询命令
                    //"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    // "styleClass":"noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "部门", field: "department", width: 15 ,sortable: true, tooltip: true,align:"center"},
                        { title: "协作态度", field: "PL", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "责任担当", field: "PS", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "沟通协调", field: "TF", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "协作质量", field: "WE", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "协作效率", field: "CA", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "工作对接", field: "SC", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "部门成绩", field: "departmentScore", width: 15,sortable: true, tooltip: true,align:"center" },
                    ]],
                    pagination:false,//是否分页
                }
            };
            loadGrid(pageparam);

            var pageparam1={
                "listtable":{
                    "listname":"#statisticsDepartmentsAvg",//table列表的id名称，需加#
                    // "querycmd":"action/examAnswerNotes/statisticsDepartmentsAvg",//table列表的查询命令
                    //"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    // "styleClass":"noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "部门名称", field: "department", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "百分制成绩", field: "Centennial", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "本次调查得分", field: "Avgs", width: 15,sortable: true, tooltip: true,align:"center"},
                        { title: "排名", field: "rank", width: 15,sortable: true, tooltip: true,align:"center",
                            formatter:function(value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                if (row.rank) {
                                    return row.rank;
                                }else{
                                    return "无"
                                }
                            }
                        },
                    ] ],
                    pagination:false,//是否分页
                }
            };
            loadGrid(pageparam1);

            var pageparam2={
                "listtable":{
                    "listname":"#countyBranchStatistics",//table列表的id名称，需加#
                    // "querycmd":"action/examAnswerNotes/countyBranchStatistics",//table列表的查询命令
                    //"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass":"noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "部门", field: "department", width: 15 ,sortable: true, tooltip: true,align:"center"},
                        { title: "专业水平", field: "PL", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "问题解决", field: "PS", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "及时反馈", field: "TF", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "工作效率", field: "WE", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "咨询解答", field: "CA", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "服务意识", field: "SC", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "政策指导", field: "PG", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "计划性", field: "PN", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "总体评价", field: "OE", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "部门成绩", field: "departmentScore", width: 15,sortable: true, tooltip: true,align:"center" },
                    ] ],
                    pagination:false,//是否分页
                }
            };
            loadGrid(pageparam2);

            var pageparam3={
                "listtable":{
                    "listname":"#departmentsAvg",//table列表的id名称，需加#
                    // "querycmd":"action/examAnswerNotes/countyBranchStatisticsDepartmentsAvg",//table列表的查询命令
                    //"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass":"noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "部门名称", field: "department", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "百分制成绩", field: "Centennial", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "本次调查得分", field: "Avgs", width: 15 ,sortable: true, tooltip: true,align:"center"},
                        { title: "排名", field: "rank", width: 15,sortable: true, tooltip: true,align:"center",
                            formatter:function(value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                if (row.rank) {
                                    return row.rank;
                                }else{
                                    return "无"
                                }
                            }
                        },
                    ] ],
                    pagination:false,//是否分页
                }
            };
            loadGrid(pageparam3);
            var pageparam4={
                "listtable":{
                    "listname":"#departmentsQuery",//table列表的id名称，需加#
                    "querycmd":"action/statisticsAnswers/statisLyjg",//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass":"noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "部门名称", field: "orgName", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "已答题人数", field: "answeredQuestions", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "未答题人数", field: "unansweredQuestions", width: 15 ,sortable: true, tooltip: true,align:"center"},
                        { title: "答题人数占比", field: "percentage", width: 15 ,sortable: true, tooltip: true,align:"center"}
                    ] ],
                    pagination:false,//是否分页
                }
            };
            loadGrid(pageparam4);
            var pageparam5={
                "listtable":{
                    "listname":"#departmentsQuerySurvy",//table列表的id名称，需加#
                    "querycmd":"action/statisticsAnswers/statisLyxf",//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass":"noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "部门名称", field: "orgName", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "已答题人数", field: "answeredQuestions", width: 15,sortable: true, tooltip: true,align:"center" },
                        { title: "未答题人数", field: "unansweredQuestions", width: 15 ,sortable: true, tooltip: true,align:"center"},
                        { title: "答题人数占比", field: "percentage", width: 15 ,sortable: true, tooltip: true,align:"center"}
                    ] ],
                    pagination:false,//是否分页
                }
            };
            loadGrid(pageparam5);

            var pageparam6={
                "listtable":{
                    "listname":"#summary",//table列表的id名称，需加#
                    "querycmd":"action/examAnswerNotes/statisticalSummary",//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass":"noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    // "sortable":false,
                    "columns":[[//列
                        { title: "部门", field: "department", width: 150 ,sortable: true, tooltip: true,align:"center"},
                        { title: "服务支撑满意度得分(县区评价)", field: "lyxfAvgs", width: 150 ,sortable: true, tooltip: true,align:"center"},
                        { title: "协同满意度得分(机关互评)", field: "lyjgAvgs", width: 150 ,sortable: true, tooltip: true,align:"center"},
                        { title: "协作满意度总得分", field: "Quarter", width: 150,sortable: true, tooltip: true,align:"center" },
                        { title: "百分制成绩", field: "Centennial", width: 150,sortable: true, tooltip: true,align:"center" },
                        { title: "排名", field: "rank", width: 150 ,sortable: true, tooltip: true,align:"center"},
                    ] ],
                    pagination:false,//是否分页
                }
            };
            // loadGrid(pageparam6);

            $('#annualQuarterCode1').combobox({//下拉框选择事件
                onLoadSuccess: function (params) {
                    $("#annualQuarterCode1").combobox("select",params[0].annualQuarterCode);
                },
                onSelect: function(param){
                    pageparam.listtable.querycmd = "action/examAnswerNotes/statisticsDepartments?annualQuarterCode="+param.annualQuarterCode
                    loadGrid(pageparam);
                }
            });        
            $('#annualQuarterCode2').combobox({
                onLoadSuccess: function (params) {
                    $("#annualQuarterCode2").combobox("select",params[0].annualQuarterCode);
                },                
                onSelect: function(param){
                    pageparam1.listtable.querycmd = "action/examAnswerNotes/statisticsDepartmentsAvg?annualQuarterCode="+param.annualQuarterCode
                    loadGrid(pageparam1);
                }
            });  
            $('#annualQuarterCode3').combobox({
                onLoadSuccess: function (params) {
                    $("#annualQuarterCode3").combobox("select",params[0].annualQuarterCode);
                },                
                onSelect: function(param){
                    pageparam2.listtable.querycmd = "action/examAnswerNotes/countyBranchStatistics?annualQuarterCode="+param.annualQuarterCode
                    loadGrid(pageparam2);
                }
            });  
            $('#annualQuarterCode4').combobox({
                onLoadSuccess: function (params) {
                    $("#annualQuarterCode4").combobox("select",params[0].annualQuarterCode);
                }, 
                onSelect: function(param){
                    pageparam3.listtable.querycmd = "action/examAnswerNotes/countyBranchStatisticsDepartmentsAvg?annualQuarterCode="+param.annualQuarterCode
                    loadGrid(pageparam3);
                }
            });
            $('#annualQuarterCode5').combobox({
                onLoadSuccess: function (params) {
                    $("#annualQuarterCode5").combobox("select",params[0].annualQuarterCode);
                },
                onSelect: function(param){
                    ajaxgeneral({
                        url:"action/examAnswerNotes/statisticalSummaryTitle?annualQuarterCode="+param.annualQuarterCode,
                        contentType:"application/json; charset=utf-8",
                        success:function(data){
                            //console.log(123);
                            pageparam6.listtable.columns=[[//列
                                { title: data.data[0].department, field: "department", width: 150 ,sortable: true, tooltip: true,align:"center"},
                                { title: data.data[0].lyxfAvgs, field: "lyxfAvgs", width: 150 ,sortable: true, tooltip: true,align:"center"},
                                { title: data.data[0].lyjgAvgs, field: "lyjgAvgs", width: 150 ,sortable: true, tooltip: true,align:"center"},
                                { title: data.data[0].Quarter, field: "Quarter", width: 150,sortable: true, tooltip: true,align:"center" },
                                { title: data.data[0].Centennial, field: "Centennial", width: 150,sortable: true, tooltip: true,align:"center" },
                                { title: data.data[0].rank, field: "rank", width: 150 ,sortable: true, tooltip: true,align:"center"},
                            ] ]
                            //console.log(data.data[0].lyxfAvgs)
                        }
                    });
                    pageparam6.listtable.querycmd = "action/examAnswerNotes/statisticalSummary?annualQuarterCode="+param.annualQuarterCode
                    loadGrid(pageparam6);
                }
            });

            //导出
            $(document).on('click','.export1',function(){
                var annualQuarterCode = $('#annualQuarterCode1').combobox("getValue");
                if (annualQuarterCode) {
                        $("#atisticsDepartmentsQueryForm").attr("action", web.rootdir + "action/statisticsExcel/exportLYJGExcel?annualQuarterCode=" + annualQuarterCode);
                        $("#atisticsDepartmentsQueryForm").attr("method", "post");
                        $("#atisticsDepartmentsQueryForm").submit();
                }else{
                    mesConfirm('','请先选择考试季度',function(){
                    });                    
                }

            });  
            $(document).on('click','.export2',function(){
                var annualQuarterCode = $('#annualQuarterCode2').combobox("getValue");
                if (annualQuarterCode) {
                        $("#statisticsDepartmentsAvgQueryForm").attr("action", web.rootdir + "action/statisticsExcel/exportLYJGAvgExcel?annualQuarterCode=" + annualQuarterCode);
                        $("#statisticsDepartmentsAvgQueryForm").attr("method", "post");
                        $("#statisticsDepartmentsAvgQueryForm").submit();
                }else{
                    mesConfirm('','请先选择考试季度',function(){
                    });                    
                }
            });
            $(document).on('click','.export3',function(){
                var annualQuarterCode = $('#annualQuarterCode3').combobox("getValue");
                if (annualQuarterCode) {
                        $("#countyBranchStatisticsQueryForm").attr("action", web.rootdir + "action/statisticsExcel/exportLYXFAvgExcel?annualQuarterCode=" + annualQuarterCode);
                        $("#countyBranchStatisticsQueryForm").attr("method", "post");
                        $("#countyBranchStatisticsQueryForm").submit();
                }else{
                    mesConfirm('','请先选择考试季度',function(){
                    });                    
                }
            });
            $(document).on('click','.export4',function(){
                var annualQuarterCode = $('#annualQuarterCode4').combobox("getValue");
                if (annualQuarterCode) {
                        $("#departmentsAvgQueryForm").attr("action", web.rootdir + "action/statisticsExcel/exportLYXFExcel?annualQuarterCode=" + annualQuarterCode);
                        $("#departmentsAvgQueryForm").attr("method", "post");
                        $("#departmentsAvgQueryForm").submit();
                }else{
                    mesConfirm('','请先选择考试季度',function(){
                    });                    
                }
            });
            $(document).on('click','.export5',function(){
                var annualQuarterCode = $('#annualQuarterCode5').combobox("getValue");
                if (annualQuarterCode) {
                    $("#summaryForm").attr("action", web.rootdir + "action/statisticsExcel/exportStatisticalSummaryExcel?annualQuarterCode=" + annualQuarterCode);
                    $("#summaryForm").attr("method", "post");
                    $("#summaryForm").submit();
                }else{
                    mesConfirm('','请先选择考试季度',function(){
                    });
                }
            });

        });
        //刷新页面
        function listLoad(){
            $(this).parent().siblings(".window-shadow").remove();
            $(this).parent().siblings(".window-mask").remove();
            $(this).parent().remove();
            $("#atisticsDepartments").datagrid("reload");
        }


    </script>
    <style>
        /* 标题 */
        h2 {
            text-align: center;
        }
        /* 下拉框盒子 */
        .select {
            margin: 10px auto;
            width: 400px;
            position: relative;
        }
        /* 导出 */
        .abtn {
            background-color: #38a8ec;
            color: white;
            width: 60px;
            height: 30px;
            position: absolute;
            top: 0;
            right: -100px;
            text-align: center;
            line-height: 30px;
        }
        .abtn:hover {
            text-decoration: none;
        }
        .atisticsDepartments,
        .departmentsAvg,
        .countyBranchStatistics,
        .statisticsDepartmentsAvg {
            margin-bottom: 35px;
        }
    </style>
</head>
<body class="body_page">
<!--searchform-->

<!--table-->
<h2>洛阳分公司各部门协同评价各维度平均分结果</h2>
<div class="select">
        <span>选择考试季度：</span><input id="annualQuarterCode1" name="annualQuarterCode"  class="easyui-combobox " style="width:300px; height: 32px;" data-options="
        valueField: 'annualQuarterCode',
        panelHeight:'auto',
        textField: 'annualQuarterInfo',
        contentType:'application/json; charset=utf-8',
        url: web.rootdir+'action/annualQuarter/getAllAnnualQuarterInfo?annualQuarterCode=lyjg'" />
    <div >
        <form id="atisticsDepartmentsQueryForm" method="post">
            <a class="abtn export1">导出</a>
        </form>
    </div>
</div>
<div class="atisticsDepartments"><table id="atisticsDepartments"></table></div>


<h2>洛阳分公司各部门协同评价平均分、百分值结果</h2>
<div class="select" >
        <span>选择考试季度：</span><input id="annualQuarterCode2" name="annualQuarterCode" class="easyui-combobox " style="width:300px; height: 32px;" data-options="
        valueField: 'annualQuarterCode',
        panelHeight:'auto',
        textField: 'annualQuarterInfo',
        contentType:'application/json; charset=utf-8',
        url: web.rootdir+'action/annualQuarter/getAllAnnualQuarterInfo?annualQuarterCode=lyjg'"/>
    <div >
        <form id="statisticsDepartmentsAvgQueryForm" method="post">
            <a class="abtn export2">导出</a>
        </form>
    </div>    
</div>
<div class="statisticsDepartmentsAvg" style="width: 1000px;margin: auto;"><table id="statisticsDepartmentsAvg"></table></div>


<h2>洛阳分公司各部门服务支撑力度评价各维度平均分结果</h2>
<div class="select" >
        <span>选择考试季度：</span><input id="annualQuarterCode3" name="annualQuarterCode" class="easyui-combobox " style="width:300px; height: 32px;" data-options="
        valueField: 'annualQuarterCode',
        panelHeight:'auto',
        textField: 'annualQuarterInfo',
        contentType:'application/json; charset=utf-8',
        url: web.rootdir+'action/annualQuarter/getAllAnnualQuarterInfo?annualQuarterCode=lyxf'" />
    <div >
        <form id="countyBranchStatisticsQueryForm" method="post">
            <a class="abtn export3">导出</a>
        </form>
    </div>          
</div>
<div class="countyBranchStatistics"><table id="countyBranchStatistics"></table></div>


<h2>洛阳分公司各部门服务支撑力度评价平均分、百分值结果</h2>
<div class="select" >
        <span>选择考试季度：</span><input id="annualQuarterCode4" name="annualQuarterCode" class="easyui-combobox" style="width:300px; height: 32px;" data-options="
        valueField: 'annualQuarterCode',
        panelHeight:'auto',
        textField: 'annualQuarterInfo',
        contentType:'application/json; charset=utf-8',
        url: web.rootdir+'action/annualQuarter/getAllAnnualQuarterInfo?annualQuarterCode=lyxf'" />
    <div >
        <form id="departmentsAvgQueryForm" method="post">
            <a class="abtn export4">导出</a>
        </form>
    </div>      
</div>
<div class="departmentsAvg" style="width: 1000px;margin: auto;"><table id="departmentsAvg"></table></div>
<h2>机关部门协作满意度统计汇总</h2>
<div class="select" >
    <span>选择考试季度：</span><input id="annualQuarterCode5" name="annualQuarterCode" class="easyui-combobox" style="width:300px; height: 32px;" data-options="
        valueField: 'annualQuarterCode',
        panelHeight:'auto',
        textField: 'annualQuarterInfo',
        contentType:'application/json; charset=utf-8',
        url: web.rootdir+'action/annualQuarter/getAllAnnualQuarterInfo?annualQuarterCode=lyjg'" />
    <div >
        <form id="summaryForm" method="post">
            <a class="abtn export5">导出</a>
        </form>
    </div>
</div>
<div class="summary" style="margin: auto;"><table id="summary"></table></div>
<h2>最近季度洛阳分公司各部门协同评价问卷答题实时情况</h2>
<div class="departmentsQuery" style="width: 1000px;margin: auto;"><table id="departmentsQuery"></table></div>
<h2>最近季度洛阳分公司各部门服务支撑力度评价问卷答题实时情况</h2>
<div class="departmentsQuerySurvy" style="width: 1000px;margin: auto;"><table id="departmentsQuerySurvy"></table></div>
</body>
</html>
