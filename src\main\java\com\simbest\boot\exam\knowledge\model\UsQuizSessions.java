/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.model;/**
 * Created by KZH on 2019/10/8 15:55.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:55
 * @desc 考试业务单据
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_quiz_sessions")
@ApiModel(value = "用户得分总分表")
public class UsQuizSessions extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "W") //主键前缀，此为可选项注解
    private String id;

   @Column(length = 500)
    @ApiModelProperty(value = "邀请ID", name = "invitaitonId", example = "999999")
    private String invitationId;

   @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户OA账户", name = "sendUserName", example = "超级管理员")
    private String sendUserName;

   @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户姓名", name = "sendTrueName", example = "超级管理员")
    private String sendTrueName;


   @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户答题是否完成", name = "sendCompleted", example = "超级管理员")
    private String sendCompleted;

   @Column(length = 500)
    @ApiModelProperty(value = "接受邀请的用户OA账户", name = "recUserName", example = "超级管理员")
    private String recUserName;

   @Column(length = 500)
    @ApiModelProperty(value = "接受邀请的用户姓名", name = "recTrueName", example = "超级管理员")
    private String recTrueName;

   @Column(length = 500)
    @ApiModelProperty(value = "接受邀请的用户答题是否完成", name = "sendCompleted", example = "超级管理员")
    private String recCompleted;

   @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户得分", name = "sendScoure")
    private Integer sendScoure;

   @Column(length = 500)
    @ApiModelProperty(value = "接受邀请的用户得分", name = "reciveScoure")
    private Integer reciveScoure;


   @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户答题用时", name = "sendTime")
    private String sendTime;


   @Column(length = 500)
    @ApiModelProperty(value = "接受邀请的用户答题用时", name = "reciveTime")
    private String reciveTime;


   @Column(length = 500)
    @ApiModelProperty(value = "对战开始时间，不能为空", name = "startTime")
    private String startTime;

   @Column(length = 500)
    @ApiModelProperty(value = "对战结束时间，不能为空", name = "endTime")
    private String endTime;

   @Column(length = 500)
    @ApiModelProperty(value = "对战结束时间，不能为空", name = "status")
    private String status;//：ONGOING：对战正在进行中 COMPLETED：对战结束





   @Column(length = 500)
    @ApiModelProperty(value = "创建时间", name = "createdAt")
    private String createdAt;

   @Column(length = 500)
    @ApiModelProperty(value = "更新时间", name = "updatedAt")
    private String updatedAt;



}
