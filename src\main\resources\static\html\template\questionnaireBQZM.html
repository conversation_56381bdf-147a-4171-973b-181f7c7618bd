<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org" style="height: 100%;">

<head>
    <title>关于搬迁至中牟生产楼办公的意见调研问卷</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="../../js/moaBridge_oa_wjdc.js?v=svn.revision" type="text/javascript"></script>


    <script type="text/javascript">
        var timeFlag = 0; // 提交数据的标识
        var remainTimeT; // 保存记录的计时器
        var username = "";
        var truename = "";
        var belongDepartmentName = ''
        var formInfo = {}
        examData = []
        $(function () {
            //获取分辨率
            var fbl = window.screen.height;
            if (fbl < 1080) {
                $(".wrapper").css({ "width": "925px" })
            } else if (fbl >= 1080) {
                $(".wrapper").css({ "width": "1100px" })
            }
            if (gps.from == "oa") {
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.uid,
                    async: false,
                    success: function (ress) {
                        username = ress.data.username;
                        truename = ress.data.truename;
                        belongDepartmentName = ress.data.belongDepartmentName;
                    }
                });
            } else if (gps.access_token) {
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.currentUserCode,
                    async: false,
                    success: function (ress) {
                        username = ress.data.username;
                        truename = ress.data.truename;
                        belongDepartmentName = ress.data.belongDepartmentName;

                    }
                });
            } else {
                getCurrent();
                username = web.currentUser.username;
                truename = web.currentUser.truename;
                belongDepartmentName = web.currentUser.belongDepartmentName;

            }
            if(username =='yujianwei'){
                belongDepartmentName ='纪委办公室(直属机关纪委)'
            }
            $('.department').html('部门：' + belongDepartmentName)
            if(gps.type == 'join'){
                $('.submitBtn').hide()
                // 已办
                ajaxgeneral({
                    url: 'action/task/examSurvey/findJoinDetail?workId='+gps.id,
                    data: {
                       
                    },
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        if (res.status == 200) {
                            var info = res.data
                            $('#inside').val(info.inside)
                            $('#taskContent').val(info.taskContent)
                            $('#outside').val(info.outside)
                            $('#outsideContent').val(info.outsideContent)
                            $('.part2 input[type="radio"]').each(function() {
                                $(this).prop('disabled', true);
                                if (this.value == info.specialNeed) {
                                    $(this).parent('li').addClass('red')
                                    $(this).prop('checked', true); // 选中对应的radio按钮
                                } else {
                                    $(this).prop('checked', false); // 确保其他未匹配的按钮未被选中
                                }
                            });
                            $('.part3 input[type="radio"]').each(function() {
                                $(this).prop('disabled', true);
                                if (this.value == info.effect) {
                                    $(this).parent('li').addClass('red')
                                    $(this).prop('checked', true); // 选中对应的radio按钮
                                } else {
                                    $(this).prop('checked', false); // 确保其他未匹配的按钮未被选中
                                }
                            });
                            $('.part4 input[type="radio"]').each(function() {
                                $(this).prop('disabled', true);
                                if (this.value == info.lifeEffect) {
                                    $(this).parent('li').addClass('red')
                                    $(this).prop('checked', true); // 选中对应的radio按钮
                                } else {
                                    $(this).prop('checked', false); // 确保其他未匹配的按钮未被选中
                                }
                            });
                            // $('.part2 input[type="radio"]').val(info.specialNeed)
                            // $('.part3 input[type="radio"]').val(info.effect)
                            // $('.part4 input[type="radio"]').val(info.lifeEffect)
                            $('#suggestion').val(info.suggestion)
                           if(info.specialNeed == 2){
                            $('#remark').val(info.remark)
                            $('#part2').show()
                           }
                           if(info.effect == 2 || info.effect == 3){
                            $('#reasons').val(info.reasons)
                            $('#part3').show()
                           }
                           if(info.lifeEffect == 2 || info.lifeEffect == 3){
                            $('#lifeReason').val(info.lifeReason)
                            $('#part4').show()
                           }
                           $(".questions").find('input').attr('disabled',true)
                           $(".questions").find('textarea').attr('disabled',true)
                           $("#closeDialog").dialog({ closed: false });

                       
                        }
                    }
                });
            }
            $(".hover").hide()
            $(".wrapper").show()
            // 第二题
            $('.part2').on('click', 'li', function (event) {
                // 阻止事件冒泡到父元素，确保只处理当前标签的点击
                if(gps.type !='join'){
                    event.stopPropagation();
                // 触发 input 的点击事件，从而选中对应的 radio 按钮
                $(this).find('input[type="radio"]').prop('checked', true).trigger('change');
                }
                
            });
            $('.part3').on('click', 'li', function (event) {
                // 阻止事件冒泡到父元素，确保只处理当前标签的点击
                if(gps.type !='join'){
                    event.stopPropagation();
                // 触发 input 的点击事件，从而选中对应的 radio 按钮
                $(this).find('input[type="radio"]').prop('checked', true).trigger('change');
                }
            });
            $('.part4').on('click', 'li', function (event) {
                // 阻止事件冒泡到父元素，确保只处理当前标签的点击
                if(gps.type !='join'){
                    event.stopPropagation();
                // 触发 input 的点击事件，从而选中对应的 radio 按钮
                $(this).find('input[type="radio"]').prop('checked', true).trigger('change');
                }
            });
            $('.part2 input[type="radio"]').on('change', function () {
                if ($(this).val() == '2') {
                    // 有需求
                    $('#part2').show()
                } else {
                    $('#part2').hide()
                    $('#remark').val('')

                }
            })
            // 第三题
            $('.part3 input[type="radio"]').on('change', function () {
                if ($(this).val() == '2' || $(this).val() == '3') {
                    // 有需求
                    $('#part3').show()
                } else {
                    $('#part3').hide()
                    $('#reasons').val('')

                }
            })
            // 第四题
            $('.part4 input[type="radio"]').on('change', function () {
                if ($(this).val() == '2' || $(this).val() == '3') {
                    // 有需求
                    $('#part4').show()
                } else {
                    $('#part4').hide()
                    $('#lifeReason').val('')
                }
            })
            // 提交
            $("#submit").click(function () {
                var inside = $('#inside').val();
                var taskContent = $('#taskContent').val();
                var outside = $('#outside').val();
                var outsideContent = $('#outsideContent').val();
                var suggestion = $('#suggestion').val();

                var specialNeed = $('.part2 input[type="radio"]:checked').val()
                var effect = $('.part3 input[type="radio"]:checked').val()
                var lifeEffect = $('.part4 input[type="radio"]:checked').val()
                var remark = $('#remark').val();
                var reasons = $('#reasons').val();
                var lifeReason = $('#lifeReason').val();
                var list = [
                    {
                    name:'内部占比',
                    flag:false,
                    },
                    {
                    name:'内部占比内容',
                    flag:false,
                    },
                    {
                    name:'外部占比',
                    flag:false,
                    },
                    {
                    name:'外部占比内容',
                    flag:false,
                    },
                ]
                    
                    
                var problem1=false;
                var strText = ''
                var kong1 = false;
                var kong2 = false;
                var kong3 = false;
                var kong4 = false;

                if(!inside && !taskContent &&!outside &&!outsideContent){
                    kong1 = true;
                }else{
                    if(!inside||inside == ''){
                    list[0].flag = true;
                }
                if(!taskContent||taskContent == ''){
                    list[1].flag = true;
                }
                if(!outside || outside == ''){
                    list[2].flag = true;
                }
                if(!outsideContent || outsideContent == ''){
                    list[3].flag = true;
                }
                var text = ''
                for(var i=0;i<list.length;i++){
                    if(list[i].flag){
                        // 有未填写
                        text += '必填'
                        problem1 = true;
                        
                    }else{
                    }
                }
                if(problem1){
                    strText+='第一题：'+text+''
                }
                
                if(Number(inside) + Number(outside)!=100){
                    if(strText){
                        strText+='，内外部占比相加必须100%'

                    }else{
                        strText+='第一题：内外部占比相加必须100%' 
                    }

                }
                }

                

                if (!specialNeed || specialNeed == '') {
                    kong2 = true;
                    if(strText){
                        strText+='<br>第二题：必填，若有需求需填写需求描述'
                    }else{
                        strText+='第二题：必填，若有需求需填写需求描述'

                    }
                } 
                if (specialNeed == 2) {
                    if (remark == '') {
                        if(strText){
                        strText+='<br>第二题：必填，若有需求需填写需求描述'
                    }else{
                        strText+='第二题：必填，若有需求需填写需求描述'

                    }
                    }
                }
                if (!effect || effect == '') {
                    kong3 = true;
                    if(strText){
                        strText+='<br>第三题：必填，若有影响需说明理由'
                    }else{
                        strText+='第三题：必填，若有影响需说明理由'

                    }
                }
                if (effect == 2 || effect == 3) {
                        if (reasons == '') {
                            if(strText){
                                strText+='<br>第三题：必填，若有影响需说明理由'
                            }else{
                                strText+='第三题：必填，若有影响需说明理由'

                            }
                        }
                }
                if (!lifeEffect || lifeEffect == '') {
                    kong4 = true;
                    if(strText){
                        strText+='<br>第四题：必填，若有影响需说明理由'
                    }else{
                        strText+='第四题：必填，若有影响需说明理由'

                    }
                }
                if (lifeEffect == 2 || lifeEffect == 3) {
                    if (lifeReason == '') {
                        if(strText){
                                strText+='<br>第四题：必填，若有影响需说明理由'
                            }else{
                                strText+='第四题：必填，若有影响需说明理由'

                            }
                    }
                }

                if (!suggestion || suggestion == '' ||suggestion == undefined) {
                    kong5 = true;
                    if(strText){
                        strText+='<br>第五题：必填，需填写具体意见建议'
                    }else{
                        strText+='第五题：必填，需填写具体意见建议'

                    }
                }
                if(kong1 && kong2 && kong3 && kong4 && kong5){
                        getparent().mesShow("温馨提示", '还未填写问券，请填写！', 2000, 'red')
                        return false

                    }
                if(!strText){
                    formInfo = {
                    inside: inside,
                    outside: outside,
                    questionBankCode: gps.examCode,
                    userName: username,
                    truename: truename,
                    outsideContent: outsideContent,
                    taskContent: taskContent,
                    suggestion: suggestion,
                    specialNeed: specialNeed,
                    effect: effect,
                    lifeEffect: lifeEffect,
                    remark: remark,
                    reasons: reasons,
                    lifeReason: lifeReason,

                }
                $("#submitDialog").dialog({ closed: false });
                }else{
                    var content
                    content='<div style="text-align: left;font-size: 14px">'+strText+'<br></div>'
                        getparent().mesAlert("温馨提示", content, 'warning')

                    

                }
                
                
                
               
               

            })

            $("#sureSubmit").click(function () {
                ajaxgeneral({
                    url: 'action/task/examSurvey/submitExamNew',
                    data: formInfo ,
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        if(res.status == 200){
                            if (remainTimeT) clearInterval(remainTimeT);
                            // 禁止答题
                            $(".questions input,.questions textarea").attr("disabled", true);
                            isFinishExam = true;
                            // $("#scoreDialog h5").html("提交成功！");
                            // $("#yes").show();
                            // moaBridge.close();
                            // location.reload()
                            $("#submitDialog").dialog({ closed: true });
                            if (gps.actionType && gps.actionType == "secrecyTask") {
                                top.dialogClose("detail");
                            } else {
                                var userAgent = navigator.userAgent;
                                if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                                    window.history.back();
                                } else {
                                    window.opener = null;
                                    window.open('', '_self');
                                }
                                if (gps.from) {
                                    window.opener = null;
                                    window.open('', '_self');
                                    window.close();
                                } else {
                                    $('#detailF', window.parent.document).parents().find('.more_close').trigger('click');
                                }
                                moaBridge.close();
                            }
                        }
                       
                    }
                });
            });
            // 试卷已完成时，关闭页面
            $("#closeBtns button").click(function () {
                    $('#closeDialog').dialog('close');
                    if (gps.actionType) {
                        top.dialogClose("detail");
                    } else {
                        var userAgent = navigator.userAgent;
                        if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                            window.history.back();
                        } else {
                            window.opener = null;
                            window.open('', '_self');
                        }
                        if (gps.from) {
                            window.opener = null;
                            window.open('', '_self');
                            window.close();
                        }
                        moaBridge.close();
                    }
                })
            // 全答对时关闭弹框
            $("#yes").click(function () {
                $("#scoreDialog").dialog({ closed: true });
                if (gps.actionType && gps.actionType == "secrecyTask") {
                    top.dialogClose("detail");
                } else {
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    if (gps.from) {
                        window.opener = null;
                        window.open('', '_self');
                        window.close();
                    } else {
                        $('#detailF', window.parent.document).parents().find('.more_close').trigger('click');
                    }
                    moaBridge.close();
                }
            });
            // main();

            function main() {
                var userAgent = navigator.userAgent;
                /**单点配置**/
                var gps = getQueryString();
                if (gps.actionType && gps.actionType == "secrecyJoin") { // 已办
                    $(".submitBtn").hide();
                } else {
                    $(".submitBtn").show();
                    //禁用鼠标右边
                    document.oncontextmenu = function () {
                        getparent().mesShow("温馨提示", "请手动答题", 2000, 'red');
                        return false;
                    };
                    //禁用ctrl+v功能
                    document.onkeydown = function () {
                        if (event.ctrlKey && window.event.keyCode == 86) {
                            getparent().mesShow("温馨提示", "请手动答题", 2000, 'red');
                            return false;
                        }
                    };
                }
                var questionBlankCode = ''; // 题库编码
                var singleLen = 0, multipleLen = 0, judgeLen = 0, shortLen = 0; // 题目数量
                var singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案
                var examData = {}; // 试卷模板
                var examRecordId = null; // 答题记录的id
                var currentAction = "";
                var isFinishExam = false; // 是否完成考试

                // 获取试卷模板
                var tempCurrentUserCode = gps.currentUserCode ? gps.currentUserCode : ""
                ajaxgeneral({
                    url: 'action/examAttribute/constructExamLayout?currentUserCode=' + tempCurrentUserCode,
                    data: { "examAppCode": gps.examAppCode },
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        questionBlankCode = res.data.questionBankCode;
                        currentAction = "test";
                        // $(".explain").html(res.data.examName);
                        // $(".examRemark").html(res.data.examRemark);
                        $(".examRemarkFooter").html(res.data.examRemarkFooter);

                        // 当前用户是否有未完成试卷
                        ajaxgeneral({
                            url: 'action/examInfo/unfinishedExam',
                            type: "POST",
                            data: { publishUsername: username, examAppCode: gps.examAppCode },
                            contentType: "application/json; charset=utf-8",
                            dataType: "json",
                            success: function (result) {
                                if (result.data != null) {
                                    examRecordId = result.data.id; // 未完成试卷id
                                    totalSS = parseInt(result.data.residueTime);
                                    var examRecord = result.data.examRecord.split(','); // 题目编号
                                    var examAnswer = result.data.examAnswer.split(','); // 保存的答案
                                    singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案
                                    // 匹配已选择的答案
                                    function matchAnswer(lists, mark) {
                                        for (var i = 0; i < examRecord.length; i++) {
                                            for (var n = 0; n < lists.length; n++) {
                                                if (examRecord[i] == lists[n].questionCode) {
                                                    if (mark != 'shortAnswer') {
                                                        var examAnswerOptions = examAnswer[i].split('/');
                                                        for (var ii = 0; ii < examAnswerOptions.length; ii++) {
                                                            for (var nn = 0; nn < lists[n].answerList.length; nn++) {
                                                                if (examAnswerOptions[ii].indexOf(":") > -1) {
                                                                    if (examAnswerOptions[ii].split(':')[0] == lists[n].answerList[nn].answerCode) {
                                                                        lists[n].answerList[nn].isSelected = true;
                                                                        lists[n].answerList[nn].contentText = examAnswerOptions[ii].split(':')[1];
                                                                        // lists[n].answerList[nn].identification = '1';
                                                                    } else {
                                                                        if (!lists[n].answerList[nn].isSelected) {
                                                                            lists[n].answerList[nn].isSelected = false;
                                                                        }
                                                                    }
                                                                } else {
                                                                    if (examAnswerOptions[ii] == lists[n].answerList[nn].answerCode) {
                                                                        lists[n].answerList[nn].isSelected = true;
                                                                    } else {
                                                                        if (!lists[n].answerList[nn].isSelected) {
                                                                            lists[n].answerList[nn].isSelected = false;
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    } else {
                                                        lists[n].answerCode = examAnswer[i];
                                                    }
                                                }
                                            }
                                        }
                                        return lists;
                                    }
                                    matchAnswer(res.data.singleQuestionList, 'single');
                                    matchAnswer(res.data.moreQuestionList, 'multiple');
                                    matchAnswer(res.data.judgeQuestionList, 'judge');
                                    matchAnswer(res.data.shortAnswerQuestionList, 'shortAnswer');
                                } else {
                                    totalSS = parseInt(res.data.setTime) * 60;
                                }
                                // 单选
                                if (res.data.singleQuestionList && res.data.singleQuestionList.length > 0) {
                                    singleLen = res.data.singleQuestionList.length;
                                    for (var i = 0; i < res.data.singleQuestionList.length; i++) {
                                        for (var j = 0; j < res.data.singleQuestionList[i].answerList.length; j++) {
                                            if (res.data.singleQuestionList[i].answerList[j].isSelected) {
                                                singleData.push({
                                                    questionCode: res.data.singleQuestionList[i].answerList[j].questionCode,
                                                    examAnswer: res.data.singleQuestionList[i].answerList[j].answerCode
                                                });
                                            }
                                        }
                                    }
                                }
                                // 多选
                                if (res.data.moreQuestionList && res.data.moreQuestionList.length > 0) {
                                    multipleLen = res.data.moreQuestionList.length;
                                    for (var i = 0; i < res.data.moreQuestionList.length; i++) {
                                        var options = [];
                                        for (var j = 0; j < res.data.moreQuestionList[i].answerList.length; j++) {
                                            if (res.data.moreQuestionList[i].answerList[j].isSelected) {
                                                options.push(res.data.moreQuestionList[i].answerList[j].answerCode);
                                            }
                                        }
                                        if (options.length > 0) {
                                            multipleData.push({
                                                questionCode: res.data.moreQuestionList[i].questionCode,
                                                examAnswer: options.join("/")
                                            });
                                        }
                                    }
                                }
                                //判断
                                if (res.data.judgeQuestionList && res.data.judgeQuestionList.length > 0) {
                                    judgeLen = res.data.judgeQuestionList.length;
                                    for (var i = 0; i < res.data.judgeQuestionList.length; i++) {
                                        for (var j = 0; j < res.data.judgeQuestionList[i].answerList.length; j++) {
                                            if (res.data.judgeQuestionList[i].answerList[j].isSelected) {
                                                judgeData.push({
                                                    questionCode: res.data.judgeQuestionList[i].answerList[j].questionCode,
                                                    examAnswer: res.data.judgeQuestionList[i].answerList[j].answerCode
                                                });
                                            }
                                        }
                                    }
                                }
                                // 简答
                                if (res.data.shortAnswerQuestionList && res.data.shortAnswerQuestionList.length > 0) {
                                    shortLen = res.data.shortAnswerQuestionList.length;
                                    for (var i = 0; i < res.data.shortAnswerQuestionList.length; i++) {
                                        if (res.data.shortAnswerQuestionList[i].answerCode) {
                                            shortData.push({
                                                questionCode: res.data.shortAnswerQuestionList[i].questionCode,
                                                examAnswer: res.data.shortAnswerQuestionList[i].answerCode
                                            });
                                        }
                                    }
                                }
                                examData = res.data;
                                if (result.data && result.data.isFinishExam) { //已完成考试
                                    isFinishExam = true;
                                } else {  //未完成考试
                                    isFinishExam = false;
                                }

                                if (result.data && result.data.isFinishExam) { //已完成考试
                                    // showQuestions('reTest', res.data);
                                    $(".submitBtn").hide();
                                    $("#closeDialog").dialog({ closed: false });
                                } else {  //未完成考试
                                    // showQuestions('test', res.data);
                                    $(".submitBtn").show();
                                    // 设置提交按钮高亮是否显示
                                    if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen) {
                                        $("#submit").addClass(" canSubmit");
                                    } else {
                                        $("#submit").removeClass("canSubmit");
                                    }
                                }

                            }
                        })
                    }
                });

                // 显示试卷
                function showQuestions(type, data) { // type的值：test测试；reTest重测
                    if (data) {
                        var qid = 1;
                        var qid1 = 1;
                        if (data.singleQuestionList && data.singleQuestionList.length > 0) {
                            for (var i = 0; i < data.singleQuestionList.length; i++) {
                                // $("<div>").addClass("single").appendTo($(".questions"));
                                // if (data.singleQuestionList[i].type) {
                                //     $('.single').eq(i).html('<span style="margin-left:25px;font-weight:600">' + data.singleQuestionList[i].type)
                                // }
                                // var part = $("<div>").addClass("part singleQues").appendTo($(".questions"));
                                // var main = $("<div>").addClass("main").appendTo(part);
                                // if(qid=='1'){
                                //   $("<h5>").html('一、基本信息').appendTo(main);
                                // }else if(qid=='2'){
                                //   $("<h5>").html('二、干部担当作为总体评价').appendTo(main);
                                // }

                                // var h6 = $("<h6>").html(qid1 + "、" + data.singleQuestionList[i].questionName).appendTo(main);
                                // var ul = $("<ul>").appendTo(main);


                                $("<div>").addClass("single").appendTo($(".questions"));
                                // $('.single').eq(0).html('<span style="margin-left:25px;font-weight:600">'+ transNum(qid6) +'、单选题')
                                if (data.singleQuestionList[i].type) {
                                    $('.single').eq(i).html('<span style="margin-left:25px;font-weight:600">' + data.singleQuestionList[i].type)
                                }

                                var part = $("<div>").addClass("part singleQues").appendTo($(".questions"));
                                var main = $("<div>").addClass("main").appendTo(part);
                                var h6 = $("<h6>").html(qid + "、" + data.singleQuestionList[i].questionName).appendTo(main);
                                var ul = $("<ul>").appendTo(main);




                                if (data.singleQuestionList[i].answerList && data.singleQuestionList[i].answerList.length > 0) {
                                    for (var j = 0; j < data.singleQuestionList[i].answerList.length; j++) {
                                        if (type == "test") { // 测试
                                            if (data.singleQuestionList[i].answerList[j].isSelected) {
                                                var li = $("<li>").addClass("active").appendTo(ul);
                                                var input = $("<input>").attr({
                                                    type: 'radio',
                                                    id: data.singleQuestionList[i].answerList[j].id,
                                                    name: data.singleQuestionList[i].answerList[j].questionCode,
                                                    value: data.singleQuestionList[i].answerList[j].answerCode,
                                                    checked: true
                                                }).appendTo(li);
                                            } else {
                                                var li = $("<li>").appendTo(ul);
                                                var input = $("<input>").attr({
                                                    type: 'radio',
                                                    id: data.singleQuestionList[i].answerList[j].id,
                                                    name: data.singleQuestionList[i].answerList[j].questionCode,
                                                    value: data.singleQuestionList[i].answerList[j].answerCode
                                                }).appendTo(li);
                                            }
                                        } else if (type == "reTest") { // 重测
                                            var li = $("<li>");
                                            var input = $("<input>").attr({
                                                type: 'radio',
                                                id: data.singleQuestionList[i].answerList[j].id,
                                                name: data.singleQuestionList[i].answerList[j].questionCode,
                                                value: data.singleQuestionList[i].answerList[j].answerCode
                                            });
                                            if (data.singleQuestionList[i].answerList[j].isSelected) { // 已填的答案   isSelected
                                                li = $("<li>").addClass("active red");
                                                input = $("<input>").attr({
                                                    type: 'radio',
                                                    id: data.singleQuestionList[i].answerList[j].id,
                                                    name: data.singleQuestionList[i].answerList[j].questionCode,
                                                    value: data.singleQuestionList[i].answerList[j].answerCode,
                                                    checked: true
                                                });
                                            }
                                            //渲染正确答案
                                            /*if (data.singleQuestionList[i].answerList[j].isCorrect) { // 正确答案
                                                li = $("<li>").removeClass("red").addClass(" green");
                                            }*/

                                            $(input).appendTo(li);
                                            $(li).appendTo(ul);
                                        }
                                        // var label = $("<label>").attr("for", data.singleQuestionList[i].answerList[j].id).html(data.singleQuestionList[i].answerList[j].answerCode + "：" + data.singleQuestionList[i].answerList[j].answerContent).appendTo(li);

                                        if (data.singleQuestionList[i].answerList[j].contentText) {
                                            var label = $("<label>").attr("for", data.singleQuestionList[i].answerList[j].id).html(data.singleQuestionList[i].answerList[j].answerCode + "：" + data.singleQuestionList[i].answerList[j].answerContent + "</br><p class='atext'>" + data.singleQuestionList[i].answerList[j].contentText + "</p>").appendTo(li);
                                        } else {
                                            var label = $("<label>").attr("for", data.singleQuestionList[i].answerList[j].id).html(data.singleQuestionList[i].answerList[j].answerCode + "：" + data.singleQuestionList[i].answerList[j].answerContent).appendTo(li);
                                        }
                                    }
                                }
                                qid++;
                            }
                        }
                        if (data.moreQuestionList && data.moreQuestionList.length > 0) {
                            for (var i = 0; i < data.moreQuestionList.length; i++) {
                                $("<div>").addClass("multiple").appendTo($(".questions"));
                                if (data.moreQuestionList[i].type) {
                                    $('.multiple').eq(i).html('<span style="margin-left:25px;font-weight:600">' + data.moreQuestionList[i].type)
                                }
                                // var part = $("<div>").addClass("part multipleQues").appendTo($(".questions"));
                                // if(qid=='3'){
                                //   $("<h5>").html('三、干部担当作为情况现状【第1题为单选，其他为多选，多选题最多选3项】').appendTo(main);
                                // }
                                // var main = $("<div>").addClass("main").appendTo(part);
                                // if(qid == '3'){
                                //     var h6 = $("<h6>").html((qid-2) + "、(单选)" + data.moreQuestionList[i].questionName).appendTo(main);
                                // }else{
                                //     var h6 = $("<h6>").html((qid-2) + "、(多选)" + data.moreQuestionList[i].questionName).appendTo(main);
                                // }
                                // var ul = $("<ul>").addClass("clearfix").attr({maxChooseNum:data.moreQuestionList[i].maxChooseNum}).appendTo(main);

                                var main = $("<div>").addClass("main").appendTo(part);
                                var h6 = $("<h6>").html(qid + "、(多选)" + data.moreQuestionList[i].questionName).appendTo(main);
                                var ul = $("<ul>").addClass("clearfix").attr({ maxChooseNum: data.moreQuestionList[i].maxChooseNum }).appendTo(main);

                                for (var j = 0; j < data.moreQuestionList[i].answerList.length; j++) {
                                    if (type == "test") { // 测试
                                        if (!data.moreQuestionList[i].answerList[j].isSelected) {
                                            if (data.moreQuestionList[i].answerList.length > 4) {
                                                var li = $("<li>").addClass("fl w100").appendTo(ul);
                                            } else {
                                                var li = $("<li>").addClass("w100").appendTo(ul);
                                            }
                                            var input = $("<input>").attr({
                                                type: 'checkbox',
                                                id: data.moreQuestionList[i].answerList[j].id,
                                                name: data.moreQuestionList[i].answerList[j].questionCode,
                                                value: data.moreQuestionList[i].answerList[j].answerCode,
                                                identification: data.moreQuestionList[i].answerList[j].identification
                                            }).appendTo(li);
                                        } else {
                                            if (data.moreQuestionList[i].answerList.length > 4) {
                                                var li = $("<li>").addClass("fl w100 active").appendTo(ul);
                                            } else {
                                                var li = $("<li>").addClass("w100 active").appendTo(ul);
                                            }
                                            var input = $("<input>").attr({
                                                type: 'checkbox',
                                                id: data.moreQuestionList[i].answerList[j].id,
                                                name: data.moreQuestionList[i].answerList[j].questionCode,
                                                value: data.moreQuestionList[i].answerList[j].answerCode,
                                                identification: data.moreQuestionList[i].answerList[j].identification,
                                                checked: true
                                            }).appendTo(li);
                                        }
                                    } else if (type == "reTest") { // 重测
                                        if (data.moreQuestionList[i].answerList.length > 4) {
                                            var li = $("<li>").addClass("fl w100");
                                        } else {
                                            var li = $("<li>").addClass("w100");
                                        }
                                        var input = $("<input>").attr({
                                            type: 'checkbox',
                                            id: data.moreQuestionList[i].answerList[j].id,
                                            name: data.moreQuestionList[i].answerList[j].questionCode,
                                            value: data.moreQuestionList[i].answerList[j].answerCode
                                        });
                                        var atext;
                                        if (data.moreQuestionList[i].answerList[j].isSelected) { // 已填的答案
                                            if (data.moreQuestionList[i].answerList.length > 4) {
                                                li = $("<li>").addClass("fl w100 active red");
                                            } else {
                                                li = $("<li>").addClass("w100 active red");
                                            }

                                            input = $("<input>").attr({
                                                type: 'checkbox',
                                                id: data.moreQuestionList[i].answerList[j].id,
                                                name: data.moreQuestionList[i].answerList[j].questionCode,
                                                value: data.moreQuestionList[i].answerList[j].answerCode,
                                                checked: true
                                            });
                                        }
                                        if (data.moreQuestionList[i].answerList[j].isCorrect) { // 正确答案
                                            if (data.moreQuestionList[i].answerList.length > 4) {
                                                li = $("<li>").removeClass("fl w100 red").addClass(" fl w100 green");
                                            } else {
                                                li = $("<li>").removeClass("w100 red").addClass(" w100 green");
                                            }
                                        }
                                        $(input).appendTo(li);
                                        $(li).appendTo(ul);
                                    }
                                    if (data.moreQuestionList[i].answerList[j].contentText) {
                                        var label = $("<label>").attr("for", data.moreQuestionList[i].answerList[j].id).html(data.moreQuestionList[i].answerList[j].answerCode + "：" + data.moreQuestionList[i].answerList[j].answerContent + "</br><p class='atext'>" + data.moreQuestionList[i].answerList[j].contentText + "</p>").appendTo(li);

                                    } else {
                                        var label = $("<label>").attr("for", data.moreQuestionList[i].answerList[j].id).html(data.moreQuestionList[i].answerList[j].answerCode + "：" + data.moreQuestionList[i].answerList[j].answerContent).appendTo(li);
                                    }

                                }
                                qid++;
                            }
                        }
                        if (data.judgeQuestionList && data.judgeQuestionList.length > 0) {
                            var part = $("<div>").addClass("part judgeQues").appendTo($(".questions"));
                            for (var i = 0; i < data.judgeQuestionList.length; i++) {
                                var main = $("<div>").addClass("main").appendTo(part);
                                var h6 = $("<h6>").html(qid + "、" + data.judgeQuestionList[i].questionName).appendTo(main);
                                var ul = $("<ul>").addClass("clearfix").appendTo(main);

                                for (var j = 0; j < data.judgeQuestionList[i].answerList.length; j++) {
                                    if (type == "test") { // 测试
                                        if (data.judgeQuestionList[i].answerList[j].isSelected) {
                                            var li = $("<li>").addClass("fl w15 active").appendTo(ul);
                                            var input = $("<input>").attr({
                                                type: 'radio',
                                                id: data.judgeQuestionList[i].answerList[j].id,
                                                name: data.judgeQuestionList[i].answerList[i].questionCode,
                                                value: data.judgeQuestionList[i].answerList[j].answerCode,
                                                checked: true
                                            }).appendTo(li);
                                        } else {
                                            var li = $("<li>").addClass("fl w15").appendTo(ul);
                                            var input = $("<input>").attr({
                                                type: 'radio',
                                                id: data.judgeQuestionList[i].answerList[j].id,
                                                name: data.judgeQuestionList[i].answerList[i].questionCode,
                                                value: data.judgeQuestionList[i].answerList[j].answerCode
                                            }).appendTo(li);
                                        }
                                    } else if (type == "reTest") { // 重测
                                        var li = $("<li>").addClass("fl w15");
                                        var input = $("<input>").attr({
                                            type: 'radio',
                                            id: data.judgeQuestionList[i].answerList[j].id,
                                            name: data.judgeQuestionList[i].answerList[i].questionCode,
                                            value: data.judgeQuestionList[i].answerList[j].answerCode
                                        });

                                        if (data.judgeQuestionList[i].answerList[j].isSelected) {
                                            li = $("<li>").addClass("fl w15 active red");
                                            input = $("<input>").attr({
                                                type: 'radio',
                                                id: data.judgeQuestionList[i].answerList[j].id,
                                                name: data.judgeQuestionList[i].answerList[i].questionCode,
                                                value: data.judgeQuestionList[i].answerList[j].answerCode,
                                                checked: true
                                            });
                                        }
                                        if (data.judgeQuestionList[i].answerList[j].isCorrect) {
                                            li = $("<li>").removeClass("fl w15 red").addClass(" fl w15 green");
                                        }
                                        $(input).appendTo(li);
                                        $(li).appendTo(ul);
                                    }

                                    var label = $("<label>").attr("for", data.judgeQuestionList[i].answerList[j].id).appendTo(li);
                                    if (data.judgeQuestionList[i].answerList[j].answerCode == "true") {
                                        var iObj = $("<i>").addClass("iconfont icon-duihao1").appendTo(label);
                                    } else {
                                        var iObj = $("<i>").addClass("iconfont icon-cuo1").appendTo(label);
                                    }
                                }
                                qid++;
                            }
                        }
                        if (data.shortAnswerQuestionList && data.shortAnswerQuestionList.length > 0) {
                            var part = $("<div>").addClass("part shortAnswer").appendTo($(".questions"));
                            for (var i = 0; i < data.shortAnswerQuestionList.length; i++) {
                                var main = $("<div>").addClass("main shortAnswer").appendTo(part);
                                var h6 = $("<h6>").html(qid + "、" + data.shortAnswerQuestionList[i].questionName).appendTo(main);
                                var pObj = $("<p>").appendTo(main);
                                if (type == "test") { // 测试
                                    if (data.shortAnswerQuestionList[i].answerCode) {
                                        var textarea = $("<textarea>").attr({ id: data.shortAnswerQuestionList[i].questionCode }).html(data.shortAnswerQuestionList[i].answerCode).appendTo(pObj);
                                    } else {
                                        var textarea = $("<textarea>").attr({ id: data.shortAnswerQuestionList[i].questionCode }).appendTo(pObj);
                                    }
                                } else if (type == "reTest") { // 重测
                                    var textarea = $("<textarea>").attr({ id: data.shortAnswerQuestionList[i].questionCode }).html(data.shortAnswerQuestionList[i].answerCode).appendTo(pObj);
                                }
                                qid++;
                            }
                        }

                        // input和textarea的事件
                        $(".questions input").on("click", function () {
                            //判断是否是其他 是其他就让用户输入内容
                            var optionName = $(this).next().text().substring(2, $(this).next().text().length)
                            var maxChooseNum = 99;//多选最多能选几项
                            if ($(this).attr('identification') == '1' && !$(this).parent().siblings("li:last-child").hasClass("other")) {
                                $(this).parent().parent().append('<li class="other">请输入：<textarea class="wenben" type="text"></textarea></li>')
                            }
                            if (!$(this).parents(".part").hasClass("multipleQues") && $(this).attr('identification') !== '1' && $(this).parent().siblings("li:last-child").hasClass("other")) {
                                $(this).parent().siblings("li:last-child").remove()
                            }
                            if ($(this).attr('identification') == '1' && $(this).attr("type") == "checkbox" && $(this).prop("checked") == false) {
                                $(this).parent().siblings("li:last-child").remove()
                            }

                            //如果是多选获取可以获取一项最多选择几项的参数
                            if ($(this).parents(".part").hasClass("multipleQues")) {
                                maxChooseNum = $(this).parent().parent().attr('maxChooseNum')
                            }

                            // 如果获取出来选项是“不存在”，把其他选项取消选择
                            if (optionName == '不存在') {
                                if ($(this).attr("checked")) {
                                    $(this).parent().siblings("li").removeClass("active");
                                    $(this).parent().siblings("li").find("input").removeAttr("checked");
                                    $(this).parent().siblings("li").find("input").attr("disabled", true)
                                    if ($(this).parent().siblings("li:last-child").hasClass("other")) {
                                        $(this).parent().siblings("li:last-child").remove()
                                    }
                                } else {
                                    $(this).parent().siblings("li").find("input").attr("disabled", false)
                                }
                            }

                            // 单选和判断的高亮、isSelected字段控制
                            if ($(this).attr("type") && $(this).attr("type") == "radio") {
                                if (!$(this).parent("li").hasClass("active")) {
                                    $(this).parent("li").addClass(" active");
                                    $(this).parent("li").siblings().removeClass(" active");

                                    // 重测时isSelected字段表示上次已选择的选项
                                    var partClass = $(this).parents(".part").attr("class").split(" ")[1];
                                    var big_index = $(this).parents(".main").index("." + partClass + " .main"); // 在大题中的索引
                                    var small_index = $(this).parents().index(); // 在小题中的索引
                                    if ($(this).parents(".part").hasClass("singleQues")) {
                                        for (var i = 0; i < examData.singleQuestionList.length; i++) {
                                            for (var j = 0; j < examData.singleQuestionList[i].answerList.length; j++) {
                                                examData.singleQuestionList[i].answerList[j].isSelected = false;
                                            }
                                        }
                                        examData.singleQuestionList[big_index].answerList[small_index].isSelected = true;
                                    } else if ($(this).parents(".part").hasClass("judgeQues")) {
                                        for (var i = 0; i < examData.judgeQuestionList.length; i++) {
                                            for (var j = 0; j < examData.judgeQuestionList[i].answerList.length; j++) {
                                                examData.judgeQuestionList[i].answerList[j].isSelected = false;
                                            }
                                        }
                                        examData.judgeQuestionList[big_index].answerList[small_index].isSelected = true;
                                    }
                                }
                            };

                            // 多选的高亮、isSelected控制
                            if ($(this).attr("type") && $(this).attr("type") == "checkbox") {
                                if (!$(this).parent("li").hasClass("active")) {
                                    //最多只能选maxChooseNum个
                                    if ($(this).parents("ul").find("li.active").length >= maxChooseNum) {
                                        $(this).removeAttr("checked");
                                        //如果有其他 去掉显示的请输入框
                                        if ($(this).parent().next(".other")) {
                                            $(this).parent().next(".other").remove()
                                        }
                                        getparent().mesShow("温馨提示", "最多选择" + maxChooseNum + "项！", 2000, 'orange');
                                    } else {
                                        $(this).parent("li").addClass(" active");
                                    }
                                } else {
                                    $(this).parent("li").removeClass("active");
                                }
                                for (var i = 0; i < examData.moreQuestionList.length; i++) {
                                    for (var j = 0; j < examData.moreQuestionList[i].answerList.length; j++) {
                                        if ($(".multipleQues .main").eq(i).find("li").eq(j).hasClass("active")) {
                                            examData.moreQuestionList[i].answerList[j].isSelected = true;
                                        } else {
                                            examData.moreQuestionList[i].answerList[j].isSelected = false;
                                        }
                                    }
                                }
                            };
                            // 单选
                            if ($(this).parents(".part").hasClass("singleQues")) {
                                singleData = [];
                                for (var i = 0; i < $(".singleQues .main").length; i++) {
                                    if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                        singleData.push({
                                            questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                            examAnswer: $(".singleQues .main").eq(i).find("input[type='radio']:checked").val()
                                        });
                                    }
                                }
                            }
                            // 多选
                            if ($(this).parents(".part").hasClass("multipleQues")) {
                                multipleData = [];
                                for (var i = 0; i < $(".multipleQues .main").length; i++) {
                                    var mulAnswer = []; // 每道多选题选中的答案
                                    if ($(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
                                        $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
                                            mulAnswer.push($(this).val());
                                        });
                                        multipleData.push({
                                            questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
                                            examAnswer: mulAnswer.join('/')
                                        });
                                    }
                                }
                            }
                            // 判断
                            if ($(this).parents(".part").hasClass("judgeQues")) {
                                judgeData = [];
                                for (var i = 0; i < $(".judgeQues .main").length; i++) {
                                    if ($(".judgeQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                        judgeData.push({
                                            questionCode: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                            examAnswer: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").val()
                                        });
                                    }
                                }
                            }
                            // 设置提交按钮高亮是否显示
                            if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen) {
                                $("#submit").addClass(" canSubmit");
                            } else {
                                $("#submit").removeClass("canSubmit");
                            }
                        });
                        $("textarea").on("input propertychange", function () {// 简答
                            if ($(this).parents(".part").hasClass("shortAnswer")) {
                                shortData = [];
                                for (var i = 0; i < $(".shortAnswer .main").length; i++) {
                                    if ($(".shortAnswer .main").eq(i).find("textarea").val().trim()) {
                                        shortData.push({
                                            questionCode: $(".shortAnswer .main").eq(i).find("textarea").attr("id"),
                                            examAnswer: $(".shortAnswer .main").eq(i).find("textarea").val()
                                        });
                                    }
                                }
                            }

                            // 设置提交按钮高亮是否显示
                            if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen) {
                                $("#submit").addClass(" canSubmit");
                            } else {
                                $("#submit").removeClass("canSubmit");
                            }
                        });

                        if (isFinishExam) { // 已完成考试禁用表单
                            $(".questions input,.questions textarea").attr("disabled", true);
                            $("#submit").hide();
                        } else {
                            $(".questions input,.questions textarea").attr("disabled", false);
                            $("#submit").show();
                        }

                    }
                }

                // 点“提交”
                $("#submit2").click(function () {
                    if (singleData.length + multipleData.length + judgeData.length + shortData.length <= 0) {
                        getparent().mesShow("温馨提示", "还未开始答题", 2000, 'red');
                    } else if (singleData.length + multipleData.length + judgeData.length + shortData.length < singleLen + multipleLen + judgeLen + shortLen) {
                        getparent().mesShow("温馨提示", "试卷未答完，请继续答题！", 2000, 'red');
                    } else {
                        $("#submitDialog").dialog({ closed: false });
                    }
                    //判断其他选项的请输入的值
                    if ($(".other")) {
                        for (var i = 0; i < $(".other").length; i++) {
                            var other = $(".other").eq(i).prev().find("input")
                            var prevValue = other.val()
                            var prevValueSubB = prevValue.substring(0, 2)
                            var prevValueSub = prevValue.substring(2, prevValue.length)
                            var otherValue = $(".other").eq(i).find("textarea").val()

                            if (prevValue.indexOf(":") == -1) {
                                other.val(prevValue + ":" + otherValue)
                            } else if (prevValueSub != otherValue) {
                                other.val(prevValueSubB + otherValue)
                            }
                        }
                    }
                    // 用户改变其他选项 请输入的值的时候
                    multipleData = [];
                    for (var i = 0; i < $(".multipleQues .main").length; i++) {
                        var mulAnswer = []; // 每道多选题选中的答案
                        if ($(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
                            $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
                                mulAnswer.push($(this).val());
                            });
                            multipleData.push({
                                questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
                                maxChooseNum: $(".multipleQues .main").eq(i).find('.clearfix').attr("maxChooseNum"),
                                examAnswer: mulAnswer.join('/')
                            });
                        }
                    }

                    singleData = [];
                    for (var i = 0; i < $(".singleQues .main").length; i++) {
                        var singleAnswer = []; // 每道单选题选中的答案
                        if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                            $(".singleQues .main").eq(i).find("input[type='radio']:checked").each(function (index) {
                                singleAnswer.push($(this).val());
                            });
                            singleData.push({
                                questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                examAnswer: singleAnswer.join('/')
                            });
                        }
                    }
                });

                $("#sureSubmit2").click(function () {
                    submitData();
                });

                // 提交答案
                function submitData() {
                    clearInterval(remainTimeT);
                    var questionCodeArry = [];
                    var examAnswerArry = [];
                    var totalData = singleData.concat(multipleData, judgeData, shortData);

                    for (var i = 0; i < totalData.length; i++) {
                        questionCodeArry.push(totalData[i].questionCode);
                        examAnswerArry.push(totalData[i].examAnswer.replace(/,/g, "，"));
                    }



                    var verify = true
                    var verify2 = true
                    for (var u in multipleData) {
                        var it = multipleData[u].examAnswer.split(':')[0].split('/')
                        if (it.length > multipleData[u].maxChooseNum) {
                            verify = false
                        }
                        if (multipleData[u].examAnswer.indexOf(':') > -1) {
                            if (!multipleData[u].examAnswer.split(':')[1]) {
                                verify2 = false
                            }
                        }
                    }

                    if (!verify) {
                        getparent().mesShow("温馨提示", "多选选择超出限制，请检查！", 2000, 'orange');
                        $('#submitDialog').dialog('close')
                        return false

                    }

                    if (!verify2) {
                        getparent().mesShow("温馨提示", "其他选项的输入框未填写，请检查！", 2000, 'orange');
                        $('#submitDialog').dialog('close')
                        return false
                    }

                    //     {
                    //       examCode: gps.examCode,
                    //       examAppCode: gps.examAppCode,
                    //       publishUsername: username,
                    //       examRecord: questionCodeArry.join(','),
                    //       examAnswer: examAnswerArry.join(','),
                    //       questionBlankCode: questionBlankCode,
                    //       residueTime: totalSS,
                    //       examInfoList:totalData
                    //     },
                    // );

                    ajaxgeneral({
                        url: 'action/examInfo/submitExam',
                        data: {
                            examCode: gps.examCode,
                            examAppCode: gps.examAppCode,
                            publishUsername: username,
                            examRecord: questionCodeArry.join(','),
                            examAnswer: examAnswerArry.join(','),
                            questionBlankCode: questionBlankCode,
                            residueTime: totalSS,

                            examInfoList: totalData,//新增List，方便后端做校验
                        },
                        contentType: "application/json; charset=utf-8",
                        success: function (res) {
                            if (remainTimeT) clearInterval(remainTimeT);
                            // 禁止答题
                            $(".questions input,.questions textarea").attr("disabled", true);
                            isFinishExam = true;
                            $("#scoreDialog h5").html("提交成功！");
                            $("#yes").show();
                            moaBridge.close();
                            location.reload()
                            $("#submitDialog").dialog({ closed: true });
                            $("#scoreDialog").dialog({ closed: false });
                        }
                    });

                }

                // 全答对时关闭弹框
                $("#yes").click(function () {
                    $("#scoreDialog").dialog({ closed: true });
                    if (gps.actionType && gps.actionType == "secrecyTask") {
                        top.dialogClose("detail");
                    } else {
                        var userAgent = navigator.userAgent;
                        if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                            window.history.back();
                        } else {
                            window.opener = null;
                            window.open('', '_self');
                        }
                        if (gps.from) {
                            window.opener = null;
                            window.open('', '_self');
                            window.close();
                        } else {
                            $('#detailF', window.parent.document).parents().find('.more_close').trigger('click');
                        }
                        moaBridge.close();
                    }
                });


                // 试卷已完成时，关闭页面
                $("#closeBtns button").click(function () {
                    $('#closeDialog').dialog('close');
                    if (gps.actionType) {
                        top.dialogClose("detail");
                    } else {
                        var userAgent = navigator.userAgent;
                        if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                            window.history.back();
                        } else {
                            window.opener = null;
                            window.open('', '_self');
                        }
                        if (gps.from) {
                            window.opener = null;
                            window.open('', '_self');
                            window.close();
                        }
                        moaBridge.close();
                    }
                })
            }
        })
    </script>
    <style type="text/css">
        /*公共样式*/
        .clearfix:after {
            content: '.';
            display: block;
            height: 0;
            line-height: 0;
            clear: both;
            visibility: hidden;
        }

        .clearfix {
            zoom: 1;
        }

        .w15 {
            width: 15%;
        }

        /*页面样式*/
        /*背景颜色*/
        body {
            background-image: url("./img/pc.jpg");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            opacity: 0.95;
            margin: 0px;
            padding: 0px;
        }

        .intext {
            text-indent: 0.15rem;
            margin: 0.3rem auto;
        }

        .wrapper {
            width: 85%;
            margin: 0 auto;
            background-color: #fff;
            color: #000;
        }

        .header {
            text-align: center;
        }

        .header,
        .header img {
            width: 100%;
        }

        .details {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            margin: auto;
        }

        .explain {
            font-weight: bolder;
            font-size: 24px;
            text-align: center;
            width: 98%;
            margin: 10px auto;
            color: #D90000;
        }

        .questions {
            padding-bottom: 20px;
        }

        .questionType {
            font-size: 20px;
            font-weight: bold;
            line-height: 1.2;
            margin-top: 20px;
        }

        .main,
        .main ul {
            padding: 0 22px;
        }

        .shortAnswer .main ul {
            padding: 0 10px;
        }

        .main ul {
            background: #F5F5F5;
            margin-bottom: 20px;
        }

        .main h6 {
            font-size: 16px;
            line-height: 1.5;
            margin: 0 0 20px;
            font-weight: 600;
        }

        .main h5 {
            font-size: 18px;
            line-height: 1.5;
            font-weight: 600;
        }

        .main li {
            line-height: 1.5;
            margin: 15px 10px;
            display: inline-block;
            font-weight: 400;
        }

        .main li.fl {
            margin-top: 0;
        }

        .main li input[type=radio]:checked:before {
            background: #D90000;
        }

        .main .active input:focus {
            outline-color: red;
        }

        .main .active {
            color: #000;
        }

        .main .green {
            /* color: #09DB87; */
            color: #E11414;
        }

        .main .red {
            /* color: #E11414; */
            color: #E11414;
        }

        .main input {
            width: auto;
        }

        .main label {
            margin-left: 10px;
        }

        .shortAnswer .main textarea {
            min-height: 160px;
            font-size: 14px;
        }

        .icon-duihao1 {
            font-size: 16px;
            margin-left: 4px;
        }

        .icon-cuo1 {
            font-size: 14px;
            font-weight: bold;
            margin-left: 4px;
        }

        .submitBtn,
        .submitPiyueScore {
            border: 0;
            outline: 0;
            width: 90px;
            height: 36px;
            background: #B4B4B4;
            border-radius: 4px;
            font-size: 14px;
            color: #fff;
            /* margin: 10px 0 0 60px; */
            letter-spacing: 2px;
        }

        .submitBtn:active {
            opacity: .85;
        }

        .canSubmit {
            background-color: #E83333;
        }

        .dialog h5 {
            font-size: 15px;
            font-weight: bold;
            text-align: center;
            margin-top: 10px;
        }

        .forceSubmitDialog p {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-top: 20px;
        }

        .scoreDialog p {
            font-size: 12px;
            text-align: center;
        }

        .submitBtns button {
            border: 0;
            outline: 0;
            padding: 0;
            margin: 0;
            height: 32px;
            font-size: 12px;
            color: #fff;
            text-align: center;
            border-radius: 4px;
            padding: 0 20px !important;
        }

        .submitBtns .gray {
            background-color: #B4B4B4;
        }

        .submitBtns .red {
            background-color: #E11414;
        }

        .remainTime {
            font-size: 15px;
            font-weight: bold;
            margin-top: 20px;
            text-align: right;
        }

        .examTime {
            position: fixed;
            top: 100px;
            right: 40px;
            font-size: 24px
        }

        .examTime h3 {
            color: red
        }

        .fipt {
            font-size: 16px;
            border: none;
            border-bottom: 1px solid #000;
            padding: 0 5px;
        }

        /* 隐藏滚动条 */
        .wrapper::-webkit-scrollbar {
            width: 0.5em;
            /* 设置滚动条宽度 */
            background-color: transparent;
            /* 设置滚动条背景颜色为透明 */
        }

        /* 隐藏滚动条轨道 */
        .wrapper::-webkit-scrollbar-track {
            background-color: transparent;
            /* 设置滚动条轨道背景颜色为透明 */
        }

        /* 隐藏滚动条滑块 */
        .wrapper::-webkit-scrollbar-thumb {
            background-color: transparent;
            /* 设置滚动条滑块背景颜色为透明 */
        }

        .wenben {
            width: 400px;
            height: 72px;
        }

        .atext {
            margin-top: 10px;
        }

        #information {
            padding: 0 22px;
        }

        .shuoming {
            padding: 0 22px;
            text-indent: 2em;
        }

        .long {
            min-height: 160px;
            margin-top: 10px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        #part4,
        #part2,
        #part3 {
            display: none;
        }
        .baifen{
            outline: none;
            border-left: none;
            border-top: none;
            border-right: none;
            background: transparent;
            border-bottom: 1px solid;
            text-align: center;
            width: 100px !important;
            font-size: 16px;
            border-radius: 0;
        }
    </style>
</head>

<body style="height: 100%;overflow-y: scroll;">
    <div class="wrapper" style="display: none;">
        <div class="details">
            <!-- 试卷标题 -->
            <p class="explain">关于搬迁至中牟生产楼办公的意见调研问卷</p>
            <p class="explain department" style="color: #333;font-size: 16px;margin-bottom: 30px;"></p>

            <!-- 试卷说明 -->
            <p class="shuoming">
                为充分了解办公地点变迁对日常工作的影响，现面向各部门（中心）主要负责人征集意见。请您结合所在部门职责及实际工作情况，如实填写以下问题，为搬迁决策提供参考。信息仅用于分析汇总，<span style="color: red;font-weight: bolder;">请于3月5日下班前提交</span>，感谢您的参与！</p>
            <div id="remainTime" class="remainTime"></div>
            <div class="questions">
                <div class="part singleQues">
                    <div class="main">
                        <h6>一、您部门日常沟通联系对象，是面向公司系统内部（含集团、省市县公司）还是外部，对相关工作占比进行估算，并简要描述工作内容。</h6>
                        <ul>
                            <h5>1.内部占比: <span style="margin-left: 5px;"><input id="inside" class="baifen" name="inside" onkeyup="value=value.replace(/[^0-9.]/g,'')""
                                        placeholder=""></input></span>%</h5>
                            <h4 style="padding:2px 0;">工作内容：</h4>
                            <p><textarea id="taskContent" class="long" placeholder=""></textarea></p>
                        </ul>
                        <ul>
                            <h5>2.外部占比: <span style="margin-left: 5px;"><input id="outside" class="baifen" name="outside" onkeyup="value=value.replace(/[^0-9.]/g,'')""
                                        placeholder=""></input></span>%</h5>
                            <h4 style="padding:2px 0;">工作内容：</h4>
                            <p><textarea id="outsideContent" class="long" placeholder=""></textarea></p>
                        </ul>

                    </div>
                </div>
                <div class="part singleQues part2">
                    <div class="main">
                        <h6>二、您部门日常工作对工作地点有哪些特殊需求？</h6>
                        <ul>
                            <li><input type="radio" id="specialNeed1" name="specialNeed" value="1"><label>1. 无需求</label>
                            </li>
                            <li><input type="radio" id="specialNeed2" name="specialNeed" value="2"><label>2.
                                    有需求（描述具体需求）</label></li>
                        </ul>
                    </div>
                </div>
                <div class="part shortAnswer" id="part2">
                    <div class="main shortAnswer">
                        <p><textarea id="remark" placeholder="请填写备注"></textarea></p>
                    </div>
                </div>
                <div class="single" style="height: 20px;"></div>
                <div class="part singleQues part3">
                    <div class="main">
                        <h6>三、如果搬迁至中牟生产楼，对您部门工作开展是否有影响，并说明理由（无影响可不填）。</h6>
                        <ul>
                            <li><input type="radio" name="effect" id="effect1" value="1"><label>1. 基本无影响</label></li>
                            <li><input type="radio" name="effect" id="effect2" value="2"><label>2. 有一定影响</label></li>
                            <li><input type="radio" name="effect" id="effect3" value="3"><label>3. 有较大影响</label></li>
                        </ul>
                    </div>
                </div>
                <div class="part shortAnswer" id="part3">
                    <div class="main shortAnswer">
                        <p><textarea id="reasons" placeholder="请填写说明理由"></textarea></p>
                    </div>
                </div>
                <div class="single" style="height: 20px;"></div>
                <div class="part singleQues part4">
                    <div class="main">
                        <h6>四、如果搬迁至中牟生产楼，据您了解对您部门员工生活是否有影响，并说明理由（无影响可不填）。</h6>
                        <ul>
                            <li><input type="radio" name="lifeEffect" id="lifeEffect1" value="1"><label>1. 基本无影响</label>
                            </li>
                            <li><input type="radio" name="lifeEffect" id="lifeEffect2" value="2"><label>2. 有一定影响</label>
                            </li>
                            <li><input type="radio" name="lifeEffect" id="lifeEffect3" value="3"><label>3. 有较大影响</label>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="part shortAnswer" id="part4">
                    <div class="main shortAnswer">
                        <p><textarea id="lifeReason" placeholder="请填写说明理由"></textarea></p>
                    </div>
                </div>
                <div class="single" style="height: 20px;"></div>
                <div class="part shortAnswer">
                    <div class="main shortAnswer">
                        <h6>五、结合部门工作，您对搬迁工作有哪些具体意见建议。</h6>
                        <p><textarea id="suggestion"></textarea></p>
                    </div>
                </div>


            </div>

            <p class="shuoming examRemarkFooter"></p>
            <!-- 提交 -->
            <div style="width: 100%;text-align: center;">
                <button class="submitBtn canSubmit" id="submit">提交</button>
            </div>
            <!-- 提交对话框 -->
            <div id="submitDialog" class="easyui-dialog dialog" title="温馨提示" closed="true"
                data-options="modal:true,buttons:'#submitBtns'" style="width:400px;height:200px;padding:10px">
                <h5>您已填写完成，是否提交问卷？</h5>
            </div>
            <div class="btns submitBtns" id="submitBtns" style="text-align:center;">
                <button class="easyui-linkbutton gray" onclick="$('#submitDialog').dialog('close')">取消</button>
                <button id="sureSubmit" class="easyui-linkbutton red">是的确认提交！</button>
            </div>

            <!-- 提交成功对话框 -->
            <div id="scoreDialog" class="easyui-dialog dialog scoreDialog" title="温馨提示" closed="true"
                data-options="modal:true,buttons:'#scoreBtns'" style="width:400px;height:200px;padding:10px">
                <h5></h5>
                <!--<p>（注：简答题不计入合格分数，只计入参考分值）</p>-->
            </div>
            <div class="submitBtns scoreBtns" id="scoreBtns" style="text-align:center;">
                <button id="reTry" class="easyui-linkbutton red hide">查看问卷</button>
                <button id="yes" class="easyui-linkbutton red hide">确定</button>
            </div>

            <!-- 打开试卷时，试卷已完成，关闭页面 -->
            <div id="closeDialog" class="easyui-dialog dialog closeDialog" title="温馨提示" closed="true"
                data-options="modal:true,buttons:'#closeBtns'" style="width:400px;height:200px;padding:10px">
                <h5>您已完成调查问卷，感谢您的参与！</h5>
            </div>
            <div class="submitBtns" id="closeBtns" style="text-align:center;">
                <button class="easyui-linkbutton red">确定</button>
            </div>
        </div>
        <div class="footer"></div>
    </div>
    <map name="Map" id="Map" style="cursor: pointer;border: 1px solid #000;">
        <area shape="rect" coords="143,433,308,372" target="_blank">
    </map>
    <div id="editNumDialog" class="hide"
        style="position: absolute;top: 50%;left: 50%;width: 500px;height: 231px;margin-left: -250px;margin-top: -115px;z-index: 999">
        <img src="/exam/html/exammanage/jpeg.gif" alt="">
    </div>
</body>

</html>