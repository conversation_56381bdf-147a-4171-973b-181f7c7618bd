/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate.path.model;

/**
 * <strong>Title : TempFilePath</strong><br>
 * <strong>Description : 临时文件路径 </strong><br>
 * <strong>Create on : 2020/11/10</strong><br>
 * <strong>Modify on : 2020/11/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public class TempFilePath {
    /**
     * 文件路径，例如/a
     */
    private String path;

    /**
     * 文件名称，例如b.txt
     */
    private String name;

    public TempFilePath(String path, String name) {
        super();
        this.path = path;
        this.name = name;
    }

    public String getPath() {
        return this.path;
    }

    public String getName() {
        return this.name;
    }

    public String getPathName() {
        return this.path + "/" + this.name;
    }

    @Override
    public String toString() {
        return getPathName();
    }
}
