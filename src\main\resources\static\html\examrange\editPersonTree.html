<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
    xmlns:th="http://www.thymeleaf.org">
<head>
  <title>新增</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
  <!--
  <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
  -->
  <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
  <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
  <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<style type="text/css">
  .ztree li ul.line{height:auto;}
  .keyinput{width: 480px;padding: 10px;}
  #key{width: 415px;}
  .clear{position: fixed;top: 10px;right: 10px; display: inline-block;width: 65px;height: 26px;line-height: 26px;text-align: center;background-color: #e34d4d;color:#fff;border-radius: 2px;z-index:9}
  .orgC{padding-top: 35px;} 
  
</style>
<body class="page_body">



<script type="text/javascript">
  var gps=getQueryString();
  var msgPerson = [];
  var newData = [];
  var nodeFlag;
  $(function(){
    getCurrent();
    initTree();
    $(document).on("click",".role a i",function(){//删除当前选择
      var id = $(this).parent("a").attr('id');
      for (var i = 0; i < msgPerson.length; i++) {
        if (id === msgPerson[i].id) {
          msgPerson.splice(i,1);
        }
      }
      clearArr(msgPerson);
      $(this).parent("a").remove();
    });

    //全部清除按钮
    $(document).on("click",".clear",function(){
      $(".role").html("");
      msgPerson = [];
    });
  });



  //查询人员信息
  window.editPerson = function (data) {
    ajaxgeneral({
      url: "action/rangeUserInfo/findAllUserInfoByGroupId",
      data:data.data,
      contentType: "application/json; charset=utf-8",
      success: function (res) {
      }
    })
  }
  //返回数据
  window.getchoosedata=function(){
    var datas=msgPerson;
    return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
  };




  //初始化
  function initTree() {
    $("#userTree").css("height",$(document).height());
    var ajaxopts = {
      url:"action/range/findOneStep?appcode="+web.appCode,
      data:{},
      contentType: "application/json; charset=utf-8",
      success: function (data) {
        userIdzNodes = data.data;
        userTree(userIdzNodes);
      }
    };
    ajaxgeneral(ajaxopts);
  }



  //树
  function userTree(userIdzNodes) {
    var datas=toTreeData(userIdzNodes,"name","id","id|id,name|text,id,name,treeType,orgDisplayName");
    $("#userTree").tree({
      lines:true,//是否显示树控件上的虚线
      treePid:'id',
      contentType: "application/json; charset=utf-8",
      cascadeCheck:false,
      data: datas,
      fileds:'id|id,name|text,id,name,treeType',
      animate:true,//节点在展开或折叠的时候是否显示动画效果
      onClick:function(node){
        if(node){
          if (node.treeType != 'user') {
            if(!node.children){//只能展开组织
              ajaxgeneral({
                url:"action/range/findOneStep?appcode="+web.appCode+"&orgCode="+node.id,
                data:{},
                contentType: "application/json; charset=utf-8",
                success:function(data){
                  if(data.data.length!=0){
                    var datas=toTreeData(data.data,"name","id","id|id,name|text,id,name,treeType,orgDisplayName");
                    if (!node.children) {
                      $("#userTree").tree("append", {
                      parent : node.target,
                      data : datas
                    });
                    }

                  }
                }
              });
            }
          }else{
            for (var index = 0; index < msgPerson.length; index++) {
              if (node.id === msgPerson[index].id) {
                top.mesAlert("提示", "成员【"+node.name+"】已存在，无需再次添加！", 'warning');     
                return;       
              }
            }
            //console.log(node.orgDisplayName);
            msgPerson[msgPerson.length] = {
              name:node.name,
              userOrgName:node.orgDisplayName,
              id:node.id,
              treeType:node.treeType,
            }
            //console.log(node);
            $(".role").append("<a name='"+node.name+"' userOrgName="+node.orgDisplayName+" treeType='"+node.treeType+"'  id='"+node.id+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
          }
        }
      },

      
      onDblClick:function(node){
        if (node.treeType != 'user') {
          ajaxgeneral({
            url:"action/range/findOneStep?appcode="+web.appCode+"&orgCode="+node.id,
            data:{},
            contentType: "application/json; charset=utf-8",
            success:function(data){
              
                //如果双击分组则把分组所有人添加入收信方
                var datas = data.data;
                newData = [];
                //遍历接收数据提取类型为 user 的需要的字段
                for (var j = 0; j < datas.length; j++) {
                  if (datas[j].treeType == 'user') {
                    newData[j] = {};
                    newData[j].name = datas[j].name;
                    newData[j].id = datas[j].id;
                    newData[j].userOrgName = datas[j].orgDisplayName;
                    newData[j].treeType = datas[j].treeType;
                  }
                }

                //拼接新旧已选择人员数组为一个新数组
                //判断筛除重复的字段
                newData = newData.concat(msgPerson);
                for (var index = 0; index < msgPerson.length; index++) {  
                  for (var i = 0; i < newData.length; i++) {
                    if (msgPerson[index].id === newData[i].id) {
                      newData.splice(i,1);
                      if (i == 0) {
                        i = -1;
                      }else{
                        i--;
                      }
                    }
                  }
                }
                //清除数组中值为空的键
                newData = clearArr(newData);
                msgPerson = newData.concat(msgPerson);
                //清除右侧已选择后重新渲染
                $(".role a").remove();
                for (var i = 0; i < msgPerson.length; i++) {
                  $(".role").append("<a name='"+msgPerson[i].name+"' userOrgName="+msgPerson[i].userOrgName+"  id='"+msgPerson[i].id+"'><font>"+msgPerson[i].name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                }
            }
          })
        }

      },
      onSelect:function(node){
      },
      onLoadSuccess:function(node,data){
        // $("#userTree").find("ul >li:not(:has(ul))").find('.tree-title').css({'background':'whith','border':'whith','color':'black'});   // 选择最后一个节点 并添加 float:left
      },
      //选择之前
      onBeforeSelect:function(node){
      }
    });
  }

    //清除数组中值为空的键
    function clearArr(arr) {
      var newArr = [];
      for (var k = 0; k < arr.length; k++) {
        if (arr[k]) {
          newArr.push(arr[k]);  
        }                        
      }
      arr = newArr;
      return arr;
    }

    //点击查询
    function chooseuser() {
      initTree($('#key').val());
    };
</script>

<ul id="userTree"></ul>
<div class="role orgC"></div>
<div class="clear">全部清空</div>

</body>
</html>
