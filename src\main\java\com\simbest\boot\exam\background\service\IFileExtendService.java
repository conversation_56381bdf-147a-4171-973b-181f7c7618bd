package com.simbest.boot.exam.background.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.background.model.ImagePath;
import com.simbest.boot.sys.model.SysFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 作用：附件扩展类
 * 作者：zsf
 * 时间：2018/07/28
 */
public interface IFileExtendService extends ILogicService<SysFile, String> {



    /**
     * 上传图片文件
     */
    List<SysFile> uploadProcessFiles(Collection<MultipartFile> multipartFiles);


}
