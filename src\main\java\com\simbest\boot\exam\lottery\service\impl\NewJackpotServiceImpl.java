/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.lottery.model.NewJackpot;
import com.simbest.boot.exam.lottery.model.RetJackpot;
import com.simbest.boot.exam.lottery.repository.NewJackpotRepository;
import com.simbest.boot.exam.lottery.service.INewJackpotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.Map;

/**
 * <strong>Title : NewJackpotServiceImpl</strong><br>
 * <strong>Description : 新奖池Service </strong><br>
 * <strong>Create on : 2021/4/10</strong><br>
 * <strong>Modify on : 2021/4/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Service
@Slf4j
public class NewJackpotServiceImpl extends LogicService<NewJackpot,String> implements INewJackpotService {
    private NewJackpotRepository repository;

    public NewJackpotServiceImpl(NewJackpotRepository repository){
        super(repository);
        this.repository=repository;

    }

    @Override
    public NewJackpot findJackpotIsResidue(String prize) {
        return this.findOne(Specifications.<NewJackpot>and()
                .eq("prize", prize)
                .eq("enabled", true)
                .build());
    }

    @Override
    public NewJackpot findJackpotIsResidueByRandomNumber(String prize, String randomNumber) {
        return this.findOne(Specifications.<NewJackpot>and()
                .eq("prize", prize)
                .eq("randomNumber", randomNumber)
                .eq("enabled", true)
                .build());
    }
}
