package com.simbest.boot.exam.examOnline.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamAttribute;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.model.ExamInfoSave;
import com.simbest.boot.exam.examOnline.repository.ExamInfoRepository;
import com.simbest.boot.exam.examOnline.repository.ExamInfoSaveRepository;
import com.simbest.boot.exam.examOnline.service.IExamAttributeService;
import com.simbest.boot.exam.examOnline.service.IExamInfoSaveService;
import com.simbest.boot.exam.examOnline.service.IExamInfoService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.OperateLogTool;
import com.simbest.boot.exam.wfquey.service.IQueryDictValueService;
import com.simbest.boot.mq.service.SystemRabbitService;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 用途：考试管理模块--考试答题明细service
 * 作者：gy
 * 时间: 2021-02-23 20:01
 */
@Slf4j
@Service
public class ExamInfoSaveServiceImpl extends LogicService<ExamInfoSave, String> implements IExamInfoSaveService {
    private final ExamInfoSaveRepository repository;
    private final ExamInfoRepository examInfoRepository;
    private final OperateLogTool operateLogTool;
    private final ISysOperateLogService operateLogService;
    private final IQueryDictValueService queryDictValueService;
    @Autowired
    private  IExamInfoService examInfoService;
    @Autowired
    private  IExamAttributeService examAttributeService;
    private final LoginUtils loginUtils;
    private final RsaEncryptor encryptor;
    private final SystemRabbitService systemRabbitService;

    private final static ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    @Autowired
    public ExamInfoSaveServiceImpl(ExamInfoSaveRepository repository, ExamInfoRepository examInfoRepository, OperateLogTool operateLogTool, ISysOperateLogService operateLogService, IQueryDictValueService queryDictValueService, LoginUtils loginUtils, RsaEncryptor encryptor, SystemRabbitService systemRabbitService) {
        super(repository);
        this.repository = repository;
        this.examInfoRepository = examInfoRepository;
        this.operateLogTool = operateLogTool;
        this.operateLogService = operateLogService;
        this.queryDictValueService = queryDictValueService;
        this.loginUtils = loginUtils;
        this.encryptor = encryptor;
        this.systemRabbitService = systemRabbitService;
    }

    /**
     * 保存试卷
     *
     * @param currentUserCode 当前人编码
     * @param source          请求来源
     * @param o               提交考试数据表
     * @return 返回答题记录信息
     */
    @Override
    public ExamInfoSave saveExam(String currentUserCode, String source, ExamInfoSave o) {
        // 准备日志数据
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/saveExam";
        String params = String.format(",source=%s,userCode=%s,form=%s", source, currentUserCode, o);
        operateLog.setInterfaceParam(params);
        log.debug("自动保存 接口----------{}---------->{}", param2, o);
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "/action/examInfo", param2, operateLog);
        if (Objects.nonNull(returnObj)) throw new IllegalStateException("保存考试出错！");

        try {
            String userName = SecurityUtils.getCurrentUserName();
            Optional<ExamInfo> examInfo = Optional.ofNullable(examInfoRepository.findAllByNew(userName, o.getExamCode(), o.getExamAppCode()));
            if (examInfo.isPresent() && examInfo.get().getIsFinishExam()) {
//                  throw new IllegalStateException("考试已结束，无法提交！");
                ExamInfoSave save = new ExamInfoSave();
                BeanUtils.copyProperties(examInfo.get(), save, "id");
                return save;
            }

            Optional<ExamInfoSave> byNew = Optional.ofNullable(repository.findAllByNew(userName, o.getExamCode(), o.getExamAppCode()));
            // 解密时间戳
            o.setTimestamp(Long.valueOf(o.getSalt()));
            // 如果用户已有保存记录, 进行多终端判断
            if (byNew.isPresent()) {
                ExamInfoSave info = byNew.get();
                if ((Objects.nonNull(o.getTimestamp())
                        && Objects.nonNull(info.getTimestamp()))
                        && (o.getTimestamp() < info.getTimestamp())) {
                    throw new IllegalStateException("已在另一终端打开, 将在5秒后关闭");
                }
            }

            // 每次 进入/重新进入 答题页面时 增加答题次数
            int number = byNew.orElseGet(ExamInfoSave::new).getExamNumber();
            if (BooleanUtils.isTrue(o.getIsVisit())) number++;
            o.setExamNumber(number);

            // 校验答题次数，超过答题次数自动提交考试
//            if (!this.checkExamNumber(number, o.getExamCode())) {
//                ExamInfo info = examInfoService.findExamInfo(userName, o.getExamCode(), o.getExamAppCode());
//                info = examInfoService.submitExamSalt(userName, Constants.SOURCE_P, info, false);
//                info = examAttributeService.computeScore(userName, o.getExamCode(), o.getExamAppCode());
//                BeanUtils.copyProperties(info, o, "id");
//                return o;
//            }

            // 设置基础信息
            o.setId(null);
            o.setSource(source);

            // 第一次保存时创建延时任务,自动交卷，自动算分
//            if (!byNew.isPresent()) this.createTask(userName, o);

            return super.insert(o);
        } finally {
            operateLogService.saveLog(operateLog);
            systemRabbitService.operateLogSend(operateLog);
        }
    }

    /**
     * 查询最新一条考试信息
     *
     * @param publishUsername 办理人
     * @param examCode        考试code
     * @param examAppCode     试卷code
     */
    @Override
    public ExamInfoSave findByNewInfo(String publishUsername, String examCode, String examAppCode) {
        return repository.findAllByNew(publishUsername, examCode, examAppCode);
    }

    /***
     * 校验答题次数是否合法
     * <br/> params [number]
     *
     * @return {@link boolean}
     * <AUTHOR>
     * @since 2023/9/12 16:24
     */
    private boolean checkExamNumber(int number, String examCode) {
        Optional<SysDictValue> first = queryDictValueService.queryByType("EXAM_NUMBER_TYPE")
                .stream().filter(v -> examCode.contains(v.getName())).findFirst();
        if (!first.isPresent()) return true;
        return number <= Integer.parseInt(first.get().getValue());
    }

    /***
     * 创建延时任务
     * <br/> params [userName, o]
     *
     * <AUTHOR>
     * @since 2023/9/14
     */
    private void createTask(String userName, ExamInfoSave o) {
        String setTime = examAttributeService.findOne(Specifications.<ExamAttribute>and().eq("examAppCode", o.getExamAppCode()).build()).getSetTime();
        long time = Long.parseLong(setTime);

        log.info("延时任务创建---userName:{}---到期时间:{}", userName, LocalDateTime.now().plusMinutes(time));

        // 自动交卷, 自动算分
        Runnable task = () -> {
            log.info("延时任务执行开始---userName:{}---时间:{}", userName, LocalDateTime.now());

            try {
                loginUtils.manualLogin(encryptor.encrypt(userName), Constants.APP_CODE);
                ExamInfo info = examInfoService.findExamInfo(userName, o.getExamCode(), o.getExamAppCode());
                info = examInfoService.submitExamSalt(userName, Constants.SOURCE_P, info, false);
                info = examAttributeService.computeScore(userName, o.getExamCode(), o.getExamAppCode());
            } catch (Exception e) {
                Exceptions.printException(e);
            }

            log.info("延时任务执行完成---userName:{}---时间:{}", userName, LocalDateTime.now());
        };

        // 添加延时任务
        scheduler.schedule(task, time, TimeUnit.MINUTES);
    }

}
