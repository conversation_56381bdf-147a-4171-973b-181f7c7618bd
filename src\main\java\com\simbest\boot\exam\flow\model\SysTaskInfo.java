package com.simbest.boot.exam.flow.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Table;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * @ClassName: UsTaskInfo
 * @description: 任务信息表
 * @author: ZHAOBO
 * @create: 2024-03-07 18:15
 */

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity(name = "sys_task_info")
@Table(appliesTo = "sys_task_info", comment = "任务信息表")
@ApiModel(description = "任务信息表")
public class SysTaskInfo extends WfFormModel{

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "STI") //主键前缀
    private String id;

    @ApiModelProperty(value = "流程实例id")
    @Column(length = 100)
    private String processInstId;

    @ApiModelProperty(value = "任务实例id")
    @Column(length = 100)
    private String taskId;

    @ApiModelProperty(value = "主数据id")
    @Column(length = 100)
    private String businessId;

    @ApiModelProperty(value = "主单据编码")
    @Column(length = 40)
    private String pmInsId;

    @ApiModelProperty(value = "主单据类型")
    @Column(length = 40)
    private String pmInsType;

    @ApiModelProperty(value = "标题")
    @Column(length = 200)
    private String title;

    @ApiModelProperty(value = "创建人")
    @Column(length = 100)
    private String createUsername;

    @ApiModelProperty(value = "创建人姓名")
    private String createTrueName;

    @ApiModelProperty(value = "上一环节ID")
    @Column(length = 40)
    private String formTaskId;

    @ApiModelProperty(value = "流程编码")
    @Column(length = 100)
    private String processDefId;

    @ApiModelProperty(value = "流程名称")
    @Column(length = 100)
    private String processDefName;

    @ApiModelProperty(value = "当前办理环节")
    @Column(length = 100)
    private String activityDefId;

    @ApiModelProperty(value = "当前办理环节名称")
    @Column(length = 100)
    private String activityDefName;

    @ApiModelProperty(value = "当前办理人部门信息")
    @Column(length = 100)
    private String displayName;

    @ApiModelProperty(value = "办理人")
    @Column(length = 100)
    private String taskUsername;

    @ApiModelProperty(value = "办理人姓名")
    @Column(length = 100)
    private String taskTrueName;

    @ApiModelProperty(value = "下一环节")
    @Column(length = 100)
    private String nextActivityDefId;

    @ApiModelProperty(value = "下一环节名称")
    @Column(length = 100)
    private String nextActivityDefName;

    @ApiModelProperty(value = "下一环节审批人， 用逗号分隔")
    @Column(length = 1000)
    private String nextTrueNames;

    @ApiModelProperty(value = "任务开始时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime startTime;

    @ApiModelProperty(value = "任务结束时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime endTime;

    @ApiModelProperty(value = "状态 10未办理、12已办理 , 100 办理中")
    private Integer status;

    @Transient
    @ApiModelProperty(value = "下一步审批人")
    private String nextInfos;

    @ApiModelProperty(value = "代表这是个待阅")
    @Column(length = 10)
    private String todoType;


    @ApiModelProperty(value = "接收手机号 ")
    @Column(length = 40)
    private String  applyPhone;

    @ApiModelProperty(value = "短信发送状态（null 未发送 1发送成功 2 发送失败） ")
    @Column(length = 40)
    private String  messageStatus;
}
