package com.simbest.boot.exam.appraise.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

/**
 * ExternalInterfaceLog
 *
 * <AUTHOR>
 * @since 2024/1/25 14:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity(name = "us_external_interface_log")
@ApiModel(description = "三方接口调用记录")
public class ExternalInterfaceLog extends LogicModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EIL") //主键前缀
    private  String id;

    @Column(length = 2000)
    @ApiModelProperty(value = "接口地址")
    private String urlInfo;

    @Lob
    @ApiModelProperty(value = "请求参数")
    private String paramInfo;

    @Lob
    @ApiModelProperty(value = "返回结果")
    private String resultInfo;

    @ApiModelProperty(value = "请求状态")
    @Column(length = 10)
    private String status;

    @Column(length = 40)
    @ApiModelProperty(value = "单据ID", required = true)
    private String pmInsId;

    @Column(length = 10)
    @ApiModelProperty(value = "调用类型 0 调用其他接口 1 接口被调用")
    private String type;

}
