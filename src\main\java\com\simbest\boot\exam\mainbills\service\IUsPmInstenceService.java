package com.simbest.boot.exam.mainbills.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.mainbills.model.UsPmInstence;

import java.util.List;
import java.util.Map;

/**
 * <strong>Title : IPmInstenceService</strong><br>
 * <strong>Description : 业务主单据表业务操作</strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IUsPmInstenceService extends ILogicService<UsPmInstence,String> {

    /**
     * 逻辑删除操作
     * @param id
     * @return
     */
    int deleteByPmId(Long id);

    /**
     * 根据pmInsId查找主单据
     * @param pmInsId 单据ID
     * @return
     */
    UsPmInstence findByPmInsId(String pmInsId);

    /**
     *
     * @param title 代办名称
     * @param sign 是否完成代办
     * @return
     */
    List<UsPmInstence> findByPmInsIdTitle(String title, String sign);

    /**
     * 获取待办参数
     * @param id  状态表id
     * @return
     */
    Map<String, Object> getTodoParams(String id);

}
