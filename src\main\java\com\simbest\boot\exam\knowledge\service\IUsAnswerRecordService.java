/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsAnswerRecord;
import org.springframework.data.repository.query.Param;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
public interface IUsAnswerRecordService extends ILogicService<UsAnswerRecord,String> {

    /**
     * 根据答题类型查询 当日已达问题列表
     * @param dailyQuestionBankCode
     * @param workType
     * @param username
     * @return
     */
    List<UsAnswerRecord> findTodyAnswerRecordByWorkType(String dailyQuestionBankCode, String workType,String username);

    JsonResponse getRecordList(Integer page, Integer size,String direction,String properties,String source,String currentUserCode,String workType,String time);

    /**
     * 查询答题记录--人人对战使用
     * @param pmInsId
     * @return
     */
    List<UsAnswerRecord> findRecordByPmInsId(String pmInsId);

    JsonResponse getSocreRanking(int page, int size, String direction, String properties, String source, String currentUserCode);

    JsonResponse getRecordRankingById(String id, String source, String currentUserCode);

    void exportSocreRanking(HttpServletRequest request, HttpServletResponse response, String source, String currentUserCode);

    /**
     * 查询答题记录，如果题目存在，则不用再次抽取题目
     * @param invitaitonId
     * @return
     */
    List<UsAnswerRecord> findRecordByInvitaitonId(String invitaitonId);

    List<UsAnswerRecord> findAnswerRecordByUser(String knowledgeQuestionBankCode, String creator);
}
