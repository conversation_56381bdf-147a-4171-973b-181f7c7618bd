<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org"  style="height: 100%;">
<head>
    <title>2022年全面从严治党监督责任履行情况测评问卷</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
    <meta name="format-detaction" content="telephone=no"/>
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/moaBridge_oa_wjdc.js?v=svn.revision"  type="text/javascript"></script>
    <style>
        #piyueScore {
            width: unset;constructExamLayout
            border-color: gray;
        }
        .optionText{
            margin-top: 10px;
        }
        .optionText span.optionLeft{
            float: left;
            font-size: 14px;
            font-weight: bold;
        }
        .optionText .surplusNum{
            padding-left: 64px;
        }
        .optionText textarea{
            min-width: 300px;
            width: 50%;
            border-radius: 5px;
        }
        .surplusNum{
            font-size: 14px;
        }
    </style>
    <script type="text/javascript">


        getCurrent()
        var allQuestionCode = []
        var timeFlag = 0; // 提交数据的标识
        var remainTimeT; // 保存记录的计时器
        var examLists=[];//存储的试题信息
        var examAppCode = "";
        var companyName='';
        var totalSS = 0; // 考试时间
        var questionBlankCode = ''; // 题库编码
        var singleLen = 0, multipleLen = 0, judgeLen = 0, shortLen = 0; // 题目数量
        var singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案
        var examAnswerText = {}//没有成效问题答案
        var examData = {}; // 试卷模板
        var examRecordId = null; // 答题记录的id
        var currentAction = "";
        var isFinishExam = false; // 是否完成考试
        $(function(){
            $("#examOverDialog").dialog({closed:true});
            var userAgent = navigator.userAgent;
            /**单点配置**/
            var username = "";
            var gps = getQueryString();
            if(gps.type == "piyue") {
                $("#submit").hide()
                $(".examTime").hide()
            }
            if (gps.type != "piyue") {
                $(".pleasePiyue").hide()
                $("#piyueScore").hide()
                $(".submitPiyueScore").hide()
            }
            // 页面认证问题
            if(gps.from=="oa"){
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.uid,
                    async: false,
                    success: function (ress) {
                        // alert(ress.data)
                        username = ress.data.username;

                    }
                });
            }else if(gps.access_token){//手机办公
                // alert('获取用户信息');
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.currentUserCode,
                    async: false,
                    success: function (ress) {
                        // alert(ress.data)
                        username = ress.data.username;
                        if(ress.data.belongCompanyTypeDictValue=='01'){
                            companyName='省公司';
                        }
                        if(ress.data.belongCompanyTypeDictValue=='02'){
                            companyName=ress.data.belongCompanyName;
                        }
                        if(ress.data.belongCompanyTypeDictValue=='03'){
                            companyName=ress.data.belongCompanyNameParent;
                        }
                    }
                });
            }else {
                getCurrent();
                username = web.currentUser.username;
            }
            ajaxgeneral({//获取背景图片
                url: "action/image/findUseImagePathByExamAppCode?examAppCode=" + gps.examAppCode,
                async: false,
                success: function (ress) {
                    if(ress.data){
                        if (ress.data.mobileFilePath) {
                            document.body.style.backgroundImage="url("+ress.data.mobileFilePath+"),url(survey.jpg)";
                        }
                    }
                }
            });

            ajaxgeneral({
                url: 'action/examWork/findByTransactorCodeAndExamCode?userName='+gps.currentUserCode+'&examCode='+gps.examCode,
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    if (!res.data){
                        isexamCode = false
                        $.messager.alert('温馨提示！','您不需要进行该问卷调查','info',function(){
                            top.dialogClose('detail')
                            window.opener=null;
                            window.open('','_self');
                            window.close();
                        })
                        for (var i=0;i<$('.panel-title').length;i++){
                            if($('.panel-title').eq(i).text()=="温馨提示！"){
                                $('.panel-title').eq(i).siblings('.panel-tool').remove()
                            }
                        }
                    }else {
                        isexamCode = true
                    }
                }
            })
            if (gps.currentUserCode&&gps.type === "piyue") {
                ajaxgeneral({//获取评阅相关信息
                    url: "action/examInfo/findExamInfoByExamCode?creator=" + gps.currentUserCode + "&examCode=" + gps.examCode,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function (res) {
                        if (res.data) {
                            $("#piyueScore").val(res.data.score);
                            $(".submitPiyueScore").addClass("canSubmit");
                        }else{
                            $(".submitPiyueScore").removeClass("canSubmit");
                        }
                    }
                });
            }

            if(gps.actionType && gps.actionType=="secrecyJoin"){ // 已办
                $(".submitBtn").hide();
            }else{

                if (gps.type != "piyue") {
                    $(".submitBtn").show();
                }
                //禁用鼠标右边
                document.oncontextmenu = function(){
                    getparent().mesShow("温馨提示","请手动答题",2000,'red');
                    return false;
                };
                //禁用ctrl+v功能
                document.onkeydown = function(){
                    if (event.ctrlKey && window.event.keyCode==86){
                        getparent().mesShow("温馨提示","请手动答题",2000,'red');
                        return false;
                    }
                };
            }

            // 秒数格式化为hh:mm:ss
            function countdown(totalSS){
                var hh = Math.floor(totalSS/3600).toString().length<2?'0'+Math.floor(totalSS/3600):Math.floor(totalSS/3600);
                var mm = Math.floor((totalSS%3600)/60).toString().length<2?'0'+Math.floor((totalSS%3600)/60):Math.floor((totalSS%3600)/60);
                var ss = Math.floor((totalSS%3600)%60).toString().length<2?'0'+Math.floor((totalSS%3600)%60):Math.floor((totalSS%3600)%60);

                var nowTime = hh+':'+mm+':'+ss;
                return nowTime
            }
            // 题目序号和答案序号格式化,0题目转为汉字，1选项转为大写英文字母
            function formatNumber(type,num) { // 0题目序号  1选项序号
                num = parseInt(num);
                var res = '';
                if (type == 0) {
                    var arr1 = new Array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九');
                    var arr2 = new Array('', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿');//可继续追加更高位转换值
                    if (!num || isNaN(num)) {
                        return "零";
                        return "零";
                    }
                    var english = num.toString().split("")
                    var result = "";
                    for (var i = 0; i < english.length; i++) {
                        var des_i = english.length - 1 - i;//倒序排列设值
                        result = arr2[i] + result;
                        var arr1_index = english[des_i];
                        result = arr1[arr1_index] + result;
                    }
                    //将【零千、零百】换成【零】 【十零】换成【十】
                    result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');
                    //合并中间多个零为一个零
                    result = result.replace(/零+/g, '零');
                    //将【零亿】换成【亿】【零万】换成【万】
                    result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
                    //将【亿万】换成【亿】
                    result = result.replace(/亿万/g, '亿');
                    //移除末尾的零
                    result = result.replace(/零+$/, '')
                    //将【零一十】换成【零十】
                    //result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
                    //将【一十】换成【十】
                    result = result.replace(/^一十/g, '十');
                    res = result;

                } else if (type == 1) {
                    res = String.fromCharCode((num-1)+65);
                }
                return res;
            }

            // 获取试卷模板
            var tempCurrentUserCode = gps.currentUserCode?gps.currentUserCode:""
            ajaxgeneral({
                url: 'action/examAttribute/constructExamLayout?currentUserCode=' + tempCurrentUserCode,
                data: {"examAppCode": gps.examAppCode},
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    for (var i in res.data.singleQuestionList) {
                        allQuestionCode.push(res.data.singleQuestionList[i].questionCode)
                    }
                    for (var i in res.data.moreQuestionList) {
                        allQuestionCode.push(res.data.moreQuestionList[i].questionCode)
                    }
                    for (var i in res.data.judgeQuestionList) {
                        allQuestionCode.push(res.data.judgeQuestionList[i].questionCode)
                    }
                    for (var i in res.data.shortAnswerQuestionList) {
                        allQuestionCode.push(res.data.shortAnswerQuestionList[i].questionCode)
                    }
                    $(".explain").html(res.data.examName);
                    examAppCode = res.data.examAppCode;
                    examLists=res.data.singleQuestionList;
                    questionBlankCode = res.data.questionBankCode;
                    currentAction = "test";
                    if(gps.type == "piyue") {
                        username = gps.username
                    }
                    // 当前用户是否有未完成试卷
                    ajaxgeneral({
                        url: 'action/examInfo/findExamInfo',
                        type: "POST",
                        data:{
                            publishUsername: username,
                            examCode:gps.examCode,
                            examAppCode:gps.examAppCode
                        },
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        success: function (result) {
                            if(result.data!=null) {
                                totalSS = parseInt(result.data.residueTime)
                                if (totalSS > 0 && !result.data.isFinishExam) {
                                    // timeOut(totalSS);
                                }else{
                                    $(".examTime").hide();
                                }
                                examRecordId = result.data.id; // 未完成试卷id
                                var examRecord = result.data.examRecord.split(','); // 题目编号
                                var examAnswer = result.data.examAnswer.split(','); // 保存的答案
                                // for(var i=0;i<examRecord.length;i++){
                                //     var arr = examRecord[i].split('-');
                                //     if(parseInt(arr[1])<13){
                                //         if(examAnswer[i]&&examAnswer[i]!='A'&&examAnswer[i]!='B'&&examAnswer[i]!='C'){
                                //             examAnswerText[examRecord[i]] = examAnswer[i]
                                //             examAnswer[i] = "D"
                                //         }
                                //     }
                                // }
                                singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案

                                // 匹配已选择的答案
                                function matchAnswer(lists,mark){
                                    for(var i = 0; i < examRecord.length; i++){
                                        for(var n = 0; n < lists.length; n++){
                                            if(examRecord[i] == lists[n].questionCode){
                                                if(mark != 'shortAnswer'){
                                                    var examAnswerOptions
                                                    if(examAnswer[i]) {
                                                        examAnswerOptions = examAnswer[i].split('/');
                                                        for(var ii = 0; ii < examAnswerOptions.length; ii++){
                                                            for(var nn = 0; nn < lists[n].answerList.length; nn++){
                                                                if(examAnswerOptions[ii].substr(0,1) == lists[n].answerList[nn].answerCode){
                                                                    lists[n].answerList[nn].isSelected = true;
                                                                    lists[n].answerList[nn].renderTextarea = examAnswerOptions[ii];
                                                                }else {
                                                                    if (!lists[n].answerList[nn].isSelected) {
                                                                        lists[n].answerList[nn].isSelected = false;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }else{
                                                    lists[n].answerCode = examAnswer[i];
                                                }
                                            }
                                        }
                                    }
                                    return lists;
                                }
                                // matchAnswer(res.data.singleQuestionList,'single');
                                matchAnswer(res.data.singleQuestionList,'single');
                                matchAnswer(res.data.moreQuestionList,'multiple');
                                matchAnswer(res.data.judgeQuestionList,'judge');
                                matchAnswer(res.data.shortAnswerQuestionList,'shortAnswer');
                            }else{
                                totalSS = parseInt(res.data.setTime)*60;
                                if (totalSS > 0) {
                                    // timeOut(totalSS);
                                }else{
                                    $(".examTime").hide();
                                }
                            }

                            if(res.data.singleQuestionList && res.data.singleQuestionList.length>0){
                                singleLen = res.data.singleQuestionList.length;
                                for(var i = 0;i < res.data.singleQuestionList.length; i++){
                                    for(var j = 0;j < res.data.singleQuestionList[i].answerList.length; j++){
                                        if(res.data.singleQuestionList[i].answerList[j].isSelected){
                                            var examAnswerValue = res.data.singleQuestionList[i].answerList[j].answerCode;
                                            if(res.data.singleQuestionList[i].answerList[j].identification){
                                                if(examAnswerValue!='A'&&examAnswerValue!='B'&&examAnswerValue!='C'){
                                                    examAnswerValue = 'D'
                                                }
                                            }
                                            singleData.push({
                                                questionCode: res.data.singleQuestionList[i].answerList[j].questionCode,
                                                examAnswer: examAnswerValue
                                            });
                                        }
                                    }
                                }
                            }
                            if(res.data.moreQuestionList && res.data.moreQuestionList.length>0){
                                multipleLen = res.data.moreQuestionList.length;
                                for(var i = 0;i < res.data.moreQuestionList.length; i++){
                                    var options = [];
                                    for(var j = 0;j < res.data.moreQuestionList[i].answerList.length; j++){
                                        if(res.data.moreQuestionList[i].answerList[j].isSelected){
                                            options.push(res.data.moreQuestionList[i].answerList[j].answerCode);
                                        }
                                    }
                                    if(options.length > 0){
                                        multipleData.push({
                                            questionCode: res.data.moreQuestionList[i].questionCode,
                                            examAnswer: options.join("/")
                                        });
                                    }
                                }
                            }
                            if(res.data.judgeQuestionList && res.data.judgeQuestionList.length>0){
                                judgeLen = res.data.judgeQuestionList.length;
                                for(var i = 0; i < res.data.judgeQuestionList.length; i++){
                                    for(var j = 0; j < res.data.judgeQuestionList[i].answerList.length; j++){
                                        if(res.data.judgeQuestionList[i].answerList[j].isSelected){
                                            judgeData.push({
                                                questionCode: res.data.judgeQuestionList[i].answerList[j].questionCode,
                                                examAnswer: res.data.judgeQuestionList[i].answerList[j].answerCode
                                            });
                                        }
                                    }
                                }
                            }
                            if(res.data.shortAnswerQuestionList && res.data.shortAnswerQuestionList.length>0){
                                shortLen = res.data.shortAnswerQuestionList.length;
                                for(var i = 0;i < res.data.shortAnswerQuestionList.length; i++){
                                    if(res.data.shortAnswerQuestionList[i].answerCode){
                                        shortData.push({
                                            questionCode: res.data.shortAnswerQuestionList[i].questionCode,
                                            examAnswer: res.data.shortAnswerQuestionList[i].answerCode
                                        });
                                    }
                                }
                            }
                            examData = res.data;
                            if(result.data && result.data.isFinishExam){ //已完成考试
                                isFinishExam = true;
                            }else{  //未完成考试
                                isFinishExam = false;
                            }
                            if(result.data && result.data.isFinishExam){ //已完成考试
                                showQuestions('reTest', res.data);
                                $(".submitBtn").hide();
                                if(gps.type != "piyue") {
                                    $("#closeDialog").dialog({closed:false});
                                }
                            }else{  //未完成考试
                                showQuestions('test', res.data);
                                if (gps.type != "piyue") {
                                    $(".submitBtn").show();
                                }
                                // 设置提交按钮高亮是否显示
                                // if(singleData.length+multipleData.length+judgeData.length+shortData.length == singleLen+multipleLen+judgeLen+shortLen){
                                //     var showSubmit = true;
                                //     for(var i=0;i<singleData.length;i++){
                                //         // 待修改
                                //         if (!singleData[i].examAnswer.trim()){
                                //             showSubmit = false
                                //             break;
                                //         }else if(singleData[i].examAnswer=='D'){
                                //             var arr = singleData[i].questionCode.split('-');
                                //             if(arr[1]<13){
                                //                 if ($('.optionText'+singleData[i].questionCode).children('textarea').val()){
                                //                     if(!$('.optionText'+singleData[i].questionCode).children('textarea').val().trim()){
                                //                         showSubmit = false
                                //                         break;
                                //                     }
                                //                 }else {
                                //                     showSubmit = false
                                //                     break;
                                //                 }
                                //             }
                                //         }
                                //     }
                                //     var showBtn = true;
                                //     for(var i=0;i<shortData.length;i++){
                                //         if(!shortData[i].examAnswer.trim()){
                                //             showBtn = false;
                                //             break;
                                //         }
                                //     }
                                //     if(showSubmit&&showBtn){
                                //         $("#submit").addClass("canSubmit");
                                //     }else{
                                //         $("#submit").removeClass("canSubmit");
                                //     }
                                // }else{
                                //     $("#submit").removeClass("canSubmit");
                                // }
                                var otherIsCom = true
                                $(".other").each(function(i, v) {
                                    if(!$(v).children("textarea").val().trim()) {
                                        otherIsCom = false
                                    }
                                })
                                if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen && otherIsCom) {
                                    $("#submit").addClass(" canSubmit");
                                } else {
                                    $("#submit").removeClass("canSubmit");
                                }
                                if(remainTimeT) clearInterval(remainTimeT);
                                if (gps.type != "piyue") {
                                    remainTimeT = setInterval(ajaxInterval,1000); //每隔5秒保存一次数据
                                }
                            }

                        }
                    })
                }
            })

            // 每隔6秒保存一次数据
            function ajaxInterval() {
                if(currentAction == "test"){

                    multipleData = [];
                    for (var i = 0; i < $(".multipleQues .main").length; i++) {
                        var other = $(".multipleQues .main").eq(i).find("li:last-child")
                        if(other.hasClass("other") && other.find("textarea").val().trim()) {
                            other.prev().find("input").val(other.prev().find("input").val().substr(0,1)+":"+other.find("textarea").val())
                        }
                        var mulAnswer = []; // 每道多选题选中的答案
                        if ($(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
                            $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
                                mulAnswer.push($(this).val());
                            });
                            multipleData.push({
                                questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
                                examAnswer: mulAnswer.join('/')
                            });
                        }
                    }

                    timeFlag = timeFlag+1;
                    if(timeFlag >= 6){
                        var questionCodeArry = [];
                        var examAnswerArry = [];
                        for(var i=0;i<singleData.length;i++){
                            var arr = singleData[i].questionCode.split('-');
                            if(arr[1]<13&&singleData[i].examAnswer=='D'){
                                var singleQuesVal=$('.optionText'+singleData[i].questionCode).eq(0).find('textarea').val()
                                singleData[i].examAnswer = singleQuesVal?singleQuesVal:'D'
                            }
                        }
                        var totalData = singleData.concat(multipleData,judgeData,shortData);
                        for(var i = 0; i < totalData.length; i++){
                            questionCodeArry.push(totalData[i].questionCode);
                            if(totalData[i].examAnswer){
                                totalData[i].examAnswer = totalData[i].examAnswer.split(',').join('，');
                            }
                            examAnswerArry.push(totalData[i].examAnswer);
                        }
                        // for (var i = 0; i < allQuestionCode.length; i++) {
                        //     var include = false
                        //     for (var j = 0; j < totalData.length; j++) {
                        //         if (allQuestionCode[i] == totalData[j].questionCode) {
                        //             include = true
                        //             break
                        //         }
                        //     }
                        //     if (include) {
                        //         examAnswerArry.push(totalData[j].examAnswer);
                        //     } else {
                        //         examAnswerArry.push(null);
                        //     }
                        // }

                        if(singleData.length>0 || multipleData.length>0 || judgeData.length>0 || shortData.length>0){
                            timeFlag = 0;
                            ajaxgeneral({
                                url: 'action/examInfo/saveExam',
                                data: {
                                    examCode: gps.examCode,
                                    examAppCode: gps.examAppCode,
                                    publishUsername: username,
                                    examRecord: questionCodeArry.join(','),
                                    examAnswer: examAnswerArry.join(','),
                                    id: examRecordId,
                                    residueTime: totalSS, // 秒
                                },
                                loading: false,
                                contentType: "application/json; charset=utf-8",
                                success: function (res) {
                                    //console.log("保存成功！");
                                    examRecordId = res.data.id;
                                }
                            })
                        }
                    }
                }
            }
            // 显示试卷
            function showQuestions(type,data){ // type的值：test测试；reTest重测
                if(data){
                    var titFlag = 0; // 标题序号
                    var qid=1;
                    var list=[];
                    var questions=list.concat(data.singleQuestionList,data.moreQuestionList,data.judgeQuestionList,data.shortAnswerQuestionList)
                    questions.sort(function (a,b) {return a.questionOrder-b.questionOrder })
                    //单选
                    for(var i=0;i<questions.length;i++){
                        if(questions[i].questionType=='single'){
                            var part = $("<div>").addClass("part singleQues").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、单选题").appendTo(part);
                            var main = $("<div>").addClass("main").appendTo(part);
                            // var h6 = $("<h6 class="+questions[i].id+">").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
                            var h6 = $("<h6 class="+questions[i].id+">").html(qid+"、"+questions[i].questionName).appendTo(main);
                            var ul = $("<ul>").appendTo(main);
                            if(questions[i].answerList && questions[i].answerList.length>0){
                                for(var j=0;j<questions[i].answerList.length;j++){
                                    var answerValue = questions[i].answerList[j].answerCode;
                                    // if(questions[i].answerList[j].identification){
                                    //     if(answerValue&&answerValue!='A'&&answerValue!='B'&&answerValue!='C'){
                                    //         answerValue = 'D'
                                    //     }
                                    // }
                                    if(type == "test"){ // 测试
                                        if(questions[i].answerList[j].isSelected){
                                            var li = $("<li>").addClass("active").appendTo(ul);
                                            if(answerValue=="D"&&questions[i].answerList[j].identification){
                                                var optionText = '<div class="optionText optionText'+questions[i].answerList[j].questionCode+'"><span class="optionLeft"><font class="col_r">*</font>具体为：</span><textarea rows="3" value="" maxlength="100" placeholder="请输入不超过100个字"></textarea><div class="surplusNum">剩余 <span style="color:red" maxLength="100">100</span> 个字</div></div>'
                                                $(optionText).appendTo(ul.parent())
                                                if(examAnswerText[questions[i].answerList[j].questionCode]=="D"){
                                                    examAnswerText[questions[i].answerList[j].questionCode]=""
                                                }
                                                $('.optionText'+questions[i].answerList[j].questionCode).children('textarea').html(examAnswerText[questions[i].answerList[j].questionCode])
                                                $('.optionText'+questions[i].answerList[j].questionCode).children('.surplusNum').children('span').text(100 - (examAnswerText[questions[i].answerList[j].questionCode]?examAnswerText[questions[i].answerList[j].questionCode].length:0));
                                            }
                                            var input = $("<input>").attr({
                                                type:'radio',
                                                id:questions[i].answerList[j].id,
                                                name:questions[i].answerList[j].questionCode,
                                                value:answerValue,
                                                optionType:questions[i].answerList[j].identification,
                                                checked:true
                                            }).appendTo(li);
                                        }else{
                                            var li = $("<li>").appendTo(ul);
                                            var input = $("<input>").attr({
                                                type:'radio',
                                                id:questions[i].answerList[j].id,
                                                name: questions[i].answerList[j].questionCode,
                                                value:answerValue,
                                                optionType:questions[i].answerList[j].identification,
                                            }).appendTo(li);
                                        }
                                    }else if(type == "reTest"){ // 重测
                                        var li = $("<li>");
                                        var input = $("<input>").attr({
                                            type:'radio',
                                            id:questions[i].answerList[j].id,
                                            name:questions[i].answerList[j].questionCode,
                                            value:answerValue,
                                            optionType:questions[i].answerList[j].identification,
                                        });
                                        if(questions[i].answerList[j].isSelected){ // 已填的答案   isSelected
                                            li = $("<li>").addClass("active red");
                                            if(answerValue=="D"&&questions[i].answerList[j].identification){
                                                var optionText = '<div class="optionText optionText'+questions[i].answerList[j].questionCode+'"><span class="optionLeft"><font class="col_r">*</font>具体为：</span><textarea rows="3" value="" maxlength="100" placeholder="请输入不超过100个字"></textarea></div>'
                                                $(optionText).appendTo(ul.parent())
                                                if(examAnswerText[questions[i].answerList[j].questionCode]=="D"){
                                                    examAnswerText[questions[i].answerList[j].questionCode]=""
                                                }
                                                $('.optionText'+questions[i].answerList[j].questionCode).children('textarea').html(examAnswerText[questions[i].answerList[j].questionCode])
                                                // $('.optionText'+questions[i].answerList[j].questionCode).children('.surplusNum').children('span').text(100 - (examAnswerText[questions[i].answerList[j].questionCode]?examAnswerText[questions[i].answerList[j].questionCode].length:0));
                                            }
                                            input = $("<input>").attr({
                                                type:'radio',
                                                id:questions[i].answerList[j].id,
                                                name:questions[i].answerList[j].questionCode,
                                                value:answerValue,
                                                optionType:questions[i].answerList[j].identification,
                                                checked:true
                                            });
                                        }
                                        if(questions[i].answerList[j].isCorrect){ // 正确答案
                                            li = $("<li>").removeClass("red").addClass(" green");
                                        }
                                        $(input).appendTo(li);
                                        $(li).appendTo(ul);
                                    }
                                    var label = $("<label>").attr("for", questions[i].answerList[j].id).html(answerValue + "：" + questions[i].answerList[j].answerContent).appendTo(li);
                                }
                                $("input[type=radio][name="+questions[i].answerList[0].questionCode+"]").change(function(){
                                    if($(this).attr('optionType')!='1'){
                                        $(this).parent().parent().siblings('.optionText').remove();
                                    }
                                    // for(var b=0;b<$('.optionText').length;b++){
                                    //     var text =  $('.optionText').eq(b).children('textarea').val().trim();
                                    //     if(!text){
                                    //         // $('.optionText').eq(b).children('textarea').focus();
                                    //         $(this).removeAttr('checked');
                                    //         top.mesAlert('警告！','您有选择的没有成效举例说明尚未填写，请先填写说明','warning')
                                    //         return false;
                                    //     }
                                    // }
                                    if($(this).attr('optionType')=='1'){
                                        $(this).parent().parent().parent().append('<div class="optionText"><span class="optionLeft"><font class="col_r">*</font>具体为：</span><textarea rows="3" value="" maxlength="100" placeholder="请输入不超过100个字"></textarea><div class="surplusNum">剩余 <span style="color:red" maxLength="100">100</span> 个字</div></div>')
                                        $(this).parent().parent().siblings('.optionText').children('textarea').focus();
                                    }
                                })
                            }
                            qid++;
                        }
                        //多选
                        if(questions[i].questionType=='more'){
                            titFlag += 1;
                            var part = $("<div>").addClass("part multipleQues").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、多选题").appendTo(part);
                            var main = $("<div>").addClass("main").appendTo(part);
                            // var h6 = $("<h6>").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
                            var h6 = $("<h6>").html(qid+"、"+questions[i].questionName).appendTo(main);
                            var ul = $("<ul>").addClass("clearfix").appendTo(main);

                            for(var j=0;j<questions[i].answerList.length;j++){
                                if(type == "test") { // 测试
                                    if (!questions[i].answerList[j].isSelected) {
                                        var li = $("<li>").appendTo(ul);

                                        var input = $("<input>").attr({
                                            type:'checkbox',
                                            id:questions[i].answerList[j].id,
                                            name:questions[i].answerList[j].questionCode,
                                            value:questions[i].answerList[j].answerCode
                                        }).appendTo(li);
                                    }else{

                                        var li = $("<li>").addClass(" active").appendTo(ul);

                                        var input = $("<input>").attr({
                                            type:'checkbox',
                                            id:questions[i].answerList[j].id,
                                            name:questions[i].answerList[j].questionCode,
                                            value:questions[i].answerList[j].answerCode,
                                            checked:true
                                        }).appendTo(li);
                                    }
                                }else if(type == "reTest") { // 重测



                                    var li = $("<li>")

                                    var input = $("<input>").attr({
                                        type:'checkbox',
                                        id:questions[i].answerList[j].id,
                                        name:questions[i].answerList[j].questionCode,
                                        value:questions[i].answerList[j].answerCode
                                    });
                                    if(questions[i].answerList[j].isSelected){ // 已填的答案



                                        li = $("<li>").addClass("active red");

                                        input = $("<input>").attr({
                                            type:'checkbox',
                                            id:questions[i].answerList[j].id,
                                            name:questions[i].answerList[j].questionCode,
                                            value:questions[i].answerList[j].answerCode,
                                            checked:true
                                        });
                                    }
                                    if(questions[i].answerList[j].isCorrect){ // 正确答案

                                        li = $("<li>").removeClass(" red").addClass(" green");


                                    }
                                    $(input).appendTo(li);
                                    $(li).appendTo(ul);
                                }
                                var label = $("<label>").attr("for",questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode+"："+questions[i].answerList[j].answerContent).appendTo(li);
                            }
                            if(questions[i].questionOrder == 19 || questions[i].questionOrder == 20) {
                                if(questions[i].answerList[questions[i].answerList.length-1].isSelected) {
                                    var textareaText = questions[i].answerList[questions[i].answerList.length-1].renderTextarea.substr(2)
                                    var $html = $("<li class='other'>请输入：<span style='color: red; margin-right: 8px;'>*</span><textarea maxlength='200' oninput='textareaChange(this)' style='min-height:50px;overflow-y: hidden;' type='text'>"+textareaText+"</textarea><div style='font-size: 14px;padding-left: 80px;'>剩余 <span style='color: red;'>"+(200-textareaText.length)+"</span> 字</div></li>")
                                    $html.appendTo(ul)
                                    $html.children("textarea").css("height", $html.children("textarea")[0].scrollHeight)
                                }
                            }
                            qid++;

                        }
                        //判断
                        if(questions[i].questionType=='judge'){
                            titFlag += 1;
                            var part = $("<div>").addClass("part judgeQues").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、多选题").appendTo(part);
                            var main = $("<div>").addClass("main").appendTo(part);
                            // var h6 = $("<h6>").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
                            var h6 = $("<h6>").html(qid+"、"+questions[i].questionName).appendTo(main);
                            var ul = $("<ul>").addClass("clearfix").appendTo(main);

                            for(var j=0;j<questions[i].answerList.length;j++){
                                if(type == "test") { // 测试
                                    if (!questions[i].answerList[j].isSelected) {
                                        var li = $("<li>").appendTo(ul);

                                        var input = $("<input>").attr({
                                            type:'radio',
                                            id:questions[i].answerList[j].id,
                                            name:questions[i].answerList[j].questionCode,
                                            value:questions[i].answerList[j].answerCode
                                        }).appendTo(li);
                                    }else{

                                        var li = $("<li>").addClass(" active").appendTo(ul);

                                        var input = $("<input>").attr({
                                            type:'radio',
                                            id:questions[i].answerList[j].id,
                                            // name:questions[i].answerList[j].questionCode,
                                            value:questions[i].answerList[j].answerCode,
                                            checked:true
                                        }).appendTo(li);
                                    }
                                }else if(type == "reTest") { // 重测



                                    var li = $("<li>")

                                    var input = $("<input>").attr({
                                        type:'radio',
                                        id:questions[i].answerList[j].id,
                                        name:questions[i].answerList[j].questionCode,
                                        value:questions[i].answerList[j].answerCode
                                    });
                                    if(questions[i].answerList[j].isSelected){ // 已填的答案

                                        li = $("<li>").addClass("active red");

                                        input = $("<input>").attr({
                                            type:'radio',
                                            id:questions[i].answerList[j].id,
                                            // name:questions[i].answerList[j].questionCode,
                                            value:questions[i].answerList[j].answerCode,
                                            checked:true
                                        });
                                    }
                                    if(questions[i].answerList[j].isCorrect){ // 正确答案

                                        li = $("<li>").removeClass(" red").addClass(" green");


                                    }
                                    $(input).appendTo(li);
                                    $(li).appendTo(ul);
                                }
                                var label = $("<label>").attr("for",questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode=="true"?"正确":"错误").appendTo(li);
                            }
                            qid++;

                        }
                        if(questions[i].questionType=='shortAnswer'){
                            titFlag += 1;
                            var part = $("<div>").addClass("part shortAnswer").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"：简答题").appendTo(part);
                            var main = $("<div>").addClass("main shortAnswer").appendTo(part);
                            // var h6 = $("<h6>").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
                            var h6 = $("<h6>").html(qid+"、"+questions[i].questionName).appendTo(main);
                            var pObj = $("<p>").appendTo(main);
                            if(type == "test"){ // 测试
                                if(questions[i].answerCode){
                                    var textarea = $("<textarea>").attr({id:questions[i].questionCode,placeholder:"请输入不超过600个字",maxlength:600}).html(questions[i].answerCode).appendTo(pObj);
                                    var surplus = "<div class='surplusNum'>剩余 <span style='color:red' maxLength='600'>"+(600 - (questions[i].answerCode?questions[i].answerCode.length:0))+"</span> 个字</div>";
                                    $(surplus).appendTo(pObj)
                                }else{
                                    var textarea = $("<textarea>").attr({id:questions[i].questionCode,placeholder:"请输入不超过600个字",maxlength:600}).appendTo(pObj);
                                    var surplus = "<div class='surplusNum'>剩余 <span style='color:red' maxLength='600'>600</span> 个字</div>";
                                    $(surplus).appendTo(pObj)
                                }
                            }else if(type == "reTest") { // 重测
                                var textarea = $("<textarea>").attr({id:questions[i].questionCode,placeholder:"请输入不超过600个字",maxlength:600}).html(questions[i].answerCode).appendTo(pObj);
                                // var surplus = "<div class='surplusNum'>剩余 <span style='color:red' maxLength='500'>"+(500 - (questions[i].answerCode?questions[i].answerCode.length:0))+"</span> 个字</div>";
                                // $(surplus).appendTo(pObj)
                            }

                            qid++;
                        }
                    }


                    // input和textarea的事件
                    $("input").on("click",function() {
                        var otherInfo = $(this).next().text().substring(2,4)
                        var otherInfoIndex = $(this).parent().parent().parent().parent().index()
                        if((otherInfo == "其他" || otherInfo == "其它") && (otherInfoIndex == 18 || otherInfoIndex == 19)) {
                            if(!$(this).parent().siblings("li:last-child").hasClass("other")) {
                                $(this).parent().parent().append('<li class="other">请输入：<span style="color: red; margin-right: 8px;">*</span><textarea maxlength="200" oninput="textareaChange(this)" style="min-height:50px;overflow-y: hidden;" type="text"></textarea><div style="font-size: 14px;padding-left: 80px;">剩余 <span style="color: red;">200</span> 字</div></li>')
                            }
                            if($(this).attr("type") == "checkbox" && $(this).prop("checked") == false) {
                                $(this).parent().siblings("li:last-child").remove()
                            }
                        }
                        // 单选和判断的高亮、isSelected字段控制
                        if ($(this).attr("type") && $(this).attr("type") == "radio") {
                            if (!$(this).parent("li").hasClass("active")) {
                                $(this).parent("li").addClass(" active");
                                $(this).parent("li").siblings().removeClass(" active");

                                // 重测时isSelected字段表示上次已选择的选项
                                var partClass = $(this).parents(".part").attr("class").split(" ")[1];

                                var big_index = $(this).parents(".main").index("." + partClass + " .main"); // 在大题中的索引
                                var small_index = $(this).parents().index(); // 在小题中的索引
                                if ($(this).parents(".part").hasClass("singleQues")) {
                                    for (var i = 0; i < examData.singleQuestionList.length; i++) {
                                        for (var j = 0; j < examData.singleQuestionList[i].answerList.length; j++) {
                                            examData.singleQuestionList[i].answerList[j].isSelected = false;
                                        }
                                    }
                                    examData.singleQuestionList[big_index].answerList[small_index].isSelected = true;
                                } else if ($(this).parents(".part").hasClass("judgeQues")) {
                                    for (var i = 0; i < examData.judgeQuestionList.length; i++) {
                                        for (var j = 0; j < examData.judgeQuestionList[i].answerList.length; j++) {
                                            examData.judgeQuestionList[i].answerList[j].isSelected = false;
                                        }
                                    }
                                    examData.judgeQuestionList[big_index].answerList[small_index].isSelected = true;
                                }
                            }
                        };

                        // 多选的高亮、isSelected控制
                        if ($(this).attr("type") && $(this).attr("type") == "checkbox") {
                            if (!$(this).parent("li").hasClass("active")) {
                                $(this).parent("li").addClass(" active");
                            } else {
                                $(this).parent("li").removeClass("active");
                            }
                            for (var i = 0; i < examData.moreQuestionList.length; i++) {
                                for (var j = 0; j < examData.moreQuestionList[i].answerList.length; j++) {
                                    if ($(".multipleQues .main").eq(i).find("li").eq(j).hasClass("active")) {
                                        examData.moreQuestionList[i].answerList[j].isSelected = true;
                                    } else {
                                        examData.moreQuestionList[i].answerList[j].isSelected = false;
                                    }
                                }
                            }
                        };

                        // 单选
                        singleData = [];
                        for (var i = 0; i < $(".singleQues .main").length; i++) {
                            if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                var examAnswer =$(".singleQues .main").eq(i).find("input[type='radio']:checked").val()
                                if($(".singleQues .main").eq(i).find("input[type='radio']:checked").val()=='D'){
                                    if($(".singleQues .main").eq(i).find("input[type='radio']:checked").attr('optiontype')){
                                        examAnswer = $(".singleQues .main").eq(i).children('.optionText').children('textarea').val()
                                    }
                                }
                                singleData.push({
                                    questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                    examAnswer: examAnswer?examAnswer:'D'
                                });
                            }
                        }
                        // 多选
                        multipleData = [];
                        for (var i = 0; i < $(".multipleQues .main").length; i++) {
                            var other = $(".multipleQues .main").eq(i).find("li:last-child")
                            if(other.hasClass("other") && other.find("textarea").val().trim()) {
                                other.prev().find("input").val(other.prev().find("input").val().substr(0,1)+":"+other.find("textarea").val())
                            }
                            var mulAnswer = []; // 每道多选题选中的答案
                            if ($(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
                                $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
                                    mulAnswer.push($(this).val());
                                });
                                multipleData.push({
                                    questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
                                    examAnswer: mulAnswer.join('/')
                                });
                            }
                        }
                        // 判断
                        judgeData = [];
                        for (var i = 0; i < $(".judgeQues .main").length; i++) {
                            if ($(".judgeQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                judgeData.push({
                                    questionCode: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                    examAnswer: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").val()
                                });
                            }
                        }
                        shortData = [];
                        for (var i = 0; i < $(".shortAnswer .main").length; i++) {
                            if ($(".shortAnswer .main").eq(i).find("textarea").val() != "") {
                                shortData.push({
                                    questionCode: $(".shortAnswer .main").eq(i).find("textarea").attr("id"),
                                    examAnswer: $(".shortAnswer .main").eq(i).find("textarea").val()
                                });
                            }
                        }
                        // 设置提交按钮高亮是否显示
                        // if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen) {
                        //     var showSubmit = true;
                        //     for(var i=0;i<singleData.length;i++){
                        //         // 待修改
                        //         if(singleData[i].examAnswer=='D'){
                        //             var arr = singleData[i].questionCode.split('-');
                        //             if(arr[1]<13){
                        //                 if ($('.optionText'+singleData[i].questionCode).children('textarea').val()){
                        //                     if(!$('.optionText'+singleData[i].questionCode).children('textarea').val().trim()){
                        //                         showSubmit = false
                        //                         break;
                        //                     }
                        //                 }else {
                        //                     showSubmit = false
                        //                     break;
                        //                 }
                        //
                        //             }
                        //         }
                        //     }
                        //     var showBtn = true;
                        //     for(var i=0;i<shortData.length;i++){
                        //         if(!shortData[i].examAnswer.trim()){
                        //             showBtn = false;
                        //             break;
                        //         }
                        //     }
                        //     if(showSubmit&&showBtn){
                        //         $("#submit").addClass("canSubmit");
                        //     }else{
                        //         $("#submit").removeClass("canSubmit");
                        //     }
                        // } else {
                        //     $("#submit").removeClass("canSubmit");
                        // }
                        var otherIsCom = true
                        $(".other").each(function(i, v) {
                            if(!$(v).children("textarea").val().trim()) {
                                otherIsCom = false
                            }
                        })
                        if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen && otherIsCom) {
                            $("#submit").addClass(" canSubmit");
                        } else {
                            $("#submit").removeClass("canSubmit");
                        }
                    });
                    $(document).on('input propertychange','textarea',function() {
                        var surplusNum = parseInt($(this).siblings('.surplusNum').children('span').attr('maxLength'));
                        var allNum = 0;
                        if(surplusNum==100){
                            allNum=100;
                        }else if(surplusNum==600){
                            allNum=600;
                        }
                        $(this).siblings('.surplusNum').children('span').text(allNum - $(this).val().length);
                        // 单选
                        singleData = [];
                        for (var i = 0; i < $(".singleQues .main").length; i++) {
                            if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                var examAnswer =$(".singleQues .main").eq(i).find("input[type='radio']:checked").val()
                                if($(".singleQues .main").eq(i).find("input[type='radio']:checked").val()=='D'){
                                    if($(".singleQues .main").eq(i).find("input[type='radio']:checked").attr('optiontype')){
                                        examAnswer = $(".singleQues .main").eq(i).children('.optionText').children('textarea').val()
                                    }
                                }
                                singleData.push({
                                    questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                    examAnswer: examAnswer?examAnswer:'D'
                                });
                            }
                        }
                        // 多选
                        multipleData = [];
                        for (var i = 0; i < $(".multipleQues .main").length; i++) {
                            var mulAnswer = []; // 每道多选题选中的答案
                            if ($(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
                                $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
                                    mulAnswer.push($(this).val());
                                });
                                multipleData.push({
                                    questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
                                    examAnswer: mulAnswer.join('/')
                                });
                            }
                        }
                        // 判断
                        judgeData = [];
                        for (var i = 0; i < $(".judgeQues .main").length; i++) {
                            if ($(".judgeQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                judgeData.push({
                                    questionCode: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                    examAnswer: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").val()
                                });
                            }
                        }
                        // 简答
                        shortData = [];
                        for (var i = 0; i < $(".shortAnswer .main").length; i++) {
                            if ($(".shortAnswer .main").eq(i).find("textarea").val() != "") {
                                shortData.push({
                                    questionCode: $(".shortAnswer .main").eq(i).find("textarea").attr("id"),
                                    examAnswer: $(".shortAnswer .main").eq(i).find("textarea").val()
                                });
                            }
                        }
                        // 设置提交按钮高亮是否显示
                        // if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen) {
                        //     var showSubmit = true;
                        //     for(var i=0;i<singleData.length;i++){
                        //         // 待修改
                        //         if (!singleData[i].examAnswer.trim()){
                        //             showSubmit = false
                        //             break;
                        //         }else if(singleData[i].examAnswer=='D'){
                        //             var arr = singleData[i].questionCode.split('-');
                        //             if(arr[1]<13){
                        //                 if ($('.optionText'+singleData[i].questionCode).children('textarea').val()){
                        //                     if(!$('.optionText'+singleData[i].questionCode).children('textarea').val().trim()){
                        //                         showSubmit = false
                        //                         break;
                        //                     }
                        //                 }else {
                        //                     showSubmit = false
                        //                     break;
                        //                 }
                        //             }
                        //         }
                        //     }
                        //     var showBtn = true;
                        //     for(var i=0;i<shortData.length;i++){
                        //         if(!shortData[i].examAnswer.trim()){
                        //             showBtn = false;
                        //             break;
                        //         }
                        //     }
                        //     if(showSubmit&&showBtn){
                        //         $("#submit").addClass("canSubmit");
                        //     }else{
                        //         $("#submit").removeClass("canSubmit");
                        //     }
                        // } else {
                        //     $("#submit").removeClass("canSubmit");
                        // }
                        var otherIsCom = true
                        $(".other").each(function(i, v) {
                            if(!$(v).children("textarea").val().trim()) {
                                otherIsCom = false
                            }
                        })
                        if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen && otherIsCom) {
                            $("#submit").addClass(" canSubmit");
                        } else {
                            $("#submit").removeClass("canSubmit");
                        }
                    });

                    if(isFinishExam){ // 已完成考试禁用表单
                        $(".other").each(function (i, v) {
                            $(v).children("div").remove()
                        })
                        $(".questions input,.questions textarea").attr("disabled",true);
                        $("#submit").hide();
                    }else{
                        $(".questions input,.questions textarea").attr("disabled",false);
                        if (gps.type != "piyue") {
                            $("#submit").show();
                        }
                    }

                }
                else{
                    getparent().mesShow("温馨提示","试卷获取失败,请联系系统管理员!!!",2000,'red');
                }
            }
            // 点“提交”
            $("#submit").click(function(){
                if(!$(this).hasClass('canSubmit')){
                    top.mesAlert('警告！','您有题目尚未答完，请完成所有题目之后再提交','warning')
                    return false
                }
                if($(".other")){
                    for(var i=0;i<$(".other").length;i++){
                        var other=$(".other").eq(i).prev().find("input")
                        var prevValue=other.val()
                        var prevValueSubB=prevValue.substring(0,2)
                        var prevValueSub=prevValue.substring(2,prevValue.length)
                        var otherValue=$(".other").eq(i).find("textarea").val()
                        if(prevValue.indexOf(":")==-1){
                            other.val(prevValue+":"+otherValue)
                        }else if(prevValueSub!=otherValue){
                            other.val(prevValueSubB+otherValue)
                        }
                    }
                }
                for(var i=0;i<examLists.length;i++){
                    for(var j=0;j<singleData.length;j++){
                        if(examLists[i].questionCode==singleData[j].questionCode){
                            examLists.splice(i,1);
                        }
                    }
                }
                $('.questions .singleQues').each(function(i,v){
                    for(var m=0;m<examLists.length;m++){
                        if($(this).find('h6').attr('class')==examLists[m].id){
                            $(this).find('h6').css({'color':'#D90000'});
                        }
                    }
                })
                if(singleData.length+multipleData.length+judgeData.length+shortData.length <= 0){
                    getparent().mesShow("温馨提示","还未开始答题",2000,'red');
                    // }else if(singleData.length+multipleData.length+judgeData.length+shortData.length < singleLen+multipleLen+judgeLen+shortLen){
                    //     getparent().mesShow("温馨提示","试卷未答完，请继续答题！",2000,'red');
                }else{
                    $("#submitDialog").dialog({closed:false});
                }
            });

            $("#sureSubmit").click(function(){
                submitData();
            });

            // 提交答案
            function submitData(){
                clearInterval(remainTimeT);
                var questionCodeArry = [];
                var examAnswerArry = [];
                for(var i=0;i<singleData.length;i++){
                    var arr = singleData[i].questionCode.split('-');
                    if(arr[1]<13&&singleData[i].examAnswer=='D'){
                        var singleQuesVal=$('.optionText'+singleData[i].questionCode).eq(0).find('textarea').val()
                        singleData[i].examAnswer = singleQuesVal?singleQuesVal:'D'
                    }
                }
                var totalData = singleData.concat(multipleData,judgeData,shortData);
                for(var i = 0; i < totalData.length; i++){
                    questionCodeArry.push(totalData[i].questionCode);
                    if(totalData[i].examAnswer){
                        totalData[i].examAnswer = totalData[i].examAnswer.split(',').join('，');
                    }
                    examAnswerArry.push(totalData[i].examAnswer);
                }
                ajaxgeneral({
                    url: 'action/examInfo/submitExam',// 调用判断方法 实际不判断直接保存记录
                    data: {
                        examCode: gps.examCode,
                        examAppCode: gps.examAppCode,
                        publishUsername: username,
                        examRecord: allQuestionCode.join(','),
                        examAnswer: examAnswerArry.join(','),
                        questionBlankCode: questionBlankCode,
                        residueTime: totalSS
                    },
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        if(remainTimeT) clearInterval(remainTimeT);
                        $("#scoreDialog h5").html("提交成功！");
                        $("#yes").show();
                        $("#submit").remove()
                        moaBridge.close();
                        window.close();
                        $("#submitDialog").dialog({closed:true});
                        $("#scoreDialog").dialog({closed:false});
                    }
                })
            }

            //考试倒计时
            function timeOut () {
                if(gps.type != "piyue") {
                    if (totalSS > 0){
                        totalSS--;
                        $(".examTime h3").html(countdown(totalSS));
                        setTimeout(timeOut,1000);
                    }else{
                        clearInterval(remainTimeT);
                        var questionCodeArry = [];
                        var examAnswerArry = [];
                        var totalData = singleData.concat(multipleData,judgeData,shortData);
                        for(var i = 0; i < totalData.length; i++){
                            questionCodeArry.push(totalData[i].questionCode);
                            if(totalData[i].examAnswer){
                                totalData[i].examAnswer = totalData[i].examAnswer.split(',').join('，');
                            }
                            examAnswerArry.push(totalData[i].examAnswer);
                        }
                        ajaxgeneral({
                            url: 'action/examInfo/submitExam',// 调用判断方法 实际不判断直接保存记录
                            data: {
                                examCode: gps.examCode,
                                examAppCode: gps.examAppCode,
                                publishUsername: username,
                                examRecord: allQuestionCode.join(','),
                                examAnswer: examAnswerArry.join(','),
                                questionBlankCode: questionBlankCode,
                                residueTime: totalSS
                            },
                            contentType: "application/json; charset=utf-8",
                            success: function (res) {
                                if(remainTimeT) clearInterval(remainTimeT);
                            }
                        })
                        $("#examOverDialog").dialog({closed:false});
                    }
                }
            };

            // 全答对时关闭弹框
            $("#yes").click(function(){
                $("#scoreDialog").dialog({closed:true});
                if(gps.actionType && gps.actionType=="secrecyTask"){
                    top.dialogClose("detail");
                }else{
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                    moaBridge.close();
                }
            });

            $("#examOver").click(function(){
                $("#examOverDialog").dialog({closed:true});
                if(gps.actionType && gps.actionType=="secrecyTask"){
                    top.dialogClose("detail");
                }else{
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                    moaBridge.close();
                }
            });


            // 试卷已完成时，关闭页面
            $("#closeBtns button").click(function(){
                $('#closeDialog').dialog('close');

                if(gps.actionType && gps.actionType=="secrecyJoin"){
                    top.dialogClose("detail");
                }else{
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                    moaBridge.close();
                }

            })
            // 提交批阅分数
            $(".submitPiyueScore").click(function () {
                $(".submitPiyueScore").removeClass("canSubmit");
                ajaxgeneral({
                    url:"action/examInfo/updateExamInfo",
                    data:{"id":gps.piyueId,"score":$("#piyueScore").val()},
                    contentType: "application/json; charset=utf-8",
                    success:function(data){
                    }
                });
            })
        })

        function debounce(fn,wait){
            var timer = null;
            return function(){
                if(timer !== null){
                    clearTimeout(timer);
                }
                timer = setTimeout(fn,wait);
            }
        }
        function textareaChange(param) {
            var height=200-$(param).val().length
            $(param).next().children("span").html(height>=0?height:0)
            $(param).css("height",$(param)[0].scrollHeight)
        }
    </script>
    <style type="text/css">
        /*公共样式*/
        .clearfix:after{content:'.';display:block;height:0;line-height:0;clear:both;visibility:hidden;}
        .clearfix{zoom:1;}
        .w15{width:15%;}
        /*页面样式*/
        /*背景颜色*/
        body {
            background-image: url("../../images/bg.jpg");
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover;
            opacity: 0.8;
            margin: 0px;
            padding: 0px;
        }
        .intext{
            text-indent: 0.15rem;
            margin: 0.3rem auto;
        }
        .wrapper{width:85%;margin:0 auto;background-color:#fff;color:#000;}
        .header{text-align: center;}
        .header,.header img{width:100%;}
        .details{width:70%;padding:10px;font-size:16px;margin: auto;}
        .explain{
            /*line-height:34px;*/
            margin-top:10px;
            /*heigh:60px;*/
            font-weight: bolder;
            font-size: 22px;
            text-align: center;
            width:98%;
            margin:0 auto;
            /*padding-right:0.3rem;*/
            color:#D90000;
        }
        .questions{padding-bottom: 20px;}
        .questionType{font-size:20px;font-weight:bold;line-height:1.2;margin-top:20px;}
        .main,.main ul{padding:0 22px;}
        .shortAnswer .main ul{padding:0 10px;}
        .main ul{background: #F5F5F5;}
        .main h6{font-size:16px;line-height:1.5;margin:25px 0;font-weight: 600;}
        .main li{line-height:1.5;margin:15px 10px;display:inline-block;font-weight:400;}
        .main li.fl{margin-top:0;}
        .main li input[type=radio]:checked:before {
            background:#D90000;

        }
        .main .active input:focus{
            outline-color:red;
        }
        .main .active{color: #D90000;}
        .main .green{color:#09DB87;}
        .main .red{color:#E11414;}
        .main input{width:auto;}
        .main label{margin-left:10px;}
        .shortAnswer .main textarea{min-height:160px;font-size:14px;}
        .icon-duihao1{font-size:16px;margin-left:4px;}
        .icon-cuo1{font-size:14px;font-weight:bold;margin-left:4px;}
        .submitBtn,.submitPiyueScore{border:0;outline:0;width:90px;height:36px;background:#B4B4B4;border-radius:4px;font-size:14px;color:#fff;margin:10px 0 0 60px;letter-spacing:2px;}
        .submitBtn:active{opacity:.85;}
        .canSubmit{background-color:#E83333;cursor: pointer;}

        .dialog h5{font-size:15px;font-weight:bold;text-align:center;margin-top:10px;}
        .forceSubmitDialog p{font-size:14px;font-weight:bold;text-align:center;margin-top:20px;}
        .scoreDialog p{font-size:12px;text-align:center;}

        .submitBtns button{border:0;outline:0;padding:0;margin:0;height:32px;font-size:12px;color:#fff;text-align:center;border-radius:4px;padding:0 20px!important;}
        .submitBtns .gray{background-color:#B4B4B4;}
        .submitBtns .red{background-color:#E11414;}
        .remainTime{font-size:15px;font-weight:bold;margin-top:20px;text-align:right;}
        .examTime{position: fixed;top: 100px;right: 40px;font-size: 24px}
        .examTime h3{color: red}
        .other{width: 100%;}
        .other textarea{width: 70%!important;}
    </style>
</head>
<body style="height: 100%;">
<div class="wrapper">

    <div class="details">
        <p class="explain"></p>
        <br>
        <p style="color: red; font-size: 18px;">填写说明</p>
        <p style="font-size: 18px;">1.为了解各分公司纪委履行全面从严治党监督责任情况，特开展此次问卷测评；</p>
        <p style="font-size: 18px;">2.请您认真负责、实事求是填写；</p>
        <p style="font-size: 18px;">3.此问卷采取不记名形式；</p>
        <p style="font-size: 18px;">4.请您仔细阅读每个问题及选项。</p>
        <div id="remainTime" class="remainTime"></div>
        <div class="questions">
        </div>
        <!-- 提交 -->
        <button class="submitBtn" id="submit">提交</button>
        <span style="margin-left: 20px" class="pleasePiyue">请打分：</span>
        <input id="piyueScore" oninput="myInput()"></input>
        <button class="submitPiyueScore">提交批阅</button>
        <!-- 提交对话框 -->
        <div id="submitDialog" class="easyui-dialog dialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#submitBtns'" style="width:400px;height:200px;padding:10px"><h5>您已答完所有题，确认提交？</h5></div>
        <div class="btns submitBtns" id="submitBtns" style="text-align:center;">
            <button class="easyui-linkbutton gray" onclick="$('#submitDialog').dialog('close')">取消</button>
            <button id="sureSubmit" class="easyui-linkbutton red">是的确认提交！</button>
        </div>

        <!-- 提交成功对话框 -->
        <div id="scoreDialog" class="easyui-dialog dialog scoreDialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#scoreBtns'" style="width:400px;height:200px;padding:10px">
            <h5></h5>
            <!--<p>（注：简答题不计入合格分数，只计入参考分值）</p>-->
        </div>
        <div class="submitBtns scoreBtns" id="scoreBtns" style="text-align:center;">
            <button id="yes" class="easyui-linkbutton red hide">确定</button>
        </div>

        <!-- 考试结束对话框 -->
        <div id="examOverDialog" class="easyui-dialog dialog examOverDialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#examOverBtns'" style="width:400px;height:200px;padding:10px">
            <h5>答卷时间已到，试卷自动提交，如已过考试参与时间则自动提交失败！</h5>
            <!--<p>（注：简答题不计入合格分数，只计入参考分值）</p>-->
        </div>
        <div class="submitBtns" id="examOverBtns" style="text-align:center;">
            <button id="examOver" class="easyui-linkbutton red ">确定</button>
        </div>

        <!-- 打开试卷时，试卷已完成，关闭页面 -->
        <div id="closeDialog" class="easyui-dialog dialog closeDialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#closeBtns'" style="width:400px;height:200px;padding:10px">
            <h5>您已完成调查问卷，感谢您的参与！</h5>
        </div>
        <div class="submitBtns" id="closeBtns" style="text-align:center;">
            <button class="easyui-linkbutton red">确定</button>
        </div>
    </div>

    <!--    <div class="examTime">-->
    <!--        考试剩余:-->
    <!--        <h3></h3>-->
    <!--    </div>-->
</div>
<script>
    function myInput() {
        var limitNum = $("#piyueScore").val();
        var reg = /^(0|([1-9]\d{0,1})|100)$/;
        if(reg.test(limitNum)==false){
            $("#piyueScore").val("");
            $(".submitPiyueScore").removeClass("canSubmit");
            return false;
        }else{
            $("#piyueScore").val(limitNum);
            $(".submitPiyueScore").addClass("canSubmit");
        }
    }
</script>
</body>
</html>
