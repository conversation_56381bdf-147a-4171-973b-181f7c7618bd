package com.simbest.boot.exam.mainbills.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.mainbills.model.UsPmInstence;
import com.simbest.boot.exam.mainbills.repository.UsPmInstenceRepository;
import com.simbest.boot.exam.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.exam.util.FormatConversion;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <strong>Title : PmInstenceServiceImpl</strong><br>
 * <strong>Description : 业务主单据表业务操作</strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service(value = "pmInstenceService")
public class UsPmInstenceServiceImpl extends LogicService<UsPmInstence,String> implements IUsPmInstenceService {

    private UsPmInstenceRepository pmInstenceRepository;

    @Autowired
    public UsPmInstenceServiceImpl ( UsPmInstenceRepository pmInstenceRepository ) {
        super( pmInstenceRepository );
        this.pmInstenceRepository = pmInstenceRepository;
    }

    /**
     * 逻辑删除操作
     * @param id
     * @return
     */
    @Override
    public int deleteByPmId(Long id) {
        return pmInstenceRepository.deleteByFromId(id);
    }

    /**
     * 查找主单据
     * @param pmInsId 单据ID
     * @return
     */
    @Override
    public UsPmInstence findByPmInsId(String pmInsId) {
        return pmInstenceRepository.findByPmInsId(pmInsId);
    }

    @Override
    public List<UsPmInstence> findByPmInsIdTitle(String title,String sign) {
        return pmInstenceRepository.findByPmInsIdTitle(title,sign);
    }

    /**
     * 获取待办参数
     * @param id  状态表id
     * @return
     */
    @Override
    public Map<String, Object> getTodoParams(String id) {
        Map<String, Object> todoParams = Maps.newHashMap();
        try {
            IUser currentUser = SecurityUtils.getCurrentUser();
            todoParams = pmInstenceRepository.getTodoParams(currentUser.getUsername(), id);
            List<Map<String , Object>> list = Lists.newArrayList();
            list.add(todoParams);
            todoParams = FormatConversion.formatConversion(list).get(0);
        } catch (Exception e ) {
            Exceptions.printException(e);
        }
        return todoParams;
    }
}
