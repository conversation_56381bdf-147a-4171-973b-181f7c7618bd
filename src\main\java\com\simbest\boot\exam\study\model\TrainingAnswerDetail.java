package com.simbest.boot.exam.study.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;

import org.hibernate.annotations.GenericGenerator;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 答题详情实体类，用于记录每个题目提交的答案及是否正确
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "training_answer_detail")
@ApiModel(value = "TrainingAnswerDetail", description = "答题详情表")
public class TrainingAnswerDetail extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "TAD") //主键前缀，此为可选项注解
    @ApiModelProperty(value = "主键ID", example = "1")
    private String id;

    @ApiModelProperty(value = "用户")
    @Column(length = 200)
    private String username;

    @Column(name = "record_id", length = 40)
    @ApiModelProperty(value = "训练记录ID", example = "DTR20250724110858")
    private String recordId;

    @Column(name = "question_id", length = 40)
    @ApiModelProperty(value = "题目ID", example = "EQ20250724110858")
    private String questionId;

    @Column(name = "question_code", length = 40)
    @ApiModelProperty(value = "题目编码", example = "EQ20250724110858")
    private String questionCode;

    @ApiModelProperty(value = "用户答案", example = "A")
    private String userAnswer;

    @ApiModelProperty(value = "正确答案")
    private String questionAnswer;

    @Column(name = "is_correct")
    @ApiModelProperty(value = "是否正确", example = "true")
    private Boolean isCorrect;


    @Column(name = "score")
    @ApiModelProperty(value = "题目得分", example = "2")
    private Integer score;

    @ApiModelProperty(value = "显示顺序", example = "1" )
    private Integer displayOrder;

    @Transient
    @ApiModelProperty(value = "序号")
    private Integer seq;
}