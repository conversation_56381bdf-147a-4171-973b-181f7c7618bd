package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.SystemRepository;
import com.simbest.boot.exam.examOnline.model.SummaryStatistics;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: SummaryStatisticsRepository
 * @projectName exam
 * @description: 季度信息
 * @date 2021/6/29  20:50
 */
public interface SummaryStatisticsRepository extends SystemRepository<SummaryStatistics,String> {


    @Modifying
    @Query(
            value = "select t.* from US_PM_INSTENCE t where t.pm_ins_id=:pmInsId order by t.created_time desc",
            nativeQuery = true
    )
    List<Map<String,Object>> findByPmInsId(@Param("pmInsId") String pmInsId);
}
