package com.simbest.boot.exam.briefDistribution.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.exam.briefDistribution.model.ApplyForm;
import com.simbest.boot.exam.briefDistribution.model.UserInfo;
import com.simbest.boot.exam.briefDistribution.service.ApplyFormService;
import com.simbest.boot.exam.briefDistribution.service.ISyncCommonService;
import com.simbest.boot.exam.briefDistribution.service.UserInfoService;
import com.simbest.boot.exam.codePatrol.model.UsCodePatrol;
import com.simbest.boot.exam.flow.service.ISysTaskInfoService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.SMSTool;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.model.SysDict;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.service.impl.SysFileService;
import com.simbest.boot.util.Zip4JUtil;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.Future;

import static com.simbest.boot.config.MultiThreadConfiguration.MULTI_THREAD_BEAN;

@Service
@Async(MULTI_THREAD_BEAN)
@Slf4j
public class SyncCommonServiceImpl implements ISyncCommonService {

    @Autowired
    private @Lazy ISysTaskInfoService sysTaskInfoService;

    @Autowired
    private @Lazy ApplyFormService applyFormService;
    @Autowired
    UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    UserInfoService userInfoService;

    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    @Autowired
    private SMSTool smsTool;

    @Override
    public Future<Map<String, Object>> startProcess(Map<String, Object> map) {
        Map<String, Object> resultMap = sysTaskInfoService.startProcess(map);
        return new AsyncResult<>(resultMap);
    }


    @Value("${app.email.num}")
    public String emailNum;


    @Autowired
    private AppConfig appConfig;

    @Autowired
    private SysFileService fileService;

    @Value("${spring.profiles.active}")
    public String active;

    @Resource
    private JavaMailSender mailSender;

    /**
     * 推送反馈报表待办
     *
     * @param pmInsId 流程实例编号
     */
    @Override
    public void createTodo(String pmInsId, ApplyForm applyForm) {

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("title", applyForm.getTitle());
        String receiveUsers = applyForm.getReceiveUsers();
        List<String> users = Arrays.asList(receiveUsers.split(","));

        List<String> failUser = new ArrayList<>();
        Map<String, Object> failMap = new HashMap<>();
        for (String username : users) {
            SimpleUser simpleUser = uumsSysUserinfoApi.findByUsername(username, Constants.APP_CODE);
            UserInfo userInfo = new UserInfo();
            userInfo.setPmInsId(pmInsId);
            userInfo.setBusinessId(applyForm.getId());
            userInfo.setUsername(simpleUser.getUsername());
            userInfo.setTruename(simpleUser.getTruename());
            userInfo.setBelongCompanyCode(simpleUser.getBelongCompanyCode());
            userInfo.setBelongCompanyName(simpleUser.getBelongCompanyName());
            userInfo.setBelongCompanyTypeDictValue(simpleUser.getBelongCompanyTypeDictValue());
            userInfo.setBelongCompanyTypeDictDesc(simpleUser.getBelongCompanyTypeDictDesc());
            userInfo.setBelongDepartmentName(simpleUser.getBelongDepartmentName());
            userInfo.setBelongDepartmentCode(simpleUser.getBelongDepartmentCode());
            userInfo.setBelongOrgCode(simpleUser.getBelongOrgCode());
            userInfo.setBelongOrgName(simpleUser.getBelongOrgName());
            userInfo.setTelephoneNumber(simpleUser.getTelephoneNumber());
            userInfo.setPreferredMobile(simpleUser.getPreferredMobile());
            userInfo.setCreator(applyForm.getCreator());
            userInfo.setModifier(applyForm.getCreator());
            userInfoService.insert(userInfo);
            try {
                paramMap.put("pmInsId", pmInsId);
                paramMap.put("businessId", applyForm.getId());
                paramMap.put("taskUsername", username);
                paramMap.put("currentUserame", applyForm.getCreator());
                Map<String, Object> startMap = sysTaskInfoService.startProcess(paramMap);
                if (StrUtil.isNotEmpty(MapUtil.getStr(startMap, "processInstId"))) {
                    applyForm.setStatus(Constants.JBPF_STATUS_SENDED);
//                    String taskId = MapUtil.getStr(startMap, "taskId");
//                    String taskId = M  apUtil.getStr(startMap, "id");
//                    applyForm.setTaskId(taskId);
                } else {
                    failUser.add(username);
                }
            } catch (Exception e) {
                log.error("--->>>> 简报派发 createTodo 创建待办异常，", e);
            } finally {
                failMap.put("userList", failUser);
                failMap.put("applicaitonId", applyForm.getId());
                applyForm.setPushFailStr(JacksonUtils.obj2json(failMap));
                applyFormService.update(applyForm);
            }
        }
    }

    /**
     * 任务下一步
     *
     * @param taskId 任务id
     */
    @Override
    public void completeTask(String taskId) {
        try {
            int isFinishSuccess = sysTaskInfoService.finishTaskInfo(taskId, null, null);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }


    @Async(MULTI_THREAD_BEAN)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushMsg(List<String> userNames, String message) {
        Boolean isPostMsg = false;   //短信开关 false 短信不发送 true 发送
        SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, "hadmin");
        if (simpleApp != null) {
            isPostMsg = simpleApp.getIsSendMsg();
        }
        if (isPostMsg) {
            userNames.stream().forEach(u -> {
                log.error("短信封装" + JSONUtil.toJsonStr(readyParams(u, message)));
                Boolean f = smsTool.postMsg(readyParams(u, message));
                if (f) {
                    log.error("已成功发送短信");
                } else {
                    log.error("发送失败");
                }

            });
        }

    }

    public static ShrotMsg readyParams(String sendUser, String msg) {
        ShrotMsg shrotMsg = new ShrotMsg();
        Content content = new Content();
        Set<Content> contentSet = new HashSet<Content>();
        shrotMsg.setAppCode(Constants.APP_CODE);
        content.setUsername(sendUser);
        content.setMsgContent(msg);
        content.setImmediately(true);
        content.setSmsPriority(1);
        contentSet.add(content);
        shrotMsg.setContents(contentSet);
        return shrotMsg;
    }

    @Async(MULTI_THREAD_BEAN)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushEmail(List<String> userNames, String password, SysDict dict, UsCodePatrol codePatrol) {
        userNames.stream().forEach(userName -> {
            IUser user = uumsSysUserinfoApi.findByUsername(userName, Constants.APP_CODE);
            String email = user.getEmail();
            String title = "“码上巡”平台" + dict.getDescription() + "编号为:[" + codePatrol.getCode() + "]的情况反映";
            String content = "【码上巡】您好，"+dict.getDescription()+"收到编号为:["+codePatrol.getCode()+"]的情况反映，相关材料见附件。";
            if (StrUtil.equals(active, "uat")) {
                email = "<EMAIL>";
            }
            log.error("开始发送邮箱接受者人邮箱地址为" + email);
            String[] staffSum = {email};

            sendEmail(staffSum, title, content, dict.getDescription(), user, codePatrol, password);
        });

    }


    public void sendEmail(String[] email, String title, String content, String description, IUser user, UsCodePatrol codePatrol, String password) {



            //创建一个待压缩的文件夹
            String zipDirPath = appConfig.getUploadTmpFileLocation().concat(ApplicationConstants.SEPARATOR).concat(description + "--" + codePatrol.getCode());
            File zipDirFile = new File(zipDirPath);
            zipDirFile.mkdirs();
            log.error("创建的代亚所未见为是" + zipDirPath);
            List<SysFile> files = new ArrayList<>();
            if (StrUtil.equals(active, "uat")) {
                files = fileService.getFilesByPmInsId("test");
            } else {
                files = fileService.getFilesByPmInsId(codePatrol.getId());

            }

            files.stream().forEach(file -> {
                // 源文件路径
                Path sourcePath = Paths.get(file.getFilePath());
                // 目标文件路径（可以指定新的文件名）
                Path targetPath = Paths.get(zipDirPath + "/" + file.getFileName());
                try {
                    Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
            /**
             * 处理填报内容
             */
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            //multipart模式 为true时发送附件 可以设置html格式
            MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage, true, "utf-8");

            messageHelper.setFrom("<EMAIL>");
            messageHelper.setTo(email);
            messageHelper.setSubject(title);
            messageHelper.setText(content);

            //写入txt
            // 创建文件夹对象
            File folder = new File(zipDirPath);
            if (!folder.exists()) {
                folder.mkdirs(); // 如果文件夹不存在，则创建它
            }

            // 创建文件对象
            File fileTxt = new File(folder, codePatrol.getCode() + ".txt");
            FileWriter bufferedWriter = new FileWriter(fileTxt);

            // 写入数据到文件
            bufferedWriter.write("被巡查对象:" + codePatrol.getPatrolObject());
            bufferedWriter.write("\n");
            bufferedWriter.write("反映的问题:" + codePatrol.getProblemDescription());
            bufferedWriter.write("\n");
            bufferedWriter.write("反映人:" + codePatrol.getReflectingPeople());
            bufferedWriter.write("\n");
            bufferedWriter.write("联系方式:" + codePatrol.getContactInformation());
            bufferedWriter.close();

            //压缩文件
            String path = Zip4JUtil.zip(zipDirPath, zipDirPath.concat(".zip"), password);

            FileSystemResource file = new FileSystemResource(new File(path));
            messageHelper.addAttachment(description + codePatrol.getCode() + ".zip", file);
            log.error("邮箱发送中");
            mailSender.send(mimeMessage);
            log.error("邮箱发送成功");

        } catch (Exception e) {
            Exceptions.printException(e);
            log.error(e.toString());
        }
    }


}
