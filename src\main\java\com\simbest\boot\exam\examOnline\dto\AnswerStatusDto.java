package com.simbest.boot.exam.examOnline.dto;

import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用途：考试汇总模块--有效考试汇总配置数据传输实体
 * 作者：sws
 * 时间: 2021-05-07 11:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "考试答题记录模块-考试答题记录数据传输实体")
public class AnswerStatusDto {

    private Integer answerStatus;

    private List<ExamQuestionUser> questionList;
}
