/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate;

import com.simbest.boot.exam.certificate.doc.util.DocxConverter;
import lombok.extern.slf4j.Slf4j;
import org.artofsolving.jodconverter.OfficeDocumentConverter;
import org.artofsolving.jodconverter.office.OfficeException;
import org.artofsolving.jodconverter.office.OfficeManager;

import java.io.File;
import java.util.regex.Pattern;

/**
 * <strong>Title : PDFConvert</strong><br>
 * <strong>Description : PDF转换 </strong><br>
 * <strong>Create on : 2020/11/12</strong><br>
 * <strong>Modify on : 2020/11/12</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
public class PDFConvert {
    private static String officeHomeDir = null;

    //private final static Logger logger = LoggerFactory.getLogger(PDFConvert.class);

    /**
     *
     *@name    文档转换为pdf工具类
     *@description 相关说明 支持：xls，xlsx，ppt，pptx，txt，其中doc，docx转换与原文件有较大差异,libreOffice 默认安装路径
     *Linux：/opt/libreoffice6.0
     *Windows：C:/Program Files (x86)/LibreOffice
     *Mac：/Application/openOfficeSoft
     *@time    创建时间:2020年11月12日下午1:49:18
     *@param sourceFile 需要转换的原文件
     *@param tarPdfFile 转换后的目标pdf文件
     *@return
     *@throws OfficeException
     *@<NAME_EMAIL>
     *@history 修订历史（历次修订内容、修订人、修订时间等）
     */
    public static String doDocToFdpLibre(String sourceFile, String tarPdfFile) throws OfficeException {

        File inputFile = new File(sourceFile);

        OfficeManager officeManager = DocxConverter.getOfficeManagerAndStartService(getOfficeHome());
        OfficeDocumentConverter converter = new OfficeDocumentConverter(officeManager);
        File outputFile = new File(tarPdfFile);
        converter.convert(inputFile, outputFile);
        stopService(officeManager);
        String pdfPath = outputFile.getPath();
        return pdfPath;
    }

    private static String getOfficeHome() {

        String osName = System.getProperty("os.name");
        if (Pattern.matches("Windows.*", osName)) {
            officeHomeDir = ConstantsOffice.LIBRE_OFFICE_PATH_WINDOWS;
            return officeHomeDir;
        } else if (Pattern.matches("Linux.*", osName)) {
            officeHomeDir = ConstantsOffice.LIBRE_OFFICE_PATH_LINUX;
            return officeHomeDir;
        } else if (Pattern.matches("Mac.*", osName)) {
            officeHomeDir = ConstantsOffice.LIBRE_OFFICE_PATH_MAC;
            return officeHomeDir;
        }
        return null;

    }

    private static void stopService(OfficeManager officeManager) throws OfficeException {
        if (null != officeManager) {
            officeManager.stop();
        }
        log.info("关闭office转换成功!");
    }




}
