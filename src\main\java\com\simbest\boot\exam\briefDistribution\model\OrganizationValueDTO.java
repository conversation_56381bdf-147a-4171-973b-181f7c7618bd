package com.simbest.boot.exam.briefDistribution.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * 组织人员DTO
 *
 * @Auther: ztz
 * @Date: 2021/3/31 16:24
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "组织人员实体类")
public class OrganizationValueDTO {



    @ExcelVOAttribute(name = "用户真实名称", column = "A")
    private String userTrueName;
    @ExcelVOAttribute(name = "用户OA账号", column = "B")
    private String userName;
    @ExcelVOAttribute(name = "电话", column = "C")
    private String phone;

}
