<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>考试维护信息</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>

  <style>
    *{margin: 0; padding: 0;}
    .table_searchD{
      height: 510px;
    }
    .companyName{
      margin-bottom: 10px;
    }


  </style>
  <script type="text/javascript">
  var rangeList=[];//公司列表
  var inputArr=[];//下拉框数据

  $(function(){
    var gps=getQueryString();
    getCurrent(initForm);


    function initForm(){//初始化表
      ajaxgeneral({//获取参与考试的组织数据
        url:"action/range/findRangeList",
        data:{"summaryId":gps.summaryId},
        contentType: "application/json; charset=utf-8",
        success:function(res){
          var datas=res.data;
          if (datas.length>0) {
            for (var i = 0; i < datas.length; i++) {
              rangeList.push(datas[i]);       
            }
            showCompany();
          }else{
            $(".table_searchD").append('<div id="message" style=" margin-top:50px; text-align:center;"><h2>暂无参与考试的组织!</h2></div>');
          }

        }
      });


      //获取下拉框需要的数据
      ajaxgeneral({
        url:"action/examAttribute/findAllNoPage",
        data:{"summaryId":gps.summaryId},
        contentType: "application/json; charset=utf-8",
        success:function(res){
          var exams = res.data;
          for (var k = 0; k < exams.length; k++) {
            var arrDatas = {};
            arrDatas.examPaperName=exams[k].examName;
            arrDatas.examPaperCode=exams[k].examAppCode;
            inputArr.push(arrDatas);
          }
          return inputArr;
        }
      });
    }


    //添加组织信息
    $(document).on('click','.findCompany',function(e){
      //ie8不兼容event、target；以下写法用来兼容ie8
      var event = e || window.event;
      var target = event.target||event .srcElement;
      top.dialogP('html/exammanage/examManage_rangeTree.html?summaryId='+gps.summaryId,window.name,'组织清单','findCompany',false,'650','600');
    })




    //删除键效果
    $(document).on('click','.delete',function(e){
      $(this).parent().remove();
      var id = $(this).parent().attr("id");
      rangeList.splice(id,1);
      $(".companyName").each(function(i,v){
        $(v).attr("id",i);
      })
    })



    });



  //展示所属公司
  function showCompany(){
    $(".table_searchD > div").remove();//删除现有样式后重新遍历渲染

    for (var  i=0 ; i< rangeList.length;i++) {//遍历查询接收到的数据进行渲染
      $(".table_searchD").append(
        '<div class="companyName" id="'+i+'" style="display:flex; padding-left:60px">'+
          '<div style="width:100px; height:32px; line-height:32px;">'+rangeList[i].companyName+'</div>'+
          '<div style="width:100px; height:32px; line-height:32px; text-align:right;" '+
          ' class="" valign="top">试卷名称：</div>'+

          //input框
          '<div style="width:234px;"><input  name="examName"'+
          '" class="easyui-combobox examName" style="width: 234px; height: 32px;" '+
          '"/>'+
          '</div>'+

          '<i style="text-align:center;line-height: 32px; width:32px; color:#FF7F50" class=\"iconfont icon-shanchu delete\"></i>'+
        '</div>'

      );
    }

    //遍历解析combobox下拉框盒子
    $('.examName').each(function (i,v){
        $(v).combobox({        
            valueField:'examPaperCode',    
            textField:'examPaperName' ,  
            data:inputArr,
            ischooseallTxt:'请选择',
            editable:false
        }); 
        // $(v).val(rangeList[i].examPaperName);
        if (rangeList[i].examPaperName!=""||rangeList[i].examPaperName!=null) {
          $(v).combobox('select', rangeList[i].examPaperName);
        }
        if (rangeList[i].examPaperCode!=""||rangeList[i].examPaperCode!=null) {
          $(v).combobox('setValue', rangeList[i].examPaperCode);
        }

        // var examPaperName1 = $("span > input",v).val();//获取变形展示的input文本
        // var examPaperName2 = $("input",v).val();//获取变形前input的文本  因为被隐藏所以始终为空
        // var examPaperCode1=$("input",v).combobox("getValue");//获取变形前input的value

        //console.log(examPaperName1,"val1");
        //console.log(examPaperName2,"val2");
        //console.log(examPaperCode1,"getValue1");

    });




  }

  //findCompany对话框的回调，发送更新请求和执行重新载入函数
  window.findCompany = function (data){//编辑考试
    var datas = data.data;
    pushDatas(datas);
    showCompany();
  }


  //筛选出以前未选择的数据push进list数组
  function pushDatas(datas){
    for (var i = 0; i < datas.length; i++) {
      var flag=0;
      for (var index = 0; index < rangeList.length; index++) {
        if (datas[i].orgCode==rangeList[index].orgCode) {
          flag=1;
        }
      }
      if (flag==0) {
        rangeList.push(datas[i]);
      }
    }
  }


  //获取数据发送到上级页面
  window.getchoosedata=function(){
    var datas=[];
      $(".companyName").each(function (index,v) {
        var examPaperName = $("span > input",v).val();
        var examPaperCode=$("input",v).combobox("getValue");
        if(examPaperName!=null||examPaperName!=""){
          rangeList[index].examPaperName=examPaperName;
        }
        if(examPaperCode!=null||examPaperCode!=""){
          rangeList[index].examPaperCode=examPaperCode;
        }


        rangeList[index].summaryId=gps.summaryId;
      })
    var summaryId = gps.summaryId;
    rangeList.summaryId=gps.summaryId;
    datas.push(rangeList);
    return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};
  };

  </script>
</head>
<body style="padding-top: 0;">
<!--searchform-->
<div class="table_searchD">
  <form id="rangeListForm">
    <table border="0" cellpadding="10" cellspacing="6" >

      <tr>
        <td width="140" class="tittle" align="right">参与考试组织名称:</td>
        <td width="60"></td>
        <td>
          <div class="w100">
            <a class="btn fl findCompany"><span>添加</span></a>
          </div>
        </td>
      </tr>
      
    </table>
  </form>
</div>

</body>
</html>
