/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.repository;/**
 * Created by KZH on 2019/10/8 16:06.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.knowledge.model.UsInvitations;
import com.simbest.boot.exam.knowledge.model.UsQuizSessions;
import com.simbest.boot.exam.knowledge.model.UsUserAnswers;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:06
 * @desc
 **/
public interface UsQuizSessionsRepository extends LogicRepository<UsQuizSessions, String> {

    /**
     * 查询对战未结束的情况
     * @param currentDay
     * @return
     */
    @Query(value = "select t.*" +
            "  from US_QUIZ_SESSIONS t" +
            " where t.enabled = 1" +
            "   and to_char(t.created_time, 'yyyy-MM-dd') =:currentDay" +
            "   and t.status='ONGOING'", nativeQuery = true)
    List<UsQuizSessions> cancleQuizSessions(@Param("currentDay") String currentDay);

    /**
     * 查询当天对战人对战情况
     * 非A即B
     * 无法判断待办答题人是邀请人还是被邀请人，可以使用不是邀请人的话那就是被邀请人的思路
     * @param currentDay
     * @return
     */
    @Query(value = "select t.*" +
            "  from US_QUIZ_SESSIONS t" +
            " where t.enabled = 1" +
            "   and to_char(t.created_time, 'yyyy-MM-dd')=:currentDay" +
            "   and t.status ='ONGOING'" +
            "   and t.send_user_name=:sendUserName", nativeQuery = true)
    List<UsQuizSessions> checkUpdateSeeeion(@Param("currentDay") String currentDay,@Param("currentDay") String sendUserName);

    /**
     * 根据时间和邀请ID查询
     * @param currentDay
     * @param invitaitonId
     * @return
     */
    @Query(value = "select t.*" +
            "  from US_QUIZ_SESSIONS t" +
            " where t.enabled = 1" +
            "   and to_char(t.created_time, 'yyyy-MM-dd')=:currentDay" +
            "   and t.status ='ONGOING'" +
            "   and t.invitation_id=:invitaitonId", nativeQuery = true)
    List<UsQuizSessions> findAndUpdateSeeeion(@Param("currentDay") String currentDay,@Param("invitaitonId") String invitaitonId);
}


