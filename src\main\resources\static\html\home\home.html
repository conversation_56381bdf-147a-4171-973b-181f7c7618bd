<!-- 答题home页 -->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
>
<head>
	<title>首页</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
	<link href="../fonts/iconfont/iconfont.css" th:href="@{/fonts/iconfont/iconfont.css}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/js/themes/default/easyui.css" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/js/themes/icon.css" th:href="@{http://************:8088/simbestui/js/themes/icon.css}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/css/public.css" th:href="@{http://************:8088/simbestui/css/public.css}" rel="stylesheet"/>
	<!-- <link href="http://************:8088/simbestui/css/index.css" th:href="@{http://************:8088/simbestui/css/index.css}" rel="stylesheet"/> -->
	<script src="http://************:8088/simbestui/js/jquery.min.js" th:src="@{http://************:8088/simbestui/js/jquery.min.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.easyui.min.js" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.calendar.js" th:src="@{http://************:8088/simbestui/js/jquery.calendar.js}" type="text/javascript"></script>
	<script src="../../js/jquery.config.js" th:src="@{/js/jquery.config.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js}" type="text/javascript"></script>

    <script src="../../js/flexible.js?v=svn.revision" th:src="@{/js/flexible.js?v=svn.revision}" type="text/javascript"></script>
</head>

<style>
    body{ background: #fff;margin: 0;padding: 0; }
    .bg{width: 100vw;height: 100vh;
        background: url(../../images/home/<USER>
        background-size: 100% 100%;
        position: relative;
      }

    .pufaBtn{
        width: 300px;
        height: 80px;
        background: url(../../images/home/<USER>
        background-size: 100% 100%;
         position: absolute;
         transform: translate(-50%, -50%);
        top: 42%;
        left: 50%;
        cursor: pointer;
    }
    .zhuanyeBtn{
        width: 300px;
        height: 80px;
        background: url(../../images/home/<USER>
        background-size: 100% 100%;
         position: absolute;
         transform: translate(-50%, -50%);
        top: 52%;
        left: 50%;
    }

    .group{
        width: 65%;
        height: 80px;
        position: absolute;
        transform: translate(-50%, -50%);
        top: 65%;
        left: 50%;
        /* text-align: center; */
        display: flex;
        justify-content: space-around;
    }
    .group .up{
        width: 50px;
        height: 40px;
        background: url(../../images/home/<USER>
        background-size: 100% 100%;
        display: inline-block;
    }
    .group .down{
        font-size: 30px;
       color: #e7ff5e;
       font-weight: bold;
    }
    a:hover{
        text-decoration: none;
    }
</style>
<body>
<div class="bg">
    <div class="pufaBtn hide"  onclick="pufaBtn()"></div>
    <div class="zhuanyeBtn hide"></div>

    <div class="group hide">
        <div style="text-align: center;  cursor: pointer;"    onclick="flBox()"  class="flBox hide">
           <div class="up"></div>
           <div class="down">法律组</div>
        </div>
        <div style="text-align: center;  cursor: pointer;"   onclick="nsBox()" class="nsBox hide">
            <div class="up"></div>
            <div class="down">内审组</div>
         </div>
        <div style="text-align: center;  cursor: pointer;" onclick="gjBox()" class="gjBox hide">
            <div class="up"></div>
            <div class="down">工建组</div>
        </div> 
        <div style="text-align: center;  cursor: pointer;"  onclick="jjBox()"  class="jjBox hide">
            <div class="up"></div>
            <div class="down">纪检组</div>
        </div>
    </div>
    <!-- <a href="" class="btn xxx">123456</a> -->
</div>
</body>
<script>
    var gps = getQueryString();
    if (gps.from == "oa") {
        ajaxgeneral({
            url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.uid,
            async: false,
            success: function (ress) {
                username = ress.data.username;
            }
        });
    } else if (gps.access_token) {//手机办公
        ajaxgeneral({
            url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.currentUserCode,
            async: false,
            success: function (ress) {
                username = ress.data.username;
                if (ress.data.belongCompanyTypeDictValue == '01') {
                    companyName = '省公司';
                }
                if (ress.data.belongCompanyTypeDictValue == '02') {
                    companyName = ress.data.belongCompanyName;
                }
                if (ress.data.belongCompanyTypeDictValue == '03') {
                    companyName = ress.data.belongCompanyNameParent;
                }
            }
        });
    } else {
        getCurrent();
        username = web.currentUser.username;
    }
    var flz =  {
        id:'',examAppCode:'',examCode:''
    } 
    var nsz =  {
        id:'',examAppCode:'',examCode:''
    } 
    var gjz =  {
        id:'',examAppCode:'',examCode:''
    } 
    var jjz =  {
        id:'',examAppCode:'',examCode:''
    } 
    var pfz =  {
        id:'',examAppCode:'',examCode:''
    } 
    // var username = web.currentUser.username

    var wpf,wfl,wgj,wns,wjj
    var showF=''
    $(function(){
        ajaxgeneral({
            url: 'action/examWork/queryMyTaskMJSF?source=PC&page=1&rows=99&size=99',
            contentType: "application/json; charset=utf-8",
            success: function (res) {
                var contentList =  res.data?res.data.content:[]
                for(var i in  contentList){
                    if(contentList[i].examCode.indexOf('flz')!== -1){
                       $('.zhuanyeBtn').show()
                       $('.group').show()
                       $('.flBox').show()
                       flz.id = contentList[i].id
                       flz.examAppCode = contentList[i].examAppCode
                       flz.examCode = contentList[i].examCode
                    }
                    if(contentList[i].examCode.indexOf('nsz')!== -1){
                        $('.zhuanyeBtn').show()
                       $('.group').show()
                       $('.nsBox').show()

                       nsz.id = contentList[i].id
                       nsz.examAppCode = contentList[i].examAppCode
                       nsz.examCode = contentList[i].examCode
                    }
                    if(contentList[i].examCode.indexOf('gcz')!== -1){
                        $('.zhuanyeBtn').show()
                       $('.group').show()
                       $('.gjBox').show()

                       gjz.id = contentList[i].id
                       gjz.examAppCode = contentList[i].examAppCode
                       gjz.examCode = contentList[i].examCode
                    }
                    if(contentList[i].examCode.indexOf('jjz')!== -1){
                        $('.zhuanyeBtn').show()
                       $('.group').show()
                       $('.jjBox').show()

                       jjz.id = contentList[i].id
                       jjz.examAppCode = contentList[i].examAppCode
                       jjz.examCode = contentList[i].examCode
                    }

                    if(contentList[i].examCode.indexOf('pfz')!== -1){
                       $('.pufaBtn').show()

                       pfz.id = contentList[i].id
                       pfz.examAppCode = contentList[i].examAppCode
                       pfz.examCode = contentList[i].examCode
                    }
                }
            }
        })
    })

    //普法赛道-跳转答题
    function pufaBtn () {
       closeFun()
        ajaxgeneral({
            url: 'action/summary/findEffectiveExamByExamCodeMJSF?source=PC&currentUserCode=' + username +'&examCodes=' + pfz.examCode,
            contentType: "application/json; charset=utf-8",
            async:false,
            success: function (res) {
                if(res.data.showFlag){
                    wpf = window.open('/exam/html/home/<USER>'+ pfz.id +'&examAppCode='+ pfz.examAppCode +'&examCode='+ pfz.examCode +'&username='+ username +'&actionType=secrecyTask&titName=null&name=detailVal')
                }else{
                    return top.mesShow("温馨提示！", "竞赛暂未开始或暂未权限！", 2000, "red")
                }
            }
        })

    };

    //法律组-跳转答题
    function flBox() {
        closeFun()
        ajaxgeneral({
            url: 'action/summary/findEffectiveExamByExamCodeMJSF?source=PC&currentUserCode=' + username +'&examCodes=' + flz.examCode,
            contentType: "application/json; charset=utf-8",
            async:false,
            success: function (res) {
                if(res.data.showFlag){
                    wfl =  window.open('/exam/html/home/<USER>'+ flz.id +'&examAppCode='+ flz.examAppCode +'&examCode='+ flz.examCode +'&username='+ username +'&actionType=secrecyTask&titName=null&name=detailVal')
                }else{
                    return top.mesShow("温馨提示！", "竞赛暂未开始或暂未权限！", 2000, "red")
                }
            }
        })
    };

     //工建组-跳转答题
     function gjBox() {
        closeFun()
        ajaxgeneral({
            url: 'action/summary/findEffectiveExamByExamCodeMJSF?source=PC&currentUserCode=' + username +'&examCodes=' + gjz.examCode,
            contentType: "application/json; charset=utf-8",
            async:false,
            success: function (res) {
                if(res.data.showFlag){
                    wgj = window.open('/exam/html/home/<USER>'+ gjz.id +'&examAppCode='+ gjz.examAppCode +'&examCode='+ gjz.examCode +'&username='+ username +'&actionType=secrecyTask&titName=null&name=detailVal')
                }else{
                    return top.mesShow("温馨提示！", "竞赛暂未开始或暂未权限！", 2000, "red")
                }
            }
        })
    };

     //内审组-跳转答题
     function nsBox() {
        closeFun()
        ajaxgeneral({
            url: 'action/summary/findEffectiveExamByExamCodeMJSF?source=PC&currentUserCode=' + username +'&examCodes=' + nsz.examCode,
            contentType: "application/json; charset=utf-8",
            async:false,
            success: function (res) {
                if(res.data.showFlag){
                    wns =  window.open('/exam/html/home/<USER>'+ nsz.id +'&examAppCode='+ nsz.examAppCode +'&examCode='+ nsz.examCode +'&username='+ username +'&actionType=secrecyTask&titName=null&name=detailVal')
                }else{
                    return top.mesShow("温馨提示！", "竞赛暂未开始或暂未权限！", 2000, "red")
                }
            }
        })
    };

     //纪检组-跳转答题
     function jjBox() {
        closeFun()
        ajaxgeneral({
            url: 'action/summary/findEffectiveExamByExamCodeMJSF?source=PC&currentUserCode=' + username +'&examCodes=' + jjz.examCode,
            contentType: "application/json; charset=utf-8",
            async:false,
            success: function (res) {
                if(res.data.showFlag){
                    wjj = window.open('/exam/html/home/<USER>'+ jjz.id +'&examAppCode='+ jjz.examAppCode +'&examCode='+ jjz.examCode +'&username='+ username +'&actionType=secrecyTask&titName=null&name=detailVal')
                }else{
                    return top.mesShow("温馨提示！", "竞赛暂未开始或暂未权限！", 2000, "red")
                }
            }
        })
    };

    function closeFun(){
        if(wpf && !wpf.closed){ wpf.close() }
        if(wfl && !wfl.closed){ wfl.close() }
        if(wgj && !wgj.closed){ wgj.close() }
        if(wns && !wns.closed){ wns.close() }
        if(wjj && !wjj.closed){ wjj.close() }
    }
</script>
</html>