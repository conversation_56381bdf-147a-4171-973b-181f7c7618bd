/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examWork.repository;/**
 * Created by KZH on 2019/10/8 16:06.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examWork.model.ExamWork;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:06
 * @desc
 **/
public interface ExamWorkRepository extends LogicRepository<ExamWork, String> {

    /**
     * 查询待办考试数量
     */
    @Query(value = "select count(*) from us_exam_work t where t.exam_code=:examCode and t.enabled=1", nativeQuery = true)
    int countExamWorkByTask(@Param("examCode") String examCode);

    /**
     * 查询统一待办考试数量
     */
    @Query(value = "select count(*) from us_exam_work t where t.exam_code=:examCode and t.IS_TODO_FLAG = 1 and t.enabled=1", nativeQuery = true)
    int countExamWorkByTodo(@Param("examCode") String examCode);

    /**
     * 根据 publishUsername 获取答题记录
     *
     * @param username
     * @return
     */
    @Query(value = "select t.*  from us_exam_work t  where t.transactor_Code=:username  and t.exam_code =:examCode  and t.enabled=1 ",
            nativeQuery = true)
    ExamWork findAllByUsername(@Param("username") String username, @Param("examCode") String examCode);


    /**
     * 根据
     *
     * @param workType
     * @return
     */
    @Query(value = "select *" +
            "  from us_exam_work t, us_pm_instence u" +
            " where t.pm_ins_id = u.pm_ins_id" +
            "   and t.exam_code = ?1" +
            "   and t.is_todo_flag = ?3" +
            "   and t.work_type = ?2" +
            "   and t.enabled = '1'" +
            "   and u.enabled = '1'" +
            "   and u.sign = '0'",
            nativeQuery = true)
    List<ExamWork> findTodoWork(String examCode, String workType, String isTodoFlag);

    /**
     * 根据
     *
     * @param workType
     * @return
     */
    @Query(value = "select * from (select t.*  from us_exam_work t  where t.work_type=:workType " +
            " and t.enabled=1 order by t.created_time DESC ) where rownum=:count",
            nativeQuery = true)
    List<ExamWork> findAllByEXamCode(@Param("workType") String workType, @Param("count") Integer count);


    @Query(value = "select t.*  from us_exam_work t  where t.transactor_Code=:username and t.enabled=1 ",
            nativeQuery = true)
    List<ExamWork> findAllByUsernameApp(@Param("username") String username);

    /**
     * 根据用户名，待办类型查询待办信息
     *
     * <AUTHOR>
     * @date 2021/6/24
     */
    @Query(value = " select * from (select t.*  from us_exam_work t  where t.transactor_Code=:transactorCode " +
            " and t.enabled=1 " +
            " and t.work_type=:workType " +
            " order by t.created_time desc ) where rownum=1 ",
            nativeQuery = true)
    ExamWork findByTransactorCodeAndWorkType(@Param("transactorCode") String transactorCode, @Param("workType") String workType);


    @Query(value = " select w.transactor_code , i.sign  from exam.us_pm_instence i , exam.us_exam_work w where i.pm_ins_id = w.pm_ins_id and w.enabled = 1 and i.enabled =1  and i.sign = 0 and w.exam_code = :examCode and w.transactor_code = :transactorCode " , nativeQuery = true)
    List<Map<String, Object>> checkIsNotDone(@Param("transactorCode") String transactorCode, @Param("examCode") String examCode);
}
