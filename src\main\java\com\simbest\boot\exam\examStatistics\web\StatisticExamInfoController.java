/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examStatistics.web;/**
 * Created by KZH on 2019/11/26 10:03.
 */

import com.google.common.collect.Maps;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examStatistics.service.IStatisticExamInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;


/**
 * <AUTHOR>
 * @create 2019-11-26 10:03
 * @desc
 **/
@Api(description = "答题情况统计")
@Slf4j
@RestController
@RequestMapping(value = "/action/statisticExamInfo")
public class StatisticExamInfoController {

    @Autowired
    private IStatisticExamInfoService iStatisticExamInfoService;


    @ApiOperation(value = "获取原始的考试条件信息", notes = "获取原始的考试条件信息")
    @PostMapping(value = {"/getRawExamConditionInfo"})
    public JsonResponse getRawExamConditionInfo(@RequestParam(required = false) String companyName) {

        return JsonResponse.success(iStatisticExamInfoService.getRawExamConditionInfo(companyName, "hnjjwz"));
    }

    @ApiOperation(value = "通用考试结果导出", notes = "通用考试结果导出")
    @PostMapping(value = {"/exportExcel"})
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, String examCode) {

        iStatisticExamInfoService.exportExcel(request, response, examCode);
    }

    @ApiOperation(value = "题目分析数据导出", notes = "题目分析数据导出")
    @PostMapping(value = {"/exportExcel2"})
    public void exportExcel2(HttpServletRequest request, HttpServletResponse response) {

        Map<String, Object> paramMap = Maps.newHashMap();
        iStatisticExamInfoService.exportExcel2(request, response, paramMap);
    }

    @ApiOperation(value = "题目分析数据导出", notes = "题目分析数据导出")
    @PostMapping(value = {"/exportExcel3"})
    public void exportExcel3(HttpServletRequest request, HttpServletResponse response) {

        iStatisticExamInfoService.exportExcel3(request, response);
    }

    @ApiOperation(value = "力量大厦计算每道题正确率", notes = "力量大厦计算每道题正确率")
    @PostMapping(value = {"/exportExcel4", "/exportExcel4/sso"})
    public JsonResponse exportExcel4() {

        return iStatisticExamInfoService.exportExcel4();
    }

    /**
     * 2024xszg_ks 考试定制化导出
     */
    @PostMapping(value = {"/exportExcelBy2024xszg_ks"})
    public void exportExcelBy2024xszg_ks(HttpServletRequest request, HttpServletResponse response) {

        iStatisticExamInfoService.exportExcelBy2024xszg_ks(request, response);
    }

}
