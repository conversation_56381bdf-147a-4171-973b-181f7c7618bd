/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.service;/**
 * Created by KZH on 2019/12/9 9:20.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.lottery.model.Lottery;

/**
 * <AUTHOR>
 * @create 2019-12-09 9:20
 * @desc
 **/
public interface ILotteryService extends ILogicService<Lottery,String> {

    /**
     * 根据概率是否中奖
     * @return 是否中奖
     */
    JsonResponse isLottery(String examCode);

    /**
     * 根据概率是否中奖
     * @param examCode 问卷code
     * @param source 来源
     * @param currentUserCode 当前人
     * @return JsonResponse 是否中奖
     */
    JsonResponse isLotteryExamPowerBuilding(String examCode,String source,String currentUserCode);

    /**
     * 对中奖人的信息进行处理
     * @param lottery
     * @return
     */
    JsonResponse createToJackpotReduce(Lottery lottery);
}
