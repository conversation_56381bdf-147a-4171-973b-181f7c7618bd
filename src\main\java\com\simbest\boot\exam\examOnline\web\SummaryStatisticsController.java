package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.web.controller.GenericController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfo;
import com.simbest.boot.exam.examOnline.model.SummaryStatistics;
import com.simbest.boot.exam.examOnline.service.IExamRangeUserInfoService;
import com.simbest.boot.exam.examOnline.service.ISummaryStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @title: SummaryStatisticsController
 * @projectName exam
 * @description: 季度信息控制器
 * @date 2021/6/29  21:01
 */
@Api(description = "季度信息控制器", tags = {"统计结果-统计结果汇总--季度信息控制器"})
@Slf4j
@RestController
@RequestMapping("/action/summaryStatistics")
public class SummaryStatisticsController extends GenericController<SummaryStatistics,String> {
    private ISummaryStatisticsService service;

    @Autowired
    public SummaryStatisticsController(ISummaryStatisticsService service) {
        super(service);
        this.service = service;
    }

    @ApiOperation(value = "获取季度信息", notes = "获取季度信息")
    @PostMapping(value = {"/listSummary", "/sso/listSummary", "/api/listSummary"})
    public JsonResponse listSummary() {
        return JsonResponse.success( service.ListSummary());
    }

}
