package com.simbest.boot.exam.examOnline.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.simbest.boot.base.service.impl.SystemService;
import com.simbest.boot.exam.examOnline.model.ExamAttribute;
import com.simbest.boot.exam.examOnline.model.ExamRange;
import com.simbest.boot.exam.examOnline.model.ExamSummary;
import com.simbest.boot.exam.examOnline.repository.ExamRangeRepository;
import com.simbest.boot.exam.examOnline.service.IExamAttributeService;
import com.simbest.boot.exam.examOnline.service.IExamRangeGroupService;
import com.simbest.boot.exam.examOnline.service.IExamRangeService;
import com.simbest.boot.exam.examOnline.service.IExamSummaryService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Iterator;
import java.util.List;

/**
 * 用途：考试信息模块--考试范围service
 * 作者：gy
 * 时间: 2021-02-01 10:45
 */
@Slf4j
@Service
public class ExamRangeServiceImpl extends SystemService<ExamRange, String> implements IExamRangeService {
    private ExamRangeRepository repository;

    @Autowired
    public ExamRangeServiceImpl(ExamRangeRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private IExamAttributeService examAttributeService;

    @Autowired
    private IExamSummaryService summaryService;

    @Autowired
    private IExamRangeGroupService examRangeGroupService;

    /**
     * 保存考试范围信息
     *
     * @param summaryId 考试汇总信息外键
     * @param rangeList 考试范围集合
     */
    @Override
    @Transactional
    public void saveExamRange(String summaryId, List<ExamRange> rangeList) {
        Assert.isTrue(null != rangeList && rangeList.size() > 0, "考试范围不能为空");
        // 根据考试汇总信息获取本次考试的考试范围
        Specification<ExamRange> spec = Specifications
                .<ExamRange>and()
                .eq("summaryId", summaryId)
                .build();
        Iterable<ExamRange> ranges = findAllNoPage(spec);
        // 清空之前的考试范围信息
        Iterator<ExamRange> iterator = ranges.iterator();
        while (iterator.hasNext()) {
            this.delete(iterator.next());
        }
        // 保存新的考试范围信息
        for (ExamRange examRange : rangeList) {
            examRange.setId(null);
            examRange.setSummaryId(summaryId);
            // 每次新增前，校验是否已经存在此次考试范围
            Specification<ExamRange> rangeSpec = Specifications
                    .<ExamRange>and()
                    .eq("summaryId", summaryId)
                    .eq("companyCode", examRange.getCompanyCode())
                    .build();
            Iterator<ExamRange> oldRangeIter = findAllNoPage(rangeSpec).iterator();
            Assert.isTrue(!oldRangeIter.hasNext(), "已经存在相同考试范围!");
            this.insert(examRange);
        }
        // 以上操作完成后，记录本次考试范围的应考人数
        ExamSummary summaryInfo = summaryService.findById(summaryId);
        Assert.notNull(summaryInfo, "考试汇总信息不存在");
        Integer joinExamNum = summaryService.findJoinExamNum(rangeList);
//        summaryInfo.setJoinExamNum(joinExamNum);
        summaryService.update(summaryInfo);
    }

    /**
     * 根据考试汇总信息外键、公司编码获取当前人归属考试范围
     *
     * @param summaryId   考试汇总信息外键
     * @param companyCode 公司编码
     * @return 返回考试范围信息
     */
    @Override
    public ExamRange findByCompanyCodeAndSummaryId(String summaryId, String companyCode) {
        ExamRange result = null;
        // 构筑查询条件
        Specification<ExamRange> spec = Specifications
                .<ExamRange>and()
                .eq("summaryId", summaryId)
                .eq("companyCode", companyCode)
                .build();
        Iterable<ExamRange> iterable = findAllNoPage(spec);
        Iterator<ExamRange> iterator = iterable.iterator();
        // 如果获取到考试范围，不管数据结果有多少个，默认获取第一个返回
        if (iterator.hasNext()) {
            result = iterator.next();
        }
        return result;
    }

    /**
     * 根据考试汇总信息外键获取考试范围
     *
     * @param summaryId 考试汇总信息外键
     * @return 返回考试范围集合
     */
    @Override
    public Iterator<ExamRange> findBySummaryId(String summaryId) {
        Specification<ExamRange> spec = Specifications.<ExamRange>and()
                .eq("summaryId", summaryId)
                .build();
        return findAllNoPage(spec).iterator();
    }

    /**
     * 保存考试范围采用的试卷
     *
     * @param id            考试范围信息主键
     * @param examPaperCode 考试试卷编码
     * @return 返回入库数据
     */
    @Override
    public ExamRange saveRangePaper(String id, String examPaperCode) {
        // 保存考试范围选择的试卷编码
        ExamRange rangeInfo = findById(id);
        rangeInfo.setExamPaperCode(examPaperCode);
        return update(rangeInfo);
    }

    /**
     * 查询考试范围信息
     *
     * @param o 表单查询条件
     * @return 返回分页后的考试范围信息
     */
    @Override
    public List<ExamRange> findRangeList(ExamRange o) {
        List<ExamRange> resultList = Lists.newArrayList();
        // 构筑查询条件
        Specification<ExamRange> spec = Specifications
                .<ExamRange>and()
                .eq("summaryId", o.getSummaryId())
                .build();
        // 构筑分页对象
        Iterator<ExamRange> rangeIter = findAllNoPage(spec).iterator();
        // 获取每个考试范围采用的试卷名称
        while (rangeIter.hasNext()) {
            ExamRange range = rangeIter.next();
            ExamAttribute examPaper = examAttributeService.getExamAttributeByExamAppCode(range.getExamPaperCode());
            if (null != examPaper) {
                range.setExamPaperName(examPaper.getExamName());
            }
            resultList.add(range);
        }
        return resultList;
    }

    /**
     * 根据考试编码获取考试范围信息
     *
     * @param examCode 考试编码
     * @return 考试范围信息集合
     */
    @Override
    public String findPaperCodeByExamCode(String examCode) {
        // 获取当前登陆人信息
        IUser iUser = SecurityUtils.getCurrentUser();
        Assert.notNull(iUser, "未获取到登录信息");
        String paperCode = "";
        //获取用户id
        String username = iUser.getUsername();
        //获取试卷编码
        paperCode = examRangeGroupService.findExamAppCodeByCurrentUser(username);
        if (StringUtils.isEmpty(paperCode)) {
            // 获取当前登陆人所属公司（县公司员工获取市公司）
            String companyCode = "";
            companyCode = iUser.getBelongCompanyCode();
            if ("03".equals(iUser.getBelongCompanyTypeDictValue())) {
                companyCode = iUser.getBelongCompanyCodeParent();
            }
            // 根据考试编码和当前登陆人所属公司获取试卷编码
            paperCode = repository.findPaperCodeByExamCode(examCode, companyCode);
        }

        return paperCode;
    }
}
