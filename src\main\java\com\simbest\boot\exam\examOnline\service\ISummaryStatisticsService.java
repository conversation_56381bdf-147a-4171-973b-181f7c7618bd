package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ISystemService;
import com.simbest.boot.exam.examOnline.model.SummaryStatistics;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @title: SummaryStatisticsService
 * @projectName exam
 * @description:
 * @date 2021/6/29  20:52
 */
public interface ISummaryStatisticsService extends ISystemService<SummaryStatistics,String> {

    /**
      *获取所有季度信息
      * <AUTHOR>
      * @date 2021/6/29
      * @param
      */
    Page<SummaryStatistics> ListSummary();
}
