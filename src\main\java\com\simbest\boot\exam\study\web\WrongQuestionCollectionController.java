package com.simbest.boot.exam.study.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.study.model.WrongQuestionCollection;
import com.simbest.boot.exam.study.service.IWrongQuestionCollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用途：WrongQuestionCollection领域对象名称控制器
 */
@Api(description = "WrongQuestionCollectionController", tags = {"学习管理-错题集"})
@Slf4j
@RestController
@RequestMapping(value="/action/study/wrongQuestionCollection")
public class WrongQuestionCollectionController extends LogicController<WrongQuestionCollection, String> {

    private IWrongQuestionCollectionService service;

    @Autowired
    public WrongQuestionCollectionController(IWrongQuestionCollectionService service) {
        super(service);
        this.service = service;
    }

    @ApiOperation(value = "根据人员查询错题集")
    @PostMapping(value = {"/findByUsername" , "/findByUsername/sso" , "/findByUsername/api"})
    public JsonResponse findByUsername(@RequestParam(defaultValue = "PC") String source ,
                                       @RequestParam(required = false) String currentUserCode,
                                       @RequestParam(defaultValue = "1") Integer page ,
                                       @RequestParam(defaultValue = "10") Integer size ,
                                       @RequestBody WrongQuestionCollection model) {
        return service.findByUsername( source , currentUserCode ,  page, size, model);
    }

    @ApiOperation(value = "根据id查询错题信息")
    @PostMapping(value = {"/findQuestionById" , "/findQuestionById/sso" , "/findQuestionById/api"})
    public JsonResponse findQuestionById(@RequestParam String id ) {
        return service.findQuestionById(id);
    }

}