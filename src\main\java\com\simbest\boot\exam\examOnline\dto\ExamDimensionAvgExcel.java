package com.simbest.boot.exam.examOnline.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: 统计洛阳市县分公司各维度满意度平均分统计结果的导出
 * @projectName exam
 * @description:
 * @date 2021/6/10  11:25
 */
@Data
public class ExamDimensionAvgExcel {

    @ExcelVOAttribute(name = "维度", column = "A")
    @Excel(name =  "维度",width = 50)
    private String department;


    @ExcelVOAttribute(name = "专业水平", column = "B")
    @Excel(name =  "专业水平",width = 50)
    private BigDecimal PL;

    @ExcelVOAttribute(name = "问题解决", column = "C")
    @Excel(name =  "问题解决",width = 50)
    private BigDecimal PS;

    @ExcelVOAttribute(name = "及时反馈", column = "D")
    @Excel(name =  "及时反馈",width = 50)
    private BigDecimal TF;

    @ExcelVOAttribute(name = "工作效率", column = "E")
    @Excel(name =  "工作效率",width = 50)
    private BigDecimal WE;

    @ExcelVOAttribute(name = "咨询解答", column = "F")
    @Excel(name =  "咨询解答",width = 50)
    private BigDecimal CA;

    @ExcelVOAttribute(name = "服务意识", column = "G")
    @Excel(name =  "服务意识",width = 50)
    private BigDecimal SC;

    @ExcelVOAttribute(name = "政策指导", column = "H")
    @Excel(name =  "政策指导",width = 50)
    private BigDecimal PG;

    @ExcelVOAttribute(name = "计划性", column = "I")
    @Excel(name =  "计划性",width = 50)
    private BigDecimal PN;

    @ExcelVOAttribute(name = "总体评价", column = "J")
    @Excel(name =  "总体评价",width = 50)
    private BigDecimal OE;

    @ExcelVOAttribute(name = "部门成绩", column = "K")
    @Excel(name =  "部门成绩",width = 50)
    private BigDecimal departmentScore;

}
