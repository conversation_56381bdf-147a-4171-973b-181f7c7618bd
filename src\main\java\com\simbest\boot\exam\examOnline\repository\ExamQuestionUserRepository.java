/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <strong>Title : ExamQuestionUserRepository</strong><br>
 * <strong>Description : 人员试卷题目repository </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface ExamQuestionUserRepository extends LogicRepository<ExamQuestionUser, String> {

    /**
     * 查询当前题目得分
     *
     * @return
     */
    @Query(value = "select round(avg (equ.answer_score),2) as average from us_exam_question_user equ" +
            "                    where equ.question_code =:questionCode " +
            "                    and equ.annual_quarter_code=:annualQuarterCode " +
            "                    and equ.answer_score is not null " +
            "                    and equ.answer_score!=0 " +
            "                    and equ.answer_status=1 ", nativeQuery = true)
    Double findQuestionScore(@Param("questionCode") String questionCode, @Param("annualQuarterCode") String annualQuarterCode);


    /**
     * 查询当前题目得分
     *
     * @return
     */
    @Query(value = "select sum(t1.average) as average" +
            "  from (select round(avg(equ.answer_score) * 0.4, 2) as average" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code = :questionCode" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_score != 0" +
            "           and equ.answer_status = 1" +
            "           and v.departmentName = :orgName" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as average" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code = :questionCode" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_score != 0" +
            "           and equ.answer_status = 1" +
            "           and v.departmentName = :orgName2" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as average" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code = :questionCode" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_score != 0" +
            "           and equ.answer_status = 1" +
            "           and v.departmentName = :orgName3" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as average" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code = :questionCode" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_score != 0" +
            "           and equ.answer_status = 1" +
            "           and v.departmentName not in (:orgName, :orgName2,:orgName3)) t1", nativeQuery = true)
    Double findQuestionScoreProportion(@Param("questionCode") String questionCode, @Param("annualQuarterCode") String annualQuarterCode, @Param("orgName") String orgName, @Param("orgName2") String orgName2,@Param("orgName3") String orgName3);

}
