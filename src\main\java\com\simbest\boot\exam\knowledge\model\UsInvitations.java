/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.model;/**
 * Created by KZH on 2019/10/8 15:55.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:55
 * @desc 考试业务单据
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_invitations")
@ApiModel(value = "邀请表")
public class  UsInvitations extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "W") //主键前缀，此为可选项注解
    private String id;

   @Column(length = 500)
    @ApiModelProperty(value = "用户答题记录表主键id", required = true)
    private String answerRecordId;

   @Column(length = 500)
    @ApiModelProperty(value = "会话ID", name = "sessionId", example = "2019678301426913763328")
    private String sessionId;

   @Column(length = 500)
    @ApiModelProperty(value = "主单据Id", name = "pmInsId", example = "2019678301426913763328")
    private String pmInsId;


    @Column(length = 40)
    @ApiModelProperty(value = "题库编码",name = "questionBankCode",example = "A-001",required = true)
    private String questionBankCode;


    @Column(length = 2000)
    @ApiModelProperty(value = "问题ID", name = "questionId", example = "问题ID")
    private String questionId;

    @Column(length = 2000)
    @ApiModelProperty(value = "问题Code", name = "questionCode", example = "问题Code")
    private String questionCode;



   @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户OA账户", name = "sendUserName", example = "超级管理员")
    private String sendUserName;

   @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户姓名", name = "sendTrueName", example = "超级管理员")
    private String sendTrueName;


   @Column(length = 500)
    @ApiModelProperty(value = "接受邀请的用户OA账户", name = "recUserName", example = "超级管理员")
    private String recUserName;

   @Column(length = 500)
    @ApiModelProperty(value = "接受邀请的用户姓名", name = "recTrueName", example = "超级管理员")
    private String recTrueName;

   @Column(length = 500)
    @ApiModelProperty(value = "邀请状态", name = "status", example = "Pending")
    private String status; //'Pending'：待接收,'ACCEPTED'：已接受,'Expired'：过期  'refuse'：拒绝, 'ONGOING' 继续答题   'FINISHED'

   @Column(length = 500)
    @ApiModelProperty(value = "邀请创建时间", name = "createdAt")
    private String createdAt;

   @Column(length = 500)
    @ApiModelProperty(value = "邀请过期时间", name = "expiresAt")
    private String expiresAt;


   @Column(length = 500)
    @ApiModelProperty(value = "邀请接受时间", name = "acceptedAt")
    private String acceptedAt;

    @Column(length = 255)
    @ApiModelProperty(value = "邀请附带信息", name = "message")
    private String message;

}
