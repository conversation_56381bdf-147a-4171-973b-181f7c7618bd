package com.simbest.boot.exam.appraise.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.appraise.model.AppraiseItem;
import com.simbest.boot.exam.appraise.repository.AppraiseItemRepository;
import com.simbest.boot.exam.appraise.service.IAppraiseItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppraiseItemServiceImpl extends LogicService<AppraiseItem, String> implements IAppraiseItemService {

    private final AppraiseItemRepository repository;

    public AppraiseItemServiceImpl(AppraiseItemRepository repository) {
        super(repository);
        this.repository = repository;
    }

    /**
     * 保存所有
     * @param list list
     * @return 返回 保存后的对象
     */
    @Override
    public List<AppraiseItem> saveAll(List<AppraiseItem> list) {
        return list.stream().map(super::insert)
                .collect(Collectors.toList());
    }

}
