package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.examOnline.dto.ExamEffectDto;
import com.simbest.boot.exam.examOnline.model.ExamRange;
import com.simbest.boot.exam.examOnline.model.ExamSummary;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * 用途：考试信息模块--考试汇总service
 * 作者：gy
 * 时间: 2021-02-01 10:41
 */
public interface IExamSummaryService extends ILogicService<ExamSummary, String> {
    /**
     * 保存考试汇总信息
     */
    ExamSummary saveExamSummary(ExamSummary o);

    /**
     * 获取考试汇总信息详情
     */
    ExamSummary findExamSummaryInfo(String id);

    /**
     * 根据考试编码获取考试汇总信息详情
     */
    Map<String,Object> findExamSummaryInfoByExamCode(String examCode);

    /**
     * 根据考试编码获取考试汇总信息详情
     */
    ExamSummary findExamSummaryByCode(String code);

    /**
     * 查询考试汇总信息列表
     */
    Page<ExamSummary> findExamSummaryList(int page, int size, String direction, String properties, ExamSummary o);

    /**
     * 根据考试编码推发统一待办
     */
    void sendTodo(String examCode);

    void dealTodo(String examCode);

    /**
     * 根据考试编码推发统一待办(定时器)
     */
    void sendTodoTask(String examCode);

    /**
     * 根据考试编码推发待办(定时器)
     */
    void createExamWorkTask(String examCode);

    /**
     * 根据考试编码发送短信催办
     */
    void sendUrgeSms(String examCode);

    /**
     * 根据考试编码发送短信催办(定时器)
     */
    void sendUrgeSmsTask(String examCode);

    /**
     * 获取应用有效token
     */
    Map<String, Object> findAccessToken(String currentUserCode, String source, String appcode);

    /**
     * 根据考试编码推发待办
     */
    void createExamWork(String examCode);

    /**
     * 获取应考人数
     */
    Integer findJoinExamNum(List<ExamRange> rangeList);

    List<ExamSummary> findLeftTree(String examCode);

    /**
      * @desc 根据工作类型查询考试信息
      * <AUTHOR>
      */
    ExamSummary findExamSummaryByWorkType(String workType);

    Map<String, String> createExamSmsWork(String examCode);

    /**
     * 短信小程序校验是否有答题权限功能开发
     * @param currentUserCode   当前登录人
     * @param source            来源 ， 默认都是SMS
     * @param examCode          考试编码
     * @return
     */
    ExamEffectDto findEffectiveExamSms(String currentUserCode, String source, String examCode);

    /**
     * 手机端问卷飘窗展示控制接口
     * @param currentUserCode   当前登录人
     * @param source            来源 ， 默认都是MOBILE
     * @return
     */
    ExamEffectDto findEffectiveExam(String currentUserCode, String source);

    /**
     * 根据试卷编码查询考试信息
     * @param examAppCode   试卷编码
     * @return
     */
    ExamSummary findInfoByExamAppCode(String examAppCode);
}
