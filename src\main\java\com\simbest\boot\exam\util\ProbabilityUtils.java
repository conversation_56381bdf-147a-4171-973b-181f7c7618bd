/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.util;/**
 * Created by KZH on 2019/12/9 15:58.
 */

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.exam.lottery.model.RetJackpot;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-12-09 15:58
 * @desc 概率工具类
 **/
@Slf4j
public class ProbabilityUtils {

    /**
     *
     * @param probabilityPercentage 概率百分比（如概率为30%请传入30）
     * @return 是否中奖
     */
    public static boolean IsHit(double probabilityPercentage) {
        //得到概率的百分比。
        probabilityPercentage = probabilityPercentage / 100;

        return InternalIsHit(probabilityPercentage);
    }

    /**
     *
     * @param probabilityPercentage 概率百分比（如概率为30%请传入30）
     * @return 是否中奖
     */
    public static RetJackpot IsHit2(double probabilityPercentage) {
        RetJackpot retJackpot=new RetJackpot();

        Map<String, Object> map = InternalIsHit2(probabilityPercentage);

        Boolean result = MapUtil.getBool(map, "result");
        Double resultValue = MapUtil.getDouble(map, "resultValue");

        retJackpot.setIsJackpot(result);

        if (result){
            if( Double.doubleToLongBits(resultValue)<Double.doubleToLongBits(0.020)){
                retJackpot.setGetPrize("一等奖");
            }
            else if(Double.doubleToLongBits(resultValue)<Double.doubleToLongBits(0.060)){
                retJackpot.setGetPrize("二等奖");
            }
            else {
                retJackpot.setGetPrize("三等奖");
            }
        }
        else {
            retJackpot.setGetPrize("未中奖");
        }

        return retJackpot;
    }

    /**
     *
     * @param probabilityPercentage 概率百分比（如概率为30%请传入30）
     * @return 是否中奖
     */
    private static Map<String,Object> InternalIsHit2(double probabilityPercentage) {
        //得到概率的百分比。
        probabilityPercentage = probabilityPercentage / 100;

        Map<String,Object> retMap= Maps.newHashMap();

        if (probabilityPercentage < 0 || probabilityPercentage >= 1){
            log.warn("随机数必须是一个介于 0.0 和 1.0 之间的数。");
            assert false;
        }
        double significand = significand(Math.random(), 2);

        retMap.put("result",probabilityPercentage>significand);
        retMap.put("resultValue",significand);

        return retMap;
    }

    private static boolean InternalIsHit(double probabilityPercentage){

        if (probabilityPercentage < 0 || probabilityPercentage >= 1){
            log.warn("随机数必须是一个介于 0.0 和 1.0 之间的数。");
            assert false;
        }
        double significand = significand(Math.random(), 15);

        return probabilityPercentage>significand;

    }

    /**保留几位有效数字
     * @param oldDouble
     * @param scale
     * @return
     */
    private static double significand(double oldDouble, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException(
                    "scale指定的精度为非负值");
        }
        /**
         * RoundingMode：舍入模式
         * UP：远离零方向舍入的舍入模式；
         * DOWN：向零方向舍入的舍入模式；
         * CEILING： 向正无限大方向舍入的舍入模式；
         * FLOOR：向负无限大方向舍入的舍入模式；
         * HALF_DOWN：向最接近数字方向舍入的舍入模式，如果与两个相邻数字的距离相等，则向下舍入；
         * HALF_UP：向最接近数字方向舍入的舍入模式，如果与两个相邻数字的距离相等，则向上舍入；
         * HALF_EVEN：向最接近数字方向舍入的舍入模式，如果与两个相邻数字的距离相等，则向相邻的偶数舍入;(在重复进行一系列计算时,此舍入模式可以将累加错误减到最小)
         * UNNECESSARY：用于断言请求的操作具有精确结果的舍入模式，因此不需要舍入。
         */
        RoundingMode rMode;
        //rMode=RoundingMode.FLOOR;
        //下面这种情况，其实和FLOOR一样的。
        if(oldDouble>0){
            rMode=RoundingMode.DOWN;
        }else{
            rMode=RoundingMode.UP;
        }
        //此处的scale表示的是，几位有效位数
        BigDecimal b = new BigDecimal(Double.toString(oldDouble),new MathContext(scale,rMode));
        return b.doubleValue();
    }

    /**
     * 获取百分比
     * @param number1 数字1
     * @param number2 数字2
     * @return 百分比
     */
    private static double GetPercentage(double number1, double number2){

        return (number1 / number2) * 100;
    }
}
