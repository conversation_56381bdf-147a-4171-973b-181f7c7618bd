package com.simbest.boot.exam.briefDistribution.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 请求工单实体类
 *
 * @Auther: ztz
 * @Date: 2021/3/31 16:24
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_apply_form")
@ApiModel(value = "请求工单实体类")
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApplyForm extends WfFormModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UAF") //主键前缀，此为可选项注解
    private String id;

    @ApiModelProperty(value = "父级id")
    @Column(length = 40)
    private String parentId;

    @ApiModelProperty(value = "工单id")
    @Column(length = 200)
    private String pmInsId;

    @ApiModelProperty(value = "主题")
    @Column(length = 200)
    private String title;//主题

    @Lob
    @ApiModelProperty(value = "简报内容")
    private String content;//简报内容

    @Column(length = 4)
    private String status;//状态（0立即发送、1已发送、2未发送）

    @Column(length = 4)
    private String type;//工单类型 P 简报派发


    @Column(length = 100)
    private String sendType;//发送人员类型（1 组织 2选择 ）

    @Column(length = 100)
    @ApiModelProperty(value = "发送人OA账号")
    private String sender;//发送人

    @Column(length = 100)
    @ApiModelProperty(value = "发送人真实姓名")
    private String senderTrueName;//发送人

    @Column(length = 100)
    @ApiModelProperty(value = "待办id")
    private String taskId;//待办id

    @Column(length = 200)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间", required = true)
    private LocalDateTime sendTime;//结束时间

    @Column(length = 4000)
    private String receiveUsers;//接收人OA账号(多个用,拼接)

    @Column(length = 1000)
    @ApiModelProperty(value = "推送失败人员OA账号")
    private String pushFailStr;//推送失败人员账号


    @Transient
    List<UserInfo> userList;

    @Transient
    List<ApplyForm> childres;

    @Transient
    private Integer receiveNumber;//接收人数

    @Transient
    private Integer viewedNumber;//已阅人数

    @Transient
    private Integer taskStatus;//已阅人数 10  12

}
