/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsInvitations;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
public interface IUsInvitationsService extends ILogicService<UsInvitations,String> {

    /**
     * 根据邀请人和被邀请人查询剩余邀请次数
     * A邀请B,只要B接受，则A与B都将少一次邀请次数
     * 也可用来查询指定日期指定人员剩余多少邀请次数
     * @param currentDay
     * @param sendUserName
     * @return
     */
    List<UsInvitations> countExamWorkByTask(String source, String username,String currentDay,  String sendUserName);

    /**
     * A邀请B
     *
     * @param sendUserName 发送者ID
     * @param recUserName 接收者ID
     * @return 邀请对象
     */
    JsonResponse sendInvitation(String source, String username,String sendUserName, String recUserName);

    /**
     * B接受邀请
     * @param invitationId
     * @return
     */
    JsonResponse acceptInvitation(String source, String username,String invitationId);

    /**
     * B拒绝邀请
     * @param invitationId
     * @return
     */
    JsonResponse refauseInvitation(String source, String username,String invitationId);



    /**
     * 查询当日所有预约信息
     * 每天12点取消当日所有预约信息
     * @param currentDay
     * @return
     */

    List<UsInvitations> cancleInvitations(String source, String username,String currentDay);

    /**
     * 查询已邀请接受但是未答完题的情况
     * @param currentDay
     * @return
     */
    List<UsInvitations> cancleInvitationsAcc(String source, String username,String currentDay);

    /**
     * 核销待办--自动任务
     * @return
     */
     JsonResponse expirePendingTasks(String source, String username,String yesterday);

    /**
     * 邀请过期处理;
     * @return
     */
    JsonResponse expireInvitations(String source, String username);

    /**
     * 查询指定日期指定状态的数邀请信息
     * @param currentDay
     * @param status
     * @return
     */
    List<UsInvitations> findByStatus(String source, String username,String currentDay, String status);

    /**
     * 查询被邀请人列表信息
     * @param currentDay
     * @param recUserName
     * @return
     */
    List<UsInvitations> InvitedTask(String currentDay,String recUserName);

    /**
     * 查询超出指定时间内的邀请信息
     * @return
     */
    List<UsInvitations> findTimeOutByStatus();

    /**
     *
     * @param source
     * @param currentUserCode
     * @param sendUserName
     * @return
     */
    JsonResponse getInvitationList(String source, String currentUserCode);

    List<UsInvitations> findByPmInsId(String pmInsId);
}
