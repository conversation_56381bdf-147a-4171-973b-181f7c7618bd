package com.simbest.boot.exam.briefDistribution.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.PredicateBuilder;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.AuthoritiesConstants;
import com.simbest.boot.exam.briefDistribution.model.ApplyForm;
import com.simbest.boot.exam.briefDistribution.model.UserInfo;
import com.simbest.boot.exam.briefDistribution.repository.ApplyFormRepository;
import com.simbest.boot.exam.briefDistribution.service.ApplyFormService;
import com.simbest.boot.exam.briefDistribution.service.ISyncCommonService;
import com.simbest.boot.exam.briefDistribution.service.UserInfoService;
import com.simbest.boot.exam.flow.model.SysTaskInfo;
import com.simbest.boot.exam.flow.service.ISysTaskInfoService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.util.PaginationHelp;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ApplyFormServiceImpl extends LogicService<ApplyForm, String> implements ApplyFormService {


    private final ApplyFormRepository repository;

    public ApplyFormServiceImpl(ApplyFormRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    PaginationHelp paginationHelp;

    @Autowired
    ISysTaskInfoService sysTaskInfoService;

    @Autowired
    ISyncCommonService syncCommonService;

    @Autowired
    UumsSysUserinfoApi uumsSysUserinfoApi;
    @Autowired
    private RsaEncryptor encryptor;
    @Autowired
    private AppConfig appConfig;

    @Autowired
    UserInfoService userInfoService;
    private static final String GET_DOWNORGUSERA_AND_ROOTORG = "/action/user/user/getDownOrgUserAndRootOrg";//查询人员列表(带父级)

    @Override
    public Page<ApplyForm> findListByPage(int page, int size, String direction, String properties, Map<String, Object> params) {
        String title = MapUtil.getStr(params, "title");//标题
        String status = MapUtil.getStr(params, "status");//标题
        String startTime = MapUtil.getStr(params, "startTime");//标题
        String endTime = MapUtil.getStr(params, "endTime");//标题
        Pageable pageable = paginationHelp.getPageable(page, size, direction, properties);
        PredicateBuilder<ApplyForm> predicateBuilder = Specifications.<ApplyForm>and()
                .eq("enabled", Boolean.TRUE)
                .eq("type",Constants.PROCESS_TYPE_P)
                .eq("parentId", null)
                .like(StrUtil.isNotEmpty(title), "title", "%" + title + "%")
                .eq(StrUtil.isNotEmpty(status), "status", status);
        if (StringUtils.isNotEmpty(startTime)&&StringUtils.isNotEmpty(endTime)){
            predicateBuilder.between("sendTime", LocalDateTime.parse(startTime),  LocalDateTime.parse(endTime));
        }
        Specification<ApplyForm> build = predicateBuilder.build();
        Page<ApplyForm> managerUserinfoPage = repository.findAllActive(build,pageable);
        for (ApplyForm applyForm : managerUserinfoPage.getContent()) {
            String pmInsId = applyForm.getPmInsId();//type (taskToDo 待办 ,myDone 已办,null 全查 )
            List<SysTaskInfo>  receiveUserList= sysTaskInfoService.findByPmInsId(pmInsId, null);//接收人数
            List<SysTaskInfo>  viewedUserList= sysTaskInfoService.findByPmInsId(pmInsId, "myDone");//已阅人数
            applyForm.setReceiveNumber(receiveUserList.size());
            applyForm.setViewedNumber(viewedUserList.size());
        }
        return managerUserinfoPage;
    }

    @Override
    public JsonResponse create(ApplyForm applyForm) {
        try {
            IUser simpleUser = SecurityUtils.getCurrentUser();
            applyForm.setSender(simpleUser.getUsername());
            applyForm.setSenderTrueName(simpleUser.getTruename());
            applyForm.setSendTime(LocalDateTime.now());
            applyForm.setBelongCompanyCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCodeParent() : simpleUser.getBelongCompanyCode() );
            applyForm.setBelongCompanyName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyNameParent() : simpleUser.getBelongCompanyName());
            applyForm.setBelongDepartmentCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCode() : simpleUser.getBelongDepartmentCode());
            applyForm.setBelongDepartmentName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyName() : simpleUser.getBelongDepartmentName());
            applyForm.setBelongOrgCode(simpleUser.getBelongOrgCode());
            applyForm.setBelongOrgName(simpleUser.getBelongOrgName());
            applyForm.setBelongCompanyTypeDictValue(simpleUser.getBelongCompanyTypeDictValue());
            applyForm.setType(Constants.ORDER_TYPE_P);
            this.insert(applyForm);

            if (Constants.JBPF_STATUS_SENDING.equals(applyForm.getStatus())){//状态（0立即发送、1已发送、2未发送、3发送失败）
                //TODO 创建待办
                String pmInsId = Constants.PROCESS_TYPE_P + IdGenerator.idWorker.nextId();
                applyForm.setPmInsId(pmInsId);
                syncCommonService.createTodo(pmInsId,applyForm);
                this.update(applyForm);
            }else {
                if (StrUtil.isNotEmpty(applyForm.getReceiveUsers())){
                    List<String> users = Arrays.asList(applyForm.getReceiveUsers().split(","));
                    for (String username : users) {
                        SimpleUser simpleUser1 = uumsSysUserinfoApi.findByUsername(username, Constants.APP_CODE);
                        UserInfo userInfo = new UserInfo();
                        userInfo.setBusinessId(applyForm.getId());
                        userInfo.setUsername(simpleUser1.getUsername());
                        userInfo.setTruename(simpleUser1.getTruename());
                        userInfo.setBelongCompanyCode(simpleUser1.getBelongCompanyCode());
                        userInfo.setBelongCompanyName(simpleUser1.getBelongCompanyName());
                        userInfo.setBelongCompanyTypeDictValue(simpleUser1.getBelongCompanyTypeDictValue());
                        userInfo.setBelongCompanyTypeDictDesc(simpleUser1.getBelongCompanyTypeDictDesc());
                        userInfo.setBelongDepartmentName(simpleUser1.getBelongDepartmentName());
                        userInfo.setBelongDepartmentCode(simpleUser1.getBelongDepartmentCode());
                        userInfo.setBelongOrgCode(simpleUser1.getBelongOrgCode());
                        userInfo.setBelongOrgName(simpleUser1.getBelongOrgName());
                        userInfo.setPreferredMobile(simpleUser1.getPreferredMobile());
                        userInfo.setTelephoneNumber(simpleUser1.getTelephoneNumber());
                        userInfoService.insert(userInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("--->>> /action/applyForm/create  接口异常，{}",e);
            return JsonResponse.fail("新增失败");
        }
        return JsonResponse.success("新增成功");
    }

    @Override
    public JsonResponse updateInfo(ApplyForm applyForm) {
        try {
            ApplyForm applyFormOld = this.findById(applyForm.getId());
            List<UserInfo> byBusinessId = userInfoService.findByBusinessId(applyForm.getId());
            List<String> ids = byBusinessId.stream().map(userInfo -> userInfo.getId()).collect(Collectors.toList());
            userInfoService.deleteAllByIds(ids);
            if (Constants.JBPF_STATUS_SENDING.equals(applyForm.getStatus())){//状态（0立即发送、1已发送、2未发送、3发送失败）
                //TODO 创建待办
                String pmInsId = Constants.PROCESS_TYPE_P + IdGenerator.idWorker.nextId();
                applyForm.setPmInsId(pmInsId);
                applyForm.setCreator(applyFormOld.getCreator());//处理前端未传旧字段问题
                syncCommonService.createTodo(pmInsId,applyForm);
            }else {
                if (StrUtil.isNotEmpty(applyForm.getReceiveUsers())){
                    List<String> users = Arrays.asList(applyForm.getReceiveUsers().split(","));
                    for (String username : users) {
                        SimpleUser simpleUser = uumsSysUserinfoApi.findByUsername(username, Constants.APP_CODE);
                        UserInfo userInfo = new UserInfo();
                        userInfo.setBusinessId(applyForm.getId());
                        userInfo.setUsername(simpleUser.getUsername());
                        userInfo.setTruename(simpleUser.getTruename());
                        userInfo.setBelongCompanyCode(simpleUser.getBelongCompanyCode());
                        userInfo.setBelongCompanyName(simpleUser.getBelongCompanyName());
                        userInfo.setBelongCompanyTypeDictValue(simpleUser.getBelongCompanyTypeDictValue());
                        userInfo.setBelongCompanyTypeDictDesc(simpleUser.getBelongCompanyTypeDictDesc());
                        userInfo.setBelongDepartmentName(simpleUser.getBelongDepartmentName());
                        userInfo.setBelongDepartmentCode(simpleUser.getBelongDepartmentCode());
                        userInfo.setBelongOrgCode(simpleUser.getBelongOrgCode());
                        userInfo.setBelongOrgName(simpleUser.getBelongOrgName());
                        userInfo.setPreferredMobile(simpleUser.getPreferredMobile());
                        userInfo.setTelephoneNumber(simpleUser.getTelephoneNumber());
                        userInfoService.insert(userInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("--->>> /action/applyForm/updateInfo  接口异常，{}",e);
            return JsonResponse.fail("修改失败");
        }
        return JsonResponse.success("修改成功");
    }

    @Override
    public JsonResponse delete(String id) {
        try {
            this.deleteById(id);
        } catch (Exception e) {
            log.error("--->>> /action/applyForm/delete  接口异常，{}",e);
            return JsonResponse.fail("删除失败");
        }
        return JsonResponse.success("删除成功");
    }

    @Override
    public JsonResponse getById(String applyFormId) {
        ApplyForm applyForm = this.findById(applyFormId);
        List<ApplyForm> applyFormList = repository.findApplyFormsByParentIdAndEnabledIsTrueAndRemovedTimeIsNull(applyFormId);
        applyForm.setChildres(applyFormList);
       List<UserInfo> userInfos= userInfoService.findByBusinessId(applyForm.getId());
        applyForm.setUserList(userInfos);
        return JsonResponse.success(applyForm);
    }

    @Override
    public JsonResponse forward(String applyFormId, String userStr, String sendType) {
        SysTaskInfo sysTaskInfo = sysTaskInfoService.findByTaskId(applyFormId);
        ApplyForm oldApplyForm = this.findById(sysTaskInfo.getBusinessId());
        ApplyForm applyForm = new ApplyForm();
        IUser simpleUser = SecurityUtils.getCurrentUser();
        applyForm.setParentId(applyFormId);
        applyForm.setPmInsId(oldApplyForm.getPmInsId());
        applyForm.setTitle(oldApplyForm.getTitle());
        applyForm.setContent(oldApplyForm.getContent());
        applyForm.setStatus(Constants.JBPF_STATUS_SENDING);
        applyForm.setSender(simpleUser.getUsername());
        applyForm.setSenderTrueName(simpleUser.getTruename());
        applyForm.setSendTime(LocalDateTime.now());
        applyForm.setReceiveUsers(userStr);
        applyForm.setBelongCompanyCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCodeParent() : simpleUser.getBelongCompanyCode() );
        applyForm.setBelongCompanyName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyNameParent() : simpleUser.getBelongCompanyName());
        applyForm.setBelongDepartmentCode(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyCode() : simpleUser.getBelongDepartmentCode());
        applyForm.setBelongDepartmentName(StrUtil.equals(simpleUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? simpleUser.getBelongCompanyName() : simpleUser.getBelongDepartmentName());
        applyForm.setBelongOrgCode(simpleUser.getBelongOrgCode());
        applyForm.setBelongOrgName(simpleUser.getBelongOrgName());
        applyForm.setBelongCompanyTypeDictValue(simpleUser.getBelongCompanyTypeDictValue());
        applyForm.setType(Constants.ORDER_TYPE_P);
        applyForm.setSendType(sendType);
        applyForm.setCreator(simpleUser.getUsername());
        this.insert(applyForm);
        //推送待办
        syncCommonService.createTodo(oldApplyForm.getPmInsId(),applyForm);
        String taskId = sysTaskInfo.getTaskId();

        //核销当前统一待办
        sysTaskInfoService.updateTaskStatus(taskId);
        //结束流程流转
        syncCommonService.completeTask(taskId);
        return JsonResponse.defaultSuccessResponse();
    }

    @Override
    public JsonResponse send(String applyFormId) {
        try {
            ApplyForm applyForm = this.findById(applyFormId);
            String pmInsId = Constants.PROCESS_TYPE_P + IdGenerator.idWorker.nextId();
            syncCommonService.createTodo(pmInsId,applyForm);
        } catch (Exception e) {
            log.error("--->>>/action/applyForm/send ，{}",e);
            return JsonResponse.fail("发送失败！");
        }
        return JsonResponse.success("发送成功");
    }

    @Override
    public JsonResponse findUsersInMsg(int page, int size, String direction, String properties, String applyFormId) {
        ApplyForm applyForm = this.findById(applyFormId);
        //type (taskToDo 待办 ,myDone 已办,null 全查 )
        Page<SysTaskInfo> taskInfoPage = sysTaskInfoService.findByPmInsIdPage(applyForm.getPmInsId(), null, page, size);
        return JsonResponse.success(taskInfoPage);
    }

    @Override
    public JsonResponse findMsgByIdNew(String applyFormId) {
        ApplyForm applyForm = this.findById(applyFormId);
        String sendTime = applyForm.getSendTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("title",applyForm.getTitle());//标题
        resultMap.put("sendTime",sendTime);//标题
        List<SysTaskInfo>  sendUserList= sysTaskInfoService.findByPmInsId(applyForm.getPmInsId(), null);//发送人数
        List<SysTaskInfo>  viewedUserList= sysTaskInfoService.findByPmInsId(applyForm.getPmInsId(), "myDone");//已阅人数
        List<SysTaskInfo>  taskToDoUserList= sysTaskInfoService.findByPmInsId(applyForm.getPmInsId(), "taskToDo");//已阅人数
        resultMap.put("sendTotal",sendUserList.size());//发送总数量
        resultMap.put("viewedTotal",viewedUserList.size());//已阅总数量
        resultMap.put("todoTotal",taskToDoUserList.size());//待阅总数量
        return JsonResponse.success(resultMap);
    }

    @Override
    public JsonResponse getOrgRangePersonData(String orgCode) {

        if (StringUtils.isEmpty(orgCode)){
            IUser currentUser = SecurityUtils.getCurrentUser();
            orgCode=currentUser.getBelongOrgCode();
        }
        String ssoUrl = "/sso?loginuser=" + encryptor.encrypt("hadmin") + "&" + AuthoritiesConstants.SSO_API_APP_CODE + "=" + Constants.APP_CODE+"&orgCode="+orgCode;
        String url = appConfig.getUumsAddress() + GET_DOWNORGUSERA_AND_ROOTORG + ssoUrl;//查询全部部门列表URL
        JsonResponse jsonResponse = HttpClient.textBody(url)
//                .param("orgCode", orgCode)
                .asBean(JsonResponse.class);
        return jsonResponse;
    }

    @Override
    public JsonResponse getFormDetail(String id , String source) {
        ApplyForm applyForm = new ApplyForm();
        SysTaskInfo sysTaskInfo = sysTaskInfoService.findById(id);
        if (sysTaskInfo==null){
            return JsonResponse.fail("未查询到工单信息");
        }
        String businessId = sysTaskInfo.getBusinessId();
        ApplyForm locationForm = this.findById(businessId);
        if (locationForm==null){
            return JsonResponse.fail("未查询到主数据");
        }
        if (StrUtil.equals(source , Constants.SOURCE_M)) {
            BeanUtils.copyProperties(locationForm , applyForm);
            applyForm.setContent(applyForm.getContent().replace("http://************1:8088/exam" , "https://m.ha.chinamobile.com:8001/exam"));
        } else {
            applyForm = locationForm ;
        }
        List<ApplyForm> applyFormList = repository.findApplyFormsByParentIdAndEnabledIsTrueAndRemovedTimeIsNull(applyForm.getId());
        applyForm.setChildres(applyFormList);
        List<UserInfo> userInfos= userInfoService.findByBusinessId(applyForm.getId());
        applyForm.setUserList(userInfos);
        applyForm.setTaskId(sysTaskInfo.getTaskId());
        applyForm.setTaskStatus(sysTaskInfo.getStatus());
        return  JsonResponse.success(applyForm);
    }
}
