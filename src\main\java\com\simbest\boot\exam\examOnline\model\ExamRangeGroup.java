package com.simbest.boot.exam.examOnline.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.SystemModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;


/**
 * 用途：考试管理--考试范围模块
 * 当前实体控制添加洛阳市组织下的分公司，分公司下部门人员
 * 作者：sws
 * 时间: 2021-04-23 10:18
 */

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_range_group")
@ApiModel(value = "考试范围群组信息")
public class ExamRangeGroup extends SystemModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ERG") //主键前缀，此为可选项注解
    private String id;


    @Column(length = 40)
    @ApiModelProperty(value = "群组编号")
    private String groupId;

    @Column(length = 100)
    @ApiModelProperty(value = "群组名称")
    private String groupName;

    @Column(length = 40)
    @ApiModelProperty(value = "此群组采用的试卷编号")
    private String examAppCode;

    @Column(length = 100)
    @ApiModelProperty(value = "此群组采用的试卷名称")
    private String paperName;

    @Column(length = 100)
    @ApiModelProperty(value = "此群组采用的考试编码")
    private String examCode;

    @Column(length = 100)
    @ApiModelProperty(value = "此群组采用的考试名称")
    private String examName;

    @Transient
    @ApiModelProperty(value = "此群组人员个数")
    private Integer countGroupUser;


}
