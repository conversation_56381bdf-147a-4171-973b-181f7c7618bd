/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.web;/**
 * Created by KZH on 2019/10/8 15:17.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:17
 * @desc 题目正确答案
 **/
@Api(description = "题目正确答案")
@Slf4j
@RestController
@RequestMapping(value = "/action/examQuestionAnswer")
public class ExamQuestionAnswerController extends LogicController<ExamQuestionAnswer,String> {

    private IExamQuestionAnswerService iExamQuestionAnswerService;

    @Autowired
    public ExamQuestionAnswerController(IExamQuestionAnswerService service){
        super(service);
        this.iExamQuestionAnswerService=service;
    }

    @ApiOperation(value = "获取到正确答案带分页", notes = "获取到正确答案带分页")
    @ApiImplicitParams({ //
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称displayOrder）", dataType = "String", //
                    paramType = "query") //
    })
    @PostMapping(value = {"/findAllByQuestionCodePage","sso/findAllByQuestionCodePage"})
    public JsonResponse findAllByQuestionCodePage(@RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int size,
                                    @RequestParam(required = false) String direction,
                                    @RequestParam(required = false) String properties,
                                    @RequestParam(required = false) String questionCode) {
        Pageable pageable = iExamQuestionAnswerService.getPageable(page, size, direction, properties);

        return  iExamQuestionAnswerService.findAllByQuestionCodePage(questionCode,pageable);

    }
}
