/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamUser;
import com.simbest.boot.exam.examOnline.service.IExamUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <strong>Title : ExamUserController</strong><br>
 * <strong>Description : 试卷人员Controller </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Api
@RestController
@RequestMapping(value = "action/examuser")
public class ExamUserController extends LogicController<ExamUser,String> {
    private IExamUserService service;

    @Autowired
    public ExamUserController(IExamUserService service){
        super(service);
        this.service=service;

    }

    @ApiOperation(value = "判断用户是否有权限答题", notes = "判断人员是否有权限答题")
    @PostMapping(value = {"/judgeUserWhetherHavePermission","sso/judgeUserWhetherHavePermission"})
    public JsonResponse judgeUserWhetherHavePermission() {

        return  service.judgeUserWhetherHavePermission();
    }

}
