package com.simbest.boot.exam.util;

import com.mzlion.easyokhttp.HttpClient;
import com.mzlion.easyokhttp.request.PostRequest;
import com.mzlion.easyokhttp.request.TextBodyRequest;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.AuthoritiesConstants;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 请求uums接口的工具类
 */
@Component
@Slf4j
public class UumsUtil {

    @Autowired
    private AppConfig config;

    @Autowired
    private RsaEncryptor encryptor;

    /**
     * 封装请求uums sso接口,表单方式提交数据,不需要appid和loginuser参数，方法里会自动添加
     *
     * @param url      主数据接口地址，/action开始的路径，比如：/action/user/user/findById/sso
     * @param parames  map的value只能基础数据类型和字符串，不能是列表或map.参数里只需要业务数据，
     * @param username 请求的用户名,一版为当前登录人，用于向uums请求接口认证
     * @return
     */
    public JsonResponse post(String url, Map<String, Object> parames, String username) {
        PostRequest post = HttpClient.post(config.getUumsAddress() + url);
        parames.put(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username));
        parames.put(AuthoritiesConstants.SSO_API_APP_CODE, Constants.APP_CODE);
        for (String key : parames.keySet()) {
            if (null != parames.get(key)) {
                post.param(key, parames.get(key).toString());
            }
        }
        return post.asBean(JsonResponse.class);
    }

    /**
     * json格式请求,不需要appid和loginuser参数，方法里会自动添加
     *
     * @param url        /action开始的路径，比如：/action/user/user/findById/sso
     * @param urlParames 要后缀到url后面的参数，如无可为空，分页参数放在这
     * @param obj        转换成json格式发送的参数
     * @param username   请求的用户名,一版为当前登录人，用于向uums请求接口认证
     * @return
     */
    public JsonResponse postJson(String url, Map<String, Object> urlParames, Object obj, String username) {
        if (urlParames == null) {
            urlParames = new HashMap<>();
        }
        urlParames.put(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username));
        urlParames.put(AuthoritiesConstants.SSO_API_APP_CODE, Constants.APP_CODE);
        urlParames.putIfAbsent("page", "1");
        urlParames.putIfAbsent("size", "10");
        urlParames.putIfAbsent("direction", "asc");
        urlParames.putIfAbsent("properties", "");

        //拼接url上的参数
        StringBuffer str = new StringBuffer("?");
        for (String key : urlParames.keySet()) {
            if (null != urlParames.get(key)) {
                str.append(key).append("=").append(urlParames.get(key).toString()).append("&");
            }
        }
        str.append("1=1");

        TextBodyRequest textBodyRequest = HttpClient.textBody(config.getUumsAddress() + url + str.toString());
        String json0 = "";
        if (obj != null) {
            json0 = JacksonUtils.obj2json(obj);
        }
        return textBodyRequest.json(json0).asBean(JsonResponse.class);
    }
}
