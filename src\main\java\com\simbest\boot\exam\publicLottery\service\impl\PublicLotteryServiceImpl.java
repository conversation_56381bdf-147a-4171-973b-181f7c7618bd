package com.simbest.boot.exam.publicLottery.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.component.distributed.lock.DistributedRedisLock;
import com.simbest.boot.exam.publicLottery.model.PublicLottery;
import com.simbest.boot.exam.publicLottery.model.PublicLotteryPerson;
import com.simbest.boot.exam.publicLottery.repository.PublicLotteryRepository;
import com.simbest.boot.exam.publicLottery.service.IPublicLotteryPersonService;
import com.simbest.boot.exam.publicLottery.service.IPublicLotteryService;
import com.simbest.boot.exam.test.util.MyUtils;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.redis.RedisUtil;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class PublicLotteryServiceImpl extends LogicService<PublicLottery, String> implements IPublicLotteryService {
    private final IPublicLotteryPersonService publicLotteryPersonServiceImpl;
    private final UumsSysUserinfoApi uumsSysUserinfoApi;
    public PublicLotteryRepository repository;

    public PublicLotteryServiceImpl(PublicLotteryRepository repository, PublicLotteryPersonServiceImpl publicLotteryPersonServiceImpl, UumsSysUserinfoApi uumsSysUserinfoApi) {
        super(repository);
        this.repository = repository;
        this.publicLotteryPersonServiceImpl = publicLotteryPersonServiceImpl;
        this.uumsSysUserinfoApi = uumsSysUserinfoApi;
    }

    private static final String ERROR_MSG = "服务繁忙，请重试！";
    private static final String WAIT_MSG = "业务繁忙，请等待！";
    private static final String NO_PERMISSION_MSG = "没有权限，请重试！";
    private static final String NO_NUMBER_MSG = "所有号码已分配完毕！";

    private static final String REDIS_NO_NUMBER_KEY = "lotteryForUser:lock:noNumber";
    private static final String REDIS_NO_NUMBER_KEY_VALUE = "no-number";
    private static final String REDIS_LOTTERY_KEY = "drawLottery:username:";


    /**
     * 随机洗牌算法 随机抽取号码
     *
     * @return 抽取到的号码
     */
    private Integer shuffleNumbers(List<Integer> numberPool) {
        Collections.shuffle(numberPool);
        int randomInt = RandomUtil.randomInt(numberPool.size());
        Integer number = numberPool.get(randomInt);
        log.info("随机抽取下标：{}，抽取号码：{}", randomInt, number);
        return number;
    }

    /**
     * 开始摇号
     */
    private PublicLottery lotteryForUser() {
        // 查询未抽取的号码
        List<PublicLottery> noPage = super.findAllNoPage((root, criteriaQuery, criteriaBuilder) -> criteriaBuilder.isNull(root.get("username")));
        List<Integer> numberPool = noPage.stream().map(PublicLottery::getNum).collect(Collectors.toList());
        if (numberPool.isEmpty()) {
            RedisUtil.setIfAbsent(REDIS_NO_NUMBER_KEY, REDIS_NO_NUMBER_KEY_VALUE);
            throw new IllegalStateException(NO_NUMBER_MSG);
        }

        // 随机洗牌 抽取号码
        Integer number = this.shuffleNumbers(numberPool);
        PublicLottery lottery = noPage.stream().filter(item -> item.getNum().equals(number)).findFirst().get();
        lottery.belongInfoSet();
        return lottery;
    }

    /**
     * 检查权限
     */
    private void checkPermission() {
        String username = SecurityUtils.getCurrentUserName();
        PublicLotteryPerson person = publicLotteryPersonServiceImpl.findOne(Specifications.<PublicLotteryPerson>and()
                .eq("username", username).build());
        if (Objects.isNull(person)) {
            throw new IllegalStateException(NO_PERMISSION_MSG);
        }
    }

    /**
     * 处理公开摇号请求
     */
    @Retryable(exclude = {IllegalStateException.class}, maxAttempts = 3, backoff = @Backoff(delay = 2000L, multiplier = 0))
    @Override
    public PublicLottery drawLottery() {
        // -----------------------优先过滤一部分无效请求 start------------------------------
        // 检查权限
        this.checkPermission();

        // 查询是否已摇过号
        PublicLottery publicLottery0 = this.getLotteryForCache();
        if (publicLottery0 != null) {
            log.info("用户：{}已摇过号，摇号结果：{}", SecurityUtils.getCurrentUserName(), publicLottery0);
            return publicLottery0;
        }

        // 号码分配完毕直接返回
        if (Objects.equals(RedisUtil.get(REDIS_NO_NUMBER_KEY), REDIS_NO_NUMBER_KEY_VALUE)) {
            MyUtils.LogTool.saveLog(Constants.SOURCE_P, SecurityUtils.getCurrentUserName(), "drawLottery", null, null, NO_NUMBER_MSG);
            throw new IllegalStateException(NO_NUMBER_MSG);
        }

        // -----------------------优先过滤一部分无效请求 end  ------------------------------

        String key = "lottery:lock:";
        PublicLottery lottery = null;
        PublicLottery target = null;
        try {
            // 使用分布式锁控制并发
            DistributedRedisLock.lock(key, 3000);
            log.info("用户：{}点击摇号", SecurityUtils.getCurrentUserName());

            // 检查权限
            this.checkPermission();

            // 查询是否已摇过号
            PublicLottery publicLottery = this.getLotteryForCache();
            if (publicLottery != null) {
                log.info("用户：{}已摇过号，摇号结果：{}", SecurityUtils.getCurrentUserName(), publicLottery);
                return publicLottery;
            }

            // 开始摇号
            lottery = this.lotteryForUser();
            log.info("用户：{}摇号结果：{}", SecurityUtils.getCurrentUserName(), lottery);

            // 更新数据库
            target = super.update(lottery);
            log.info("用户：{}摇号持久化结果：{}", SecurityUtils.getCurrentUserName(), target);
            MyUtils.LogTool.saveLog(Constants.SOURCE_P, SecurityUtils.getCurrentUserName(), "drawLottery", null, target.toString(), null);

            // 存储在redis中 方便查询
            RedisUtil.setBean(REDIS_LOTTERY_KEY + SecurityUtils.getCurrentUserName(), target, 60 * 60 * 24 * 3);

            return target;
        } catch (IllegalStateException e) {
            log.error("用户：{}摇号异常", SecurityUtils.getCurrentUserName(), e);
            MyUtils.LogTool.saveLog(Constants.SOURCE_P, SecurityUtils.getCurrentUserName(), "drawLottery", null, null, e.getMessage());
            throw e;
        } catch (Exception e) {
            MyUtils.LogTool.saveLog(Constants.SOURCE_P, SecurityUtils.getCurrentUserName(), "drawLottery", null, null, e.getMessage());

            // 摇号成功，但是操作数据库失败
            if (Objects.nonNull(lottery) && Objects.isNull(target)) {
                log.error("用户：{}摇号成功，但是操作数据库失败，摇号结果：{}", SecurityUtils.getCurrentUserName(), lottery, e);
            } else {
                log.error("用户：{}摇号异常", SecurityUtils.getCurrentUserName(), e);
            }

            throw new RuntimeException(ERROR_MSG);
        } finally {
            // 释放分布式锁
            DistributedRedisLock.unlock(key);
        }
    }

    /**
     * 查询个人信息 优先缓存
     */
    public PublicLottery getLotteryForCache() {
        String json = RedisUtil.get(REDIS_LOTTERY_KEY + SecurityUtils.getCurrentUserName());
        if (Objects.nonNull(json)) {
            return JacksonUtils.json2obj(json, PublicLottery.class);
        } else {
            return this.getLottery();
        }
    }

    /**
     * 查询个人信息
     */
    @Override
    public PublicLottery getLottery() {
        try {
            return super.findOne(Specifications.<PublicLottery>and().eq("username", SecurityUtils.getCurrentUserName()).build());
        } catch (Exception e) {
            log.error("用户：{}查询个人信息异常", SecurityUtils.getCurrentUserName(), e);
            throw new RuntimeException(ERROR_MSG);
        }
    }

    /**
     * 摇号结果公示
     */
    @Override
    public List<PublicLottery> getLotteryList() {
        try {
            return super.findAllNoPage(Sort.by(Sort.Direction.ASC, "num"));
        } catch (Exception e) {
            log.error("用户：{}查询摇号结果公示异常", SecurityUtils.getCurrentUserName(), e);
            throw new RuntimeException(ERROR_MSG);
        }
    }

    /**
     * 重置摇号池
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetLottery() {
        RedisUtil.delete(REDIS_NO_NUMBER_KEY);
        Set<String> keys = RedisUtil.keys(REDIS_LOTTERY_KEY + "*");
        log.info("keys：{}", keys);
        keys.stream().map(v -> v.replaceAll("cache:key:exam:", "")).forEach(RedisUtil::delete);

        repository.myDeleteAll();

        IntStream.rangeClosed(1, 500).forEach(v -> {
            PublicLottery lottery = new PublicLottery();
            lottery.setNum(v);
            super.insert(lottery);
        });
    }

    /**
     * 添加摇号人员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetLotteryPerSon(String username, boolean allDelete) {
        if (allDelete) {
            publicLotteryPersonServiceImpl.deleteAll();
        }

        Arrays.stream(username.split(",")).parallel().forEach(v -> {
            try {
                PublicLotteryPerson person = new PublicLotteryPerson();
                SimpleUser user = uumsSysUserinfoApi.findByUsernameFromCurrent(v, Constants.APP_CODE);
                person.belongInfoSet(user);
                publicLotteryPersonServiceImpl.insert(person);
            } catch (Exception e) {
                Exceptions.printException(e);
                log.error(v);
            }
        });
    }

}
