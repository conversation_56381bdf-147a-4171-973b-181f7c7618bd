/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.web;/**
 * Created by KZH on 2019/10/8 16:09.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.exam.knowledge.model.UsInvitations;
import com.simbest.boot.exam.knowledge.model.UsUserScore;
import com.simbest.boot.exam.knowledge.service.IUsInvitationsService;
import com.simbest.boot.exam.knowledge.service.IUsUserScoreService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.DateUtil;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.security.LoginUtils;
import io.swagger.annotations.Api;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.simbest.boot.base.web.response.JsonResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:09
 * @desc
 **/
@Api(description = "邀请信息", tags = {"邀请信息相关处理控制器"})
@Slf4j
@RestController
@RequestMapping(value = "/action/usInvitations")
public class UsInvitationsController extends LogicController<UsInvitations, String> {

    private IUsInvitationsService usInvitationsService;

    @Autowired
    public UsInvitationsController(IUsInvitationsService usInvitationsService) {
        super(usInvitationsService);
        this.usInvitationsService = usInvitationsService;
    }

    @Autowired
    private LoginUtils loginUtils;
    @Autowired
    SysDictValueService sysDictValueService;
    /**
     * A邀请B
     *
     * @param sendUserName 发送者ID
     * @param recUserName 接收者ID
     * @return 邀请对象
     */
    @PostMapping(value = {"/sendInvitation", "/api/sendInvitation", "/sendInvitation/sso",})
    public JsonResponse sendInvitation(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                       @RequestParam(value = "currentUserCode",required = false) String currentUserCode,
                                       @RequestParam  String sendUserName,
                                       @RequestParam String recUserName) {
       // return usInvitationsService.sendInvitation(source,currentUserCode,sendUserName, recUserName);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }

    /**
     * B接受邀请
     * @param invitationId 邀请ID
     * @return
     */
    @PostMapping(value = {"/acceptInvitation", "/api/acceptInvitation", "/acceptInvitation/sso",})
    public JsonResponse acceptInvitation(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                         @RequestParam(value = "currentUserCode",required = false) String currentUserCode ,
                                         @RequestParam String invitationId) {
       // return usInvitationsService.acceptInvitation(source,currentUserCode,invitationId);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }

    /**
     * B拒绝邀请
     * @param invitationId 邀请ID
     * @return
     */
    @PostMapping(value = {"/refauseInvitation", "/api/refauseInvitation", "/refauseInvitation/sso",})
    public JsonResponse refauseInvitation(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                         @RequestParam(value = "currentUserCode",required = false) String currentUserCode ,
                                         @RequestParam String invitationId) {
     //   return usInvitationsService.refauseInvitation(source,currentUserCode,invitationId);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }


    /**
     * 判断人人对战答题次数
     * @param currentDay
     * @param recUserName
     * @param sendUserName
     * @return
     */
    @PostMapping(value = {"/countExamWorkByTask", "/api/countExamWorkByTask", "/countExamWorkByTask/sso",})
    public JsonResponse countExamWorkByTask(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                            @RequestParam(value = "currentUserCode",required = false) String currentUserCode ,
                                            @RequestParam(required = false)  String currentDay,
                                            @RequestParam(required = false)  String recUserName,
                                            @RequestParam String sendUserName) {
//        if (StringUtils.isEmpty(currentDay) ){
//            currentDay= DateUtil.getCurrentStr();
//        }
//        Integer everyoneQuestionCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneQuestionCount").get(0).getValue());//人人答题数量
//        Integer everyoneAnswerCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneAnswerCount").get(0).getValue());//人人答题次数
//        Integer everyoneTimeOut =Integer.valueOf(sysDictValueService.findByDictType("everyoneTimeOut").get(0).getValue());//人人答题超时时间
//
//        List<UsInvitations> sendList = usInvitationsService.countExamWorkByTask(source,currentUserCode,currentDay, sendUserName);
//        if(sendList.size()>everyoneAnswerCount){
//            return JsonResponse.fail("当日邀请次数已用完，请明日尝试邀请");
//        }
//        List<UsInvitations> reciveList = usInvitationsService.countExamWorkByTask(source,currentUserCode,currentDay, recUserName);
//        if(reciveList.size()>everyoneAnswerCount){
//            return JsonResponse.fail("选择对战人员被邀请次数已用完，请明日尝试邀请或切换其他人员进行邀请");
//        }
//        if(StringUtil.isNotEmpty(recUserName)&&recUserName.equals(sendUserName)){
//            return JsonResponse.fail("请勿邀请自己");
//        }
//        Map<String,Object> map=new HashMap<>();
//        if(StringUtil.isNotEmpty(recUserName)){
//            map.put("everyoneAnswerCount",everyoneAnswerCount);//总答题次数
//            map.put("answerdCount",reciveList.size());//已答题次数
//            map.put("unAnswerdCount",everyoneAnswerCount-reciveList.size());//未答题次数
//        }else{
//            map.put("everyoneAnswerCount",everyoneAnswerCount);//总答题次数
//            map.put("answerdCount",sendList.size());//已答题次数
//            map.put("unAnswerdCount",everyoneAnswerCount-sendList.size());//未答题次数
//        }
      //  return JsonResponse.success(map);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }


    /**
     * 夜间核销待办任务
     * 核销包含两部门
     * 1、核销统一待办、待办状态变更
     * 2、当日所有邀请信息变更为过期
     */
    @PostMapping(value = {"/expirePendingTasks", "/api/expirePendingTasks", "/expirePendingTasks/sso",})
    public JsonResponse expirePendingTasks(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                           @RequestParam(value = "currentUserCode",required = false) String currentUserCode,
                                           @RequestParam(value = "yesterday",required = false) String yesterday) {
       // usInvitationsService.expirePendingTasks(source,currentUserCode,yesterday);

     //   return JsonResponse.defaultSuccessResponse();
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }

    /**
     * 邀请过期处理
     */
    @PostMapping(value = {"/expireInvitations", "/api/expireInvitations", "/expireInvitations/sso",})
    public JsonResponse expireInvitations(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                          @RequestParam(value = "currentUserCode",required = false) String currentUserCode ) {
       // usInvitationsService.expireInvitations(source,currentUserCode);

     //   return JsonResponse.defaultSuccessResponse();
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }

    /**
     * 查询被邀请人列表信息
     */
    @PostMapping(value = {"/InvitedTask", "/api/InvitedTask", "/InvitedTask/sso",})
    public JsonResponse InvitedTask(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                    @RequestParam(value = "currentUserCode",required = false) String currentUserCode,
                                    @RequestParam(required = false) String currentDay,
                                    @RequestParam String recUserName) {
//        //手机端模拟登陆
//        if (Constants.SOURCE_M.equals(source)){
//            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
//        }
//        if(StringUtil.isBlank(currentDay)){
//            currentDay = DateUtil.getCurrentStr();
//        }
//        List<UsInvitations> usInvitationsList = usInvitationsService.InvitedTask(currentDay, recUserName);
//        return JsonResponse.success(usInvitationsList);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }



    /**
     * A邀请B
     *
     * @return 邀请对象
     */
    @PostMapping(value = {"/getInvitationList", "/api/getInvitationList", "/getInvitationList/sso",})
    public JsonResponse getInvitationList(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                       @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
          //  return usInvitationsService.getInvitationList(source,currentUserCode);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }


}
