package com.simbest.boot.exam.uums.web;


import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.exam.uums.service.ExtendUumsSysRoleService;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.util.PaginationHelp;
import com.simbest.boot.util.encrypt.RsaEncryptor;

import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @title: UumsSysRoleController
 * @projectName exam
 * @description: 考试管理系统角色管理控制器
 * @date 2021/6/28  12:11
 */

@Api(description = "考试管理系统角色管理相关接口",tags = "考试管理系统角色管理相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/roleManage")
public class ExtendUumsSysRoleController {


    @Autowired
    private ExtendUumsSysRoleService extendUumsSysRoleService;

    @ApiOperation(value = "根据登录用户查询其创建的角色信息列表",notes = "根据登录用户查询其创建的角色信息列表")
    @PostMapping(value = {"/findRoleByCreator", "/api/findRoleByCreator", "/findRoleByCreator/sso", "/anonymous/findRoleByCreator"})
    public JsonResponse findRoleNameIsARoleDim( @RequestParam(required = false ,defaultValue = "1") int page,
                                                @RequestParam(required = false,defaultValue = "10") int size ,
                                                @RequestBody(required = false) SimpleRole simpleRole) {
      return   extendUumsSysRoleService.findRoleNameIsARoleDim(page,size,simpleRole.getRoleName(),simpleRole.getRoleCode());
    }

    @ApiOperation(value = "新增角色时的角色code的校验",notes = "新增角色时的角色code的校验")
    @PostMapping(value = {"/isHaveCode", "/api/isHaveCode", "/isHaveCode/sso", "/anonymous/isHaveCode"})
    public JsonResponse isHaveCode(@RequestParam String roleCode){
      return JsonResponse.success(extendUumsSysRoleService.isHaveCode(roleCode));
    }

    @ApiOperation(value = "新增角色信息",notes = "新增角色信息")
    @PostMapping(value = {"/createRole", "/api/createRole", "/createRole/sso", "/anonymous/createRole"})
    public JsonResponse createRole(@RequestBody SimpleRole simpleRole){
        SimpleRole role = extendUumsSysRoleService.createRole(simpleRole);
        if (role==null) return JsonResponse.fail("此角色已存在");
        return JsonResponse.success(role);
    }

    @ApiOperation(value = "根据id查询角色信息",notes = "根据id查询角色信息")
    @PostMapping(value = {"/findById", "/api/findById", "/findById/sso", "/anonymous/findById"})
    public JsonResponse findRoleById(@RequestParam String id){
      return JsonResponse.success(extendUumsSysRoleService.findRoleById(id));
    }

    @ApiOperation(value = "修改角色信息",notes = "修改角色信息")
    @PostMapping(value = {"/updateRoleInfo", "/api/updateRoleInfo", "/updateRoleInfo/sso", "/anonymous/updateRoleInfo"})
    public JsonResponse updateRoleInfo(@RequestBody SimpleRole simpleRole){
      return   JsonResponse.success(extendUumsSysRoleService.updateRoleInfo(simpleRole));
    }

    @ApiOperation(value = "删除角色信息",notes = "删除角色信息")
    @PostMapping(value = {"/deleteRoleByID", "/api/deleteRoleByID", "/deleteRoleByID/sso", "/anonymous/deleteRoleByID"})
    public JsonResponse delRoleInfo(@RequestParam String id){
                if (extendUumsSysRoleService.delRoleInfo(id)){
                    return JsonResponse.success("删除角色成功");
                }
      return   JsonResponse.fail("删除角色失败");
    }

    @ApiOperation(value = "批量删除角色信息",notes = "批量删除角色信息")
    @PostMapping(value = {"/deleteRoleByIDs", "/api/deleteRoleByIDs", "/deleteRoleByIDs/sso", "/anonymous/deleteRoleByIDs"})
    public JsonResponse delRoleInfo(@RequestBody String[] ids){
        if (extendUumsSysRoleService.delRoleInfos(ids)){
            return JsonResponse.success("删除角色成功");
        }
        return   JsonResponse.fail("删除角色失败");
    }

    @ApiOperation(value = "模糊查询出人所在的组织树",notes = "模糊查询出人所在的组织树")
    @PostMapping(value = {"/findDimUserTree", "/api/findDimUserTree", "/findDimUserTree/sso", "/anonymous/findDimUserTree"})
    public JsonResponse findDimUserTree(@RequestBody(required = false) SimpleUser truename){
      return   JsonResponse.success(extendUumsSysRoleService.findDimUserTree(truename));
    }


}
