package com.simbest.boot.exam.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.SystemModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * 用途：考试管理--考试范围模块
 * 当前实体控制每次开展考试涉及的范围、持续的时间、采用的哪套试卷
 * 作者：gy
 * 时间: 2021-02-01 10:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_range")
@ApiModel(value = "考试范围信息")
public class ExamRange extends SystemModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ER") //主键前缀，此为可选项注解
    private String id;

    @ApiModelProperty(value = "考试汇总信息外键")
    @Column(length = 40, nullable = false)
    private String summaryId;

    @ApiModelProperty(value = "组织等级")
    @Column(length = 10, nullable = false)
    private String levelDictValue;
//     * 0 根级 中国移动集团河南有限公司
//     * 1 一级 省公司管理层/分公司管理层
//     * 2 二级 省公司部门或一级中心/分公司部门或县公司
//     * 3 三级 省公司二级中心/分公司二级中心或县公司部门
//     * 4 四级 省公司科室/分公司科室班组或县公司营业厅
//     * 5 五级 待定
//     * 6 六级 待定

    @ApiModelProperty(value = "参与考试公司名称")
    @Column(length = 100, nullable = false)
    private String companyName;

    @ApiModelProperty(value = "参与考试公司编码")
    @Column(length = 40, nullable = false)
    private String companyCode;//TODO 当前考试范围默认以公司为单位，相关部门科室等组织暂时不支持，需要后续调整

    @ApiModelProperty(value = "参与考试组织名称")
    @Column(length = 100, nullable = false)
    private String orgName;

    @ApiModelProperty(value = "参与考试组织编码")
    @Column(length = 40, nullable = false)
    private String orgCode;

    @ApiModelProperty(value = "参与考试父组织名称")
    @Column(length = 100)
    private String parentCompanyName;

    @ApiModelProperty(value = "参与考试父组织编码")
    @Column(length = 40)
    private String parentCompanyCode;

    @ApiModelProperty(value = "采用试卷编码")
    @Column(length = 40)
    private String examPaperCode;



    @ApiModelProperty(value = "试卷名称")
    @Transient
    private String examPaperName;

}
