package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.service.IExamStatisticsAnswersService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title: ExamStatisticsAnswersController
 * @projectName exam
 * @description: 统计当前组织参与考实时情况
 * @date 2021/6/24  12:08
 */
@Api(description = "统计各组织参与考实时情况", tags = {"统计各组织参与考实时情况"})
@Slf4j
@RestController
@RequestMapping("/action/statisticsAnswers")
public class ExamStatisticsAnswersController {

   @Autowired
    private IExamStatisticsAnswersService iExamStatisticsAnswersService;

    @ApiOperation(value = "统计各县公司参与考试的实时情况",notes = "统计各县公司参与考试的实时情况")
    @PostMapping(value = {"/statisLyxf", "sso/statisLyxf", "api/statisLyxf"})
    public JsonResponse statisLyxf(){
      return JsonResponse.success(iExamStatisticsAnswersService.statisticsLYXF(),null)  ;
    }



    @ApiOperation(value = "统计洛阳机关部门人员参与考试的实时情况",notes = "统计洛阳机关部门人员参与考试的实时情况")
    @PostMapping(value = {"/statisLyjg", "sso/statisLyjg", "api/statisLyjg"})
    public JsonResponse statisLyjg(){
        return JsonResponse.success(iExamStatisticsAnswersService.statisticsLYJG(),null)  ;
    }




}
