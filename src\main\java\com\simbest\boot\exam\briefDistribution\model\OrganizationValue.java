package com.simbest.boot.exam.briefDistribution.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.security.IUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 组织人员实体类
 *
 * @Auther: ztz
 * @Date: 2021/3/31 16:24
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_organization_value")
@ApiModel(value = "组织人员实体类")
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrganizationValue extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UOV") //主键前缀，此为可选项注解
    private String id;

    @ApiModelProperty(value = "组织类型")
    @Column(length = 100)
    private String groupType;

    @ApiModelProperty(value = "组织名称")
    @Column(length = 100)
    private String groupName;

    @ApiModelProperty(value = "用户OA账号")
    @Column(length = 100)
    private String userName;

    @ApiModelProperty(value = "用户真实名称")
    @Column(length = 100)
    private String userTrueName;

    @ApiModelProperty(value = "电话")
    @Column(length = 100)
    private String phone;

    @ApiModelProperty(value = "是否启用（1是 0否）")
    @Column(length = 4)
    private String status;

}
