package com.simbest.boot.exam.appraise.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.appraise.model.AppraiseTemplate;
import com.simbest.boot.exam.appraise.service.IAppraiseTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Slf4j
@RestController
@RequestMapping("/action/appraise/template")
public class AppraiseTemplateController extends LogicController<AppraiseTemplate, String> {

    private final IAppraiseTemplateService service;

    public AppraiseTemplateController(IAppraiseTemplateService service) {
        super(service);
        this.service = service;
    }

    /**
     * 保存所有
     */
    @PostMapping(value = {"/saveAll", "sso/saveAll", "api/saveAll"})
    public JsonResponse saveAll(@RequestParam(required = false, defaultValue = "PC") String source,
                                @RequestParam(required = false) String currentUserCode,
                                @RequestBody List<AppraiseTemplate> list) {
        return JsonResponse.success(service.saveAll(list));
    }

}
