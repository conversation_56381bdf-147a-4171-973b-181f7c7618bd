package com.simbest.boot.exam.examOnline.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用途：考试汇总模块--有效考试汇总配置数据传输实体
 * 作者：gy
 * 时间: 2021-02-02 11:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "考试汇总模块-有效考试汇总配置数据传输实体")
public class ExamEffectDto {
    @ApiModelProperty(value = "是否显示")
    private Boolean showFlag;

    @ApiModelProperty(value = "小程序考试跳转页面路径")
    private String appExamUrl;

    @ApiModelProperty(value = "pc端考试跳转页面路径")
    private String pcExamUrl;

    @ApiModelProperty(value = "悬浮窗口图片路径")
    private String appImageUrl;

    @ApiModelProperty(value = "悬浮窗口图片路径")
    private String pcImageUrl;

    @ApiModelProperty(value = "考试编码")
    private String examCode;

    @ApiModelProperty(value = "考试标题")
    private String title;

    @ApiModelProperty(value = "yingyongbianma")
    private String appcode;
}
