/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <strong>Title : ExamQuestionUser</strong><br>
 * <strong>Description : 生成人员试卷关联 </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_question_user")
@ApiModel(value = "生成人员关联试卷")
@Builder
public class ExamQuestionUser extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EXAMQUSER") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "试卷code", name = "bankCode", example = "hadmin", required = true)
    private String bankCode;

    @Column(length = 250)
    @ApiModelProperty(value = "题目类型", name = "questionType", example = "hadmin", required = true)
    private String questionType;

    @Column(length = 250)
    @ApiModelProperty(value = "题目code", name = "questionCode", example = "hadmin", required = true)
    private String questionCode;

    @Column(length = 250)
    @ApiModelProperty(value = "用户选择答案", name = "usernameAnswer", example = "hadmin", required = true)
    private String usernameAnswer;

    /**
     * 为开发洛阳市县分公司对机关各部门进行满意度评价
     * 以及部门间相互满意度评价的项目添加的属性值
     * 所选答案得分  A。非常满意（5分） B。满意（4分） C。一般（3分）D。不满意（2分）E。非常不满意（1分）
     */
    @Column(length = 2)
    @ApiModelProperty(value = "用户所选答案得分", name = "answerScore", example = "1~5分", required = true)
    private Integer answerScore;

    @Column(length = 2)
    @ApiModelProperty(value = "用户答卷状态", name = "answerStatus", example = "1(已提交)/0（仅保存）", required = true)
    private Integer answerStatus;

    @Column(length = 250)
    @ApiModelProperty(value = "考试code", name = "bankCode", example = "hadmin", required = true)
    private String examCode;

    @Column(length = 250)
    @ApiModelProperty(value = "考试所属年度季度信息Code", name = "annualQuarter", example = "2021年秋季度", required = true)
    private String annualQuarterCode;

}
