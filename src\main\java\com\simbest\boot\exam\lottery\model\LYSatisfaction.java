package com.simbest.boot.exam.lottery.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_lysatisfaction_form")
@ApiModel(value = "洛阳移动工会满意度问卷调查抽奖表")
public class LYSatisfaction extends WfFormModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UAF") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "问卷Code", name = "examCode", example = "hadmin", required = true)
    private String examCode;

    @Column(length = 100)
    @ApiModelProperty(value = "职务")
    private String duty;

    @Column(length = 100)
    @ApiModelProperty(value = "oa账号")
    private String username;
    @Column(length = 250)
    @ApiModelProperty(value = "中奖OA姓名")
    private String truename;

    @Column(length = 100)
    @ApiModelProperty(value = "联系电话")
    private String phone;
    @Column(length = 100)
    @ApiModelProperty(value = "email")
    private String email;

    @Column(length = 100)
    @ApiModelProperty(value = "是否中奖1是,0否")
    private String IsWinner;
}
