package com.simbest.boot.exam.examOnline.dto;

import com.simbest.boot.exam.examOnline.model.ExamRangeGroup;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-05-08
 * @desc
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "分组信息模块-分组信息数据传输实体")
public class GroupCountUserDto {

    private Integer groupCountUser;

    private List<ExamRangeGroup>  examRangeGroupList;
}
