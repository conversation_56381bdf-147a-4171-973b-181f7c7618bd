<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>导入excel文件</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">

        var gps=getQueryString();

        var dataArray = [];
        $(function () {
            $('input').css('width',0)
            var id = localStorage.getItem('itemID')
            ajaxgeneral({
                url:"action/examQuestion/findByQuestionBankCode?questionBankCode="+id,
                // data: {
                //     questionBankCode: localStorage.getItem('itemID'),
                    // anonymous: $('input:radio[name="isRealName"]:checked').val(),
                    // commentContent: $("#studyNodesCont").val(),
                    // commentDate: getNow("yyyy-MM-dd hh:mm:ss")
                // },
                contentType: "application/json; charset=utf-8",
                success:function(data){
                   /* //console.log(data)
                    //console.log(data.data.length)*/
                    for (var i = 0;i<data.data.length;i++){
                        $('#item').append('<input type="checkbox"  name="id" value='+data.data[i].questionCode+' >'+data.data[i].questionName+'<br>')
                        //console.log(data.data[i].questionName)
                    }
                }
            });

        });




        window.getchoosedata=function() {
            var data= new Object();
            data.answerListImport = dataArray;
            var idArr = new Array()
            $('#item input:checked').each(function(){
                idArr.push($(this).attr('value'))
            })

            //console.log($('#item input:checked'))
            return {"data": {name:'林佳伟',arr:idArr}, "state": 1};
        }


    </script>
    <style>
        input{
            width: 20px;
            margin:20px;
        }
    </style>
</head>
<body>
<!--scontentType获取数据时设置传参方式，不设置默认为kv  cmd-select获取数据命令配置，不配置自动从列表中取   cmd-insert新增  cmd-update修改-->
<div class="basic tab_table">
    <div id="item"></div>
</div>

</body>
</html>