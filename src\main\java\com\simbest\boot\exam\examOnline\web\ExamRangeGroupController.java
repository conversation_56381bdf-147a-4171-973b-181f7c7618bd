package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.controller.GenericController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamRangeGroup;
import com.simbest.boot.exam.examOnline.service.IExamRangeGroupService;
import com.simbest.boot.exam.examOnline.service.IExamRangeUserInfoService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.SimpleGroup;
import com.simbest.boot.util.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2021-04-23
 * @desc 考试人员范围群组控制器
 **/


@Api(description = "ExamRangeGroupController", tags = {"考试信息模块-考试人员范围群组信息控制器"})
@Slf4j
@RestController
@RequestMapping("/action/rangeGroup")
public class ExamRangeGroupController extends GenericController<ExamRangeGroup, String> {
    private IExamRangeGroupService service;

    @Autowired
    public ExamRangeGroupController(IExamRangeGroupService service) {
        super(service);
        this.service = service;
    }

    @Autowired
    private IExamRangeUserInfoService iExamRangeUserInfoService;


    @ApiOperation(value = "创建群组时校验当前群组编码是否可用", tags = "创建群组时校验当前群组编码是否可用")
    @PostMapping(value = {"/isHaveCode", "/sso/isHaveCode", "/api/isHaveCode"})
    public JsonResponse isHaveCode(@RequestBody(required = false) SimpleGroup simpleGroup) {
        return JsonResponse.success(service.isHaveCode(simpleGroup.getSid()));
    }

    @ApiOperation(value = "创建群组，当是洛阳的梁洁则将分组信息存储到本项目数据库否则存到uums", notes = "创建群组，当是洛阳的梁洁则将分组信息存储到本项目数据库否则存到uums")
    @PostMapping(value = {"/createGroup", "/sso/createGroup", "/api/createGroup"})
    public JsonResponse createGroup(@RequestBody ExamRangeGroup examRangeGroup) {
        //如果是洛阳的梁洁操作则将群组信息保存至本项目数据库
        String currentUserName = SecurityUtils.getCurrentUserName();
        if (currentUserName.equals(Constants.USERNAME_MANAGER1)) {
            return JsonResponse.success(service.saveExamRangeGroup(examRangeGroup));
        } else {
            return JsonResponse.success(service.saveUumsSysGroup(examRangeGroup));
        }

    }

    @ApiOperation(value = "修改群组信息，当是洛阳的梁洁则修改本项目数据库群组信息否则修改uums的群组信息", notes = "当是洛阳的梁洁则修改本项目数据库群组信息否则修改uums的群组信息")
    @PostMapping(value = {"/updateGroup", "/sso/updateGroup", "/api/updateGroup"})
    public JsonResponse updateGroup(@RequestBody ExamRangeGroup examRangeGroup) {
        //如果是洛阳的梁洁操作则将群组信息保存至本项目数据库
        String currentUserName = SecurityUtils.getCurrentUserName();
        if (currentUserName.equals(Constants.USERNAME_MANAGER1)) {
            return JsonResponse.success(service.update(examRangeGroup));
        } else {
            return JsonResponse.success(service.updateUumsSysGroup(examRangeGroup));
        }
    }


    @ApiOperation(value = "根据id删除此条群组信息，并将属于该群组的人员信息删除", notes = "根据id删除此条群组信息，并将属于该群组的人员信息删除")
    @PostMapping(value = {"/deleteExamRangeGroupById", "/sso/deleteExamRangeGroupById", "/api/deleteExamRangeGroupById"})
    public JsonResponse DeleteExamRangeGroupById(@RequestParam String id) {
        String currentUserName = SecurityUtils.getCurrentUserName();
        if (currentUserName.equals(Constants.USERNAME_MANAGER1)) {
            ExamRangeGroup examInfo = service.findById(id);
            String groupId = examInfo.getGroupId();
            iExamRangeUserInfoService.deleteByGroupId(groupId);
            return deleteById(id);
        } else {
            ExamRangeGroup examInfo = service.findById(id);
            String groupId = examInfo.getGroupId();
            //如果是操作洛阳满意度则删除的人员信息为本考试管理项目的数据
            if (groupId.equals("1") || groupId.equals("5")) {  // 1 and 5 为洛阳满意度的考试群组编码
                iExamRangeUserInfoService.deleteByGroupId(groupId);
                this.deleteById(id);
                return JsonResponse.success("删除成功");
            } else {
                Boolean aBoolean = service.delUumsSysGroup(groupId);
                this.deleteById(id);
                if (aBoolean) {
                    return JsonResponse.success("删除成功");
                }
            }

            return JsonResponse.fail("删除失败");
        }
    }

    @ApiOperation(value = "根据考试编码examCode查询群组名称", notes = "根据考试编码examCode查询群组名称")
    @PostMapping(value = {"/findExamDetailAndGroup", "/sso/findExamDetailAndGroup", "/api/findExamDetailAndGroup"})
    public JsonResponse findExamDetailAndGroup(@RequestParam String examCode) {
        try {
            return JsonResponse.success(service.findExamDetailAndGroup(examCode));
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(e.getMessage());
        }


    }

    @ApiOperation(value = "分页查询群组信息可根据群组名称、群组id模糊查询", notes = "分页查询")
    @PostMapping(value = {"/findByExamRangeGroupName", "/sso/findByExamRangeGroupName", "/api/findByExamRangeGroupName"})
    public JsonResponse findByExamRangeGroupName(@RequestParam(required = false, defaultValue = "1") int page,
                                                 @RequestParam(required = false, defaultValue = "10") int size,
                                                 @RequestBody(required = false) ExamRangeGroup o) {
        try {
            return JsonResponse.success(service.findExamGroupInfoByGroupName(page, size, o));
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询所有群组的考试信息")
    @PostMapping(value = {"/listExamRangeGroup", "/sso/listExamRangeGroup", "/api/listExamRangeGroup"})
    public JsonResponse listExamRangeGroup() {
        return JsonResponse.success(service.listExamRangeGroup());
    }

    @ApiOperation(value = "根据考试编码查询试卷编码")
    @PostMapping(value = {"/findByExamCode", "/sso/findByExamCode", "/api/findByExamCode"})
    public JsonResponse findByExamCode(@RequestParam String examCode) {
        return JsonResponse.success(service.findExamRangeGroupByExamCode(examCode));
    }

}
