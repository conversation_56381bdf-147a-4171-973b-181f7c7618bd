<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>待办维护</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">

        $(function () {

            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#examBacklogTable",//table列表的id名称，需加#
                    "querycmd": "action/examWork/findAllCustom",//table列表的查询命令
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "sortOrder": 'desc',
                    "columns": [[//列
                        {title: "主单据Id", field: "pmInsId", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "办理人", field: "transactor", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "办理人oa账号", field: "transactorCode", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "办理人所在部门", field: "departmentName", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "办理人所在公司", field: "companyName", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "办理人所在组织", field: "orgName", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "部门全路径", field: "displayName", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "考试标题", field: "title", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "是否推送过统一待办", field: "isTodoFlag", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "待办类型", field: "workType", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "推送短信", field: "isPostMsg", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "推送短信最后时间", field: "lastPostTime", width: 100,sortable: true, tooltip: true,align:"center"},
                        {
                            field: "opt", title: "操作", width: 150, rowspan: 1,sortable: true, tooltip: true,align:"center",//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g=[];
                                g.push("<a href='#' class='readDialog' readDialogindex='" + index + "'>【查看】</a>");
                                g.push("<a href='#' class='showDialog' showDialogindex='" + index + "'>【修改】</a>");
                                g.push("<a href='#' delete='action/examWork/deleteById' deleteid='" + row.id + "'>【删除】</a>");

                                return g.join("");
                            }
                        }

                    ]]
                },
                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "ctable": "maUserOrg",
                    "formname": "#examBacklogTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/examWork/create",//新增命令
                    "updatacmd": "action/examWork/update",//修改命令
                    "onSubmit": function (param) {//在请求加载数据之前触发。返回false可以停止该动作

                        return true;
                    }

                },
                "readDialog":{//查看
                    "dialogid": "#readDag",
                    "dialogedit": true,//查看对话框底部要不要编辑按钮
                    "formname":"#examBacklogTableReadForm"
                }
            };
            loadGrid(pageparam);



        });

        //表单校验
        window.fvalidate = function () {
            return $("#examBacklogTableAddForm").form("validate");
        };

        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data, isupdate) {
            if (isupdate) {
                $('.update-readonly').hide();
            } else {
                $('.update-readonly').show();
            }
        };

        function initsystem(){
            //初始化表单操作代码

        };

        $(document).on("click", ".generateTodo", function () {
            top.dialogP("html/examwork/generateTodo.html", 'examBacklog', '生成待办', 'generateTodo', false, '850', '600',close);
        });

        $(document).on("click", ".pushTodo", function () {
            //console.log("推送待办");
            var transactor = $('.transactor').val();
            var transactorCode = $('.transactorCode').val();
            var departmentName = $('.departmentName').val();
            var title = $('.title').val();
            var isTodoFlag = $('.isTodoFlag').val();
            var isPostMsg = $('.isPostMsg').val();
            var companyName = $('.companyName').val();
            var workType = $('.workType').val();
            ajaxgeneral({
                url:"action/examWork/sendUnifiedToDo",
                data:{
                    "transactor":transactor,
                    "transactorCode":transactorCode,
                    "departmentName":departmentName,
                    "title":title,
                    "isTodoFlag":isTodoFlag,
                    "isPostMsg":isPostMsg,
                    "companyName":companyName,
                    "workType":workType
                },
                contentType:"application/json; charset=utf-8",
                success:function(datas){

                }
            });

        });

        $(document).on("click", ".urgedToDo", function () {
            //console.log("推送待办");
            var transactor = $('.transactor').val();
            var transactorCode = $('.transactorCode').val();
            var departmentName = $('.departmentName').val();
            var title = $('.title').val();
            var isTodoFlag = $('.isTodoFlag').val();
            var isPostMsg = $('.isPostMsg').val();
            var companyName = $('.companyName').val();
            var workType = $('.workType').val();
            ajaxgeneral({
                url:"action/examWork/urgedToDo",
                data:{
                    "transactor":transactor,
                    "transactorCode":transactorCode,
                    "departmentName":departmentName,
                    "title":title,
                    "isTodoFlag":isTodoFlag,
                    "isPostMsg":isPostMsg,
                    "companyName":companyName,
                    "workType":workType
                },
                contentType:"application/json; charset=utf-8",
                success:function(datas){

                }
            });

        });

        //简要信息导出
        $(document).on("click", ".brief", function () {


            $("#exportForm").attr("action",web.rootdir+"action/statisticExamInfo/exportExcel");
            $("#exportForm .exportSubmit").trigger("click");
        });
        //题目详细信息导出
        $(document).on("click", ".detailed", function () {

            $("#exportForm").attr("action",web.rootdir+"action/statisticExamInfo/exportExcel2");
            $("#exportForm .exportSubmit").trigger("click");
        });

        //工会信息导出
        $(document).on("click", ".AnalyzeUnIon", function () {

            $("#exportForm").attr("action",web.rootdir+"action/statisticExamInfo/exportExcel3");
            $("#exportForm .exportSubmit").trigger("click");
        });

        //初始化职务
        $(document).on("click", ".initPosition", function () {
            ajaxgeneral({
                url:"action/examInfo/manualInitPosition",
                contentType:"application/json; charset=utf-8",
                success:function(datas){

                }
            });

        });



        //弹出框关闭时，去刷新列表
        function close() {
            $("#examBacklogTable").datagrid("reload");
        }

        // function generateTodo(data) {
        //
        // }
        // function pushTodo(data) {
        //
        // }


    </script>
</head>
<body class="body_page">
<form id="examBacklogTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td colspan="5" width="300">
            </td>
        </tr>
        <tr>

            <td width="120" align="right">办理人：</td>
            <td width="150">
                <input name="transactor" class="transactor" type="text" value=""/>
            </td>

            <td width="120" align="right">办理人oa账户：</td>
            <td width="150">
                <input name="transactorCode" class="transactorCode" type="text" value=""/>
            </td>

            <td width="120" align="right">办理人部门：</td>
            <td width="150">
                <input name="departmentName" class="departmentName" type="text" value=""/>
            </td>

            <td width="120" align="right">公司：</td>
            <td width="150">
                <input name="companyName" class="companyName" type="text" value=""/>
            </td>

            <td width="120" align="right">考试标题：</td>
            <td width="150">
                <input name="title"  class="title" type="text" value=""/>
            </td>

            <td width="120" align="right">待办类型：</td>
            <td width="150">
                <input name="workType"  class="workType" type="text" value=""/>
            </td>
            <td width="120" align="right">是否推送过统一待办：</td>
            <td width="150">
                <input name="isTodoFlag" class="isTodoFlag" type="text" value=""/>
            </td>
            <td width="120" align="right">是否推送过短信：</td>
            <td width="150">
                <input name="isPostMsg" class="isPostMsg" type="text" value=""/>
            </td>
        </tr>

    </table>

    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td style="width: 40%;height:32px;"> <p ><font class="col_r">提示：查询后直接点推送待办，会推送未推送过统一待办的待办</font></p></td>
            <td>
                <a class="btn fl searchtable"><span>查询</span></a>

                <a class="btn  showDialog fr"><span>新增</span></a>
            </td>

            <td style="width: 50%;height:32px;">

                <a class="btn a_success  fl generateTodo"><span>批量生成待办</span></a>
                <a class="btn a_success  fl pushTodo"><span>批量推送待办</span></a>
                <a class="btn a_success  fl urgedToDo"><span>短信催办</span></a>
            </td>
        </tr>

        <tr>
            <td colspan="3">
                <a class="btn a_danger  fl AnalyzeUnIon"  style="width: 400px"><span>通用数据导出(需配置数据字典Excel名称以及题库编码)</span></a>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                <a class="btn a_warning  fl brief"  style="width: 400px"> <span>简要信息导出</span></a>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                <a class="btn a_warning  fl detailed"  style="width: 400px"><span>纪检题目分析数据导出</span></a>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                <a class="btn   fl initPosition"  style="width: 400px"><span>初始化职务</span></a>
            </td>
        </tr>
    </table>
</form>
<!--searchform-->

<!--table-->
<div class="examBacklogTable">
    <table id="examBacklogTable"></table>
</div>

<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:850px;height:650px;">
    <form id="examBacklogTableAddForm" method="post" contentType="application/json; charset=utf-8" initsystem="initsystem()">
        <input id="id" name="id" type="hidden"/>
        <table border="0" cellpadding="0" cellspacing="10" width="100%">


            <tr>
                <td width="10%" align="left">主单据Id：</td>
                <td width="20%" length="100">
                    <input id="pmInsId" name="pmInsId" type="text" value="" class="easyui-validatebox" editable="false" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">办理人：</td>
                <td width="20%" length="100">
                    <input id="transactor" name="transactor" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">办理人oa账号：</td>
                <td width="20%" length="100">
                    <input id="transactorCode" name="transactorCode" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">人员级别：</td>
                <td width="20%" length="100">
                    <input id="positionLevel" name="positionLevel" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">办理人所在部门编码：</td>
                <td width="20%" length="100">
                    <input id="departmentCode" name="departmentCode" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">办理人所在部门：</td>
                <td width="20%" length="100">
                    <input id="departmentName" name="departmentName" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>
            <tr>
                <td width="10%" align="left">办理人所在公司编码：</td>
                <td width="20%" length="100">
                    <input id="companyCode" name="companyCode" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">父级值：</td>
                <td width="20%" length="100">
                    <input id="parentCompanyCode" name="parentCompanyCode" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">办理人所在公司：</td>
                <td width="20%" length="100">
                    <input id="companyName" name="companyName" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">办理人所在组织编码：</td>
                <td width="20%" length="100">
                    <input id="orgCode" name="orgCode" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">办理人所在组织：</td>
                <td width="20%" length="100">
                    <input id="orgName" name="orgName" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">部门全路径：</td>
                <td width="20%" length="100">
                    <input id="displayName" name="displayName" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">考试标题：</td>
                <td width="20%" length="100">
                    <input id="title" name="title" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">是否推送过统一待办：</td>
                <td width="20%" length="100">
                    <input id="isTodoFlag" name="isTodoFlag" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">推送短信：</td>
                <td width="20%" length="100">
                    <input id="isPostMsg" name="isPostMsg" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">待办类型：</td>
                <td width="20%" length="100">
                    <input id="workType" name="workType" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">推送短信最后时间：</td>
                <td width="20%" length="100">
                    <input id="lastPostTime" name="lastPostTime" type="text" value="" class="easyui-validatebox" style="width:  350px; height: 32px;"/>
                </td>
            </tr>


        </table>



    </form>
</div>

<div id="readDag" title="详情" class="easyui-dialog" style="width:650px;height:350px;">
    <form id="examBacklogTableReadForm" method="post" contentType="application/json; charset=utf-8">
        <table border="0" cellpadding="0" cellspacing="10" width="100%">



            <tr>
                <td width="15%" align="left">主单据Id：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="pmInsId" readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">办理人：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="transactor"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">办理人oa账号：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="transactorCode"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">人员级别：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="positionLevel"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">办理人所在部门：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="departmentCode"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">办理人所在部门：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="departmentName"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">办理人所在公司：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="companyCode"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">父级值：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="parentCompanyCode"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">办理人所在公司：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="companyName"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">办理人所在组织：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="orgCode"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">办理人所在组织：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="orgName"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">部门全路径：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="displayName"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">考试标题：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="title"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">是否推送过统一待办：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="isTodoFlag"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">推送短信：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="isPostMsg"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">待办类型：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="workType"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" align="left">推送短信最后时间：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="lastPostTime"  readonly="readonly"/>
                </td>
            </tr>

        </table>

    </form>
</div>
<form id="exportForm" class="hide" method="post">

    <input type="submit" class="exportSubmit"/>
</form>

</body>
</html>
