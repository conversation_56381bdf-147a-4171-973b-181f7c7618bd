<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>巡察整改工作满意度评估调查问卷</title>
    <!--    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>-->
    <!--    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"/>-->
    <!--    <meta name="apple-mobile-web-app-capable" content="yes"/>-->
    <!--    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>-->
    <!--    <meta name="format-detaction" content="telephone=no"/>-->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        // var currentUser={}; //存储移动端url携带的参数
        // var gps = getQueryString();
        // gps.access_token='0';
        // if(!gps.access_token){
        //     window.location.href='errorShow.html';
        // }else{
        //     //console.log(window.location.href)
        //     currentUser=parseURL(window.location.href);
        //     ajaxgeneral({
        //         url: "getCurrentUser?appcode="+currentUser.appcode+"&access_token=" + currentUser.access_token+'&currentUserCode='+currentUser.currentUserCode+'&examCode='+currentUser.examCode,
        //         async: false,
        //         success: function (res) {
        //             //console.log(res)
        //             web.currentUser=res.data;
        //         }
        //     });
        // }
        var reladFlag=false;
        $(function(){
            var titName='分公司';
            //获取分辨率
            var fbl = window.screen.height;
            if(fbl<1080){
                $(".wrapper").css({"width": "925px"})
            } else if(fbl>=1080){
                $(".wrapper").css({"width": "1100px"})
            }
            var gps = getQueryString();
            //console.log(gps)
            if(gps.from=="oa"){
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.uid,
                    async: false,
                    success: function (res) {
                        if(res.data.belongCompanyTypeDictValue=='01'){
                            titName='省公司';
                        }
                        if(res.data.belongCompanyTypeDictValue=='02'){
                            titName=res.data.belongCompanyName;
                        }
                        if(res.data.belongCompanyTypeDictValue=='03'){
                            titName=res.data.belongCompanyNameParent;
                        }
                       // titName=res.data.belongCompanyName?res.data.belongCompanyName:res.data.belongCompanyNameParent;
                    }
                });
            }else {
                getCurrent();
                //console.log(web.currentUser)
                var arr=['省公司','商丘','鹤壁','安阳','开封'];
                for(var i=0;i<arr.length;i++){
                    //直接是市级公司
                    if(web.currentUser.belongCompanyName.indexOf(arr[i])>-1){
                        titName=web.currentUser.belongCompanyName;
                    }
                    //市级下的县公司，按其所属市公司展示
                    if(web.currentUser.belongCompanyNameParent.indexOf(arr[i])>-1){
                        titName=web.currentUser.belongCompanyNameParent;
                    }
                    if(gps.titName && gps.titName.indexOf(arr[i])>-1){
                        titName=gps.titName;
                    }
                }

                // $('.titName').text(titName);
            }
            $('.titName').text(titName);
            var actionType=null;
            if(gps.actionType){
                actionType=gps.actionType;
            }
            $(document).on('click','#Map',function(){
                var url='html/exammanage/examQuestionPostAssess.html?actionType='+actionType;
                // top.dialogP(url, 'examProblemSurvey','答题区域','audit',true,'maximized','maximized',listLoad);
                // winOpen(url, window.name,'答题区域','detail','maximized','maximized',listLoad);
                window.location.href='examQuestionPostAssess.html?actionType='+actionType;
                // reloadList();
            })
        })
        function changeFlag(){
            reladFlag=true;
        }
        function reloadList(){
            var loop = setInterval(function() {
                if (reladFlag==true) {
                    window.opener.listLoad();
                    clearInterval(loop);
                    parent.window.close();
                }
            },1000)
        }
        function listLoad(){
        }
        function parseURL(url){
            if(!url) return;
            url = decodeURI(url);
            var url = url.split("?")[1];
            var para = url.split("&");
            var len = para.length;
            var res = {};
            var arr = [];
            for(var i=0;i<len;i++){
                arr = para[i].split("=");
                res[arr[0]] = arr[1];
            }
            return res;
        }
    </script>
    <style>
        body {
            /*background-image: url("survey.jpg");*/
            /*background-position: center center;*/
            /*background-repeat: no-repeat;*/
            /*background-size: cover;*/
            /*opacity: 0.8;*/
            /*margin: 0px;*/
            /*padding: 0px;*/
        }
        .wrapper{widdth:100%;}
        .main{
            /*width:80%;height:100%;margin:0 auto;margin-top:1%;*/
            /*background-image: url("search.jpg");*/
            /*background-position: center center;*/
            /*background-repeat: no-repeat;*/
            /*background-size: 100% 100%;*/
            /*filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='search.jpg',sizingMethod='scale');*/
            /*opacity: 0.8;*/
            /*position: relative;*/
            min-height:1924px;margin:0 auto;background-color:#fff;color:#000;background: url("./examPics.png") no-repeat top;
        }
        .explain{color:#FFFFFF;font-size:34px;font-weight:700;text-align: center}
        .mainVal{width:100%;border:1px solid #B2C5E5;color:#FDF8FC;margin-top:4%;background:rgba(245,245,245,0.3);padding: 20px 20px;font-size:16px}
        .mainVal p{margin-top: 1%}
        .mainVal p span{color:#FFFFFF}
        /*.startTest{position:absolute;bottom:6%;border:none;cursor:pointer;font-size:16px;width:190px;height:40px;text-align: center;line-height:40px;border-radius:20px;background:linear-gradient(to right, #016FE2 ,#00A9F5);color:#FFFFFF;margin-left: 39%;margin-top: 2%;}*/
        .startTest{position:absolute;bottom:6%;font-weight:600;border:none;cursor:pointer;font-size:16px;width:20%;height:40px;text-align: center;line-height:40px;border-radius:20px;background:#F7B70f;color:#051400;margin-left: 39%;margin-top: 2%;}
        .hover{position: relative;width:450px;margin: auto;}
        /*.main_wrap{position: absolute;width:39%;bottom:32%;left:31%;}*/
        .main_wrap{position: absolute;bottom:32%;left:8%;padding-right:30px;}
        .main_wrap p{font-size:16px;color: #2A2A2A;}
        .main_wrap .titClass{text-indent:29px;}
        @media (max-width:900px){
            .hover{
                width:100%;
                background:red;
            }
        }
        .bigPic{}
    </style>
</head>
<body style="height: 100%;">
<!--<div class="wrapper">-->
<div class="hover">
    <img class="bigPic" src="./examPics.png" style="display: block;" usemap="#Map">
    <div class="main_wrap">
        <p>各位同仁：</p>
        <p class="titClass">您好！欢迎参加公司2020年巡察整改工作满意度评估调查问卷。</p>
        <p class="titClass"> 公司党委于2020年6月对<span class="titName"></span>进行了内部巡察，分公司于12月进行了巡察整改公开通报。希望通过您的真实回答，使我们真正了解巡察整改开展以来取得的成效，以便为今后更好地开展工作提供决策依据。本问卷采用匿名填答方式，您的信息我们会为您保密，请如实回答，谢谢！</p>
    </div>
</div>
<map name="Map" id="Map" style="cursor: pointer;">
    <area shape="rect" coords="63,600,382,662" target="_blank">
</map>
</body>
</html>