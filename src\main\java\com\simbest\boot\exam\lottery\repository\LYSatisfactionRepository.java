package com.simbest.boot.exam.lottery.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.lottery.model.LYSatisfaction;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface LYSatisfactionRepository  extends LogicRepository<LYSatisfaction,String> {
    /**
     * 通过username 获取表单
     * @param username
     * @return
     */
    @Query(
            value = "select a.* from us_lysatisfaction_form a where a.enabled = 1  and a.username=:username and a.exam_code=:examCode order by  a.created_Time desc ",
            nativeQuery = true
    )
    List<LYSatisfaction> findByUsername(@Param("username") String username,@Param("examCode") String examCode);
}
