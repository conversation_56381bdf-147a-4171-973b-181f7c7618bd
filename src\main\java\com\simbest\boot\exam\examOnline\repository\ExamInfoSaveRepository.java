/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamInfoSave;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <strong>Title : ExamUserRepository</strong><br>
 * <strong>Description : 试卷人员Dao </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR> kou<PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface ExamInfoSaveRepository extends LogicRepository<ExamInfoSave, String> {

    /**
     * 根据 publishUsername examCode examAppCode 获取最新的自动保存记录
     */
    @Query(value = "SELECT *\n" +
            "  FROM (SELECT t.*\n" +
            "          FROM us_exam_info_save t\n" +
            "         WHERE t.creator = :publishUsername\n" +
            "           AND t.exam_app_code = :examAppCode\n" +
            "           and t.exam_code = :examCode\n" +
            "           AND t.enabled = 1\n" +
            "         ORDER BY t.created_time DESC)\n" +
            " WHERE rownum = 1", nativeQuery = true)
    ExamInfoSave findAllByNew(@Param("publishUsername") String publishUsername, @Param("examCode") String examCode, @Param("examAppCode") String examAppCode);

    @Query(value = "SELECT *\n" +
            "  FROM (SELECT t.*\n" +
            "          FROM us_exam_info_save t\n" +
            "         WHERE t.creator = :publishUsername\n" +
            "           AND t.exam_app_code = :examAppCode\n" +
            "           AND t.enabled = 1\n" +
            "         ORDER BY t.created_time DESC)\n" +
            " WHERE rownum = 1", nativeQuery = true)
    ExamInfoSave findAllByNew1(@Param("publishUsername") String publishUsername, @Param("examAppCode") String examAppCode);

}
