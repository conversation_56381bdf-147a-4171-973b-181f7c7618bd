package com.simbest.boot.exam.todo.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.cmcc.mss.importsrvresponse.ImportSrvResponse;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.cmcc.hq.clients.HqTodoRestClient;
import com.simbest.boot.cmcc.hq.model.*;
import com.simbest.boot.cmcc.hq.service.IUsernameAccountService;
import com.simbest.boot.exam.todo.model.UsTodoModel;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <strong>Title : TodoBusOperatorService</strong><br>
 * <strong>Description : 统一代办业务操作</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Component
public class TodoBusOperatorService{

    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    @Autowired
    private IUsTodoModelService usTodoModelService;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private HqTodoRestClient todoRestClient;

    @Autowired
    private IUsernameAccountService usernameAccountService;

    @Value("${spring.profiles.active}")
    private String profilesActive;

    /**
     * 推送统一代办
     * @param usTodoModel              待办操作业务对象
     */
    public void openTodo( UsTodoModel usTodoModel){
        log.debug( "TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办开始");
        ImportSrvResponse importSrvResponse = null;
        try {
            Boolean isTodoFlag = false;  //false 待办代表不推送  true推送
            String sendUser = usTodoModel.getSender();
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode( Constants.APP_CODE,sendUser );
            if ( simpleApp != null ){
                isTodoFlag = simpleApp.getTodoOpen();
            }
            if ( isTodoFlag ) {

                SimpleHqTodo hqTodo = SimpleHqTodo.builder()
                        .appId(Constants.APP_ID)
                        .itemCreateTime( System.currentTimeMillis())
                        .appName(Constants.APP_NAME)
                        .itemId(usTodoModel.getWorkItemId())
                        .itemType("0") //0：待办 ， 1：待阅 ， 2：已办 ， 3：已阅
                        .lastUpdateTime(System.currentTimeMillis())
                        .itemUrl(usTodoModel.getOaHtmlUrl() + usTodoModel.getUrlParams())
                        .itemMOAUrl(usTodoModel.getOaHtmlUrl() + usTodoModel.getUrlParams())
                        .processInstanceId(usTodoModel.getWorkItemId())
                        .build();
                //获取办理人信息
                Set<String> usernameSet = Sets.newHashSet();//办理人
                usernameSet.add(usTodoModel.getUserName());
                //上一步办理人
                String activityDefId = "exam.question";
                String activityDefName = "答题环节";

                Map<String, HqUserInfo> userInfoMap = findByUsernames(usernameSet);
                Assert.notEmpty(userInfoMap , "查询人员信息失败！");
                HqUserInfo receiver = userInfoMap.get(usTodoModel.getUserName());
                Assert.notNull(receiver , "当前办理人信息查询失败！");
                hqTodo.setReceiver(receiver);
                hqTodo.setReceiverUserId(receiver.getUserId());
                //封装上一办理人信息
                HqUserInfo lastHandleuser = HqUserInfo.builder().userId("-").userName("-").idType("0").build();
                Assert.notNull(lastHandleuser , "获取起草人失败！");
                //获取流程实例信息
                ProcessDef processDef = ProcessDef.builder()
                        .activityId(activityDefId)
                        .activityName(activityDefName)
                        .processTypeId(Constants.APP_SYS_ID)
                        .processTypeName(Constants.APP_NAME)
                        .definitionId(usTodoModel.getWorkItemId())
                        .definitionName(Constants.APP_NAME)
                        .instanceStartTime(System.currentTimeMillis())
                        .instanceCreateUser("-")
                        .startFlag("false")
                        .endFlag("false")
                        .build();
                hqTodo.setProcessDef(processDef);
                hqTodo.setLastHandler(Collections.singletonList(lastHandleuser));

                //封装文种信息
                DocInfo docInfo = DocInfo.builder()
                        .docInsId(usTodoModel.getWorkItemId())
                        .docState("1")
                        .itemTitle(usTodoModel.getTitle())
                        .createDept("-")
                        .docCreateTime(System.currentTimeMillis())
                        .docTypeId(Constants.APP_ID)
                        .docTypeName(Constants.APP_NAME)
                        .isMyDoc("N")
                        .drafter(lastHandleuser)
                        .build();
                hqTodo.setDocInfo(docInfo);
                log.warn("推送统一待办数据：{}" , JacksonUtils.obj2json(hqTodo));
                JsonResponse jsonResponse = todoRestClient.pushHqTodoSimple(hqTodo);
                if (jsonResponse.getErrcode() != 0) {
                    throw new Exception("推送统一待办待办失败！");
                }
            }
        }catch ( Exception e ){
            log.error( "TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办异常" + e.getMessage());
            Exceptions.printException( e );
        }
        log.debug( "TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办结束");
    }

    /**
     *  核销统一代办
     * @param usTodoModel              待办操作业务对象
     */
    public void closeTodo(UsTodoModel usTodoModel){
        log.debug( "TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台推送统一代办开始");
        try {
            usTodoModelService.update( usTodoModel );
            Set<String> userSet = Sets.newHashSet();
            userSet.add(usTodoModel.getUserName());
            Map<String, HqUserInfo> userInfoMap = this.findByUsernames(userSet);
            HqUserInfo userInfo = userInfoMap.get(usTodoModel.getUserName());
            Assert.notNull(userInfo , "获取办理人信息失败！");
            String url = usTodoModel.getOaHtmlUrl() + usTodoModel.getUrlParams().replace("type=task" , "type=join").replace("itemType=0" , "itemType=2");
            SimpleHqTodo hqTodo = SimpleHqTodo.builder()
                    .appId(Constants.APP_ID)
                    .appName(Constants.APP_NAME)
                    .itemUrl(url)
                    .itemMOAUrl(url)
                    .itemId(usTodoModel.getWorkItemId())
                    .itemType("4")//0：待办 ， 1：待阅 ， 2：已办 ， 3：已阅
                    .lastUpdateTime(System.currentTimeMillis())
                    .receiverUserId(userInfo.getUserId())
                    .receiver(userInfo)
                    .build();
            JsonResponse jsonResponse = todoRestClient.updateStatusTodo(hqTodo);
            if (jsonResponse.getErrcode() != 0) {
                throw new Exception("推送统一待办待办失败！");
            }


        }catch ( Exception e ){
            log.error( "TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台推送统一代办异常"+ e.getMessage());
            Exceptions.printException( e );
        }
        log.debug( "TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台推送统一代办结束");
    }

    /**
     * 删除统一代办
     * @param usTodoModel              待办操作业务对象
     */
    public void cancelTodo(UsTodoModel usTodoModel){}


    //根据账号查询账号信息
    private Map<String, HqUserInfo> findByUsernames(Set<String> usernameSet) {
        Map<String, HqUserInfo> userMap = Maps.newHashMap();
        Assert.notEmpty(usernameSet , "人员信息不能为空！");
        String usernames = usernameSet.stream().collect(Collectors.joining(","));
        Map<String, String> userAccountMap = Maps.newHashMap();
        if (!StrUtil.equals(profilesActive , "prd")){
            List<String> userAllList = Lists.newArrayList();
            usernameSet.stream().forEach(username -> userAllList.add(username + "@ha.cmcc"));
            Specification<UsernameAccount> build = Specifications.<UsernameAccount>and()
                    .in("username", userAllList)
                    .build();
            Iterable<UsernameAccount> usernameAccounts = usernameAccountService.findAllNoPage(build);
            for (UsernameAccount usernameAccount : usernameAccounts) {
                userAccountMap.put(usernameAccount.getUsername().replace("@ha.cmcc", ""), usernameAccount.getAccount().replace("@ha.cmcc", ""));
            }
        }
        JsonResponse jsonResponse = uumsSysUserinfoApi.findUserByUsernameNoPageNoSession(Constants.APP_CODE, Constants.USER_ADMIN, usernames);
        if (jsonResponse.getErrcode() == 0) {
            List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
            if (CollectionUtil.isNotEmpty(data)) {
                Map<String, Object> map = data.get(0);
                List<Map<String, Object>> userList = MapUtil.get(map, "user", new TypeReference<List<Map<String, Object>>>() {});
                if (CollectionUtil.isNotEmpty(userList)) {
                    userList.stream().forEach(user -> {
                        if (StrUtil.equals(MapUtil.getStr(user, "treeType"), "user")) {
                            String id = MapUtil.getStr(user, "id");
                            HqUserInfo userInfo = HqUserInfo.builder().userId(id).userName(MapUtil.getStr(user, "name")).idType("0").build();
                            if (!StrUtil.equals(profilesActive , "prd")) {
                                userInfo.setUserId(MapUtil.getStr(userAccountMap , id));
                                userInfo.setUserName( "xx" + userInfo.getUserName().substring(userInfo.getUserName().length() -1 , userInfo.getUserName().length()));
                            }
                            userMap.put( id , userInfo);
                        }
                    });
                }
            }
        }
        log.warn("查询人员数据信息：{}" , JacksonUtils.obj2json(userMap));
        return userMap;
    }
}
