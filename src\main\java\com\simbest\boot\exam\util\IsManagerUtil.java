package com.simbest.boot.exam.util;

import com.simbest.boot.security.IRole;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;

import java.util.Set;

/**
 * <AUTHOR>
 * @create 2018-09-20 11:49
 * @desc
 **/
public class IsManagerUtil {

    private IsManagerUtil() {
    }

    /**
     * @Description 判断当前人是否是考试系统超级管理员
     * @throws Exception
     */
    static public boolean isSupperManage()throws Exception{
        boolean admin=false;
        IUser currentUser = SecurityUtils.getCurrentUser();
        try {

            Set<? extends IRole> authRoles = currentUser.getAuthRoles();
            for (IRole authRole : authRoles) {
                if (Constants.ROLE_EXAM_SUPPER.equals(authRole.getRoleCode())){
                    admin=true;
                    break;
                }
            }

        }catch ( Exception e ){
            throw e;
        }
        return admin;
    }
}
