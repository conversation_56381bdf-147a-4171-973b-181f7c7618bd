<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>角色管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        html,body{
            overflow-y:auto
        }
    </style>
    <script type="text/javascript">
		$(function(){
			//showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
			var pageparam={
				"listtable":{
					"listname":"#roleTable",//table列表的id名称，需加#
					"querycmd":"action/sys/role/findRoleNameIsARoleDim",//table列表的查询命令					
					"checkboxall":true,
					//"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
					"nowrap": true,//把数据显示在一行里,默认true
					"frozenColumns":[[
						{ field: "ck",checkbox:true}
					]],//固定在左侧的列
					"columns":[[//列   
						{ title: "角色编码", field: "roleCode", width: 60,sortable:true},
						{ title: "角色名称", field: "roleName", width: 100,sortable:true},
						{ title: "是否是业务角色", field: "isApplicationRole", width: 40,sortable:true,
							formatter:function(value,row,index){
								return value?"是":"否";
							} 
						},
						{
							field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
							formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
								var g = "<a href='#' class='showDialog' showDialogindex='" + index + "'>【修改】</a>"
                                +"<a href='#' delete='action/sys/role/deleteById' deleteid='"+row.id+"'>【删除】</a>"
								+"<a class='openDialog' paras='key="+row.id+"&keyType=role&id="+row.id+"'>【菜单权限配置】</a>"// idname='id' id='" + row.id + "'
                                +"<a class='posSet' roleName='"+row.roleName+"' id='"+row.id+"'>【关联职务配置】</a>"
                                +"<a class='containPop' roleName='"+row.roleName+"' id='"+row.id+"'>【角色配置】</a>";
								return g;
							}
						}
					] ],
					"pagerbar": [{
						id:"deleteall",
						iconCls: 'icon-remove',
						text:"批量删除&nbsp;"
					}],
					"deleteall":{//批量删除deleteall.id要与pagerbar.id相同
						"id":"deleteall",
						"url":"action/sys/role/deleteByIds",
						"contentType":"application/json; charset=utf-8"
					}
				},
				"dialoglistbtn":{
					"dialogid":"#buttons2",//对话框的id
					"buttons":[{
						text:"确认",
						handler:function(){
							var obj = $('#buttons2').dialog('options');
							var urlstr = obj["queryParams"];
							if(btn_sure==1){
								btn_sure=2;
								var aa=$("#permissionIds").tree("getChecked");
								var ids=[]; 
								for(var i in aa){
									ids.push(aa[i].id);
								}
								ajaxgeneral({
									url:"action/role/permission/updateListByRoleId",
									data:{"roleId":urlstr.id,"permissionIds":ids.join(","),"appId":urlstr.appId},
									success:function(data){
                                        btn_sure=1;
                                        top.mesShow("温馨提示","操作成功",2500);
										$("#buttons2").dialog("close");//关闭对话框
									},sError:function(data){
										btn_sure=1;
									},error:function(data){
										btn_sure=1;
									}
								});
							}
						}
					},{
						text:"关闭",
						handler:function(){
							$("#buttons2").dialog("close");
						}
					}],
					"dialogurl":"html/syspermission/authorization.html?type=user"//对话框页面的路径
				},
				"dialogform":{
					"dialogid":"#buttons",//对话框的id
					"formname":"#roleTableAddForm",//新增或修改对话框的formid需加#
					"insertcmd":"action/sys/role/create",//新增命令
					"updatacmd":"action/sys/role/update",//修改命令
					"onSubmit":function(data){						
						if($("#id").val()=="") data.displayOrder="1";
						if($("#roleCode").attr("codeError")){
							top.mesAlert("提示信息","角色编码已存在,请重新输入！", 'error');
							return false;
						}
						return true;
					}
				}
			};
			loadGrid(pageparam);
			//职务配置
            $(document).on("click","a.posSet",function(){
                var gps=getQueryString();
                var $t=$(this);
                var id=$t.attr("id");
                var url=tourl((gps.form?"uums/":"")+'html/sysrole/sysRolePositionList.html',{"roleId":id});
                top.dialogP(url,window.name,$t.attr("roleName")+'职务设置','posSet',true,'1100');
            });
            //角色下人员
            $(document).on("click","a.containPop",function(){
                var gps = getQueryString();
                var $t = $(this);
                var id = $t.attr("id");
                var url = tourl((gps.form?"uums/":"")+'html/sysrole/sysRoleSetUser.html',{"roleId":id});
                top.dialogP(url,window.name,$t.attr("roleName")+'包含人员','containPop',true,'1200');
            });
		});
		//form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
		function beforerender(data,isupdate){
			if(isupdate){
				$('.update-readonly').hide();
			}else{
				$('.update-readonly').show();
			}
		};
		function getcallback() {
        //     if($("#isApplicationRole").val()=="true"){
        //         $("#isApplicationRole option").eq(0)
        //     }else{
        //         $("#isApplicationRole option").val("否")
        //     }
        }
    </script>
</head>
<body class="body_page_uums">
<h6 class="pageTit"><font class="col_b fwb">角色管理</font><i class="iconfont">&#xe66e;</i><small>角色的增删改查和授权</small></h6>
<!--searchform-->
<form id="roleTableQueryForm"> 
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
		<tr>
            <td width="90" align="right">角色编码：</td><td width="150"><input name="roleCode" type="text"  /></td>
            <td width="90" align="right">角色名称：</td><td width="150"><input name="roleName" type="text"  /></td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fl searchtable"><span>查询</span></a>   
					<a class="btn a_success showDialog fr"><span>新增</span></a>
                </div>
                </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="roleTable"><table id="roleTable"></table></div>
<!--dialog-->
<div id="buttons2" title="角色特殊授权" class="easyui-dialog" style="width:700px;height:600px;"></div>
<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:520px;height:350px;">
<form id="roleTableAddForm" method="post" contentType="application/json; charset=utf-8" cmd-select="action/sys/role/findById" beforerender="beforerender()" getcallback="getcallback()">
	<input id="id" name="id" type="hidden" />
	<table border="0" cellpadding="0" cellspacing="5">
        <tr class="update-readonly">
            <td width="100" align="right"><font class="col_r">*</font>角色编码：</td>
            <td width="300">
                <input id="roleCode" name="roleCode" cmd="action/sys/role/isHaveCode" onblur="isHaveCode(this)" type="text" class="easyui-validatebox" required='required' />
            </td>
        </tr>
		<tr>
            <td width="100" align="right">角色名称：</td>
            <td width="300">
                <input id="roleName" name="roleName" type="text" />
            </td>
        </tr>
        <tr>
            <td width="100" align="right">是否是业务角色：</td>
            <td width="300">
                <select id="isApplicationRole" name="isApplicationRole" class="easyui-combobox" style="width: 100%;height: 32px;" data-options="editable:false,panelHeight:'auto'">
                    <option value="true">是</option>
                    <option value="false">否</option>
                </select>
            </td>
        </tr>
    </table>
</form>
</div>
</body>
</html>
