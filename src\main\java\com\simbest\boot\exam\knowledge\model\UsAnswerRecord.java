package com.simbest.boot.exam.knowledge.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_answer_record")
@ApiModel(value = "答题记录表")
public class UsAnswerRecord extends WfFormModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UAR") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 255)
    @ApiModelProperty(value = "题库编码")
    private String examCode;

   @Column(length = 500)
    @ApiModelProperty(value = "主单据Id", name = "pmInsId", example = "2019678301426913763328")
    private String pmInsId;


   @Column(length = 500)
    @ApiModelProperty(value = "邀请ID", name = "invitaitonId", example = "999999")
    private String invitationId;


    @Column(length = 500)
    @ApiModelProperty(value = "答题用户OA账户", name = "ansewersUserName", example = "hadmin")
    private String ansewersUserName;

    @Column(length = 500)
    @ApiModelProperty(value = "答题用户姓名", name = "ansewersTrueName", example = "超级管理员")
    private String ansewersTrueName;

    @Column(length = 50)
    @ApiModelProperty(value = "答题类型(A 每日答题、B挑战答题，C:人人答题)")
    private String workType;

    @Column(length = 2000)
    @ApiModelProperty(value = "问题ID", name = "questionId", example = "问题ID")
    private String questionId;

    @Column(length = 2000)
    @ApiModelProperty(value = "问题Code", name = "questionCode", example = "问题Code")
    private String questionCode;



    @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户OA账户", name = "sendUserName", example = "超级管理员")
    private String sendUserName;

    @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户姓名", name = "sendTrueName", example = "超级管理员")
    private String sendTrueName;


    @Column(length = 500)
    @ApiModelProperty(value = "接受邀请的用户OA账户", name = "recUserName", example = "超级管理员")
    private String recUserName;

    @Column(length = 500)
    @ApiModelProperty(value = "接受邀请的用户姓名", name = "recTrueName", example = "超级管理员")
    private String recTrueName;

    @Column(length = 500)
    @ApiModelProperty(value = "结束时间", name = "endTime")
    private String endTime;

    @Column(length = 500)
    @ApiModelProperty(value = "花费时长", name = "duration")
    private Long duration;

    @Transient
    private String rightCount; //标题

    @Transient
    private String title; //标题

    @Transient
    private String starTime; //标题

    @Transient
    private String spendTime; //标题
}
