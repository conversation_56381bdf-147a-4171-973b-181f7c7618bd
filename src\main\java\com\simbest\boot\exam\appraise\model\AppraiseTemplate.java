package com.simbest.boot.exam.appraise.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * AppraiseTemplate
 *
 * <AUTHOR>
 * @since 2024/1/24 9:21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity(name = "us_appraise_template")
@ApiModel(value = "评价模板表")
public class AppraiseTemplate extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UAT") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 255)
    @ApiModelProperty("标题")
    private String title;

    @Column(length = 255)
    @ApiModelProperty("项目")
    private String project;

    @Column(length = 255)
    @ApiModelProperty("子项")
    private String item;

    @Column(length = 1000)
    @ApiModelProperty("内容")
    private String content;

    @Column(length = 40)
    @ApiModelProperty("模板code")
    private String code;

    @Column(length = 10)
    @ApiModelProperty("分数")
    private String score;

    @Column(length = 10)
    @ApiModelProperty("评分类型 0 打分类型 1 填写类型")
    private String type;

    @Column(length = 10)
    @ApiModelProperty(value = "排序")
    private Long displayOrder;


    @Transient
    @ApiModelProperty(value = "用户填写内容")
    private String userContent;

    /**
     * 20240129
     * 按照前端要求的数据结构开发
     */
    @Transient
    private List<Map<String, Object>> templateItems = new ArrayList<>();

}
