package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamNumber;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ExamNumberRepository extends LogicRepository<ExamNumber, String> {

    @Query(value = "select t.* from US_EXAM_NUMBER t  where t.exam_code =:examCode and t.exam_user_name =:userName and t.enabled =1"
            , nativeQuery = true)
    ExamNumber findExamNumber(@Param("examCode") String examCode, @Param("userName") String userName);
}
