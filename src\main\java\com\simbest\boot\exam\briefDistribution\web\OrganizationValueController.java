package com.simbest.boot.exam.briefDistribution.web;


import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.briefDistribution.model.OrganizationValue;
import com.simbest.boot.exam.briefDistribution.service.OrganizationValueService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 管理员管理模块
 */
@RestController
@RequestMapping(value = "/action/organizationValue")
@Slf4j
public class OrganizationValueController {

    @Autowired
    private OrganizationValueService service;
    @ApiOperation(value = "管理员管理(分页)", notes = "管理员管理(分页)")
    @PostMapping(value = {"/findList", "/sso/findList", "/api/findList"})
    public JsonResponse findList() {
        try {
            List<OrganizationValue> pageList = service.findList();
            return JsonResponse.success(pageList);
        } catch (Exception e) {
            log.error("信息查询接口异常，{}", e);
            return JsonResponse.fail("接口异常！");
        }
    }

    @ApiOperation(value = "组织人员导入", notes = "组织人员导入")
    @PostMapping(value = {"/importExcel", "/sso/importExcel", "/api/importExcel"})

    public void importExcel(HttpServletRequest request,HttpServletResponse response) {
        service.importExcel(request, response);
    }

}
