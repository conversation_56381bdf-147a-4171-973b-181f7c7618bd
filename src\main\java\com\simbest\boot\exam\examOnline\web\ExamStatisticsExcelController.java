package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.exam.examOnline.service.impl.ExamStatisticsExcelServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @title: 洛阳市满意度统计结果的导出
 * @projectName exam
 * @description:
 * @date 2021/6/10  15:00
 */
@Api(description = "导出满意度评级统计结果表格")
@Slf4j
@RestController
@RequestMapping(value = "/action/statisticsExcel")
public class ExamStatisticsExcelController {

    @Autowired
    private ExamStatisticsExcelServiceImpl examStatisticsExcelService;

    /**
      *功能描述 导出洛阳市机关部门互评各维度统计结果
      * <AUTHOR>
      * @date 2021/6/10

      */
    @ApiOperation(value = "导出机关部门互评各维度结果", notes = "导出机关部门互评各维度结果")
    @PostMapping(value ={"/exportLYJGExcel","/anonymous/exportLYJGExcel"})
    public void exportLYJGExcel(HttpServletRequest request, HttpServletResponse response ,
                              @RequestParam(required = false) String annualQuarterCode)throws Exception{

        examStatisticsExcelService.statisticsDepartments(annualQuarterCode,request,response);
    }

    @ApiOperation(value = "导出机关各部门总分的平均分及排名", notes = "导出机关各部门总分的平均分及排名")
    @PostMapping(value = "/exportLYJGAvgExcel")
    public void exportLYJGAvgExcel(HttpServletRequest request, HttpServletResponse response ,
                                @RequestParam(required = false) String annualQuarterCode)throws Exception{

        examStatisticsExcelService.statisticsDepartmentsAvg(annualQuarterCode,request,response);
    }

    @ApiOperation(value = "导出县分公司对机关各部门满意度评价各维度结果", notes = "导出县分公司对机关各部门满意度评价各维度结果")
    @PostMapping(value = "/exportLYXFAvgExcel")
    public void exportLYXFExcel(HttpServletRequest request, HttpServletResponse response ,
                                   @RequestParam(required = false) String annualQuarterCode)throws Exception{

        examStatisticsExcelService.countyBranchStatistics(annualQuarterCode,request,response);
    }

    @ApiOperation(value = "导出县分公司对机关部门评价总分的平均分及排名", notes = "导出机关各部门总分的平均分及排名")
    @PostMapping(value = "/exportLYXFExcel")
    public void exportLYXFAvgExcel(HttpServletRequest request, HttpServletResponse response ,
                                   @RequestParam(required = false) String annualQuarterCode)throws Exception{

        examStatisticsExcelService.countyBranchStatisticsDepartmentsAvg(annualQuarterCode,request,response);
    }

    @ApiOperation(value = "导出统计汇总表", notes = "导出统计汇总表")
    @PostMapping(value = "/exportStatisticalSummaryExcel")
    public void statisticalSummaryExcel(HttpServletRequest request, HttpServletResponse response ,
                                   @RequestParam(required = false) String annualQuarterCode)throws Exception{
        examStatisticsExcelService.statisticalSummary(annualQuarterCode,request,response);
    }
}
