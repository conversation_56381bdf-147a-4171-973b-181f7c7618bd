/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.knowledge.model.UsQuizSessions;
import com.simbest.boot.exam.knowledge.model.UsUserAnswers;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
public interface IUsQuizSessionsService extends ILogicService<UsQuizSessions,String> {
    /**
     * 查询对战未结束的情况
     * @param currentDay
     * @return
     */
    List<UsQuizSessions> cancleQuizSessions(String currentDay);
    /**
     * 查询当天对战人对战情况
     * 非A即B
     * 无法判断待办答题人是邀请人还是被邀请人，可以使用不是邀请人的话那就是被邀请人的思路
     * @param currentDay
     * @return
     */
    List<UsQuizSessions> checkUpdateSeeeion(String currentDay,String sendUserName);

    /**
     * 根据时间和邀请ID查询
     * @param currentDay
     * @param invitaitonId
     * @return
     */
    List<UsQuizSessions> findAndUpdateSeeeion( String currentDay,String invitaitonId);

}
