package com.simbest.boot.exam.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName FormatConversion
 * @Description TODO 转小驼峰
 * @Date 2019/12/27
 * @Vsersion 0.1
 */

@Slf4j
public class FormatConversion {
    /**
     * （格式转换）下划线转小驼峰
     * @param data
     * @return
     */


    public static List<Map<String,Object>> formatConversion(List<Map<String,Object>> data) {
        List<Map<String,Object>> result = Lists.newArrayList();
        try {
            for (Map<String,Object> m : data) {
                Set<String> keySet = m.keySet();
                Map<String, Object> newMap = Maps.newHashMap();
                for (String s : keySet) {
                    String[] split = s.split("_");
                    String newKey = "";
                    for (int i = 0 ; i < split.length ; i++) {
                        if(i == 0) {
                            newKey = split[i].toLowerCase();
                        } else {
                            newKey += split[i].substring(0,1) + split[i].substring(1).toLowerCase();
                        }
                    }
                    newMap.put(newKey,m.get(s));
                }
                result.add(newMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.debug("--------转换失败-----------");
            return null;
        }
        return result;
    }

}
