package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.SystemRepository;
import com.simbest.boot.exam.examOnline.model.ExamAttributeQuestion;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @desc: 试卷题目Repository
 * @date 2021/7/4  11:31
 */
public interface ExamAttributeQuestionRepository extends SystemRepository<ExamAttributeQuestion,String> {

    /**
      * @desc 根据试卷编码删除试卷题目信息
      * <AUTHOR>
      */
    void deleteByExamAppCode(@Param("examAppCode") String examAppCode);

}
