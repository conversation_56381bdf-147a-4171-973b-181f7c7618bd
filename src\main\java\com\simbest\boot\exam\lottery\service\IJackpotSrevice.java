/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.service;/**
 * Created by KZH on 2019/12/9 16:19.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.lottery.model.Jackpot;

/**
 * <AUTHOR>
 * @create 2019-12-09 16:19
 * @desc 奖池
 **/
public interface IJackpotSrevice extends ILogicService<Jackpot,String> {

    /**
     * 查询奖池中是否存在剩余奖项
     * @return 是或否
     */
    Jackpot findJackpotIsResidue();
}
