package com.simbest.boot.exam.codePatrol.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.codePatrol.model.UsCodePatrol;


public interface IUsCodePatrolService extends ILogicService<UsCodePatrol, String> {

    /**
     * 添加
     *
     * @param codePatrol
     * @return
     */
    JsonResponse addPatrol(UsCodePatrol codePatrol);

    /**
     * 查询试卷分类
     *
     * @return
     */
    JsonResponse queryPaperType(String dictType);


    /**
     * PC后台查询填报内容
     *
     * @param page
     * @param size
     * @param codePatrol
     * @param currentUserCode
     * @param source
     * @return
     */
    JsonResponse queryAllPatrol(int page,
                                int size,
                                UsCodePatrol codePatrol,
                                String currentUserCode,
                                String source);
}
