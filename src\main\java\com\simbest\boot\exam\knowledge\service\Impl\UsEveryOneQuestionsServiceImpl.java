/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service.Impl;/**
 * Created by KZH on 2019/10/8 16:07.
 */


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.component.distributed.lock.DistributedRedisLock;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionService;
import com.simbest.boot.exam.knowledge.model.*;
import com.simbest.boot.exam.knowledge.service.*;
import com.simbest.boot.exam.util.BelongInfoTool;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.SMSUtil;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.Offset;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class UsEveryOneQuestionsServiceImpl implements IUsEveryOneQuestionsService {

    @Autowired
    SysDictValueService sysDictValueService;

    @Autowired
    IExamQuestionService iExamQuestionService;

    @Autowired
    IUsAnswerRecordService usAnswerRecordService;

    @Autowired
     LoginUtils loginUtils;

    @Autowired
    IUsUserAnswersService userAnswersService;

    @Autowired
    IUsPendingTaskService usPendingTaskService;

    @Autowired
    IExamQuestionAnswerService iExamQuestionAnswerService;

    @Autowired
    IUsEveryOneQuestionsService usEveryOneQuestionsService;

    @Autowired
    IUsQuizSessionsService usQuizSessionsService;


    @Autowired
    private SMSUtil smsUtil;


    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    @Autowired
    private IUsInvitationsService usInvitationsService;

    @Autowired
    private RsaEncryptor rsaEncryptor;



    /**
     * 获取答题题目列表接口
     * @param source
     * @param username
     * @return
     */
    @Override
    public JsonResponse getAnswersList(String source, String username,String workType,String pmInsId,String invitaitonId) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(username,Constants.APP_CODE);
            username = rsaEncryptor.decrypt(username);
        }
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();//题库编码字典获取方式
        Integer everyoneQuestionCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneQuestionCount").get(0).getValue());//人人答题数量
        Integer everyoneAnswerCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneAnswerCount").get(0).getValue());//人人答题次数
        Integer everyoneTimeOut =Integer.valueOf(sysDictValueService.findByDictType("everyoneTimeOut").get(0).getValue());//人人答题超时时间
        List<ExamQuestion> randomQuestionsByCategory =new ArrayList<>();
        //查询是否已经有记录信息
        List<UsAnswerRecord> recordByInvitaitonId = usAnswerRecordService.findRecordByInvitaitonId(invitaitonId);
        IUser currentUser = SecurityUtils.getCurrentUser();
        if(CollectionUtil.isNotEmpty(recordByInvitaitonId)){
            String questionId=recordByInvitaitonId.get(0).getQuestionId();
            System.out.println("questionId是"+questionId);
            String questionCode=recordByInvitaitonId.get(0).getQuestionCode();
            System.out.println("questionCode是"+questionCode);
            //进行答题记录，后续方便统计
            UsAnswerRecord usAnswerRecord = new UsAnswerRecord();

            usAnswerRecord.setInvitationId(invitaitonId);
            usAnswerRecord.setQuestionId(questionId);
            usAnswerRecord.setQuestionCode(questionCode);
            usAnswerRecord.setExamCode(knowledgeQuestionBankCode);
            usAnswerRecord.setAnsewersUserName(currentUser.getUsername());
            usAnswerRecord.setAnsewersTrueName(currentUser.getTruename());
            usAnswerRecord.setWorkType(workType);
            BelongInfoTool.setBelongCompanyAndDepartment(usAnswerRecord);
            usAnswerRecord.setBelongOrgCode(currentUser.getBelongOrgCode());
            usAnswerRecord.setBelongOrgName(currentUser.getAuthOrgs().iterator().next().getDisplayName());
            usAnswerRecordService.insert(usAnswerRecord);
            List<UsPendingTask> pendingTaskByStatue = usPendingTaskService.findPendingTaskByStatue(invitaitonId, "ACCEPTED");
            for (UsPendingTask usPendingTask : pendingTaskByStatue) {
                if(currentUser.getUsername().equals(usPendingTask.getPendUserName())){
                    usPendingTask.setStatus("ONGOING");
                    usPendingTask.setAnswerRecordId(usAnswerRecord.getId());
                    usPendingTaskService.update(usPendingTask);
                    usAnswerRecord.setPmInsId(usPendingTask.getPmInsId());
                    usAnswerRecordService.update(usAnswerRecord);
                }
            }
            UsInvitations usInvitations = usInvitationsService.findById(invitaitonId);
            usInvitations.setAnswerRecordId(usAnswerRecord.getId());
            usInvitationsService.update(usInvitations);
            usAnswerRecord.setSendUserName(usInvitations.getSendUserName());
            usAnswerRecord.setSendTrueName(usInvitations.getSendTrueName());
            usAnswerRecord.setRecUserName(usInvitations.getRecUserName());
            usAnswerRecord.setRecTrueName(usInvitations.getRecTrueName());
            usAnswerRecordService.update(usAnswerRecord);
            List<String> result= Arrays.asList(questionCode.split(","));
            List<ExamQuestion> allByQuestionId = iExamQuestionService.findAllByQuestionCodes(result);
            for (ExamQuestion examQuestion : allByQuestionId) {
                List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
                examQuestion.setAnswerList(examQuestionAnswerList);
                examQuestion.setAnswerRecordId(usAnswerRecord.getId());
                randomQuestionsByCategory.add(examQuestion);
            }
        }else{
            //先判断人人答题数量书否已用完
            List<UsAnswerRecord> usAnswerRecordList = usAnswerRecordService.findTodyAnswerRecordByWorkType(knowledgeQuestionBankCode, Constants.ANSWER_RECORD_PERSION,currentUser.getUsername());
            Iterator<UsAnswerRecord> iterator = usAnswerRecordList.iterator();
            String answerRecordId="";
            while (iterator.hasNext()){
                UsAnswerRecord usAnswerRecord = iterator.next();
                answerRecordId = usAnswerRecord.getId();
                Specification<UsUserAnswers> specification= Specifications.<UsUserAnswers>and().eq("answerRecordId",answerRecordId).build();
                List<UsUserAnswers> usUserAnswers = userAnswersService.findAllNoPage(specification);
                if (usUserAnswers.size()<everyoneAnswerCount){
                    iterator.remove();
                }
            }
            if (everyoneAnswerCount==usAnswerRecordList.size()){
                return JsonResponse.fail("今日已答题，请明日再答！");
            }
            //处理题目未答完退出的问题。
            /*if (everyoneQuestionCount>usAnswerRecordList.size()){
                everyoneQuestionCount = everyoneQuestionCount - usAnswerRecordList.size();
            }*/
//            randomQuestionsByCategory = iExamQuestionService.getRandomQuestionsByCategory(knowledgeQuestionBankCode, everyoneQuestionCount);
            randomQuestionsByCategory = iExamQuestionService.getRandomQuestionsByCategoryWithOutAnswer(knowledgeQuestionBankCode, everyoneQuestionCount);
            //进行答题记录，后续方便统计
            UsAnswerRecord usAnswerRecord = new UsAnswerRecord();
            usAnswerRecord.setPmInsId(pmInsId);
            usAnswerRecord.setInvitationId(invitaitonId);
            usAnswerRecord.setExamCode(knowledgeQuestionBankCode);
            usAnswerRecord.setAnsewersUserName(currentUser.getUsername());
            usAnswerRecord.setAnsewersTrueName(currentUser.getTruename());
            usAnswerRecord.setWorkType(workType);
            BelongInfoTool.setBelongCompanyAndDepartment(usAnswerRecord);
            usAnswerRecord.setBelongOrgCode(currentUser.getBelongOrgCode());
            usAnswerRecord.setBelongOrgName(currentUser.getAuthOrgs().iterator().next().getDisplayName());
            usAnswerRecordService.insert(usAnswerRecord);
            if("C".equals(workType)){
                List<String> questionIdList=new ArrayList<>();
                List<String> questionCodeList=new ArrayList<>();
                if(CollectionUtil.isNotEmpty(randomQuestionsByCategory)){
                    for (ExamQuestion examQuestion : randomQuestionsByCategory) {
                        questionIdList.add(examQuestion.getId());
                        questionCodeList.add(examQuestion.getQuestionCode());
                        examQuestion.setAnswerRecordId(usAnswerRecord.getId());
                    }
                }
                usAnswerRecord.setQuestionId(String.join(",", questionIdList));
                usAnswerRecord.setQuestionCode(String.join(",", questionCodeList));
                System.out.println("questionId22222是"+String.join(",", questionIdList));
                System.out.println("questionCode2222是"+String.join(",", questionCodeList));
                UsInvitations usInvitations = usInvitationsService.findById(invitaitonId);
                usInvitations.setAnswerRecordId(usAnswerRecord.getId());
                usInvitationsService.update(usInvitations);
                usAnswerRecord.setSendUserName(usInvitations.getSendUserName());
                usAnswerRecord.setSendTrueName(usInvitations.getSendTrueName());
                usAnswerRecord.setRecUserName(usInvitations.getRecUserName());
                usAnswerRecord.setRecTrueName(usInvitations.getRecTrueName());
                usAnswerRecordService.update(usAnswerRecord);
                List<UsPendingTask> pendingTaskByStatue = usPendingTaskService.findPendingTaskByStatue(invitaitonId, "ACCEPTED");
                for (UsPendingTask usPendingTask1 : pendingTaskByStatue) {
                    if(currentUser.getUsername().equals(usPendingTask1.getPendUserName())){
                        usPendingTask1.setStatus("ONGOING");
                        usPendingTask1.setAnswerRecordId(usAnswerRecord.getId());
                        usPendingTaskService.update(usPendingTask1);
                        usAnswerRecord.setPmInsId(usPendingTask1.getPmInsId());
                        usAnswerRecordService.update(usAnswerRecord);
                    }
                }

            }
        }
        return JsonResponse.success(randomQuestionsByCategory);
    }

    /**
     * 获取答题题目列表接口--查询没有答完的题目信息
     *
     * @param source
     * @param username
     * @param pmInsId
     * @return
     */
    @Override
    public JsonResponse findSurplusExam(String source, String username, String pmInsId) {
        List<ExamQuestion> examQuestions=new ArrayList<>();
        List<String> questionIdList=new ArrayList<>();
        List<String> answerList=new ArrayList<>();
        String sendTime="";//发送邀请的用户答题用时
        String reciveTime="";//接受邀请的用户答题用时
        String isStart="";//1是邀请人  0是被邀请人
        String isCorrect="0";
        if (source.equals("MOBILE")) {
            username = rsaEncryptor.decrypt(username);
        }else{
            username = SecurityUtils.getCurrentUserName();
        }
        //所有问题ID
        List<UsAnswerRecord> usAnswerRecords = usAnswerRecordService.findRecordByPmInsId(pmInsId);
        for (UsAnswerRecord usAnswerRecord : usAnswerRecords) {
            String questionId = usAnswerRecord.getQuestionId();
            questionIdList=Arrays.asList(questionId.split(","));
        }
        List<UsQuizSessions> usQuizSessions = usQuizSessionsService.findAndUpdateSeeeion(DateUtil.getCurrentStr(),usAnswerRecords.get(0).getInvitationId());
        for (UsQuizSessions usQuizSession : usQuizSessions) {
            if(username.equals(usQuizSession.getSendUserName())){
                sendTime=usQuizSession.getSendTime();//邀请人
                isStart="1";//1是邀请人
            }else{
                reciveTime=usQuizSession.getReciveTime();//被邀请人
                isStart="0";//0是被邀请人
            }
        }
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();//题库编码字典获取方式

        if (StrUtil.equals(isStart  , "1")) {
            List<UsInvitations> invitations = usInvitationsService.findByPmInsId(pmInsId);
            if (CollectionUtil.isNotEmpty(invitations)) {
                UsInvitations usInvitations = invitations.get(0);
                Specification<UsPendingTask> build = Specifications.<UsPendingTask>and()
                        .eq("enabled", Boolean.TRUE)
                        .eq("invitationId", usInvitations.getId())
                        .eq("pendUserName", username)
                        .build();
                List<UsPendingTask> allNoPage = usPendingTaskService.findAllNoPage(build);
                if (CollectionUtil.isNotEmpty(allNoPage)) {
                    pmInsId = allNoPage.get(0).getPmInsId();
                }
            }
        }

        //已经答过的问题ID
        List<UsUserAnswers> answerUserNameInfo = userAnswersService.findAnswerUserNameInfo(pmInsId, username, knowledgeQuestionBankCode);
        for (UsUserAnswers usUserAnswers : answerUserNameInfo) {
            answerList.add(usUserAnswers.getQuestionId());
        }
        if(CollectionUtil.isNotEmpty(questionIdList)&&CollectionUtil.isNotEmpty(answerList)){
            // 从list1中移除list2中存在的元素
            List<String> result = questionIdList.stream()
                    .filter(element -> !answerList.contains(element))
                    .collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(result)){
                List<ExamQuestion> allByQuestionId = iExamQuestionService.findAllByQuestionId(result);
                for (ExamQuestion examQuestion : allByQuestionId) {
                    List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
                    examQuestionAnswerList.forEach(examQuestionAnswer -> examQuestionAnswer.setIsCorrect(null));
                    examQuestion.setAnswerRecordId(usAnswerRecords.get(0).getId());
                    if(StringUtil.isNotBlank(sendTime)){
                        examQuestion.setSendTime(sendTime);
                    }
                    if(StringUtil.isNotBlank(reciveTime)){
                        examQuestion.setReciveTime(reciveTime);
                    }
                    examQuestion.setIsStart(isStart);
                    examQuestion.setAnswerList(examQuestionAnswerList);
                    examQuestions.add(examQuestion);

                }
            }
        }
        if(CollectionUtil.isEmpty(answerList)){
            List<ExamQuestion> allByQuestionId = iExamQuestionService.findAllByQuestionId(questionIdList);
            for (ExamQuestion examQuestion : allByQuestionId) {
                List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
                examQuestionAnswerList.forEach(examQuestionAnswer -> examQuestionAnswer.setIsCorrect(null));
                examQuestion.setAnswerRecordId(usAnswerRecords.get(0).getId());
                if(StringUtil.isNotBlank(sendTime)){
                    examQuestion.setSendTime(sendTime);
                }
                if(StringUtil.isNotBlank(reciveTime)){
                    examQuestion.setReciveTime(reciveTime);
                }
                examQuestion.setIsStart(isStart);
                examQuestion.setAnswerList(examQuestionAnswerList);
                examQuestions.add(examQuestion);
            }
        }

        return JsonResponse.success(examQuestions);
    }


    /**
     * 开始答题
     * @param workType
     * @param source
     * @param currentUserCode
     * @return
     */
    @Override
    public JsonResponse saveRecord(String workType, String source, String currentUserCode,String pmInsId) {
        String answerRecordId="";
        Map<String,Object> map=new HashMap<>();
        List<UsAnswerRecord> usAnswerRecords=new ArrayList<>();
        try {
            //手机端模拟登陆
            if (Constants.SOURCE_M.equals(source)){
                loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
                currentUserCode = rsaEncryptor.decrypt(currentUserCode);
            }
            IUser currentUser = SecurityUtils.getCurrentUser();
            String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();//题库编码字典获取方式
            Integer everyoneQuestionCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneQuestionCount").get(0).getValue());//人人答题数量
            Integer everyoneAnswerCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneAnswerCount").get(0).getValue());//人人答题次数
            Integer everyoneTimeOut =Integer.valueOf(sysDictValueService.findByDictType("everyoneTimeOut").get(0).getValue());//人人答题超时时间
            usAnswerRecords = usAnswerRecordService.findRecordByPmInsId(pmInsId);
        } catch (Exception e) {
           log.error("--->>>saveRecord接口异常，{}",e.toString());
           return JsonResponse.fail("答题开始失败，请联系管理员");
        }
        return JsonResponse.success(usAnswerRecords);
    }

    /**
     * 提交答案接口
     * @param requestParam
     * @param source
     * @param currentUserCode
     * @return
     */
    @Override
    public JsonResponse saveAnswer(Map<String, Object> requestParam, String source, String currentUserCode) {
        JsonResponse jsonResponse = null;
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
            currentUserCode = rsaEncryptor.decrypt(currentUserCode);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();

        String answerRecordId = MapUtil.getStr(requestParam, "answerRecordId");//用户答题记录表主键id
        String questionId = MapUtil.getStr(requestParam, "questionId");//问题ID
        String questionCode = MapUtil.getStr(requestParam, "questionCode");//问题Code
        String chosenAnswer = MapUtil.getStr(requestParam, "chosenAnswer");//用户回答的答案
        String answerTime = MapUtil.getStr(requestParam, "answerTime");//用户回答的所有时间
        String pmInsId = MapUtil.getStr(requestParam, "pmInsId");//主单据ID
        String invitationId = MapUtil.getStr(requestParam, "invitationId");//邀请ID
        Assert.notNull(answerRecordId, "answerRecordId不能为空，检查参数！");
        Assert.notNull(questionId, "questionId不能为空，检查参数！");
        Assert.notNull(questionCode, "questionCode不能为空，检查参数！");
        Assert.notNull("answerTime", "answerTime，检查参数！");//答题用时
        Assert.notNull("pmInsId", "pmInsId，检查参数！");//答题用时
        ExamQuestion examQuestion = iExamQuestionService.findAllByQuestionCode(questionCode);
        if (examQuestion==null){
            return  JsonResponse.fail("未查询到对应试题");
        }
        String key = answerRecordId + "-" + questionCode;
        DistributedRedisLock.lock(key , 10);
        try {
            //判断答题是否正确
            List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
            List<ExamQuestionAnswer> examQuestionAnswers = new ArrayList<>();
            for (ExamQuestionAnswer examQuestionAnswer : examQuestionAnswerList) {
                if(StringUtil.isNotBlank(examQuestionAnswer.getAnswerContent())){
                    if (examQuestionAnswer.getIsCorrect()) {
                        examQuestionAnswers.add(examQuestionAnswer);
                    }
                }
            }

            List<String> collect = new ArrayList<>();
            for (ExamQuestionAnswer examQuestionAnswer : examQuestionAnswers) {
                collect.add(examQuestionAnswer.getAnswerCode());
            }
            String isCorrect="0";
            if(StringUtil.isNotBlank(chosenAnswer)){
                List<String> chosenAnswerList = Arrays.asList(chosenAnswer.split(","));
                Collections.sort(chosenAnswerList);
                Collections.sort(collect);
                if (examQuestionAnswers.size()>0&&chosenAnswerList.equals(collect)){//正确
                    isCorrect="1";
                }
            }
            //查询是否重复提交
            Specification<UsUserAnswers> userAnswersSpecification = Specifications.<UsUserAnswers>and().eq("answerRecordId", answerRecordId).eq("questionCode", questionCode).build();
            List<UsUserAnswers> userAnswersList = userAnswersService.findAllNoPage(userAnswersSpecification);
            UsUserAnswers usUserAnswers = null;
            if (CollectionUtil.isNotEmpty(userAnswersList)) {
                usUserAnswers = userAnswersList.get(0);
            } else {
                usUserAnswers = new UsUserAnswers();
            }
            usUserAnswers.setInvitationId(invitationId);
            usUserAnswers.setPmInsId(pmInsId);
            usUserAnswers.setAnswerRecordId(answerRecordId);
            usUserAnswers.setAnsewersUserName(currentUser.getUsername());
            usUserAnswers.setAnsewersTrueName(currentUser.getTruename());
            usUserAnswers.setQuestionBankCode(knowledgeQuestionBankCode);
            usUserAnswers.setQuestionId(questionId);
            usUserAnswers.setQuestionCode(questionCode);
            usUserAnswers.setChosenAnswer(chosenAnswer);
            usUserAnswers.setTrueAnswer(String.join(",",collect));
            usUserAnswers.setIsCorrect(isCorrect);
            usUserAnswers.setScore(examQuestion.getQuestionScore());
            usUserAnswers.setAnswerTime(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
            usUserAnswers.setWorkType(Constants.ANSWER_RECORD_PERSION);
            BelongInfoTool.setBelongCompanyAndDepartment(usUserAnswers);
            usUserAnswers.setBelongOrgCode(currentUser.getBelongOrgCode());
            usUserAnswers.setBelongOrgName(currentUser.getAuthOrgs().iterator().next().getDisplayName());
            List<UsPendingTask> pendingTaskByStatue = usPendingTaskService.findPendingTaskByStatue(invitationId, "ONGOING");
            for (UsPendingTask usPendingTask1 : pendingTaskByStatue) {
                if(currentUser.getUsername().equals(usPendingTask1.getPendUserName())){
                    usUserAnswers.setPmInsId(usPendingTask1.getPmInsId());
                }
            }
            if (StrUtil.isNotEmpty(usUserAnswers.getId())) {
                userAnswersService.update(usUserAnswers);
            } else {
                userAnswersService.insert(usUserAnswers);
            }
            List<UsQuizSessions> usQuizSessions = usQuizSessionsService.findAndUpdateSeeeion(DateUtil.getCurrentStr(),invitationId);
            if(CollectionUtil.isNotEmpty(usQuizSessions)){
                for (UsQuizSessions usQuizSession : usQuizSessions) {
                    if(currentUser.getUsername().equals(usQuizSession.getRecUserName())){
                        if(StringUtil.isBlank(usUserAnswers.getScore())){
                            usQuizSession.setReciveScoure(Integer.valueOf(usUserAnswers.getScore()));
                        }else{
                            if(null!=usQuizSession.getReciveScoure()){
                                usQuizSession.setReciveScoure(Integer.valueOf(usUserAnswers.getScore())+Integer.valueOf(usQuizSession.getReciveScoure()));
                            }else{
                                usQuizSession.setReciveScoure(Integer.valueOf(usUserAnswers.getScore())+Integer.valueOf(examQuestion.getQuestionScore()));
                            }
                        }
                        usQuizSession.setReciveTime(answerTime);
                    }else{
                        if(StringUtil.isBlank(usUserAnswers.getScore())){
                            usQuizSession.setSendScoure(Integer.valueOf(usUserAnswers.getScore()));
                        }else{
                            if(null!=usQuizSession.getSendScoure()){
                                usQuizSession.setSendScoure(Integer.valueOf(usUserAnswers.getScore())+Integer.valueOf(usQuizSession.getSendScoure()));
                            }else{
                                usQuizSession.setSendScoure(Integer.valueOf(usUserAnswers.getScore())+Integer.valueOf(examQuestion.getQuestionScore()));
                            }

                        }
                        usQuizSession.setSendTime(answerTime);
                    }
                    if(StringUtil.isBlank(usQuizSession.getStartTime())){
                        usQuizSession.setStartTime(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
                    }
                    usQuizSession.setUpdatedAt(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
                    usQuizSessionsService.update(usQuizSession);
                }
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("isCorrect",isCorrect);
            resultMap.put("realAnswer",examQuestionAnswers);
            jsonResponse = JsonResponse.success(resultMap);
        } catch (Exception e ) {
            Exceptions.printException(e);
            jsonResponse = JsonResponse.fail(-1 , "操作失败！");
        } finally {
            DistributedRedisLock.unlock(key);
        }
        return jsonResponse;
    }

    /**
     * 提交答案--最后一题的处理
     *
     * @param requestParam
     * @param source
     * @param currentUserCode
     * @return
     */
    @Override
    public JsonResponse saveLastAnswer(Map<String, Object> requestParam, String source, String currentUserCode) {
        JsonResponse jsonResponse = null;
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
            currentUserCode = rsaEncryptor.decrypt(currentUserCode);
        }
        String msgResult="";
        IUser currentUser = SecurityUtils.getCurrentUser();
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();//题库编码字典获取方式
        Integer everyoneQuestionCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneQuestionCount").get(0).getValue());//人人答题数量
        Integer everyoneAnswerCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneAnswerCount").get(0).getValue());//人人答题次数
        Integer everyoneTimeOut =Integer.valueOf(sysDictValueService.findByDictType("everyoneTimeOut").get(0).getValue());//人人答题超时时间

        String answerRecordId = MapUtil.getStr(requestParam, "answerRecordId");//用户答题记录表主键id
        String questionId = MapUtil.getStr(requestParam, "questionId");//问题ID
        String questionCode = MapUtil.getStr(requestParam, "questionCode");//问题Code
        String chosenAnswer = MapUtil.getStr(requestParam, "chosenAnswer");//用户回答的答案
        String answerTime = MapUtil.getStr(requestParam, "answerTime");//用户回答的答案
        String pmInsId = MapUtil.getStr(requestParam, "pmInsId");//用户回答的答案
        String invitaitonId = MapUtil.getStr(requestParam, "invitationId");//邀请ID
        Assert.notNull(answerRecordId, "answerRecordId不能为空，检查参数！");
        Assert.notNull(questionId, "questionId不能为空，检查参数！");
        Assert.notNull(questionCode, "questionCode不能为空，检查参数！");
        Assert.notNull("answerTime", "answerTime，检查参数！");//答题用时
        Assert.notNull("pmInsId", "pmInsId，检查参数！");//答题用时
        ExamQuestion examQuestion = iExamQuestionService.findAllByQuestionCode(questionCode);
        if (examQuestion==null){
            return  JsonResponse.fail("未查询到对应试题");
        }
        DistributedRedisLock.lock(answerRecordId + "-" + questionCode , 10);
        try {
            //判断答题是否正确
            List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
            List<ExamQuestionAnswer> examQuestionAnswers = examQuestionAnswerList.stream().filter(examQuestionAnswer -> true == examQuestionAnswer.getIsCorrect()).collect(Collectors.toList());
            List<String> collect = examQuestionAnswers.stream().map(examQuestionAnswer -> examQuestionAnswer.getAnswerCode()).collect(Collectors.toList());
            String isCorrect="0";
            if(StringUtil.isNotBlank(chosenAnswer)){
                List<String> chosenAnswerList = Arrays.asList(chosenAnswer.split(","));
                Collections.sort(chosenAnswerList);
                Collections.sort(collect);
                if (examQuestionAnswers.size()>0&&chosenAnswerList.equals(collect)){//正确
                    isCorrect="1";
                }
            }
            //
            List<UsInvitations> usInvitationsList=usInvitationsService.findByPmInsId(pmInsId);

            if (usInvitationsList.size()>0){
                for (UsInvitations usInvitations : usInvitationsList) {
                    //确认两人是否都已经打完了。
                    Specification<UsPendingTask> build = Specifications.<UsPendingTask>and()
                            .eq("enabled", Boolean.TRUE)
                            .eq("invitationId", usInvitationsList.get(0).getId())
                            .in("status" , "ACCEPTED" , "PENDING" , "ONGOING")
                            .build();
                    List<UsPendingTask> allNoPage = usPendingTaskService.findAllNoPage(build);
                    if (allNoPage.size() == 1 ) {
                        usInvitations.setStatus("FINISHED"); //'Pending'：待接收,'ACCEPTED'：已接受,'Expired'：过期  'refuse'：拒绝, 'ONGOING' 继续答题   'FINISHED'
                        this.usInvitationsService.update(usInvitations);
                    }
                }
            }

            //查询是否重复提交
            Specification<UsUserAnswers> userAnswersSpecification = Specifications.<UsUserAnswers>and().eq("answerRecordId", answerRecordId).eq("questionCode", questionCode).build();
            List<UsUserAnswers> userAnswersList = userAnswersService.findAllNoPage(userAnswersSpecification);
            UsUserAnswers usUserAnswers = null;
            if (CollectionUtil.isNotEmpty(userAnswersList)) {
                usUserAnswers = userAnswersList.get(0);
            } else {
                usUserAnswers = new UsUserAnswers();
            }
            usUserAnswers.setInvitationId(invitaitonId);
            usUserAnswers.setAnswerRecordId(answerRecordId);
            usUserAnswers.setAnsewersUserName(currentUser.getUsername());
            usUserAnswers.setAnsewersTrueName(currentUser.getTruename());
            usUserAnswers.setQuestionBankCode(knowledgeQuestionBankCode);
            usUserAnswers.setQuestionId(questionId);
            usUserAnswers.setQuestionCode(questionCode);
            usUserAnswers.setChosenAnswer(chosenAnswer);
            usUserAnswers.setTrueAnswer(String.join(",",collect));
            usUserAnswers.setIsCorrect(isCorrect);
            usUserAnswers.setScore(examQuestion.getQuestionScore());
            usUserAnswers.setAnswerTime(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
            usUserAnswers.setWorkType(Constants.ANSWER_RECORD_PERSION);
            BelongInfoTool.setBelongCompanyAndDepartment(usUserAnswers);
            usUserAnswers.setBelongOrgCode(currentUser.getBelongOrgCode());
            usUserAnswers.setBelongOrgName(currentUser.getAuthOrgs().iterator().next().getDisplayName());
            if (StrUtil.isNotEmpty(usUserAnswers.getId())) {
                userAnswersService.update(usUserAnswers);
            } else {
                userAnswersService.insert(usUserAnswers);
            }
            //待办状态更新
            List<UsPendingTask> pendingTaskByStatue = usPendingTaskService.findPendingTaskByStatue(invitaitonId, "ONGOING");
            for (UsPendingTask usPendingTask : pendingTaskByStatue) {
                if(currentUser.getUsername().equals(usPendingTask.getPendUserName())){
                    usPendingTask.setStatus("COMPLETED");
                    usPendingTask.setAnswerTime(answerTime);
                    usPendingTaskService.update(usPendingTask);
                    usUserAnswers.setPmInsId(usPendingTask.getPmInsId());
                    userAnswersService.update(usUserAnswers);
                    //核销待办
                    usPendingTaskService.cancleOpenTodo(usPendingTask);
                }
            }
            List<UsQuizSessions> usQuizSessions = usQuizSessionsService.findAndUpdateSeeeion(DateUtil.getCurrentStr(),invitaitonId);
            if(CollectionUtil.isNotEmpty(usQuizSessions)){
                for (UsQuizSessions usQuizSession : usQuizSessions) {
                    if(currentUser.getUsername().equals(usQuizSession.getRecUserName())){
                        if(StringUtil.isBlank(usUserAnswers.getScore())){
                            usQuizSession.setReciveScoure(Integer.valueOf(usUserAnswers.getScore()));
                        }else{
                            if(null!=usQuizSession.getReciveScoure()){
                                usQuizSession.setReciveScoure(Integer.valueOf(usUserAnswers.getScore())+Integer.valueOf(usQuizSession.getReciveScoure()));
                            }else{
                                usQuizSession.setReciveScoure(Integer.valueOf(usUserAnswers.getScore())+Integer.valueOf(examQuestion.getQuestionScore()));
                            }
                        }
                        usQuizSession.setRecCompleted("COMPLETED");
                        usQuizSession.setReciveTime(answerTime);
                    }else{
                        if(StringUtil.isBlank(usUserAnswers.getScore())){
                            usQuizSession.setSendScoure(Integer.valueOf(usUserAnswers.getScore()));
                        }else{
                            if(null!=usQuizSession.getSendScoure()){
                                usQuizSession.setSendScoure(Integer.valueOf(usUserAnswers.getScore())+Integer.valueOf(usQuizSession.getSendScoure()));
                            }else{
                                usQuizSession.setSendScoure(Integer.valueOf(usUserAnswers.getScore())+Integer.valueOf(examQuestion.getQuestionScore()));
                            }

                        }
                        usQuizSession.setSendTime(answerTime);
                        usQuizSession.setSendCompleted("COMPLETED");
                    }
                    if(StringUtil.isBlank(usQuizSession.getStartTime())){
                        usQuizSession.setStartTime(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
                    }else{
                        usQuizSession.setEndTime(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
                    }
                    usQuizSession.setUpdatedAt(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
                    usQuizSessionsService.update(usQuizSession);
                    if("COMPLETED".equals(usQuizSession.getSendCompleted())&&"COMPLETED".equals(usQuizSession.getRecCompleted())){
                        usQuizSession.setStatus("COMPLETED");
                        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                        Map map = new HashMap();
                        map.put("PARTICIPANT", usQuizSession.getSendUserName());
                        map.put("PARTI_NAME", usQuizSession.getSendTrueName());
                        map.put("RECEIPT_TITLE", "2024年合规知识竞赛人人对战");
                        list.add(map);
                        Map secondMap = new HashMap();
                        secondMap.put("PARTICIPANT", usQuizSession.getRecUserName());
                        secondMap.put("PARTI_NAME", usQuizSession.getRecTrueName());
                        secondMap.put("RECEIPT_TITLE", "2024年合规知识竞赛人人对战");
                        list.add(secondMap);
                        //发送邀请的用户答题正确个数
                        List<UsUserAnswers> sendRightList = userAnswersService.getRightCount(DateUtil.getCurrentStr(), "C", knowledgeQuestionBankCode, invitaitonId, usQuizSession.getSendUserName());
                        //接受邀请的用户答题正确个数
                        List<UsUserAnswers> reciveRightList = userAnswersService.getRightCount(DateUtil.getCurrentStr(), "C", knowledgeQuestionBankCode, invitaitonId, usQuizSession.getRecUserName());
//                    String spendTime="";
//                    String recRpendTime="";
//                    String sendAnswerRecordId="";
//                    String recAnswerRecordId="";
//                    List<UsAnswerRecord> usAnswerRecordList = usAnswerRecordService.findRecordByInvitaitonId(invitaitonId);
//                    for (UsAnswerRecord usAnswerRecord : usAnswerRecordList) {
//                        if(usAnswerRecord.getSendUserName().equals(currentUser.getUsername())){
//                            Map<String,Object>   timeByRecordByUser= userAnswersService.findListByAnswerRecordId(usAnswerRecord.getId());
//                            if (map.get("MAXTIME")!=null){
//                                DateTime maxTime = DateUtil.getJodaDateTime(map.get("MAXTIME") + "", "yyyy-MM-dd HH:mm:ss");
//                                DateTime minTime = DateUtil.getJodaDateTime(map.get("MINTIME") + "", "yyyy-MM-dd HH:mm:ss");
//                                // 将毫秒转换为分钟和秒
//                                Duration duration =new Duration(minTime, maxTime);
//                                long totalSeconds = duration.getStandardSeconds();
//                                long minutes = totalSeconds / 60;
//                                long seconds = totalSeconds % 60;
//                                // 格式化输出
//                                String formattedTime = String.format("%d.%02d", minutes, seconds);
//                                spendTime=formattedTime;
//                            }
//                        }
//                    }

//                    //发送邀请的用户答题耗时
                        List<Map<String, Object>> sendTimeUSer = userAnswersService.getTimeByRecordByUser("C", knowledgeQuestionBankCode, invitaitonId, usQuizSession.getSendUserName());
                        String sentTime = formateTime(sendTimeUSer);
                        // 接受邀请的用户答题耗时
                        List<Map<String, Object>> recTimeUSer = userAnswersService.getTimeByRecordByUser("C", knowledgeQuestionBankCode, invitaitonId, usQuizSession.getRecUserName());
                        String recTime = formateTime(recTimeUSer);
                        msgResult="【2024年度合规知识竞赛】您好，您本次参与的人人对战已结束，对战结果如下：【"
                                +usQuizSession.getSendTrueName()+"】答对"+(sendRightList==null?0:sendRightList.size())+"道，得分"+(sendRightList==null?0:sendRightList.size())*10+"分，用时"
                                +sentTime+"；【"
                                +usQuizSession.getRecTrueName()+"】答对"+(reciveRightList==null?0:reciveRightList.size())+"道，得分"+(reciveRightList==null?0:reciveRightList.size())*10+"分，用时"
                                +recTime+"，请知悉！";
                       usEveryOneQuestionsService.sendShortMessage(source, currentUserCode, list, msgResult);
                    }
                }
            }
            if(StringUtil.isNotBlank(pmInsId)){
                List<UsAnswerRecord> recordByPmInsId = usAnswerRecordService.findRecordByPmInsId(pmInsId);
                if(CollectionUtil.isNotEmpty(recordByPmInsId)){
                    for (UsAnswerRecord usAnswerRecord : recordByPmInsId) {

                        Map<String,Object>  map= userAnswersService.findListByAnswerRecordId(answerRecordId);
                        DateTime minTime = DateUtil.getJodaDateTime(map.get("MINTIME") + "", "yyyy-MM-dd HH:mm:ss");
                        // 将毫秒转换为分钟和秒
                        Date date = new Date();
                        DateTime dateTime = new DateTime(date);
                        Duration duration =new Duration(minTime, dateTime);
                        usAnswerRecord.setDuration(duration.getMillis());
                        usAnswerRecord.setEndTime(DateUtil.getDate(date,"yyyy-MM-dd HH:mm:ss"));
                        usAnswerRecordService.update(usAnswerRecord);
                    }
                }
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("isCorrect",isCorrect);
            resultMap.put("realAnswer",examQuestionAnswers);
            jsonResponse = JsonResponse.success(resultMap);
        } catch (Exception e ) {
            Exceptions.printException(e);
            jsonResponse = JsonResponse.fail(-1 , "操作失败!");
        } finally {
            DistributedRedisLock.unlock(answerRecordId + "-" + questionCode);
        }

        return jsonResponse;
    }

    /**
     * 完成答题后推送短信信息给双方
     *
     * @param source
     * @param currentUserCode
     * @return
     */
    @Override
    public JsonResponse sendShortMessage(String source, String currentUserCode, List<Map<String, Object>> list, String reslut) {
        Boolean isPostMsgOK = false;
        Boolean isPostMsg = false;   //false 短信不发送     true 发送
        try{
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE,"hadmin3" );
            if ( simpleApp != null ){
                isPostMsg = simpleApp.getIsSendMsg();
            }
            if ( isPostMsg ) {
                String msg = reslut;
                /**准备参数**/
                for ( Map<String,Object> map : list ){
                    Map<String,Object> mapParam = Maps.newHashMap();
                    String sendUser = map.get("PARTICIPANT") != null ? map.get("PARTICIPANT").toString():"";
                    String itemSubject = map.get("RECEIPT_TITLE") != null ? map.get("RECEIPT_TITLE").toString():"";
                    mapParam.put("sendUser", sendUser);
                    mapParam.put("fromUser", "hadmin3");
                    mapParam.put("itemSubject", itemSubject);
                    mapParam.put("msg", msg);
                    log.warn("调用sendSMS接口----------参数mapParam="+mapParam.toString());
                    log.warn("调用sendSMS接口----------参数mapParam="+mapParam.toString());
                    isPostMsgOK = smsUtil.postMsgWithAnddoc(smsUtil.readyParams(mapParam));

                }
            }
        }catch ( Exception e ){
            Exceptions.printException( e );
        }
        return JsonResponse.success(isPostMsgOK ,isPostMsgOK?"发送成功":"发送失败");
    }


    public String formateTime(List<Map<String, Object>> list){
        String spendTime="";
        String serultTime="";
       for (Map<String, Object> map : list) {
            if (map.get("MAXTIME")!=null){
                DateTime maxTime = DateUtil.getJodaDateTime(map.get("MAXTIME") + "", "yyyy-MM-dd HH:mm:ss");
                DateTime minTime = DateUtil.getJodaDateTime(map.get("MINTIME") + "", "yyyy-MM-dd HH:mm:ss");
                // 将毫秒转换为分钟和秒
                Duration duration =new Duration(minTime, maxTime);
                long totalSeconds = duration.getStandardSeconds();
                long minutes = totalSeconds / 60;
                long seconds = totalSeconds % 60;
                //格式化输出
                String formattedTime = String.format("%d.%02d", minutes, seconds);
                spendTime=formattedTime;
                // 截取小数点前的整数部分作为分钟
                String minutesNew = spendTime.substring(0, spendTime.indexOf('.'));
                // 截取小数点后的数字部分作为秒
                String secondsNew = spendTime.substring(spendTime.indexOf('.') + 1);
                serultTime=minutesNew + "分" + secondsNew + "秒";
            }
        }
       return serultTime;
    }
}
