<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org" style="height: 100%;">

<head>
    <title>在线考试</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
    <meta name="format-detaction" content="telephone=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
          rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/moaBridge_oa_wjdc.js?v=svn.revision" type="text/javascript"></script>
    <style>
        #piyueScore {
            width: unset;
            border-color: gray;
        }

        .twentyone {
            display: none;
        }
    </style>
    <script type="text/javascript">
        var examLists = [];
        var examAppCode = '';
        var questionBlankCode = '';
        var currentAction = '';
        $(function () {
            //console.log("123");
            //getCurrent();
            //console.log("4561");
            // 获取试卷模板
            var tempCurrentUserCode = gps.currentUserCode ? gps.currentUserCode : gps.username
            if (gps.actionType == "secrecyJoin") $("#submit").hide();
            //console.log("7892");

            // var url = 'action/examAttribute/constructExamLayout?currentUserCode=' + tempCurrentUserCode;
            var url = 'action/examAttribute/sso/constructExamLayoutTWO?loginuser=' + tempCurrentUserCode;
            //console.log("tempCurrentUserCode=="+tempCurrentUserCode);
            if (gps.appcode) {
                //console.log("aaaaaaaaaaaaa");
                // url = web.rootdir + 'action/examAttribute/sso/constructExamLayoutTWO?currentUserCode=' + tempCurrentUserCode + '&uid=' + tempCurrentUserCode + '&appcode=' + gps.appcode + '&source=MOBILE'
                url = web.rootdir +  'action/examAttribute/sso/constructExamLayoutTWO?loginuser=' + tempCurrentUserCode + '&appcode=' + gps.appcode + '&source=MOBILE'
                //console.log("bbbbbbbbbbbb");
            }
            //console.log(url)
            if (gps.actionType == 'secrecyJoin') url = 'action/examAttribute/constructExamLayoutAndAnswer?currentUserCode=' + tempCurrentUserCode

            ajaxgeneral({
                url: url,
                data: {"examAppCode": gps.examAppCode},
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    $(".explain").html(res.data.examName);
                    examAppCode = res.data.examAppCode;
                    examLists = res.data.singleQuestionList;
                    questionBlankCode = res.data.questionBankCode;
                    currentAction = "test";
                    showQuestions(currentAction, examLists)
                }
            })

            // 显示试卷
            function showQuestions(type, data) { // type的值：test测试；reTest重测
                if (data) {
                    var qid = 1;
                    var questions = data;
                    questions.sort(function (a, b) {
                        return a.questionOrder - b.questionOrder
                    })
                    //单选
                    for (var i = 0; i < questions.length; i++) {
                        if (i != 20) {
                            var part = $("<div>").addClass("part singleQues").appendTo($(".questions"));
                        } else {
                            var part = $("<div>").addClass("part singleQues twentyone").appendTo($(".questions"));
                        }
                        var main = $("<div>").addClass("main").appendTo(part);
                        if (i != 20) {
                            var h6 = $("<h6 class=" + questions[i].id + ">").html(qid + "、" + questions[i].questionName).appendTo(main);
                        } else {
                            var h6 = $("<h6 class=" + questions[i].id + ">").html(questions[i].questionName).appendTo(main);
                        }

                        var ul = $("<ul>").appendTo(main);
                        if (questions[i].answerList && questions[i].answerList.length > 0) {
                            for (var j = 0; j < questions[i].answerList.length; j++) {
                                var answer = questions[i].answer ? questions[i].answer.split(',') : [];
                                if (i == 13 || i == 14 || i == 16 || i == 20) {
                                    if (answer.indexOf(questions[i].answerList[j].answerCode) > -1) {
                                        var li = $("<li dindex='" + i + "' qindex='" + j + "'>").appendTo(ul);
                                        var input = $("<input>").attr({
                                            type: 'checkbox',
                                            id: questions[i].answerList[j].id,
                                            name: questions[i].answerList[j].questionCode,
                                            value: questions[i].answerList[j].answerCode,
                                            checked: true
                                        }).appendTo(li);
                                    } else {
                                        var li = $("<li dindex='" + i + "' qindex='" + j + "'>").appendTo(ul);
                                        var input = $("<input>").attr({
                                            type: 'checkbox',
                                            id: questions[i].answerList[j].id,
                                            name: questions[i].answerList[j].questionCode,
                                            value: questions[i].answerList[j].answerCode
                                        }).appendTo(li);
                                    }
                                } else {
                                    var li = $("<li dindex='" + i + "' qindex='" + j + "'>").appendTo(ul);
                                    if (answer.indexOf(questions[i].answerList[j].answerCode) > -1) {
                                        var input = $("<input>").attr({
                                            type: 'radio',
                                            id: questions[i].answerList[j].id,
                                            name: questions[i].answerList[j].questionCode,
                                            value: questions[i].answerList[j].answerCode,
                                            checked: true
                                        }).appendTo(li);
                                    } else {
                                        var input = $("<input>").attr({
                                            type: 'radio',
                                            id: questions[i].answerList[j].id,
                                            name: questions[i].answerList[j].questionCode,
                                            value: questions[i].answerList[j].answerCode
                                        }).appendTo(li);
                                    }
                                }
                                var label = $("<label>").attr("for", questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode + "：" + questions[i].answerList[j].answerContent).appendTo(li);

                                // 判断是否添加input
                                if (examLists[i].answerList[j].answerContent.indexOf('具体说明') > -1) {
                                    if (answer.indexOf(questions[i].answerList[j].answerCode) > -1) {
                                        var input1 = $("<input class='qmessage' style='width: 100%;margin: 20px 0 0 0;' placeholder='请填写具体说明' value='" + questions[i].message + "'>").appendTo(li)
                                    } else {
                                        var input1 = $("<input class='qmessage' style='display: none; width: 100%;margin: 20px 0 0 0;' placeholder='请填写具体说明'>").appendTo(li)
                                    }
                                }
                            }
                        }
                        qid++;
                    }
                    var twentyone = $('.twentyone').clone();
                    $('.questions .singleQues').eq(1).after(twentyone);
                    var twoAnswer = $('.questions .singleQues').eq(1).find('input[checked]').attr('value')
                    if (twoAnswer == 'C' || twoAnswer == 'D') {
                        $('.twentyone').eq(0).show();
                    }


                    // input和textarea的事件
                    $("input").on("click", function () {
                        if (gps.actionType == "secrecyJoin") return;
                        // 单选和判断的高亮、isSelected字段控制
                        var dindex = $(this).parent("li").attr('dindex');
                        var ddindex = Number(dindex) <= 1 ? Number(dindex) : Number(dindex) + 1;
                        ddindex = ddindex == 21 ? 2 : ddindex;
                        var qindex = $(this).parent("li").attr('qindex');
                        if ((dindex == 1 && qindex == 2) || (dindex == 1 && qindex == 3)) {
                            $('.twentyone').eq(0).show();
                        }

                        if ((dindex == 1 && qindex == 0) || (dindex == 1 && qindex == 1) || (dindex == 1 && qindex == 4)) {
                            $('.twentyone').hide();
                        }
                        if ($(this).attr("type") && $(this).attr("type") == "radio") {
                            $(this).parent("li").siblings().removeClass("active");
                            $(this).parent("li").addClass("active");
                            for (let i = 0; i < examLists[dindex].answerList.length; i++) {
                                examLists[dindex].answerList[i].isSelected = false;
                                $(".singleQues .main").eq(ddindex).find("li").eq(i).children('.qmessage').hide();
                            }
                            $(".singleQues .main").eq(ddindex).find("li").eq(qindex).children('.qmessage').show();
                            examLists[dindex].answerList[qindex].isSelected = true;
                        }
                        ;

                        // 多选的高亮、isSelected控制
                        if ($(this).attr("type") && $(this).attr("type") == "checkbox") {
                            if (!$(this).parent("li").hasClass("active")) {
                                $(this).parent("li").addClass("active");
                            } else {
                                $(this).parent("li").removeClass("active");
                            }
                            for (let i = 0; i < examLists[dindex].answerList.length; i++) {
                                if ($(".singleQues .main").eq(ddindex).find("li").eq(i).hasClass("active")) {
                                    // 选中时判断是否显示input
                                    $(".singleQues .main").eq(ddindex).find("li").eq(i).children('.qmessage').show();
                                    examLists[dindex].answerList[i].isSelected = true;
                                } else {
                                    $(".singleQues .main").eq(ddindex).find("li").eq(i).children('.qmessage').hide();
                                    examLists[dindex].answerList[i].isSelected = false;
                                }
                            }
                        }
                        ;

                        // // 选中时判断是否添加input
                        // if ($(this).parent("li").hasClass("active") && $(this).parent("li").children('.qmessage').length > 0) {
                        // 	$(this).parent("li").children('.qmessage').show();
                        // } else {
                        // 	$(this).parent("li").children('.qmessage').hide();
                        // }


                        var checkLength = 0;
                        var length1 = examLists.length;
                        if (!(examLists[1].answerList[2].isSelected || examLists[1].answerList[3].isSelected)) {
                            length1 = examLists.length - 1;
                            for (let i = 0; i < examLists[20].answerList.length; i++) {
                                examLists[20].answerList[i].isSelected = false;
                            }
                        }
                        ;
                        for (let i = 0; i < examLists.length; i++) {
                            var length = 0
                            for (let j = 0; j < examLists[i].answerList.length; j++) {
                                if (examLists[i].answerList[j].isSelected) length++;
                            }
                            // if(i == 20) length++;
                            if (length > 0) checkLength++;
                        }


                        if (length1 == checkLength) {
                            $("#submit").addClass("canSubmit");
                        } else {
                            $("#submit").removeClass("canSubmit");
                        }
                    });

                    $('.qmessage').blur(function () {
                        if (gps.actionType == "secrecyJoin") return;
                        if ($(this).val()) {
                            var dindex = $(this).parent("li").attr('dindex');
                            var qindex = $(this).parent("li").attr('qindex');
                            examLists[dindex].answerList[qindex].message = $(this).val();
                        }
                    })
                    if (gps.actionType == "secrecyJoin") $('input').attr('disabled', true)
                } else {
                    getparent().mesShow("温馨提示", "试卷获取失败,请联系系统管理员!!!", 2000, 'red');
                }
            }

            // 点“提交”
            $("#submit").click(function () {
                if ($(this).hasClass('canSubmit')) {
                    submitData();
                } else {
                    getparent().mesShow("温馨提示", "试卷未答完，请继续答题！", 2000, 'red');
                }
            });

            $("#sureSubmit").click(function () {
                submitData();
            });

            // 提交答案
            function submitData() {
                var list = [];
                for (var i = 0; i < examLists.length; i++) {
                    var item = {
                        questionCode: examLists[i].questionCode,
                        questionIndex: i + 1
                    };
                    var examAnswer = [];
                    for (var j = 0; j < examLists[i].answerList.length; j++) {
                        if (examLists[i].answerList[j].isSelected) {
                            examAnswer.push(examLists[i].answerList[j].answerCode)
                            if (examLists[i].answerList[j].message) {
                                item.message = examLists[i].answerList[j].message;
                            }
                        }
                    }
                    ;
                    item.examAnswer = examAnswer.join(',');
                    list.push(item);
                }
                ajaxgeneral({
                    url: 'action/examInformation/submitExam',// 调用判断方法 实际不判断直接保存记录
                    data: {
                        examCode: gps.examCode,
                        examAppCode: gps.examAppCode,
                        publishUsername: web.currentUser.username,
                        examInformationList: list,
                        questionBlankCode: questionBlankCode,
                    },
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        $("#scoreDialog h5").html("提交成功！");
                        $("#yes").show();
                        window.close();
                        $("#submitDialog").dialog({closed: true});
                        $("#scoreDialog").dialog({closed: false});
                    }
                })
            }

            // 全答对时关闭弹框
            $("#yes").click(function () {
                $("#scoreDialog").dialog({closed: true});
                if (gps.actionType && gps.actionType == "secrecyTask") {
                    top.dialogClose("detail");
                } else {
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                    moaBridge.close();
                }
            });


            // 试卷已完成时，关闭页面
            $("#closeBtns button").click(function () {
                $('#closeDialog').dialog('close');
            })
        })
    </script>
    <style type="text/css">
        /*公共样式*/
        .clearfix:after {
            content: '.';
            display: block;
            height: 0;
            line-height: 0;
            clear: both;
            visibility: hidden;
        }

        .clearfix {
            zoom: 1;
        }

        .w15 {
            width: 15%;
        }

        /*页面样式*/
        /*背景颜色*/
        body {
            background-image: url("examPic.png");
            /*background-position: center center;*/
            background-repeat: no-repeat;
            background-size: 100% 100%;
            /*background-size:contain;*/
            opacity: 0.7;
            margin: 0px;
            padding: 0px;
        }

        .intext {
            text-indent: 0.15rem;
            margin: 0.3rem auto;
        }

        .wrapper {
            width: 85%;
            margin: 0 auto;
            background-color: #fff;
            color: #000;
        }

        .header {
            text-align: center;
        }

        .header,
        .header img {
            width: 100%;
        }

        .details {
            width: 70%;
            padding: 10px;
            font-size: 16px;
            margin: auto;
        }

        .explain {
            /*line-height:34px;*/
            margin-top: 10px;
            /*heigh:60px;*/
            font-weight: bolder;
            font-size: 20px;
            text-align: center;
            width: 98%;
            margin: 0 auto;
            /*padding-right:0.3rem;*/
            color: #D90000;
        }

        .questions {
            padding-bottom: 20px;
        }

        .questionType {
            font-size: 20px;
            font-weight: bold;
            line-height: 1.2;
            margin-top: 20px;
        }

        .main,
        .main ul {
            padding: 0 22px;
        }

        .shortAnswer .main ul {
            padding: 0 10px;
        }

        .main ul {
            background: #F5F5F5;
        }

        .main h6 {
            font-size: 16px;
            line-height: 1.5;
            margin: 25px 0;
            font-weight: 600;
        }

        .main li {
            line-height: 1.5;
            padding: 15px 10px;
            display: block;
            font-weight: 400;
        }

        .main li.fl {
            margin-top: 0;
        }

        .main li input[type=radio]:checked:before {
            background: #D90000;

        }

        .main .active input:focus {
            outline-color: red;
        }

        .main .active {
            color: #D90000;
        }

        .main .green {
            color: #09DB87;
        }

        .main .red {
            color: #E11414;
        }

        .main input {
            width: auto;
        }

        .main label {
            margin-left: 10px;
        }

        .shortAnswer .main textarea {
            min-height: 160px;
            font-size: 14px;
        }

        .icon-duihao1 {
            font-size: 16px;
            margin-left: 4px;
        }

        .icon-cuo1 {
            font-size: 14px;
            font-weight: bold;
            margin-left: 4px;
        }

        .submitBtn,
        .submitPiyueScore {
            border: 0;
            outline: 0;
            width: 90px;
            height: 36px;
            background: #B4B4B4;
            border-radius: 4px;
            font-size: 14px;
            color: #fff;
            margin: 10px 0 0 60px;
            letter-spacing: 2px;
        }

        .submitBtn:active {
            opacity: .85;
        }

        .canSubmit {
            background-color: #E83333;
        }

        .dialog h5 {
            font-size: 15px;
            font-weight: bold;
            text-align: center;
            margin-top: 10px;
        }

        .forceSubmitDialog p {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-top: 20px;
        }

        .scoreDialog p {
            font-size: 12px;
            text-align: center;
        }

        .submitBtns button {
            border: 0;
            outline: 0;
            padding: 0;
            margin: 0;
            height: 32px;
            font-size: 12px;
            color: #fff;
            text-align: center;
            border-radius: 4px;
            padding: 0 20px !important;
        }

        .submitBtns .gray {
            background-color: #B4B4B4;
        }

        .submitBtns .red {
            background-color: #E11414;
        }

        .remainTime {
            font-size: 15px;
            font-weight: bold;
            margin-top: 20px;
            text-align: right;
        }

        .examTime {
            position: fixed;
            top: 100px;
            right: 40px;
            font-size: 24px
        }

        .examTime h3 {
            color: red
        }
    </style>
</head>

<body style="height: 100%;">
<div class="wrapper">

    <div class="details">
        <p class="explain"></p>

        <div id="remainTime" class="remainTime"></div>
        <div class="questions">

        </div>
        <!-- 提交 -->
        <button class="submitBtn" id="submit">提交</button>
        <!-- 提交对话框 -->
        <div id="submitDialog" class="easyui-dialog dialog" title="温馨提示" closed="true"
             data-options="modal:true,buttons:'#submitBtns'" style="width:400px;height:200px;padding:10px">
            <h5>您已答完所有题，确认提交？</h5>
        </div>
        <div class="btns submitBtns" id="submitBtns" style="text-align:center;">
            <button class="easyui-linkbutton gray" onclick="$('#submitDialog').dialog('close')">取消</button>
            <button id="sureSubmit" class="easyui-linkbutton red">是的确认提交！</button>
        </div>

        <!-- 提交成功对话框 -->
        <div id="scoreDialog" class="easyui-dialog dialog scoreDialog" title="温馨提示" closed="true"
             data-options="modal:true,buttons:'#scoreBtns'" style="width:400px;height:200px;padding:10px">
            <h5></h5>
            <!--<p>（注：简答题不计入合格分数，只计入参考分值）</p>-->
        </div>
        <div class="submitBtns scoreBtns" id="scoreBtns" style="text-align:center;">
            <button id="yes" class="easyui-linkbutton red hide">确定</button>
        </div>

        <!-- 考试结束对话框 -->
        <div id="examOverDialog" class="easyui-dialog dialog examOverDialog" title="温馨提示" closed="true"
             data-options="modal:true,buttons:'#examOverBtns'" style="width:400px;height:200px;padding:10px">
            <h5>答卷时间已到，试卷自动提交，如已过考试参与时间则自动提交失败！</h5>
            <!--<p>（注：简答题不计入合格分数，只计入参考分值）</p>-->
        </div>
        <div class="submitBtns" id="examOverBtns" style="text-align:center;">
            <button id="examOver" class="easyui-linkbutton red ">确定</button>
        </div>

        <!-- 打开试卷时，试卷已完成，关闭页面 -->
        <div id="closeDialog" class="easyui-dialog dialog closeDialog" title="温馨提示" closed="true"
             data-options="modal:true,buttons:'#closeBtns'" style="width:400px;height:200px;padding:10px">
            <h5>您已完成调查问卷，感谢您的参与！</h5>
        </div>
        <div class="submitBtns" id="closeBtns" style="text-align:center;">
            <button class="easyui-linkbutton red">确定</button>
        </div>
    </div>
</div>
</body>

</html>