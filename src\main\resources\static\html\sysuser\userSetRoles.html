﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>单用户赋角色</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .role{
            width: 275px!important;
            height: 345px!important;}
    </style>
    <script type="text/javascript">
	var gps=getQueryString();
	window.getchoosedata=function(){
		var ids=[];
		$(".role_choose a").each(function(i,v){
			ids.push($(v).attr("id"));
		});
		return {"data":ids,"state":1,"username":gps.username};
	};
	$(function(){
		//所有业务角色
		ajaxgeneral({
			url:"action/sys/role/findAllApplicationRolesNoPage",
			contentType:"application/json; charset=utf-8",
			success:function(data){
				for(var i in data.data){
					$("<a id='"+data.data[i].id+"' text='"+data.data[i].roleName+"'>"+data.data[i].roleName+"</a>").appendTo(".role_all");
				}
			}
		});
		//用户当前业务角色角色
		ajaxgeneral({
			url:"action/user/role/findAllApplicationNoPage",
			data:{"username":gps.username},
			contentType:"application/json; charset=utf-8",
			success:function(data){
				for(var i in data.data){
					$("<a id='"+data.data[i].id+"' text='"+data.data[i].roleName+"'>"+data.data[i].roleName+"</a>").appendTo(".role_choose");
				}
			}
		});
		//左移右  右移左
		$(document).on("click",".role_all a,.role_choose a",function(){
			$(".role_choose a,.role_all a").removeClass("a_hover");
			var divC=$(this).parent().hasClass("role_all")?"role_choose":"role_all";
			if($("."+divC+" a[id="+$(this).attr("id")+"]").length==0){
				$(this).addClass("a_hover").appendTo("."+divC);
			}else{
				$(this).remove();
				$("."+divC+" a[id="+$(this).attr("id")+"]").addClass("a_hover");
			}
		});
	});
    </script>
</head>
<body>
<table border="0" cellpadding="0" cellspacing="6">
<tr>
<td><b>所有角色:</b></td>
<td></td>
<td><b>所选角色:</b></td>
</tr>
<tr>
<td><div class="role role_all"></div></td>
<td width="30" align="center"><font class="col_h">>></font></br></br></br><font class="col_h"><<</font></td>
<td><div class="role role_choose"></div></td>
</tr>
</table>
</body>
</html>
