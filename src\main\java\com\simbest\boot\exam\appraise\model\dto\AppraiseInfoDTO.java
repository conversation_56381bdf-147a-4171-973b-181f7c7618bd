package com.simbest.boot.exam.appraise.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * AppraiseInfoDTO 外部调用传入信息封装
 *
 * <AUTHOR>
 * @since 2024/1/24 10:43
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppraiseInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("评价人")
    private String username;

//    @ApiModelProperty("评价人")
//    private String truename;

    @ApiModelProperty("被评价人")
    private String appraiseUsername;

    @ApiModelProperty("被评价人")
    private String appraiseTruename;

    @ApiModelProperty("被评价的工单Id")
    private String appraiseWorkId;

    @ApiModelProperty("被评价的商机名称")
    private String appraiseWorkTitle;

    @ApiModelProperty("评价模板code")
    private String templateCode;


}
