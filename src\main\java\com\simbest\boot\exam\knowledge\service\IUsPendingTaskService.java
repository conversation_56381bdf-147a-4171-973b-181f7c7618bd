/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service;
/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.cmcc.a4.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsPendingTask;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
public interface IUsPendingTaskService extends ILogicService<UsPendingTask,String> {
    /**
     * 生成并推送统一待办
     * @param usPendingTask
     * @return
     */
    JsonResponse saveOpenTodo(UsPendingTask usPendingTask);


    /**
     * 核销统一待办
     * @param usPendingTask
     * @return
     */
    JsonResponse cancleOpenTodo(UsPendingTask usPendingTask);

    /**
     * 查询当日所有待办信息
     * 用于核销待办
     * @param currentDay
     * @return
     */
    List<UsPendingTask> cancleTask(String currentDay);

    /**
     * 根据pmInsId查询待办信息
     * @param pmInsId
     * @return
     */
    UsPendingTask findPendingTaskByPmInsId( String pmInsId);

    /**
     * 用于调整答题状态
     * @param invitaitonId
     * @param status
     * @return
     */
    List<UsPendingTask> findPendingTaskByStatue( String invitaitonId, String status);
    /**
     *
     * 根据邀请信息查询
     * @param invitaitonId
     * @return
     */
    List<UsPendingTask> findPendingTaskByInvitaitonId(String invitaitonId);


    /**
     * 查询被邀请人列表信息
     * @param currentDay
     * @param recUserName
     * @return
     */
    List<UsPendingTask> InvitedTask(String currentDay,String recUserName);

    UsPendingTask getUsPendingTaskByPmInsId(String pmInsId);
}
