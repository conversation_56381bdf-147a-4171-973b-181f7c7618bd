/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.web;/**
 * Created by KZH on 2019/10/8 16:09.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsUserAnswers;
import com.simbest.boot.exam.knowledge.service.IUsUserAnswersService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:09
 * @desc
 **/
@Api(description = "用户答题记录表", tags = {"用户答题记录表相关处理控制器"})
@Slf4j
@RestController
@RequestMapping(value = "/action/usUserAnswers")
public class UsUserAnswersController extends LogicController<UsUserAnswers, String> {

    private IUsUserAnswersService usUserAnswersService;

    @Autowired
    public UsUserAnswersController(IUsUserAnswersService usUserAnswersService) {
        super(usUserAnswersService);
        this.usUserAnswersService = usUserAnswersService;
    }


    @ApiOperation(value = "获取答题题目列表接口", notes = "获取答题题目列表接口")
    @PostMapping(value = {"/getRecordAnswersList", "/sso/getRecordAnswersList", "/api/getRecordAnswersList"})
    public JsonResponse getRecordAnswersList(@RequestParam(value = "id" ,required = true) String id) {
        return usUserAnswersService.getRecordAnswersList(id);
    }
}
