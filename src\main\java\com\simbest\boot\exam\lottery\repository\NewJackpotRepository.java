/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.lottery.model.NewJackpot;

/**
 * <strong>Title : NewJackpotRepository</strong><br>
 * <strong>Description : 新奖池Repository </strong><br>
 * <strong>Create on : 2021/4/10</strong><br>
 * <strong>Modify on : 2021/4/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface NewJackpotRepository extends LogicRepository<NewJackpot,String> {
}
