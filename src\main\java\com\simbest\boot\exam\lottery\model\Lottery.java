/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.model;/**
 * Created by KZH on 2019/12/9 9:13.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @create 2019-12-09 9:13
 * @desc 问卷抽奖
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_lottery")
@ApiModel(value = "问卷抽奖")
public class Lottery extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EL") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "问卷Code",name = "examCode",example = "hadmin",required = true)
    private String examCode;

    @Column(length = 250)
    @ApiModelProperty(value = "中奖OA账号",name = "username",example = "hadmin",required = true)
    private String username;

    @Column(length = 250)
    @ApiModelProperty(value = "奖项",name = "prize",example = "hadmin",required = true)
    private String prize;

    @Column(length = 250)
    @ApiModelProperty(value = "中奖OA姓名",name = "truename",example = "管理员",required = true)
    private String truename;

    @Column(length = 250)
    @ApiModelProperty(value = "联系方式",name = "phone",example = "132****3211",required = true)
    private String phone;

    @Column(length = 250)
    @ApiModelProperty(value = "收货地址",name = "address",example = "郑州市金水区",required = true)
    private String address;


}
