/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.web;/**
 * Created by KZH on 2019/10/8 16:09.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsTerminalLoginInfo;
import com.simbest.boot.exam.knowledge.service.IUsTerminalLoginInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:09
 * @desc
 **/
@Api(description = "答题记录表", tags = {"答题记录表控制器"})
@Slf4j
@RestController
@RequestMapping(value = "/action/usTerminalLoginInfo")
public class UsTerminalLoginInfoController extends LogicController<UsTerminalLoginInfo, String> {

    private IUsTerminalLoginInfoService terminalLoginInfoService;

    @Autowired
    public UsTerminalLoginInfoController(IUsTerminalLoginInfoService terminalLoginInfoService) {
        super(terminalLoginInfoService);
        this.terminalLoginInfoService = terminalLoginInfoService;
    }


    @ApiOperation(value = "获取答题正确次数及本次得分接口", notes = "获取答题正确次数及本次得分接口")
    @PostMapping(value = {"/permissionCheck", "/sso/permissionCheck", "/api/permissionCheck"})
    public JsonResponse getRecordList(@RequestParam(required = true,value = "timestamp" ) Long timestamp,
                                      @RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                      @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
        return terminalLoginInfoService.permissionCheck(timestamp,source,currentUserCode);
    }
}
