package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;


import com.simbest.boot.exam.examOnline.model.ExamInformation;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface ExamInformationRepository extends LogicRepository<ExamInformation,String> {
    @Query(value =  "select * from US_EXAM_INFORMATION t where t.question_code=:questionCode and t.publish_username=:publishUsername and t.enabled=1",
            nativeQuery = true)
    ExamInformation findAnswer(@Param("questionCode") String questionCode, @Param("publishUsername") String publishUsername);

}
