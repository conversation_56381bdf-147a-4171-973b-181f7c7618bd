package com.simbest.boot.exam.background.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.SystemService;
import com.simbest.boot.exam.background.model.ImagePath;
import com.simbest.boot.exam.background.repository.ImagePathRepository;
import com.simbest.boot.exam.background.service.ImagePathService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc: 试卷背景图片业务实现类
 * @date 2021/7/15  15:41
 */
@Slf4j
@Service
public class ImagePathServiceImpl extends SystemService<ImagePath,String> implements ImagePathService {

    private ImagePathRepository repository;

    @Autowired
    public ImagePathServiceImpl(ImagePathRepository repository) {
        super(repository);
        this.repository = repository;
    }

    /**
      * @desc 根据试卷编码查询背景图片信息
      * <AUTHOR>
      */
    @Override
    public Page<ImagePath> findImagePathByExamAppCode(String examAppCode, Pageable pageable) {
        Specification<ImagePath> spec = Specifications.<ImagePath>and()
                .eq("examAppCode", examAppCode)
                .build();

        Page<ImagePath> all = this.findAll(spec, pageable);
        return all;
    }



    /**
      * @desc 根据试卷编码查询试卷正在使用的背景图片
      * <AUTHOR>
      */
    @Override
    public ImagePath findUseImagePathByExamAppCode(String examAppCode) {

        Specification<ImagePath> spec = Specifications.<ImagePath>and()
                .eq("examAppCode", examAppCode)
                .eq("useNow",true)
                .build();
        return this.findOne(spec);
    }

    /**
      * @desc 保存试卷上传的图片信息
      * <AUTHOR>
      */
    @Override
    public ImagePath saveImagePath(List<Map<String,Object>> imagePaths) {
        for (Map<String,Object> imagePath : imagePaths) {
            String id = (String) imagePath.get("id");
            String mobileFilePath = (String) imagePath.get("mobileFilePath");
            // String mobileFilePath = imagePath.getMobileFilePath();
      //      String str = imagePath.getPaperBackgroundPath();
       //     String newDownLoadUrl="";
          //  String downLoadUrl = imagePath.getDownLoadUrl();
         //   newDownLoadUrl="/exam"+downLoadUrl;
         //   String str1=str.substring(0, str.indexOf("="));
         //   String str2=str.substring(str1.length()+1, str.length());
            ImagePath imagePath1 = new ImagePath();
            imagePath1.setImageId(id);
            imagePath1.setDownLoadUrl(mobileFilePath);
            String examAppCode = (String) imagePath.get("examAppCode");
            imagePath1.setExamAppCode(examAppCode);
            imagePath1.setMobileFilePath(mobileFilePath);
            Boolean useNow = (Boolean) imagePath.get("useNow");
            imagePath1.setUseNow(useNow);
            return this.insert(imagePath1);
        }
        return null;
    }

    /**
      * @desc 选择其中的某一图片作为试卷背景
      * <AUTHOR>
      */
    @Override
    public ImagePath saveUseImagePath(ImagePath imagePath) {

        try {
            /** 当更换别的图片作为背景图时，将原选择的关联解绑 **/
            Specification<ImagePath> spec = Specifications.<ImagePath>and()
                    .eq("examAppCode", imagePath.getExamAppCode())
                    .eq("useNow",true)
                    .build();
            ImagePath oleImg = this.findOne(spec);//查询原选择的图片信息
            if (ObjectUtil.isNotNull(oleImg)){
                oleImg.setUseNow(false);
                this.update(oleImg);//将其状态更改
            }

            //将新选择的考试背景图片进行绑定
            imagePath.setUseNow(true);
           return update(imagePath);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return null;
    }

    /**
     * @desc 根据服务器存储的图片id主键与本系统存储图片信息表的主键进行逻辑删除图片信息
     * <AUTHOR>
     * http://10.92.82.161:8088/spsc/sys/file/deleteById?id=F364745001459073024 前端需要调的删除接口
     */
    @Override
    public void delImage(ImagePath imagePath) {
        this.deleteById(imagePath.getId());
    }
}
