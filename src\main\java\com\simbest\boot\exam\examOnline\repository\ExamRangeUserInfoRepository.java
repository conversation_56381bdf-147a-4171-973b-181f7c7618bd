package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.SystemRepository;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfo;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-04-23
 * @desc 考试范围群组  人员信息Dao
 **/
public interface ExamRangeUserInfoRepository extends SystemRepository<ExamRangeUserInfo, String> {

    @Modifying
    void deleteByGroupId(@Param("groupId") String groupId);

    List<ExamRangeUserInfo> findByGroupId(@Param("groupId") String groupId);

    @Query(value = "select count(ui.user_name) from us_exam_range_user_info ui where ui.GROUP_ID=:groupId",
            nativeQuery = true)
    Integer countByUserName(@Param("groupId") String groupId);


    @Query(value = "select t.* from US_EXAM_RANGE_USER_INFO t where t.user_name = :userName and t.exam_name =:examName",
            nativeQuery = true)
    ExamRangeUserInfo findByUserName(@Param("userName") String userName,@Param("examName") String examName);
}
