<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>题库页面</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    /*"idFiled":"ID", 用于自定义id的情况，当接口查出的id不为id，比如是ID时，在这里设置字段名 */
                    "listname":"#examQuestionBankTable",//table列表的id名称，需加# menuExpalinTable
                    "querycmd":"action/examQuestionBank/listExamQuestionBank",//table列表的查询命令
                    "checkboxall":true,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[[
                        { field: "ck",checkbox:true }
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "题库编码", field: "questionBankCode", width: 120,sortable: true, tooltip: true,align:"center"},
                        { title: "题库名", field: "questionBankName", width: 120,sortable: true, tooltip: true,align:"center"},
                        {
                            field: "opt", title: "操作", width: 110,sortable: true, tooltip: true,align:"center", rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                                    +"<a href='#' delete='action/examQuestionBank/delExamQuestionBank' deleteid='"+row.id+"'>【删除】</a>";
                                return g;
                            }
                        }
                    ] ],
                    "pagerbar": [{
                        id:"deleteall",
                        iconCls: 'icon-remove',
                        text:"批量删除&nbsp;"
                    }],
                    "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                        "id":"deleteall",
                        "url":"action/examQuestionBank/deleteAllByIds",
                        "contentType":"application/json; charset=utf-8"
                    }
                },
                "dialogform":{
                    "dialogid":"#buttons",//对话框的id
                    "formname":"#examQuestionBankTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd":"action/examQuestionBank/createExamQuestionBank",//新增命令
                    "updatacmd":"action/examQuestionBank/updateExamQuestionBank",//修改命令
                    "onSubmit":function(data){

                        return true;
                    }
                }
            };
            loadGrid(pageparam);


        });
        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data,isupdate){
            if(isupdate){
                $('.update-readonly').hide();
            }else{
                $('.update-readonly').show();
            }
        };
        //回调函数
        window.programaInfo=function(data){
        };

    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="examQuestionBankTableQueryForm" >
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <input id="programaCode" name="programaCode" type="hidden"/>
        <tr>
            <td width="100" align="right">题库编码</td>
            <td width="150"><input name="questionBankCode" type="text" value="" /></td>

            <td width="100" align="right">题库名称</td>
            <td width="150"><input name="questionBankName" type="text" value="" />
            </td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fl searchtable"><span>查询</span></a>
                    <a class="btn a_success showDialog fr"><span>新增</span></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="examQuestionBankTable"><table id="examQuestionBankTable"></table></div>
<!--新增修改的dialog页面-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:500px;height:300px;">
    <form id="examQuestionBankTableAddForm" method="post" contentType="application/json; charset=utf-8"  onSubmit="onSubmit()" >
        <input id="id" name="id" type="hidden" />
        <table border="0" cellpadding="0" cellspacing="10">

            <fieldset class="title">
                <font class="ml15 f12 col_r">提示：题库编码不可重复</font></legend>
            </fieldset>

            <tr>
                <td width="100" align="right"><font class="col_r">*</font>题库编码</td>
                <td width="200" >
                    <input id="questionBankCode" name="questionBankCode" style="width:100%;height: 32px" type="text"  class="easyui-validatebox" data-options="validType:'maxLength[200]'"  required='required' /></td>
            </tr>
            <tr>
                <td width="100" align="right"><font class="col_r">*</font>题库名</td>
                <td width="200" >
                    <input id="questionBankName" name="questionBankName" style="width:100%;height: 32px" type="text" class="easyui-validatebox" data-options="validType:'maxLength[200]'"  required='required' /></td>
            </tr>

        </table>

    </form>
</div>
</body>
</html>
