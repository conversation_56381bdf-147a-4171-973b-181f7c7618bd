﻿/**全局公共样式**/
* {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
div,
dl,
dt,
dd,
ul,
ol,
li,
p,
blockquote,
pre,
hr,
table,
th,
td,
form,
fieldset,
input,
button,
textarea,
menu,
i {
	margin: 0;
	padding: 0;
	font-family: "Microsoft Yahei", "微软雅黑", arial, "宋体", sans-serif;
}

body {
	font-size: 12px;
	margin: 0;
	padding: 0;
	background: #FFF;
}

input,
textarea,
select {
	font-family: inherit;
	line-height: 18px;
	outline: none;
	font-size: 12px;
	vertical-align: middle;
}

input {
	font-family: "Microsoft Yahei", "微软雅黑", arial, "宋体", sans-serif;
	font-size: 12px;
}

input,
textarea,
select {
	padding: 7px;
	line-height: 16px;
	border: 1px solid #e5e5e5;
	-moz-border-radius: 2px;
	-webkit-border-radius: 2px;
	border-radius: 2px;
	background: #fff;
}

input.input_left {
	-moz-border-top-left-radius: 3px;
	-webkit-border-top-left-radius: 3px;
	border-top-left-radius: 3px;
	-moz-border-bottom-left-radius: 3px;
	-webkit-border-bottom-left-radius: 3px;
	border-bottom-left-radius: 3px;
	-moz-border-top-right-radius: 0px;
	-webkit-border-top-right-radius: 0px;
	border-top-right-radius: 0px;
	-moz-border-bottom-right-radius: 0px;
	-webkit-border-bottom-right-radius: 0px;
	border-bottom-right-radius: 0px;
}

a,
a:link,
a:visited {
	text-decoration: none;
	cursor: pointer;
	color: #39aef5;
}

a:hover {
	text-decoration: underline;
}

a:focus {
	text-decoration: none;
}

img {
	vertical-align: middle;
	border: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: 500;
	color: inherit;
}

h1,
h2,
h3,
h4,
h5 {
	padding: 10px 0;
}

h1 {
	font-size: 36px;
}

h2 {
	font-size: 30px;
}

h3 {
	font-size: 25px;
}

h4 {
	font-size: 18px;
}

h5 {
	font-size: 14px;
}

h6 {
	font-size: 14px;
}

p {
	margin: 0px;
	line-height: 25px;
}

li {
	list-style: none;
}

ul {
	margin: 0;
	padding: 0;
	list-style: none;
}

li ul {
	padding-left: 10px;
}

table,
th,
td {
	color: #333333;
}

th {
	font-weight: bold;
}

div {
	word-wrap: break-word;
}

/**button**/
a.btn {
	width: auto;
	height: 38px;
	float: left;
	cursor: pointer;
	line-height: 36px;
	padding: 0 18px;
	color: #fff;
	background-color: #39aef5;
	text-align: center;
	-moz-border-radius: 2px;
	-webkit-border-radius: 2px;
	border-radius: 2px;
}

a.a_left {
	-moz-border-top-left-radius: 2px;
	-webkit-border-top-left-radius: 2px;
	border-top-left-radius: 2px;
	-moz-border-bottom-left-radius: 2px;
	-webkit-border-bottom-left-radius: 2px;
	border-bottom-left-radius: 2px;
	-moz-border-top-right-radius: 0px;
	-webkit-border-top-right-radius: 0px;
	border-top-right-radius: 0px;
	-moz-border-bottom-right-radius: 0px;
	-webkit-border-bottom-right-radius: 0px;
	border-bottom-right-radius: 0px;
}

a.a_right {
	border-left: 0px none;
	-moz-border-top-right-radius: 2px;
	-webkit-border-top-right-radius: 2px;
	border-top-right-radius: 2px;
	-moz-border-bottom-right-radius: 2px;
	-webkit-border-bottom-right-radius: 2px;
	border-bottom-right-radius: 2px;
	-moz-border-top-left-radius: 0px;
	-webkit-border-top-left-radius: 0px;
	border-top-left-radius: 0px;
	-moz-border-bottom-left-radius: 0px;
	-webkit-border-bottom-left-radius: 0px;
	border-bottom-left-radius: 0px;
}

a.a_all3 {
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
	border-radius: 3px;
}

a.a_primary {
	background-color: #337ab7;
	border-color: #2e6da4;
	color: #fff;
}

a.a_success {
	background-color: #5cb85c;
	border-color: #4cae4c;
	color: #fff;
}

a.a_info {
	background-color: #5bc0de;
	border-color: #46b8da;
	color: #fff;
}

a.a_warning {
	background-color: #f0ad4e;
	border-color: #eea236;
	color: #fff;
}

a.a_danger {
	background-color: #d9534f;
	border-color: #d43f3a;
	color: #fff;
}

a.btn i {
	width: auto;
	line-height: 18px;
	padding: 10px 0px;
	float: left;
	margin: 0px 2px 0 0;
}

a.small {
	height: 30px;
	line-height: 28px;
	padding: 0 10px;
}

a.small i {
	padding: 6px 0;
}

a.small font {
	font-size: 12px;
}

a.btn i.i_add {
	padding: 7px 0px 5px;
}

a.btn:hover,
a.a_hover {
	text-decoration: none;
	background: #38a8ec;
}

a.a_reset {
	background-color: #fff;
	border: 1px solid #c9c9c9;
	color: #555;
}

a.a_reset:hover {
	border-color: #39aef5;
	background: #fff;
}

a.a_right:hover {
	border-left: 0px none;
}

/**images**/
.images img {
	margin: 5px;
	height: 120px;
}

/*基础行高100 h数字 表示倍数 如h6表示600高*/
.hs {
	height: 80px;
}

.hsm {
	height: 60px;
}

.hsmm {
	height: 35px;
}

.h1 {
	height: 100px;
}

.h2 {
	height: 200px;
}

.h3 {
	height: 300px;
}

.h4 {
	min-height: 400px;
	_height: 400px;
}

.h5 {
	min-height: 500px;
	_height: 500px;
}

.h6 {
	min-height: 600px;
	_height: 600px;
}

.h32 {
	height: 32px;
}

.lh32 {
	line-height: 32px;
}

/*基础宽度100 同上*/
.ws {
	width: 70px;
}

.wsm {
	width: 60px;
}

.wsmm {
	width: 35px;
}

.w1 {
	width: 100px;
}

.w2 {
	width: 200px;
}

.w3 {
	width: 300px;
}

.w4 {
	width: 400px;
}

.w5 {
	width: 500px;
}

.w6 {
	width: 600px;
}

.w7 {
	width: 700px;
}

.w8 {
	width: 800px;
}

.w9 {
	width: 900px;
}

.w100 {
	width: 100%;
}

.w50 {
	width: 50%;
}

/*常用padding margin*/
.p0 {
	padding: 0;
}

.p5 {
	padding: 5px;
}

.p10 {
	padding: 10px;
}

.p15 {
	padding: 15px;
}

.p20 {
	padding: 20px;
}

.p30 {
	padding: 30px;
}

.plr15 {
	padding: 0px 15px;
}

.plr18 {
	padding: 0 18px;
}

.plr10 {
	padding: 0 10px;
}

.pt10lr18 {
	padding: 10px 18px 0;
}

.m0 {
	margin: 0;
}

.m5 {
	margin: 5px;
}

.m10 {
	margin: 10px;
}

.m15 {
	margin: 15px;
}

.m20 {
	margin: 20px;
}

.m30 {
	margin: 30px;
}

.m50 {
	margin: 50px;
}

.m60 {
	margin: 30px 60px;
}

.pr12 {
	padding-right: 12px;
}

.pr18 {
	padding-right: 18px;
}

.pb10 {
	padding-bottom: 10px;
}

.plf15 {
	padding: 0 15px;
}

.mr10 {
	margin-right: 10px;
}

.mr15 {
	margin-right: 15px;
}

.ml10 {
	margin-left: 10px;
}

.mr5 {
	margin-right: 5px;
}

.ml15 {
	margin-left: 15px;
}

.mt10 {
	margin-top: 10px;
}

.fright,
.fr,
a.fr {
	float: right;
}

.fleft,
.fl {
	float: left;
}

.hide,
.none {
	display: none;
}

.hide.show {
	display: table-cell;
}

.lh20 {
	line-height: 20px;
}

.bor_b {
	border-bottom: 1px solid #e6e6e6;
}

.text_c {
	text-align: center;
}

.c9a {
	color: #9a9a9a;
}

/*清除浮动*/
.txtl {
	text-align: left
}

.txtc {
	text-align: center
}

.txtr {
	text-align: right
}

.f10 {
	font-size: 10px;
	color: #999;
}

.f20 {
	font-size: 20px;
}

.f18 {
	font-size: 18px;
}

.f50 {
	font-size: 50px;
}

.f12 {
	font-size: 12px;
}

.line {
	clear: both;
	height: 1px;
	margin: 10px 0;
	width: 100%;
	overflow: hidden;
	background: #eee
}

/*字体颜色*/
.col_r {
	color: #d9534f;
}

.col_b {
	color: #00b4f1;
}

.col_h {
	color: #9a9a9a;
}

.rightData {
	position: absolute;
	top: 0px;
	left: 200px;
	bottom: 0;
	right: 0;
	overflow: auto;
	border-left: solid 1px #F0F0F0;
}

.process_org {
	left: 360px;
}

.i_style {
	color: #00b4f1;
	cursor: pointer;
	font-size: 18px;
}

/*图片上传器*/
.cselectorImageUL {
	position: relative;
}

.cselectorImageUL .btn {
	position: absolute;
	z-index: 3;
}

.cselectorImageUL input {
	position: absolute;
	z-index: 5;
	filter: alpha(opacity=0);
	opacity: 0;
	cursor: pointer;
}

/*图片上传样式*/
.uploadImage {
	position: absolute;
	left: 130px;
	top: 0px;
	z-index: 10;
	*left: 140px;
}

.uploadImage img {
	max-width: 40px;
	max-height: 40px;
}

.uploadImage_del {
	display: block;
	position: absolute;
	right: 0;
	top: -15px;
}

/*ctable*/
table.ctable {
	border-collapse: collapse;
	border-spacing: 0;
	empty-cells: show;
	max-width: 100%;
}

table.ctable thead tr {
	font-weight: bold;
	background: #e2f4ff;
}

table.ctable td {
	padding: 8px;
	line-height: 21px;
	border-width: 1px 0px 0px;
	border-style: solid;
	border-color: #e6e6e6;
	white-space: nowrap;
}

table.ctable thead tr td {
	border: 0px none;
}

table.ctable tbody {
	border-width: 0px 1px;
	border-style: solid;
	border-color: #e6e6e6;
}

table.ctable tbody tr:last-child td {
	border-width: 1px 0px 1px;
	border-style: solid;
	border-color: #e6e6e6;
}

table.ctable tbody tr:nth-child(even) {
	background: #fafafa;
}

table.ctable tbody tr:hover {
	background: #e2f4ff;
}

table.ctable tfoot td {
	border: none;
	padding: 5px 0;
	background: #f5f6fa;
}

.over_point {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.imgtobig {
	position: absolute;
	text-align: center;
	z-index: 11000;
	width: 60%;
	height: 60%;
	padding: 28px;
	left: 20%;
	top: 20%;
	overflow-x: hidden;
	overflow-y: auto;
	border: 1px solid #e0e0e0;
	background: #fff;
}

.imgtobig i {
	position: absolute;
	z-index: 11001;
	right: 18px;
	top: 15px;
	color: #00b4f1;
	font-size: 25px;
	cursor: pointer;
}

.choose a {
	background: #f0f0f0;
	color: #aaa;
}

.choose a.a_hover {
	background: #f88f69;
}

.pagination {
	background: #f5f6fa;
	padding: 5px 0 6px;
}

.tab {
	width: 100%;
	float: left;
	background-color: #00b4f1;
	line-height: 32px;
	color: #fff;
	margin-bottom: 5px;
}

.tab li {
	float: left;
	width: 200px;
	height: 32px;
	text-align: center;
	cursor: pointer;
}

.tab2 li {
	width: 20%;
}

.tab_click {
	background-color: #f3f3f3;
	color: #00b4f1;
}