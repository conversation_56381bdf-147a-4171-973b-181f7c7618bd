package com.simbest.boot.exam.examOnline.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: 洛阳满意度问卷平均分与排名统计结果导出
 * @projectName exam
 * @description:
 * @date 2021/6/10  11:10
 */
@Data
public class ExamAvgRankExcel {

    @ExcelVOAttribute(name = "部门", column = "A")
    @Excel(name =  "部门",width = 50)
    private String department;

    @ExcelVOAttribute(name = "本次调查得分", column = "B")
    @Excel(name =  "本次调查得分",width = 50)
    private BigDecimal Avgs;

    @ExcelVOAttribute(name = "百分制成绩", column = "C")
    @Excel(name =  "百分制成绩",width = 50)
    private BigDecimal Centennial;

    @ExcelVOAttribute(name = "排名", column = "D")
    @Excel(name =  "排名",width = 50)
    private Integer rank;

}
