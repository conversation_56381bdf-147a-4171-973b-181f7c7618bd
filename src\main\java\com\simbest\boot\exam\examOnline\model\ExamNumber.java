package com.simbest.boot.exam.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @createTime  2021-12-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_number")
@ApiModel(value = "答题次数表")
public class ExamNumber extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EN") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 100)
    @ApiModelProperty(name = "考试次数" ,example = "0/1")
    private Integer examNumber;

    @Column(length = 100)
    @ApiModelProperty(name = "考试人" )
    private String examUserName;

    @Column(length = 100)
    @ApiModelProperty(name = "考试编码" ,example = "zfjs-01")
    private String examCode;

}
