package com.simbest.boot.exam.lottery.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.lottery.model.LyJackpot;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface LyJackpotRepository extends LogicRepository<LyJackpot,String> {
    @Query(
            value = "select a.* from us_ly_exam_jackpot a where a.enabled = 1  and a.type=:type order by  a.created_time desc ",
            nativeQuery = true
    )
    List<LyJackpot> findAllOrderByCreateTime(@Param("type")String type);
}
