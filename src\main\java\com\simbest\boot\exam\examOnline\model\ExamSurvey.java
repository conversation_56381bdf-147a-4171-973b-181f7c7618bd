/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <strong>Title : ExamUser</strong><br>
 * <strong>Description : 生成人员关联试卷 </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_survey")
@ApiModel(value = "生成人员关联试卷")
@Builder
public class ExamSurvey extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EXAMUSER") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "主单据ID",name = "pmInsId")
    private String examCode;//考试编码

    @Column(length = 250)
    @ApiModelProperty(value = "主单据ID",name = "pmInsId")
    private String pmInsId;//主单据ID


    @Column(length = 250)
    @ApiModelProperty(value = "提交人OA",name = "userName")
    private String userName;//提交人


    @Column(length = 250)
    @ApiModelProperty(value = "提交人姓名",name = "trueName")
    private String trueName;//提交人

    @Column(length = 250)
    @ApiModelProperty(value = "试卷code",name = "questionBankCode")
    private String questionBankCode;//试卷code

    @ApiModelProperty(value = "问题名称",name = "title")
    @Column(length = 2500)
    private String title;//问题名称

    @ApiModelProperty(value = "内部占比",name = "inside")
    @Column(length = 250)
    private String inside;//内部占比

    @ApiModelProperty(value = "工作内容",name = "taskContent")
    @Column(length = 2500)
    private String taskContent;//工作内容

    @ApiModelProperty(value = "pmInsType",name = "outside")
    @Column(length = 250)
    private String pmInsType;//类型

    @ApiModelProperty(value = "外部占比",name = "outside")
    @Column(length = 250)
    private String outside;//外部占比


    @ApiModelProperty(value = "特殊需求",name = "specialNeed")
    @Column(length = 2500)
    private String specialNeed;//特殊需求


    @ApiModelProperty(value = "备注",name = "remark")
    @Column(length = 2500)
    private String remark;//备注

    @ApiModelProperty(value = "影响",name = "effect")
    @Column(length = 250)
    private String effect;//影响



    @ApiModelProperty(value = "说明理由",name = "reasons")
    @Column(length = 2500)
    private String reasons;//说明理由


    @ApiModelProperty(value = "外部占比的工作内容字段",name = "reasons")
    @Column(length = 2500)
    private String outsideContent;//说明理由

    @ApiModelProperty(value = "第四题影响",name = "lifeEffect")
    @Column(length = 250)
    private String lifeEffect;//影响

    @ApiModelProperty(value = "第四题影响",name = "lifeReason")
    @Column(length = 2500)
    private String lifeReason;//影响

     @ApiModelProperty(value = "建议字段",name = "suggestion")
    @Column(length = 2500)
    private String suggestion;//影响



}
