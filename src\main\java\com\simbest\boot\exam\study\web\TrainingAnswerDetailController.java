package com.simbest.boot.exam.study.web;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.exam.study.model.TrainingAnswerDetail;
import com.simbest.boot.exam.study.service.ITrainingAnswerDetailService;

/**
 * 用途：TrainingAnswerDetail领域对象名称控制器
 */
@Api(description = "TrainingAnswerDetailController", tags = {"学习管理-答题详情"})
@Slf4j
@RestController
@RequestMapping(value="/study/trainingAnswerDetail")
public class TrainingAnswerDetailController extends LogicController<TrainingAnswerDetail, String> {

    private ITrainingAnswerDetailService service;

    @Autowired
    public TrainingAnswerDetailController(ITrainingAnswerDetailService service) {
        super(service);
        this.service = service;
    }
}