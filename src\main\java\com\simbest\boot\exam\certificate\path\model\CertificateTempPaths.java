/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate.path.model;

import cn.hutool.core.io.FileUtil;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.exam.certificate.path.util.TempPathGenerator;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.List;

/**
 * <strong>Title : CertificateTempPaths</strong><br>
 * <strong>Description : 证书临时文件路径 </strong><br>
 * <strong>Create on : 2020/11/10</strong><br>
 * <strong>Modify on : 2020/11/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR> kou<PERSON><EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Data
@Slf4j
public class CertificateTempPaths {
    private TempFilePath tempDoc;

    private TempFilePath tempPdf;

    private TempFilePath tempImage;

    private File tempFile;

    public static CertificateTempPaths newInstance(String docSuffix, String pdfSuffix, String imageSuffix)  {
        List<TempFilePath> tempFilePaths = TempPathGenerator.generateTempPaths(docSuffix, pdfSuffix, imageSuffix);
        createTempFiles(tempFilePaths);
        return new CertificateTempPaths(tempFilePaths);
    }

    public static void createTempFiles(List<TempFilePath> tempFilePaths) {
        for (TempFilePath tempFilePath : tempFilePaths) {
            createTempFile(tempFilePath);
        }
    }

    public static void createTempFile(TempFilePath tempFilePath)  {
        try {
//            File path = new File(tempFilePath.getPath());
//            if (!path.exists()) {
//                path.mkdirs();
//            }

            log.info("------------------------临时文件路径为:{}",tempFilePath.getPath());
            log.info("------------------------临时文件名称为:{}",tempFilePath.getPathName());

            File tempFile =FileUtil.file(tempFilePath.getPathName());
            if (!tempFile.exists()) {
                FileUtil.touch(tempFile);

                if(FileUtil.exist(tempFile)){
                    log.info("------------------------文件创建成功");
                }
                else {
                    log.info("------------------------文件创建失败");
                }
                //tempFile.createNewFile();
            }
        }catch (Exception e){
            Exceptions.printException( e );
        }

    }

    public boolean deleteTempFiles() {
        boolean docResult = deleteTempFile(this.tempDoc);
        boolean pdfResult = deleteTempFile(this.tempPdf);
        boolean imageResult = deleteTempFile(this.tempImage);
        return docResult && pdfResult && imageResult;
    }

    public boolean deleteTempFile(TempFilePath tempPath) {
        return deleteTempFile(tempPath.getPathName());
    }

    public boolean deleteTempFile(String filePath) {
        try {
            //File tempFile = new ClassPathResource(filePath).getFile();
            //System.out.println(filePath);
            File tempFile = new File(filePath);
            if (tempFile.exists()) {
                FileUtils.deleteQuietly(tempFile);
                FileUtils.deleteQuietly(tempFile.getParentFile());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    public CertificateTempPaths(List<TempFilePath> tempFilePaths) {
        this(tempFilePaths.get(0), tempFilePaths.get(1), tempFilePaths.get(2));
    }

    public CertificateTempPaths(TempFilePath tempDoc, TempFilePath tempPdf, TempFilePath tempImage) {
        super();
        this.tempDoc = tempDoc;
        this.tempPdf = tempPdf;
        this.tempImage = tempImage;
    }

    public String getTempDocDirPath() {
        return tempDoc.getPath();
    }

    public String getTempDocPathName() {
        return tempDoc.getPathName();
    }

    public String getTempPdfDirPath() {
        return tempPdf.getPath();
    }

    public String getTempPdfPathName() {
        return tempPdf.getPathName();
    }

    public String getTempImagePath() {
        return tempImage.getPath();
    }

    public String getTempImagePathName() {
        return tempImage.getPathName();
    }
}
