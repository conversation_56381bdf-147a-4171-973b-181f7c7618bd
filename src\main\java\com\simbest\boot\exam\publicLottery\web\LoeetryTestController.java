package com.simbest.boot.exam.publicLottery.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.publicLottery.service.IPublicLotteryService;
import com.simbest.boot.exam.publicLottery.service.impl.TestImpl;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.security.LoginUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/action/test")
public class LoeetryTestController {
    private final LoginUtils loginUtils;
    private final RsaEncryptor rsaEncryptor;
    private final IPublicLotteryService service;
    private final TestImpl testImpl;


    @PostMapping(value = {"/resetLotteryPerSon"})
    public JsonResponse resetLotteryPerSon(@RequestParam(required = false) String source,
                                           @RequestParam(required = false) String currentUserCode) {
        testImpl.resetLotteryPerSon();
        return JsonResponse.defaultSuccessResponse();
    }

    @PostMapping(value = {"/resetLottery"})
    public JsonResponse resetLottery(@RequestParam(required = false) String source,
                                     @RequestParam(required = false) String currentUserCode) {
        testImpl.resetLottery();
        return JsonResponse.defaultSuccessResponse();
    }

    @PostMapping(value = {"/drawLottery"})
    public JsonResponse drawLottery(@RequestParam(required = false) String source,
                                    @RequestParam(required = false) String currentUserCode) {
        testImpl.drawLottery();
        return JsonResponse.defaultSuccessResponse();
    }

    @PostMapping(value = {"/testSave"})
    public JsonResponse testSave(@RequestParam(required = false) String source,
                                 @RequestParam(required = false) String currentUserCode) {
        testImpl.testOptimisticLocking();
        return JsonResponse.defaultSuccessResponse();
    }
}
