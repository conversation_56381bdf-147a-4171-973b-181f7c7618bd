<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>考试维护信息</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
 
  <script type="text/javascript">
    $(function(){
      //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
      //判断是否是查看方式打开
      getCurrent();
      loadForm("examBacklogTableUpdateForm");

      //修改验证框样式
      $('.easyui-validatebox').validatebox({    
        tipPosition: 'right',
        validateOnCreate:false
      });
        $('#examStartTime').datetimebox({
            onChange: function(data){
                dateHorizon("examEndTime",data.substr(0,10),"2099-12-30");
            }
        });
        $('#examEndTime').datetimebox({
            onChange: function(data){
                if(!$('#examStartTime').datetimebox("getValue")) {
                    dateHorizon("examStartTime","2000-01-01",$('#examEndTime').datetimebox("getValue").substr(0,10));
                }
            }
        });

    });


    //加载数据
     function onSubmit(){

     }

     //表单效验
     window.fvalidate = function () {
        return $("#examBacklogaTableAddForm").form("validate");
    };

     window.getchoosedata=function(){
      // $('.easyui-validatebox').validatebox('enableValidation');
      if (fvalidate()) {
        //..
      }else{
        return;
      }
        var datas=[];
            var data={};
            data.examName=$('.examName').val();
            data.examCode = $('.examCode').val();
            data.appExamUrl=$('.appExamUrl').val();
            data.appImageUrl = $('.appImageUrl').val();
            data.pcExamUrl=$('.pcExamUrl').val();
            data.pcImageUrl = $('.pcImageUrl').val();
            data.examStartTime = $('#examStartTime').datebox('getValue');
            data.examEndTime = $('#examEndTime').datebox('getValue');
            data.weightValue = $('.weightValue').val();
            data.workType = $('.workType').val();
            datas.push(data);
            // if (data.examName==null || data.examName==""){
            //     top.mesAlert("提示", "考试名称不能为空", 'warning');
            //     return {"state":0};
            // }
            // if (data.examCode==null || data.examCode==""){
            //     top.mesAlert("提示", "考试编号不能为空", 'warning');
            //     return {"state":0};
            // }
 
            // if (data.examStartTime==null || data.examStartTime==""){
            //     top.mesAlert("提示", "考试开始时间不能为空", 'warning');
            //     return {"state":0};
            // }
            // if (data.examEndTime==null || data.examEndTime==""){
            //     top.mesAlert("提示", "考试结束时间不能为空", 'warning');
            //     return {"state":0};
            // }
            // if (data.weightValue==null || data.weightValue==""){
            //     top.mesAlert("提示", "考试权重系数不能为空", 'warning');
            //     return {"state":0};
            // }
        return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    };

     function loadList(){
            $('#examBacklogTableUpdateForm').datagrid('reload');
    }

  </script>
</head>
<body style="padding-top: 0;">
<!--searchform-->
<div class="table_searchD">
  <form id="examBacklogaTableAddForm"  bindval="bindval" initsystem="initsystem" cmd-insert="action/summary/saveExamSummary"
  contentType="application/json; charset=utf-8" beforeSubmit="beforeSubmit">
    <table border="0" cellpadding="10" cellspacing="16" width="100%">
      <tr>
        <td width="90" align="right"><span class="col_r">*</span>考试名称：</td>
        <td width="150"><input id="examName" class="easyui-validatebox examName" required="ture" validType="string" name="examName" type="text"  /></td>

        <td width="90" align="right"><span class="col_r">*</span>考试编号：</td>
        <td width="150"><input id="examCode" class="easyui-validatebox examCode" required="ture" validType="string" name="examCode" type="text"  /></td>

      </tr>
      <tr>

        <td width="90" align="right"><span class="col_r"></span>移动端考试跳转页面路径：</td>
        <td width="150"><input id="appExamUrl" class="easyui-validatebox appExamUrl"  validType="url" name="appExamUrl" type="text"  /></td>

        <td width="90" align="right"><span class="col_r"></span>移动端端悬浮窗口图片路径：</td>
        <td width="150"><input  id="appImageUrl" class="easyui-validatebox appImageUrl"  validType="url" name="appImageUrl"  type="text"  /></td>
        <td>

      </tr>
      <tr>

        <td width="90" align="right"><span class="col_r"></span>pc端考试跳转页面路径：</td>
        <td width="150"><input id="pcExamUrl" class="easyui-validatebox pcExamUrl"  validType="url" name="pcExamUrl" type="text"  /></td>

        <td width="90" align="right"><span class="col_r"></span>pc端悬浮窗口图片路径：</td>
        <td width="150"><input id="pcImageUrl" class=" easyui-validatebox pcImageUrl"  validType="url" name="pcImageUrl" type="text"  /></td>
        <td>

      </tr>
      <tr>

        <td width="90" align="right"><span class="col_r">*</span>考试开始时间：</td>
        <td width="150"><input id="examStartTime" class="easyui-datetimebox  examStartTime"   name="examStartTime"
          data-options="showSeconds:true,editable:false" style="width:148px; height: 32px;"></td>

        <td width="90" align="right"><span class="col_r">*</span>考试结束时间：</td>
        <td width="150"><input id="examEndTime" class="easyui-datetimebox  examEndTime"  name="examEndTime"
          data-options="showSeconds:true,editable:false," style="width:148px; height: 32px;"></td>

      </tr>

      <tr>

        <td width="90" align="right"><span class="col_r">*</span>考试权重系数：</td>
        <td width="150"><input id="weightValue" data-options="validType:['zinteger','maxLength[100]']" class="easyui-validatebox weightValue" required="ture" name="weightValue" type="text"  /></td>

        <td width="90" align="right" >待办类型：</td>
        <td width="150"><input placeholder="通用模板采用H类型" value="H" id="workType" class="easyui-validatebox  workType"  required="ture"  name="workType" type="text"  /></td>
      </tr>

    </table>
  </form>
</div>

  <!-- <div class="appInfoTable" style="margin-top: 20px;"><table id="appInfoTable"></table></div> -->
</body>
</html>
