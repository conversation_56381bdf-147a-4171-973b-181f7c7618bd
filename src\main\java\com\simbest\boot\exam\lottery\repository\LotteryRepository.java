/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.repository;/**
 * Created by KZH on 2019/12/9 9:19.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.lottery.model.Lottery;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-12-09 9:19
 * @desc
 **/
public interface LotteryRepository extends LogicRepository<Lottery,String> {

    /**
     * 通过username 获取表单
     * @param username
     * @return
     */
    @Query(
            value = "select a.* from us_exam_lottery a where a.enabled = 1  and a.username=:username order by  a.created_Time desc ",
            nativeQuery = true
    )
    List<Lottery> findByUsername(@Param("username") String username);
}