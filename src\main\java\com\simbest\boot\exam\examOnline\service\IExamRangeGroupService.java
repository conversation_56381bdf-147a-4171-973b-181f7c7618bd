package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ISystemService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamRangeGroup;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfo;
import com.simbest.boot.security.SimpleGroup;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-04-23
 * @desc 考试信息模块 创建人员群组Service
 **/
public interface IExamRangeGroupService extends ISystemService<ExamRangeGroup, String> {

    /**
     * 保存群组信息 到此项目数据库
     *
     * @param examRangeGroup
     * @return ExamRangeGroup
     */
    ExamRangeGroup saveExamRangeGroup(ExamRangeGroup examRangeGroup);

    /**
     * uums创建群组
     *
     */
    SimpleGroup saveUumsSysGroup(ExamRangeGroup examRangeGroup);

    /**
     * uums修改群组信息
     *
     */
    SimpleGroup updateUumsSysGroup(ExamRangeGroup examRangeGroup);

    /**
     * UUMS 删除群组信息
     *
     */
     Boolean delUumsSysGroup(String groupId);


    /**
     * 根据考试编号查询参考人员
     *
     * @param examAppCode
     * @return
     */
    List<Map<String, Object>> findUserByExamAppCode(String examAppCode);


    /**
     * 根据查询群组名称
     *
     * @return
     */
    List<Map<String, Object>> findExamDetailAndGroup(String examCode);

    /**
     * 根据当前登陆人获取其参加考试的试卷编号
     *
     * @param userName
     * @return
     */
    String findExamAppCodeByCurrentUser(String userName);

    /**
     * 分页+模糊查询群组信息包含计算当前分组的人员数
     *
     * @param
     * @return
     */
    Page<ExamRangeGroup> findExamGroupInfoByGroupName(int page,int size, ExamRangeGroup o);

    /**
     * 分页+模糊查询群组信息包含计算当前分组的人员数UUMS
     *
     * @param
     * @return
     */
    int findExamGroupInfoByUUMS(int page,int size, Map<String,Object> userGroupMap);

    /**
      * @desc 校验当前群组编号是否可用
      * <AUTHOR>
      */
    boolean isHaveCode(String groupId);

    /**
      * @desc 查询所有群组的考试信息
      * <AUTHOR>
      */
    List<ExamRangeGroup> listExamRangeGroup();

    /**
      * @desc 根据考试编码查询试卷编码
      * <AUTHOR>
      */
    Iterable<ExamRangeGroup> findExamRangeGroupByExamCode(String examCode);

    /**
      * @desc 根据考试名称查询考试编码
      * <AUTHOR>
      */
    ExamRangeGroup findByExamName(String examName);

    /**
     * 根据考试编码获取试卷编码
     * @param examCode
     * @return
     */
     ExamRangeGroup findByExamCode(String examCode);
}
