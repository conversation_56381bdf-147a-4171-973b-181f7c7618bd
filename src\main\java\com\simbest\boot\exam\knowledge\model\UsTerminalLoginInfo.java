package com.simbest.boot.exam.knowledge.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_terminal_login_info")
@ApiModel(value = "终端登录记录表")
public class UsTerminalLoginInfo extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UAR") //主键前缀，此为可选项注解
    private String id;




    @Column(length = 255)
    @ApiModelProperty(value = "时间戳")
    private Long timestamp;

    @Column(length = 255)
    @ApiModelProperty(value = "登录人")
    private String username;
    @Column(length = 255)
    @ApiModelProperty(value = "登录人")
    private String userTruename;
    @Column(length = 255)
    @ApiModelProperty(value = "终端类型")
    private String source;


}
