<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>我的已办</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/static/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/static/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#secrecyJoin",//table列表的id名称，需加#
                    "querycmd":"action/examWork/queryMyJoin?source=PC",//table列表的查询命令
                    //"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass":"noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "标题", field: "title", width: 40,sortable: true, tooltip: true,align:"center" },
                            { title: "办理时间", field: "modifiedTime", width: 80,sortable: true, tooltip: true,align:"center" },
                        { title: "状态", field: "sign", width: 40,sortable: true, tooltip: true,align:"center",
                            formatter:function (value,row,index) {
                                return value == "0" ? "未办":"已办";
                            }
                        },
                        {
                            field: "opt", title: "操作", width: 40, rowspan: 1,sortable: true, tooltip: true,align:"center",//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var id = row.id;
                                var createdTime = row.createdTime;
                                var username = row.transactorCode;
                                var workType = row.workType;
                                var appHtml = web.appHtml;
                                var url;
                                switch (workType) {
                                    case "A":url=appHtml.A;
                                        break;
                                    case "B":url=appHtml.B;
                                        break;
                                    case "C":url=appHtml.C;
                                        break;
                                    case "D":url=appHtml.D;
                                        break;
                                    case "E":url=appHtml.E;
                                        break;
                                    case "F":url=appHtml.F;
                                        break;
                                    case "G":url=appHtml.G;
                                        break;
                                    case "H":url=appHtml.H;
                                        break;
                                    case "W":url=appHtml.W;
                                        break;
                                    case "I":url=appHtml.I;
                                        break;
                                    case "J":url=appHtml.J;
                                        break;
                                    case "K":url=appHtml.K;
                                        break;
                                    case "L":url=appHtml.L;
                                        break;
                                    case "M":url=appHtml.M;
                                        break;
                                    case "Z":url=appHtml.Z;
                                        break;
                                    case "N":url=appHtml.N;
                                        break;
                                    case "Q":url=appHtml.Q;
                                        break;
                                    case "T":
                                        url = appHtml.T;
                                        break;
                                    case "S":
                                        url = appHtml.S;
                                        break;
                                    case "X":
                                        url = appHtml.X;
                                        break;
                                    case "R":
                                        url = appHtml.R;
                                        break;
                                    case "O":
                                        url = appHtml.O;
                                        break;
                                }

                                var actionType = "secrecyJoin"; // 已办
                                var g = "<a class='detail col_b' index='"+index+"'  path='"+url+"?id="+id+"&examAppCode="+row.examAppCode+"&examCode="+row.examCode+"&username="+username+"&actionType="+actionType + "&type=join" +"&createdTime="+encodeURIComponent(createdTime)+"&titName="+encodeURI(row.companyName)+"'>【查看】</a>";
                                return g;
                            }
                        }
                    ] ]
                }
            };
            loadGrid(pageparam);

            var pageparam2={
                "listtable":{
                    "listname":"#briefJoin",//table列表的id名称，需加#
                    "querycmd":"action/task/myJoin?source=PC",//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "styleClass":"noScroll",
                    //"queryParams":gps,
                    "nowrap": true,//把数据显示在一行里,默认true
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "标题", field: "title", width: 40,sortable: true, tooltip: true,align:"center" },
                            { title: "办理时间", field: "modifiedTime", width: 80,sortable: true, tooltip: true,align:"center" },
                        { title: "状态", field: "sign", width: 40,sortable: true, tooltip: true,align:"center",
                            formatter:function (value,row,index) {
                                return value == "0" ? "未办":"已办";
                            }
                        },
                        {
                            field: "opt", title: "操作", width: 40, rowspan: 1,sortable: true, tooltip: true,align:"center",//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var id = row.id;
                                var businessId = row.businessId;
                                var createdTime = row.createdTime;
                                var username = row.transactorCode;
                                var pmInsType = row.pmInsType;
                                var appHtml = web.appHtml;
                                var url;
                                switch (pmInsType) {
                                    case "P":
                                        url = appHtml.P;
                                        break;
                                }

                                console.log(url,'url');

                                var actionType = "secrecyJoin"; // 已办
                                var g = "<a class='detail col_b' index='"+index+"'  path='"+url+"?id="+id+"&examAppCode="+row.examAppCode+"&examCode="+row.examCode+"&username="+username+"&actionType="+actionType+"&createdTime="+encodeURIComponent(createdTime)+"&titName="+encodeURI(row.companyName)+"'>【查看】</a>";
                                return g;
                            }
                        }
                    ] ]
                }
            };
            loadGrid(pageparam2);

            setTimeout(function(){
                $('.ks').trigger('click')
            },500)

            //办理
            $(document).on("click","a.detail",function(){
                // top.dialogP($(this).attr("path"),'',' ','detail',true,'maximized','maximized');
                // winOpen($(this).attr("path"), window.name,' ','detail','maximized','maximized',listLoad);
                top.dialogP($(this).attr("path") , window.name,'查看','detail',true,'maximized','maximized',listLoad);
            });

            $(".myTabs div").on("click", function () {
                var index  = $(this).attr('index')
                $('.ks').removeClass('active')
                $('.jb').removeClass('active')
                if(index == '0'){
                    $('.ks').addClass('active')
                    $('.secrecyJoin').show()
                    $('#secrecyJoinQueryForm').show()
                    $('#briefJoinQueryForm').hide()
                    $('.briefJoin').hide()
                    loadGrid(pageparam);
                }
                if(index == '1'){
                    $('.jb').addClass('active')
                    $('.briefJoin').show()
                    $('#briefJoinQueryForm').show()
                    $('#secrecyJoinQueryForm').hide()

                    $('.secrecyJoin').hide()
                    loadGrid(pageparam2);
                }
            })

        });
        //刷新页面
        function listLoad(){
            $("#secrecyJoin").datagrid("reload");
        };

    </script>
</head>
<body class="body_page">
<!--searchform-->

<div class="myTabs">
    <div class="ks" index="0">考试已办</div>
    <div class="jb" index="1">简报已办</div>
</div>

<form id="secrecyJoinQueryForm" class="hide">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">任务标题：</td><td width="150"><input name="title" type="text" value="" /></td>
            <td>
                <div class="w100">
                    <a class="btn fl searchtable"><font>查询</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>

<form id="briefJoinQueryForm"  class="hide">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">任务标题：</td><td width="150"><input name="title" type="text" value="" /></td>
            <td>
                <div class="w100">
                    <a class="btn fl searchtable"><font>查询</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>





<!--table-->
<div class="secrecyJoin">
    <table id="secrecyJoin"></table>
</div>
<div class="briefJoin hide">
    <table id="briefJoin"></table>
</div>
</body>
</html>
<style>
    .myTabs{
        display: flex;
        margin-bottom: 20px;
    }
    .myTabs div{
        width: 120px;
        height: 32px;
        border: 1px solid #95bfe6;
        font-size: 16px;
        text-align: center;
        cursor: pointer;
        line-height: 32px;
        
    }
    .myTabs div.active{
        color: #333;
        font-weight: 700;
        color: #356885;
        border: 3px solid 95bfe6;
    }
</style>

