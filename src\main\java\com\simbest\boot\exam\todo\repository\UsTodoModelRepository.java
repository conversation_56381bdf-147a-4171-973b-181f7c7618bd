package com.simbest.boot.exam.todo.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.todo.model.UsTodoModel;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * @用途: 统一待办
 * @作者：zsf
 * @时间: 2018/12/27
 */
public interface UsTodoModelRepository extends LogicRepository<UsTodoModel,Long> {

    @Query(value =  "select t.*  from us_todo_model t   where t.username=:username  and t.type_Status='核销待办' and t.process_def_id=:workType  and t.enabled=1 ",
            nativeQuery = true)
    UsTodoModel findAllTypeStatus(@Param("username")String username,@Param("workType")String workType);
}
