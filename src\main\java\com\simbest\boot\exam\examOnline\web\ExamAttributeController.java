/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.web;/**
 * Created by KZH on 2019/10/8 15:17.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.dto.ExamAttributeDto;
import com.simbest.boot.exam.examOnline.model.ExamAttribute;
import com.simbest.boot.exam.examOnline.service.IExamAttributeService;
import com.simbest.boot.exam.util.OperateLogTool;
import com.simbest.boot.sys.model.SysOperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:17
 * @desc 试卷属性
 **/
@Api(description = "试卷属性")
@Slf4j
@RestController
@RequestMapping(value = "/action/examAttribute")
public class ExamAttributeController extends LogicController<ExamAttribute, String> {

    private IExamAttributeService iExamAttributeService;

    @Autowired
    public ExamAttributeController(IExamAttributeService service) {
        super(service);
        this.iExamAttributeService = service;
    }

    @Autowired
    private OperateLogTool operateLogTool;

    @Override
    public JsonResponse findAllNoPage(ExamAttribute examAttribute ) {
        Specification<ExamAttribute> specification = iExamAttributeService.getSpecification(examAttribute);
        return JsonResponse.success(iExamAttributeService.findAllNoPage(specification , Sort.by(Sort.Direction.DESC , "createdTime")));
    }

    @ApiOperation(value = "获取通用试卷模板", notes = "获取试卷模板")
    @PostMapping(value = {"/constructExamLayout", "sso/constructExamLayout", "api/constructExamLayout"})
    public JsonResponse constructExamLayout(@RequestParam(required = false) String currentUserCode,
                                            @RequestParam(required = false) String source,
                                            @RequestBody(required = false) Map<String, Object> condition) {
        return iExamAttributeService.constructExamLayout(currentUserCode, source, condition);
    }


    @ApiOperation(value = "获取通用试卷模板", notes = "获取试卷模板")
    @PostMapping(value = {"/constructExamLayoutTWO", "/sso/constructExamLayoutTWO", "api/constructExamLayoutTWO"})
    public JsonResponse constructExamLayoutTWO(@RequestParam(required = false) String loginuser,
                                               @RequestParam(required = false) String source,
                                               @RequestBody(required = false) Map<String, Object> condition) {
        return iExamAttributeService.constructExamLayoutTWO(loginuser, source, condition);
    }

    @ApiOperation(value = "根据题库code和试卷数量随机生成该用户的试卷", notes = "获取试卷模板")
    @PostMapping(value = {"/constructSpecialExamLayout", "sso/constructSpecialExamLayout"})
    public JsonResponse constructSpecialExamLayout(@RequestBody(required = false) Map<String, Object> condition) {
        return iExamAttributeService.constructSpecialExamLayout(condition);
    }

    @ApiOperation(value = "获取试卷和答案记录", notes = "获取试卷和答案记录")
    @PostMapping(value = {"/constructExamLayoutAndAnswer", "sso/constructExamLayoutAndAnswer", "api/constructExamLayoutAndAnswer"})
    public JsonResponse constructExamLayoutAndAnswer(@RequestParam(required = false) String currentUserCode,
                                                     @RequestParam(required = false) String source,
                                                     @RequestBody(required = false) Map<String, Object> condition) {
        return iExamAttributeService.constructExamLayoutAndAnswer(currentUserCode, source, condition);
    }

    @ApiOperation(value = "获取通用试卷模板", notes = "获取试卷模板")
    @PostMapping(value = {"/constructWindowsExamLayout", "sso/constructWindowsExamLayout"})
    public JsonResponse constructWindowsExamLayout(@RequestBody(required = false) Map<String, Object> condition) {

        return iExamAttributeService.constructWindowsExamLayout(condition);
    }

    @ApiOperation(value = "获取通用试卷模板-力量大厦问卷使用", notes = "获取试卷模板-力量大厦问卷使用")
    @PostMapping(value = {"/constructPowerBuildingLayout", "sso/constructPowerBuildingLayout"})
    public JsonResponse constructPowerBuildingLayout(@RequestParam(required = false) String currentUserCode,
                                                     @RequestParam(required = false) String source,
                                                     @RequestBody(required = false) Map<String, Object> condition) {
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/constructPowerBuildingLayout";
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examAttribute", "constructPowerBuildingLayout", operateLog);
        if (returnObj != null) {
            return returnObj;
        }
        return iExamAttributeService.constructPowerBuildingLayout(condition);
    }

    @ApiOperation(value = "获取通用试卷模板", notes = "获取试卷模板")
    @PostMapping(value = {"/findExamPaper", "sso/findExamPaper", "api/findExamPaper"})
    public JsonResponse findExamPaper(@RequestParam(required = false) String currentUserCode,
                                      @RequestParam(required = false) String source,
                                      @RequestBody(required = false) Map<String, Object> condition) {
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/constructExamLayout";
//        String params = "source="+source+",currentUserCode="+currentUserCode;
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examAttribute", "findExamPaper", operateLog);
        if (returnObj != null) {
            return returnObj;
        }
        return JsonResponse.success(iExamAttributeService.findExamPaper(currentUserCode, source, condition));
    }

    @ApiOperation(value = "根据登录人获取试卷信息不分页", notes = "根据登录人获取试卷信息")
    @PostMapping(value = {"/findExamPaperInfoNoPage", "sso/findExamPaperInfoNoPage", "api/findExamPaperInfoNoPage"})
    public JsonResponse findExamPaperInfoNoPage(@RequestBody ExamAttribute examAttribute) {
        return JsonResponse.success(iExamAttributeService.findExamPaper(examAttribute));
    }

    @ApiOperation(value = "根据登录人获取试卷信息", notes = "根据登录人获取试卷信息")
    @PostMapping(value = {"/findExamPaperInfo", "sso/findExamPaperInfo", "api/findExamPaperInfo"})
    public JsonResponse findExamPaperInfo(@RequestBody ExamAttribute examAttribute, @RequestParam(required = false, defaultValue = "1") int page, @RequestParam(required = false, defaultValue = "10") int size) {
        Pageable pageable = iExamAttributeService.getPageable(page, size , Sort.Direction.DESC.toString() , "createdTime");
        return JsonResponse.success(iExamAttributeService.findExamPaperInfo(examAttribute, pageable));
    }

    @ApiOperation(value = "根据试卷出题方式保存试卷信息", notes = "根据试卷出题方式保存试卷信息")
    @PostMapping(value = {"/createExamPaper", "sso/createExamPaper", "api/createExamPaper"})
    public JsonResponse createExamPaper(@RequestBody ExamAttributeDto examAttributeDto) {
        String examPaper = iExamAttributeService.createExamPaper(examAttributeDto);
        if (examPaper.contains("创建试卷失败")) return JsonResponse.fail(examPaper);
        return JsonResponse.success(examPaper);
    }

    @ApiOperation(value = "修改试卷信息", notes = "修改试卷信息")
    @PostMapping(value = {"/updateExamPaper", "sso/updateExamPaper", "api/updateExamPaper"})
    public JsonResponse updateExamPaper(@RequestBody ExamAttributeDto examAttributeDto) {
        boolean examPaper = iExamAttributeService.updateExamPaper(examAttributeDto);
        if (examPaper) return JsonResponse.success("修改成功");
        return JsonResponse.fail("修改失败");
    }

    @ApiOperation(value = "自动判题", notes = "自动判题")
    @PostMapping(value = {"/computeScore", "sso/computeScore", "api/computeScore"})
    public JsonResponse computeScore(String userName, String examCode, String examAppCode) {
        return JsonResponse.success(iExamAttributeService.computeScore(userName, examCode, examAppCode));
    }

    @ApiOperation(value = "2023卓越工程师页面跳转接口", notes = "2023卓越工程师页面跳转接口")
    @PostMapping(value = {"/pageJump", "sso/pageJump", "api/pageJump"})
    public JsonResponse pageJump() {
        return iExamAttributeService.pageJump();
    }


    @ApiOperation(value = "定制化考试出题-IT运维知识测评", notes = "定制化考试出题-IT运维知识测评")
    @PostMapping(value = {"/specialExamLayout", "sso/specialExamLayout", "api/specialExamLayout"})
    public JsonResponse specialExamLayout(@RequestParam(required = false) String currentUserCode,
                                            @RequestParam(required = false) String source,
                                            @RequestBody(required = false) Map<String, Object> condition) {
        return iExamAttributeService.specialExamLayout(currentUserCode, source, condition);
    }


    @ApiOperation(value = "定制化考试出题-公共加专业", notes = "定制化考试出题-公共加专业")
    @PostMapping(value = {"/specialExamPublic", "sso/specialExamPublic", "api/specialExamPublic"})
    public JsonResponse specialExamPublic(@RequestParam(required = false) String currentUserCode,
                                          @RequestParam(required = false) String source,
                                          @RequestBody(required = false) Map<String, Object> condition) {
        return iExamAttributeService.specialExamPublic(currentUserCode, source, condition);
    }

    @ApiOperation(value = "定制化考试出题-2024年度领导干部自我监督测评(第二期)", notes = "定制化考试出题-2024年度领导干部自我监督测评(第二期)")
    @PostMapping(value = {"/specialExamLDGBZWJDCP2", "sso/specialExamLDGBZWJDCP2", "api/specialExamLDGBZWJDCP2"})
    public JsonResponse specialExamLDGBZWJDCP2(@RequestParam(required = false) String currentUserCode,
                                          @RequestParam(required = false) String source,
                                          @RequestBody(required = false) Map<String, Object> condition) {
        return iExamAttributeService.specialExamLDGBZWJDCP2(currentUserCode, source, condition);
    }
}
