/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsAnswerRecord;
import com.simbest.boot.exam.knowledge.model.UsTerminalLoginInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
public interface IUsTerminalLoginInfoService extends ILogicService<UsTerminalLoginInfo,String> {
    JsonResponse permissionCheck(Long timestamp, String source, String currentUserCode);
}
