/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.repository;/**
 * Created by KZH on 2019/10/8 15:09.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.security.core.parameters.P;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:09
 * @desc 题目的正确答案
 **/
public interface ExamQuestionAnswerRepository extends LogicRepository<ExamQuestionAnswer,String> {

    /**
     * 根据题目编码获取答案
     * @param questionCode
     * @return
     */
    @Query(value =  "select t.*  from us_exam_question_answer t  where t.question_Code=:questionCode and t.enabled=1  order by t.answer_code",
            nativeQuery = true)
    List<ExamQuestionAnswer> findAllByQuestionCode(@Param("questionCode")String questionCode);

    @Query(value =  "select t.*  from us_exam_question_answer t  where t.parent_id=:id and t.enabled=1  order by t.answer_code",
            nativeQuery = true)
    List<ExamQuestionAnswer> findAllByParentId(@Param("id")String id);

    @Query(value =  "select t.*  from us_exam_question_answer t  where t.question_Code=:questionCode and t.answer_Code=:answerCode and t.is_correct=1 and t.enabled=1 ",
            nativeQuery = true)
    ExamQuestionAnswer findAllCorrect(@Param("questionCode")String questionCode,@Param("answerCode")String answerCode);

    @Query(value =  "select t.*  from us_exam_question_answer t  where t.question_Code=:questionCode  and t.is_correct=:isCorrect and t.answer_Code=:answerCode   and t.enabled=1 ",
            nativeQuery = true)
    ExamQuestionAnswer findAllCorrectAndAndIsCorrect(@Param("questionCode")String questionCode,@Param("isCorrect")String isCorrect,@Param("answerCode")String answerCode);

    /**
     * 根据题目编码获取答案
     * @param questionCode
     * @return
     */
    @Query(value =  "select t.*  from us_exam_question_answer t  where t.question_Code=:questionCode   and t.enabled=1 ",
            countQuery = "select count(1)from us_exam_question_answer t where  t.question_Code=:questionCode and t.enabled=1  ",
            nativeQuery = true)
    Page<ExamQuestionAnswer> findAllByQuestionCodePage(@Param("questionCode")String questionCode, Pageable pageable);


    @Query(value =  "select t.answer_code from US_EXAM_QUESTION_ANSWER t where t.question_code =:questionCode and t.is_correct =:isCorrect and t.enabled=1 order by t.answer_code  ",
            nativeQuery = true)
    List<String> findIsCorrectByExamCode(@Param("questionCode")String questionCode,@Param("isCorrect")String isCorrect);


    @Query(value =  "SELECT  LISTAGG(ueqa.answer_code, '/') WITHIN GROUP (ORDER BY ueqa.answer_code) AS trueanswer ,ueqa.question_code as answecode ,ueq.question_score score " +
            "from exam.US_EXAM_QUESTION_ANSWER ueqa " +
            "LEFT JOIN  exam.US_EXAM_QUESTION ueq ON ueq.question_code =ueqa.question_code " +
            "where ueqa.is_correct=1 and  ueqa.question_code in(select i.question_code  from exam.US_EXAM_QUESTION i where i.question_bank_code= :questionBankCode) " +
            "group by   ueqa.question_code,ueq.question_score order by  ueqa.question_code ",
            nativeQuery = true)
    List<Map<String,Object>>  findAllCorrectByQuestionBankCode(@Param("questionBankCode") String questionBankCode);
}
