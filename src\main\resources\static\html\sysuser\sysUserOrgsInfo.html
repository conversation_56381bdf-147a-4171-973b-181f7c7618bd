﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>新增或修改</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var gps = getQueryString();
        $(function () {
            //点击打开选择部门
            $(".chooseOrgPosition").on("click", function () {
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                var href = {"multi": "0", "name": "chooseOrgPositionVal"};
                if ($("#orgCode").val() != "") {
                    top.chooseWeb.chooseOrgPositionVal={"data":{"orgCode":$("#orgCode").val(),"positionId":$("#positionId").val()}};
                    href.mod=1;
                }else {
                    top.chooseWeb.chooseOrgPositionVal = {};
                }
                var url = tourl('html/sysuser/chooseOrgPosition.html', href);
                top.dialogP(url, 'sysUserOrgsTableshowDialogTopF', '选择部门', 'chooseOrgPosition', false, '1100','580');
            });
        });
        //只读
        window.formReady = function () {
            formReadonly("sysUserOrgsTableAddForm");
        };
        //取消只读
        window.formReadyNO = function () {
            formReadonlyNo("sysUserOrgsTableAddForm");
        };
        //选择企业回调
        window.chooseOrgPosition = function (data) {
            $("#orgCode").val(data.data.orgCode);
            $("#orgName").val(data.data.orgName);
            $("#positionId").val(data.data.positionId);
            $("#positionName").val(data.data.positionName);
        };
        //初始化界面
        window.initsystem = function () {
            $("#username").val(gps.username);
        };
        //绑定值到form
        window.bindval = function (data, pageparam) {
            data.orgName=data.org.orgName;
            data.positionName=data.position.positionName;
            //把取到的数据赋值到对应form表单
            formval(data, pageparam.dialogform.formname, pageparam.dialogform.ctable, (pageparam.dialogform.divimages || ""), pageparam.dialogform.ctablerender);
        };
        //表单校验
        window.fvalidate = function () {
            return $("#sysUserOrgsTableAddForm").form("validate");
        };
    </script>
</head>
<body>
<form id="sysUserOrgsTableAddForm" method="post" contentType="application/json; charset=utf-8" class="clear" cmd-select="action/user/org/findById" bindval="bindval()" initsystem="initsystem()">
    <!--........隐藏属性begin........-->
    <input id="id" name="id" type="hidden"/>
    <input id="username" name="username" type="hidden"/>
    <input id="orgCode" name="orgCode" type="hidden"/>
    <input id="status" name="status" value="0" type="hidden"/>
    <input id="positionId" name="positionId" type="hidden"/>
    <!--........隐藏属性end........-->
    <table border="0" cellpadding="0" cellspacing="10" width="100%">
        <tr>
            <td width="100" align="right"><font class="col_r">*</font>所属部门：</td>
            <td width="300">
                <input id="orgName" name="orgName" type="text" readonly="readonly" value="" class="easyui-validatebox" required='required'/><!-- 这里要弹出查询企业列表页面-->
            </td>
            <td><a class="btn small a_warning chooseOrgPosition"><i class="iconfont">&#xe634;</i></a></td>
        </tr>
        <tr>
            <td width="100" align="right"><font class="col_r">*</font>部门职务：</td>
            <td width="300">
                <input id="positionName" name="positionName" type="text" readonly="readonly" value="" class="easyui-validatebox" required='required'/><!-- 这里要弹出查询企业列表页面-->
            </td>
        </tr>
        <tr>
            <td width="100" align="right"<font class="col_r">*</font>显示顺序：</td>
            <td width="300">
                <input id="displayOrder" name="displayOrder" type="text" validType="zinteger" class="easyui-validatebox" required='required'/><!-- 这里要弹出查询企业列表页面-->
            </td>
        </tr>
    </table>
</form>
</body>
</html>
