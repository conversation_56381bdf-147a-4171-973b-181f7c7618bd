//package com.simbest.boot.exam.publicLottery.service.impl;
//
//import cn.hutool.core.io.FileUtil;
//import cn.hutool.core.net.NetUtil;
//import com.google.common.collect.Lists;
//import com.simbest.boot.base.exception.Exceptions;
//import com.simbest.boot.cmcc.a4.response.JsonResponse;
//import com.simbest.boot.exam.publicLottery.model.PublicLottery;
//import com.simbest.boot.exam.publicLottery.repository.PublicLotteryRepository;
//import com.simbest.boot.exam.publicLottery.service.IPublicLotteryPersonService;
//import com.simbest.boot.exam.publicLottery.service.IPublicLotteryService;
//import com.simbest.boot.util.encrypt.RsaEncryptor;
//import com.simbest.boot.util.http.client.TextBodyRequest;
//import com.simbest.boot.util.security.LoginUtils;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
//import org.springframework.stereotype.Service;
//import org.springframework.web.client.RestTemplate;
//
//import javax.annotation.PostConstruct;
//import java.io.File;
//import java.io.IOException;
//import java.nio.file.Files;
//import java.nio.file.Path;
//import java.util.*;
//import java.util.concurrent.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class TestImpl_bak {
//
//    private final LoginUtils loginUtils;
//    private final RsaEncryptor rsaEncryptor;
//    private final IPublicLotteryService publicLotteryService;
//    private final IPublicLotteryPersonService publicLotteryPersonService;
//
//    @Autowired
//    private final PublicLotteryRepository publicLotteryRepository;
//
//    private final ExecutorService executor = Executors.newFixedThreadPool(20);
//    private final CompletionService<JsonResponse> completionService = new ExecutorCompletionService<>(executor);
//    private final ConcurrentLinkedQueue<JsonResponse> queue = new ConcurrentLinkedQueue<>();
//
//    @Value("${app.host.port}")
//    private static String host;
//    private static List<String> users;
//    /**
//     * httpPost
//     */
//    private static final Function<String, TextBodyRequest> httpPost = (String u) -> {
//        TextBodyRequest textBodyRequest = new TextBodyRequest(u);
//        RestTemplate restTemplate = new RestTemplate();
//        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
//        factory.setReadTimeout(60000); // 设置读取超时时间（毫秒）
//        factory.setConnectTimeout(60000); // 设置连接超时时间（毫秒）
//        restTemplate.setRequestFactory(factory);
//        textBodyRequest.setRestTemplate(restTemplate);
//        return textBodyRequest;
//    };
//
//    @PostConstruct
//    public void init() {
//        String ip = NetUtil.getLocalhostStr();
//        if (ip.equals("***********")) host = "http://***********:10063";
////        if (ip.equals("***********")) host = "http://************:8088";
//        host += "/exam";
//
//        Path path = new File("C:\\Users\\<USER>\\Desktop\\users.md").toPath();
//        try {
//            users = Files.readAllLines(path);
//        } catch (IOException e) {
//            users = new ArrayList<>();
//            Exceptions.printException(e);
//        }
//    }
//
//    /**
//     * 乐观锁测试
//     */
//    public void testOptimisticLocking() {
//
//        // Simulate concurrent updates
//        ExecutorService executor = Executors.newFixedThreadPool(2);
//        Future<?> future1 = executor.submit(() -> testSave("abc"));
//        Future<?> future2 = executor.submit(() -> testSave("cde"));
//        Future<?> future3 = executor.submit(() -> testSave("cde1"));
//        Future<?> future4 = executor.submit(() -> testSave("cde2"));
//        Future<?> future5 = executor.submit(() -> testSave("cde3"));
//        Future<?> future6 = executor.submit(() -> testSave("cde4"));
//
//        // Wait for the threads to finish
//        executor.shutdown();
//        try {
//            executor.awaitTermination(10, TimeUnit.SECONDS);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//
//        // Verify that one update succeeded and the other threw an OptimisticLockingFailureException
//        // You can also check the database to see the final state of the product
//    }
//
//    public void testSave(String username) {
//        PublicLottery byId = publicLotteryService.findById("UPL731173617272709120");
//        byId.setUsername(username);
//        PublicLottery update = publicLotteryService.update(byId);
//        System.out.println(update);
//    }
//
//    /**
//     * 压测抽奖
//     */
//    public void drawLottery() {
//        loginUtils.adminLogin();
//        String url = String.format("%s%s%s?1=1", host, "/action/publicLottery", "/drawLottery/anonymous");
//
//        Function<String, JsonResponse> handler = (String v) -> {
//            String format = String.format("%s%s", url, "&currentUserCode=" + v);
//            JsonResponse response = httpPost.apply(format).asBean(JsonResponse.class);
//            log.info("response:{}", response);
//            return response;
//        };
//
//        List<Future<JsonResponse>> collect = users.parallelStream().map(user -> {
//            return completionService.submit(() -> handler.apply(user));
//        }).collect(Collectors.toList());
//
//        collect.forEach(future -> {
//            try {
//                JsonResponse response = completionService.take().get();
//                if (response != null) {
//                    queue.offer(response);
//                }
//            } catch (InterruptedException | ExecutionException e) {
//                log.error("执行异常", e);
//            }
//        });
//        // 关闭ExecutorService，等待所有任务完成
//        executor.shutdown();
//
////        users.parallelStream().forEach(v -> {
////            new Thread(() -> handler.accept(v)).start();
////        });
//
//        writeFile("drawLottery.txt", Collections.singleton(queue));
//    }
//
//    /**
//     * 重置摇号池
//     */
//    public void resetLottery() {
//        loginUtils.adminLogin();
//        String url = String.format("%s%s%s?1=1", host, "/action/publicLottery", "/resetLottery/anonymous");
//
//        url = String.format("%s%s", url, "&1=1");
//
//        JsonResponse response = httpPost.apply(url).asBean(JsonResponse.class);
//        log.info("response:{}", response);
//
//        writeFile("resetLottery.txt", Arrays.asList(response));
//    }
//
//    /**
//     * 添加抽奖人员
//     */
//    public void resetLotteryPerSon() {
//        loginUtils.adminLogin();
//        String url = String.format("%s%s%s?1=1", host, "/action/publicLottery", "/resetLotteryPerSon/anonymous");
//
//        ArrayList<Object> list = Lists.newArrayList();
//        users.parallelStream().forEach(user -> {
//
//            String format = String.format("%s%s%s", url, "&username=" + user, "&allDelete=false");
//            JsonResponse response = httpPost.apply(format).asBean(JsonResponse.class);
////            log.info("response:{}", response);
//            list.add(response);
//        });
//
//        writeFile("resetLotteryPerSon.txt", list);
//    }
//
//    /**
//     * 写入文件
//     *
//     * @param name
//     * @param list
//     */
//    public static void writeFile(String name, Collection<Object> list) {
//        File file = FileUtil.file("C:\\Users\\<USER>\\Desktop\\" + name);
//        FileUtil.writeUtf8Lines(list, file);
//    }
//
//}
