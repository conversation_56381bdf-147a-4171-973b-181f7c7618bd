<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>考试维护</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
            <style>
                .searchtable{
                    margin-right: 20px;
                }
            </style>
    <script type="text/javascript">

        $(function () {
            getCurrent();
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#examBacklogTable",//table列表的id名称，需加#
                    "querycmd": "action/summary/findExamSummaryList",//table列表的查询命令
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "sortOrder": 'desc',
                    "scrollbarSize": "20px",
                    // "styleClass":"noScroll", 
                    "frozenColumns":[[
                        {title: "考试名称", field: "examName", width: 170,sortable: true, tooltip: true,align:"center",
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g=[];
                                g.push("<a href='#' class='readInfo  operateBlue' id='"+row.id + "' readDialogindex='" + index + "'>"+row.examName+"</a>");
                                return g;
                            }
                        },
                    ]],
                    "columns": [[//列

                        {title: "考试编码", field: "examCode", width: 100,sortable: true, tooltip: true,resizable:false,align:"center"},
                        {title: "已推待办", field: "joinExamNum", width: 100,sortable: true, tooltip: true,resizable:false,align:"center"},
                        {title: "已推统一待办", field: "todoExamNum", width: 100,sortable: true, tooltip: true,resizable:false,align:"center"},
                        {title: "实考人数", field: "actualExamNum", width: 100,sortable: true, tooltip: true,resizable:false,align:"center"},
                        {title: "考试开始时间", field: "examStartTime", width: 100,sortable: true, tooltip: true,resizable:false,align:"center"},
                        {title: "考试结束时间", field: "examEndTime", width: 100,sortable: true, tooltip: true,resizable:false,align:"center"},
                        {title: "待办推送状态", field: "workFlag", width: 100,sortable: true, tooltip: true,resizable:false,align:"center",
                            formatter:function (value, row, index) {
                                if (value == true) {
                                    return "是";
                                }
                                return "否";
                            }
                        },
                        {title: "统一待办推送状态", field: "todoFlag", width: 120,sortable: true, tooltip: true,resizable:false,align:"center",
                            formatter:function (value, row, index) {
                                if (value == true) {
                                    return "是";
                                }
                                return "否";
                            }
                        },
                        {title: "短信推送状态", field: "messageFlag", width: 100,sortable: true, tooltip: true,resizable:false,align:"center",
                            formatter:function (value, row, index) {
                                if (value == true) {
                                    return "是";
                                }
                                return "否";
                            }
                        },
                        {title: "移动端展示", field: "appEnabled", width: 100,sortable: true, tooltip: true,resizable:false,align:"center",
                            formatter:function (value, row, index) {
                                if (value == true) {
                                    return "是";
                                }
                                return "否";
                            }
                        },
                        {title: "PC端展示", field: "pcEnabled", width: 100,sortable: true, tooltip: true,resizable:false,align:"center",
                            formatter:function (value, row, index) {
                                if (value == true) {
                                    return "是";
                                }
                                return "否";
                            }
                        },
                        {title: "考试权重值", field: "weightValue", width: 90,sortable: true, tooltip: true,align:"center"},
                        // {title: "考试待办类型", field: "workType", width: 90,sortable: true, tooltip: true,align:"center"},
                        {title: "定时器状态", field: "taskStatus", width: 70,align:"center",
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                if(row.taskStatus == 0){
                                    var g = "未开启";
                                }else if(row.taskStatus == 1){
                                    var g = "已开启";
                                }
                                return g;
                            }
                        },
                        {
                            field: "opt", title: "操作", width: 800, rowspan: 1,sortable: true, tooltip: true,resizable:false,align:"center",//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g=[];
                                g.push("<a href='#' class=' update' id='" + row.id + "' readDialogindex='" + index + "'>【编辑】</a>");
                                // g.push("<a href='#' class='editExamRange' id='" + row.id + "' readDialogindex='" + index + "'>【考试范围】</a>");
                                g.push("<a href='#' class='openbacDialog' examCode='" + row.examCode + "' readDialogindex='" + index + "'>【推送待办】</a>");
                                g.push("<a href='#' class='openDialog' examCode='" + row.examCode + "' readDialogindex='" + index + "'>【推送统一待办】</a>");
                                g.push("<a href='#' class='openDealDialog' examCode='" + row.examCode + "' readDialogindex='" + index + "'>【核销待办、统一待办】</a>");
                                g.push("<a href='#' class='opensaDialog' examCode='" + row.examCode + "' readDialogindex='" + index + "'>【推送短信催办】</a>");
                                g.push("<a href='#' class='openTaskStatus' examCode='" + row.examCode + "' readDialogindex='" + index + "'>【开关定时推送】</a>");
                                g.push("<a href='#' class='openSmsDialog' examCode='" + row.examCode + "' readDialogindex='" + index + "'>【推送短信待办】</a>");
                                g.push("<a href='#' class='exportCYLDialog' examCode='" + row.examCode + "' readDialogindex='" + index + "'>【通用考试结果导出】</a>");
                                // g.push("<a href='#' class='openDetailDialog' examCode='" + row.examCode + "' readDialogindex='" + index + "'>【生成考试答题明细】</a>");
                                return g.join("");
                            }
                        },
                    ]],
                },

                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "ctable": "maUserOrg",
                    "formname": "#examBacklogTableForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/examWork/create",//新增命令
                    "updatacmd": "action/examWork/update",//修改命令
                    "onSubmit": function (param) {//在请求加载数据之前触发。返回false可以停止该动作
                        return true;
                    }

                },
                "readDialog":{//查看
                    "dialogid": "#readDag",
                    "dialogedit": true,//查看对话框底部要不要编辑按钮
                    "formname":"#examBacklogTableReadForm"
                }
            };
            loadGrid(pageparam);



            //查看考试信息
            $(document).on('click','.readInfo',function(e){

                //ie8不兼容event、target；以下写法用来兼容ie8
                var event = e || window.event;
                var target = event.target||event .srcElement;
                top.dialogP('html/exammanage/examManage_edit.html?type=read&id='+target.id,window.name,'查看信息','readInfoExam',false,'750','500');
            })

            window.readInfoExam = function (){//查看信息
                //loadList();
            }



            //编辑考试信息
            $(document).on('click','.update',function(e){

                //ie8不兼容event、target；以下写法用来兼容ie8
                var event = e || window.event;
                var target = event.target||event .srcElement;
                top.dialogP('html/exammanage/examManage_edit.html?type=edit&id='+target.id,window.name,'编辑信息','appUpdate',false,'750','600');
            })



            //appUpdate的回调，发送更新请求和执行重新载入函数
            window.appUpdate = function (data){//编辑考试
                //console.log(data);
            ajaxgeneral({
                url: "action/summary/saveExamSummary",
                data:data.data[0],
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    if (res.status == 200) {
                        formreset('examBacklogTableQueryForm');
                        $('#examBacklogTable').datagrid('reload');
                    }
                }
            })
            }



            //新增考试信息
            $(document).on('click','.add',function(e){
                //ie8不兼容event、target；以下写法用来兼容ie8
                var event = e || window.event;
                var target = event.target||event .srcElement;
                top.dialogP('html/exammanage/examManage_add.html',window.name,'新增','addInfo',false,'650','600');
            })



            //新增考试信息的回调，发送更新请求和执行重新载入函数
            window.addInfo = function (data){//新增考试
            var datas=data.data[0];

            ajaxgeneral({
                url: "action/summary/saveExamSummary",
                data:datas,

                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    if (res.status == 200) {
                        formreset('examBacklogTableQueryForm');
                        $('#examBacklogTable').datagrid('reload');
                    }
                }
            })
            }




            //新增考试的回调，发送更新请求和执行重新载入函数
            window.appAdd = function (data){//添加考试

            ajaxgeneral({
                url: "action/summary/saveExamSummary",
                data:data.data,
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    if (res.status == 200) {
                        formreset('examBacklogTableQueryForm');
                        $('#examBacklogTable').datagrid('reload');
                    }
                }
            })
            }

            //重置按钮
            $(document).on('click','.reset',function (){
                formreset('examBacklogTableQueryForm');
            });

            //编辑考试范围信息
            $(document).on('click','.editExamRange',function(e){
                //ie8不兼容event、target；以下写法用来兼容ie8
                var event = e || window.event;
                var target = event.target||event .srcElement;
                top.dialogP('html/exammanage/examManage_range.html?summaryId='+target.id,window.name,'考试范围','editExamRange',false,'650','600');
            })


            //编辑考试范围信息的回调
            window.editExamRange = function (data) {//考试范围
                var summaryId = data.data[0].summaryId;
                var datas = data.data[0];
                for (var index = 0; index < datas.length; index++) {
                    datas[index].parentCompanyCode=datas[index].belongCompanyCode;
                    datas[index].companyCode=datas[index].orgCode;
                }
            ajaxgeneral({
                url: "action/range/saveExamRange?",
                data:{"summaryId":summaryId,"rangeList":datas},
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    if (res.status == 200) {
                        formreset('examBacklogTableQueryForm');
                        $('#examBacklogTable').datagrid('reload');
                    }
                }
            })
            }



            //推送待办
            $(document).on('click','.openbacDialog',function(){
                var examCode=$(this).attr('examCode');
                mesConfirm('','确定推送吗',function(){
                    ajaxgeneral({
                        url: "action/summary/createExamWork?examCode=" + examCode,
                        async: false,
                        success: function (res) {
                        }
                    });
                });
            })

            //推送统一待办
            $(document).on('click','.openDialog',function(){
                var examCode=$(this).attr('examCode');
                mesConfirm('','确定推送吗',function(){
                    ajaxgeneral({
                        url: "action/summary/sendTodo?examCode=" + examCode,
                        async: false,
                        success: function (res) {
                        }
                    });
                });
            });

            //核销待办、统一待办
            $(document).on('click','.openDealDialog',function(){
                var examCode=$(this).attr('examCode');
                mesConfirm('','确定核销吗',function(){
                    ajaxgeneral({
                        url: "action/summary/dealTodo?examCode=" + examCode,
                        async: false,
                        success: function (res) {
                        }
                    });
                });
            });

            //推送催发短息
            $(document).on('click','.opensaDialog',function(){
                var examCode=$(this).attr('examCode');
                mesConfirm('','确定推送吗',function(){
                    ajaxgeneral({
                        url: "action/summary/sendUrgeSms?examCode=" + examCode,
                        async: false,
                        success: function (res) {

                        }
                    });
                });
            });

            //推送短信问卷办理入口
            $(document).on('click','.openSmsDialog',function(){
                var examCode=$(this).attr('examCode');
                mesConfirm('','确定推送吗',function(){
                    ajaxgeneral({
                        url: "action/summary/createExamSmsWork?examCode=" + examCode,
                        async: false,
                        success: function (res) {

                        }
                    });
                });
            });

            // 参与率导出
            $(document).on('click', '.exportCYLDialog', function () {
                var examCode = $(this).attr('examCode');
                $("#exportForm").attr("action",web.rootdir+"action/statisticExamInfo/exportExcel?examCode=" + examCode);
                $("#exportForm .exportSubmit").trigger("click");
            });

            //推送催发短息
            $(document).on('click','.openTaskStatus',function(){
                var examCode=$(this).attr('examCode');
                mesConfirm('','是否更改定时器状态',function(){
                    ajaxgeneral({
                        url: "action/task/manager/taskStatusManage?examCode=" + examCode,
                        async: false,
                        success: function (res) {
                            window.location.reload();
                        }
                    });
                });
            });


            //生成考试答题明细
            $(document).on('click','.openDetailDialog',function(){
                var examCode=$(this).attr('examCode');
                mesConfirm('','确定生成考试明细吗',function(){
                    ajaxgeneral({
                        url: "action/examInfo/saverExamInfoDetails?examCode=" + examCode,
                        async: false,
                        success: function (res) {
                        }
                    });
                });
            });

            //测试导出
            $(document).on('click', '.export1', function () {
                $("#deptInfoExport").attr("action", web.rootdir + "action/statisticExamInfo/exportExcelBy2024xszg_ks");
                $("#deptInfoExport").attr("method", "post");
                $("#deptInfoExport").submit();
            });
        });

        //表单校验
        window.fvalidate = function () {
            return $("#examBacklogTableAddForm").form("validate");
        };



        function initsystem(){
            //初始化表单操作代码

        };



        //弹出框关闭时，去刷新列表
        function close() {
            $("#examBacklogTable").datagrid("reload");
        }


        function loadList(){
            $('#examBacklogTableQueryForm').datagrid('reload');
    }


    </script>

</head>
<body class="body_page">

<!--searchform-->
<div class="table_searchD">
    <form id="examBacklogTableQueryForm">
      <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
          <td width="90" align="right">考试名称：</td>
          <td width="150"><input id="examName" name="examName" type="text"  /></td>
          <td width="90" align="right">考试编码：</td>
          <td width="150"><input id="examCode" name="examCode" type="text"  /></td>
          <td>
            <div class="w100">
              <a class="btn fl searchtable"><span>查询</span></a>
              <a class="btn fl reset"><span>重置</span></a>
              <a class="btn a_green fr add "   width="680" height="700">新建</a>
            </div>
          </td>
        </tr>
      </table>
    </form>
  </div>

<!--table-->
<div class="examBacklogTable">
    <table id="examBacklogTable"></table>
</div>

<form id="exportForm" class="hide" method="post">
    <input type="submit" class="exportSubmit"/>
</form>
<div>
    <form id="deptInfoExport" method="post">
        <button hidden="hidden" class="abtn export1" style="width: 200px; height: 30px">2024xszg_ks测试导出</button>
    </form>
</div>
</body>
</html>
