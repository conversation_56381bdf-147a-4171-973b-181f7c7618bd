/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service;/**
 * Created by KZH on 2019/10/8 15:11.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.model.ExamInfoSave;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:11
 * @desc 答题记录
 **/
public interface IExamInfoService extends ILogicService<ExamInfo,String> {


    /**
     * 保存答题记录
     * @param examInfo
     * @return
     */
    JsonResponse saveExamInfo(ExamInfo examInfo);
    JsonResponse saveExamInfo(String currentUserCode,String source,ExamInfo examInfo);

    /**
     * 保存答题记录 只新增 适用于只回答一次
     * @param examInfo
     * @return
     */
    JsonResponse saveExamInfoNoUpdate(ExamInfo examInfo);

    JsonResponse saveSpecialExamInfoNoUpdate(ExamInfo examInfo);

    /**
     * 获取到人的未完成试卷
     * @param examInfo
     * @return
     */
    JsonResponse unfinishedExam(ExamInfo examInfo);

    /**
     * 获取到人的未完成试卷 返回结果为实体对象
     * @param publishUsername
     * @param examAppCode
     * @return
     */
    ExamInfo unfinishedBackExam(String publishUsername,String examAppCode);

    /**
     * 判题
     * @param examInfo
     * @return
     */
    JsonResponse judgeExam(ExamInfo examInfo);


    JsonResponse judgeExamPowerBuilding(ExamInfo examInfo);

    JsonResponse judgeExamAndCertificate(ExamInfo examInfo);

    /**
     * 阅卷
     * @param publishUsername
     * @return
     */
    JsonResponse markingExam(String publishUsername);

    /**
     * 获取未阅卷人员
     * @param examInfo
     * @param pageable
     * @return
     */
    JsonResponse findAllByIsMarkingExam(ExamInfo examInfo, Pageable pageable);

    boolean accomplishMarking(String publishUsername);

    JsonResponse manualInitPosition();

    ExamInfo findByExamInfo(String publishUsername ,String workType);

    JsonResponse getExamInfoByUsername(String username);

    /**
     * 保存试卷
     */
    ExamInfo saveExam(String currentUserCode, String source, ExamInfo examInfo);

    ExamInfoSave saveExamSalt(String currentUserCode, String source, String str);

    /**
     * 保存试卷
     */
    ExamInfo saveExam2(String currentUserCode, String source, ExamInfo examInfo);

    @Transactional(rollbackFor = Exception.class)
    ExamInfo submitExamSalt(String currentUserCode, String source, ExamInfo o, boolean checkExamTime);

    ExamInfo submitExam1(String currentUserCode, String source, ExamInfo o);

    /**
     * 提交试卷
     */
    ExamInfo submitExam(String currentUserCode, String source, ExamInfo examInfo);

    ExamInfo submitExamSalt(String currentUserCode, String source, String str);

    ExamInfo submitExamSalt(String currentUserCode, String source, ExamInfo o);

    /**
     * 提交试卷(嵌入式廉洁风险责任人员测试)
     *
     * @param currentUserCode 当前人编码
     * @param source          请求来源
     * @param examInfo       提交考试数据表
     * @return 返回答题记录信息
     */
    ExamInfo submitExamHonestRisk(String currentUserCode, String source, ExamInfo examInfo);

    /**
     * 查询指定人的答题情况
     */
    ExamInfo findExamInfo(String publishUsername, String examCode, String examAppCode);

    /**
     * 查询指定人的答题情况
     */
    ExamInfo findExamInfo(String publishUsername,  String examAppCode);

    ExamInfo findExamInfo1(String publishUsername, String examAppCode);

    /**
     * 统计参与考试人数
     */
    Integer countActualExamNum(String examCode);

    List<ExamInfo> findExamInfo(String examCode);

    /**
      * @desc 根据考试编码查询当前人的考试成绩
      * <AUTHOR>
      */
    ExamInfo findExamInfoByExamCode(String examCode,String creator);

    /**
      * @desc 根据考试编码查询参与考试的人员信息
      * <AUTHOR>
      */
    Page<ExamInfo> findExamUserInfoByExamCode(String examInfo,String publishTruename, int page, int size);

    /**
      * @desc 评阅考试分数
      * <AUTHOR>
      */
    ExamInfo updateExamInfo(ExamInfo examInfo);

    /**
      * @desc 根据考试编码 人员名称进行模糊查询参与考试的人员
      * <AUTHOR>
      */
    Page<ExamInfo> findUserByExamCodeTrueName(ExamInfo examInfo,int page,int size);

    /**
     * 测评结果查询
     * @return
     */
    JsonResponse findTestResultSelect(Map<String,Object> paramMap, int pageIndex, int pageSize);

    /**
     * 测评结果导出
     * @param request
     * @param response
     * @param paramMap
     * @return
     */
    void exportTestResultSelect(HttpServletRequest request,HttpServletResponse response,Map<String, Object> paramMap);

    ExamInfo submitExamNew(String currentUserCode, String source, ExamInfo examInfo);
}
