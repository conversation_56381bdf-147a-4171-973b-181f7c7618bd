/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.repository;/**
 * Created by KZH on 2019/10/8 16:06.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.knowledge.model.UsTerminalLoginInfo;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:06
 * @desc
 **/
public interface UsTerminalLoginInfoRepository extends LogicRepository<UsTerminalLoginInfo, String> {

    @Query(value = "SELECT t. * FROM us_terminal_login_info  t WHERE t.enabled=1 AND  t.creator= :creator AND t.timestamp> :timestamp ORDER BY t.created_time ASC " ,
            nativeQuery = true)
    List<UsTerminalLoginInfo> findByUserNameAndTimetamp(@Param("creator") String creator,@Param("timestamp") Long timestamp);


    @Modifying
    @Query(value = "delete  from   US_TERMINAL_LOGIN_INFO t where  t.timestamp  < :timestamp  " ,
            nativeQuery = true)
    void deleteByCreateTime(@Param("timestamp") Long timestamp);
}
