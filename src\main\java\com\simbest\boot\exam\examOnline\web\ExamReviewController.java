/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.web;/**
 * Created by KZH on 2019/10/17 16:48.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamReview;
import com.simbest.boot.exam.examOnline.service.IExamReviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-17 16:48
 * @desc
 **/
@Api(description = "评测记录")
@Slf4j
@RestController
@RequestMapping(value = "/action/examReview")
public class ExamReviewController extends LogicController<ExamReview, String> {

    private IExamReviewService iExamReviewService;

    @Autowired
    public ExamReviewController(IExamReviewService service) {
        super(service);
        this.iExamReviewService = service;

    }

    @ApiOperation(value = "保存评测记录", notes = "保存评测记录")
    @PostMapping(value = {"/saveExamReview", "sso/saveExamReview"})
    public JsonResponse saveExamReview(@RequestBody(required = false) Map<String, Object> sumMap) {

        return iExamReviewService.saveExamReview(sumMap);
    }
}
