package com.simbest.boot.exam.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_answer_info")
@ApiModel(value = "答题记录表")
public class ExamAnswerInfo extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EAI") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "考试编码",name = "examCode")
    private String examCode;

    @Column(length = 40)
    @ApiModelProperty(value = "试卷编码",name = "examAppCode",example = "hnjjwz")
    private String examAppCode;

    @Column(length = 40)
    @ApiModelProperty(value = "题库编码",name = "questionBankCode",example = "A-001",required = true)
    private String questionBankCode;

    @Column(length = 250)
    @ApiModelProperty(value = "题目编码",name = "questionCode",example = "A-001-1",required = true)
    private String questionCode;

    @Column(length = 2000)
    @ApiModelProperty(value = "答案解析",name = "analysis",example = "A-001-1",required = true)
    private String analysis;

    @Column(length = 2000)
    @ApiModelProperty(value = "对应方法",name = "solution",example = "A-001-1",required = true)
    private String solution;
}
