/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.service.impl;/**
 * Created by KZH on 2019/12/9 9:20.
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.lottery.model.Jackpot;
import com.simbest.boot.exam.lottery.model.Lottery;
import com.simbest.boot.exam.lottery.model.NewJackpot;
import com.simbest.boot.exam.lottery.model.RetJackpot;
import com.simbest.boot.exam.lottery.repository.LotteryRepository;
import com.simbest.boot.exam.lottery.service.IJackpotSrevice;
import com.simbest.boot.exam.lottery.service.ILotteryService;
import com.simbest.boot.exam.lottery.service.INewJackpotService;
import com.simbest.boot.exam.util.ProbabilityUtils;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

import static com.simbest.boot.exam.util.Constants.APP_CODE;
import static com.simbest.boot.exam.util.Constants.SOURCE_M;


/**
 * <AUTHOR>
 * @create 2019-12-09 9:20
 * @desc
 **/
@Service
@Slf4j
public class LotteryServiceImpl extends LogicService<Lottery, String> implements ILotteryService {

    private LotteryRepository lotteryRepository;

    @Autowired
    private IJackpotSrevice iJackpotSrevice;

    @Autowired
    private INewJackpotService iNewJackpotService;

    @Autowired
    private ISysDictValueService iSysDictValueService;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    public LotteryServiceImpl(LotteryRepository repository) {
        super(repository);
        this.lotteryRepository = repository;

    }

    //中奖概率
    @Value("${app.exam.lottery.probability}")
    private int PROBABILITY;

    /**
     * 根据概率是否中奖
     *
     * @return 是否中奖
     */
    @Override
    public JsonResponse isLottery(String examCode) {
        log.info("当前中奖概率为：{}", PROBABILITY);
        //根据工具类获取是否中奖 中奖概率为固定25%
        boolean isLottery = ProbabilityUtils.IsHit(PROBABILITY);
        if (isLottery) {
            //中奖后判断奖池是否有剩余奖项
            Jackpot jackpot = iJackpotSrevice.findJackpotIsResidue();
            int remain = jackpot.getRemain();
            if (remain > 0) {
                IUser user = SecurityUtils.getCurrentUser();
                Lottery lottery = new Lottery();
                lottery.setUsername(user.getUsername());
                lottery.setExamCode(examCode);
                this.insert(lottery);
                remain--;
                jackpot.setRemain(remain);
                iJackpotSrevice.update(jackpot);
                return JsonResponse.success(true);
            }
        }
        return JsonResponse.success(false);
    }

    @Override
    public JsonResponse isLotteryExamPowerBuilding(String examCode, String source, String currentUserCode) {
        String s = RandomUtil.randomNumbers(6);
        // 如果用户是手机端登录，需要做模拟登录处理
        if (SOURCE_M.equals(source)) {
            loginUtils.manualLogin(currentUserCode, APP_CODE);
        }

        SysDictValue sysDictValue = iSysDictValueService.findByDictTypeAndName("retJackpotType", "中奖几率");
        String value = sysDictValue.getValue();

        RetJackpot retJackpot = ProbabilityUtils.IsHit2(Double.parseDouble(value));

        Boolean isJackpot = retJackpot.getIsJackpot();
        String getPrize = retJackpot.getGetPrize();

        if (isJackpot) {
            NewJackpot jackpotIsResidue = iNewJackpotService.findJackpotIsResidue(getPrize);

            // 没有查询到对应奖池后直接未中奖返回
            if (jackpotIsResidue == null) {
                retJackpot.setIsJackpot(false);
                retJackpot.setGetPrize("未中奖");
            } else {
                int remain = jackpotIsResidue.getRemain();
                if (remain > 0) {
                    jackpotIsResidue.setRandomNumber(s);
                    iNewJackpotService.update(jackpotIsResidue);
                    // 确认奖池有奖后 数量减一并更新库
                    remain--;
                    jackpotIsResidue.setRemain(remain);

                    NewJackpot newJackpot = iNewJackpotService.findJackpotIsResidueByRandomNumber(getPrize, s);

                    // 根据随机值和奖项确保奖项还存在
                    if (newJackpot != null) {
                        iNewJackpotService.update(jackpotIsResidue);
                        IUser user = SecurityUtils.getCurrentUser();
                        Lottery lottery = new Lottery();
                        lottery.setUsername(user.getUsername());
                        lottery.setPrize(getPrize);
                        lottery.setExamCode(examCode);
                        this.insert(lottery);
                    } else {
                        retJackpot.setIsJackpot(false);
                        retJackpot.setGetPrize("未中奖");
                    }
                } else {
                    retJackpot.setIsJackpot(false);
                    retJackpot.setGetPrize("未中奖");
                }
            }

        }

        return JsonResponse.success(retJackpot);

    }

    /**
     * 对中奖人的信息进行处理
     *
     * @param lottery
     * @return
     */
    @Override
    public JsonResponse createToJackpotReduce(Lottery lottery) {
        Assert.notNull(lottery, lottery + "参数不可为空");
        String username = lottery.getUsername();
        List<Lottery> lotteryList = lotteryRepository.findByUsername(username);

        if (CollUtil.isEmpty(lotteryList)) {
            return JsonResponse.fail(-1, "填报信息失败，验证中奖失败");
        }
        Lottery lottery1 = lotteryList.get(lotteryList.size() - 1);
        lottery1.setTruename(lottery.getTruename());
        lottery1.setPhone(lottery.getPhone());
        lottery1.setAddress(lottery.getAddress());
        this.update(lottery1);

        return JsonResponse.defaultSuccessResponse();
    }
}
