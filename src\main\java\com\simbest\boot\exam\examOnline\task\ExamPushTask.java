package com.simbest.boot.exam.examOnline.task;

import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.component.distributed.lock.AppRuntimeMaster;
import com.simbest.boot.component.task.AbstractTaskSchedule;
import com.simbest.boot.exam.examOnline.model.ExamTask;
import com.simbest.boot.exam.examOnline.service.IExamSummaryService;
import com.simbest.boot.exam.examOnline.service.IExamTaskService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.sys.repository.SysTaskExecutedLogRepository;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.security.LoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 洛阳分公司满意度调查问卷定时任务
 * 每季度推送一次
 */
@Slf4j
@Component
public class ExamPushTask extends AbstractTaskSchedule {

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private IExamTaskService examTaskService;

    @Autowired
    private IExamSummaryService examSummaryService;

    @Autowired
    public ExamPushTask(AppRuntimeMaster master, SysTaskExecutedLogRepository repository) {
        super(master, repository);
    }

    /**
     * 每天执行状态检查更新
     */
  //  @Scheduled(cron = "0 0 0 1 1/3 ? ")
  //  @Scheduled(cron = "0 0/5 * * * ?")  //每五分钟执行一次
    @Scheduled(cron = "0 0 1 * * ?")   //每天晚上一点执行一次
    public void checkAndExecute() {
        // 不记录日志
        super.checkAndExecute(false);
    }

    @Override
    public String execute() {
        loginUtils.adminLogin();
        String currSimpleMonth = DateUtil.getCurrSimpleMonth();
        String currSimpleDay = DateUtil.getCurrSimpleDay();
        String currDate=currSimpleMonth+currSimpleDay;
        System.out.println("定时任务的时间-------------------------------------------------"+currDate);
        //判断是否是对应的时间，如对应则分发洛阳满意度问卷调查
        if (currDate.equals(Constants.ONETIME) || currDate.equals(Constants.TWOTIME) || currDate.equals(Constants.THRTIME) || currDate.equals(Constants.FORTIME)){
        // 判断定时器是否已经打开
        Specification<ExamTask> build = Specifications.<ExamTask>and()
                .eq("enabled", Constants.QUERY_VALID_CONDITION)
                .eq("taskStatus", Constants.QUERY_VALID_CONDITION)
                .predicate(Specifications.<ExamTask>or()
                        .eq("examCode", Constants.EXAM_CODE_BRANCH_LY)
                        .eq("examCode", Constants.EXAM_CODE_OFFICE_LY)
                        .build())
                .build();
        List<ExamTask> result = examTaskService.findAllNoPage(build);
        // 定时任务开启，执行推送以及发短信操作
        if (null != result && result.size() > 0) {
            for (ExamTask examTask : result) {
                //推送满意度度调查问卷待办
                examSummaryService.createExamWorkTask(examTask.getExamCode());
                //推送满意度调查问卷统一待办
                examSummaryService.sendTodoTask(examTask.getExamCode());
                //发送催办短信
                examSummaryService.sendUrgeSmsTask(examTask.getExamCode());
            }
        }
        }
        return null;
    }
}
