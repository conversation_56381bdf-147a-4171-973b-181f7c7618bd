/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examStatistics.service;/**
 * Created by KZH on 2019/11/26 9:45.
 */

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examStatistics.model.Temporary;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-11-26 9:45
 * @desc 统计考试信息
 **/
public interface IStatisticExamInfoService  {

    /**
     * 获取到各个公司的答题情况 带答案
     * @param companyName 公司名称
     * @param questionBankCode 题库Code
     * @return
     */
    List<Temporary> getRawExamConditionInfo(String companyName,String questionBankCode);

    /**
     * 获取到各个公司中应完成问卷人数
     * @param companyName 公司名称
     * @return
     */
    int getCompanyShouldFinishPeople(String companyName);

    /**
     * 获取到各个公司中应完成问卷人数,排除某些职位
     * @param companyName 公司名称
     * @param department 部门
     * @param paramList 职位
     * @return
     */
    List<Temporary> getCompanyShouldFinishPeopleExclude(String companyName, String department,List<String> paramList);

    /**
     * 获取到各个公司部门的答题情况 带答案 根据指定职位
     * @param companyName 公司名称
     * @param department 部门
     * @param positionName 职位名称
     * @return
     */
    List<Temporary> getRawExamInfoByPositionName(String companyName,String department,String[] positionName);


    /**
     * 获取到各个公司部门的答题人数  根据指定职位
     * @param companyName 公司名称
     * @param department 部门
     * @param positionName 职位名称
     * @return
     */
    int getFinishPeopleByPositionName(String companyName,String department,String[] positionName);

    /**
     * 简要信息导出
     * @param request 请求
     * @param response 响应
     */
    void exportExcel(HttpServletRequest request, HttpServletResponse response, String examCode);

    /**
     * 题目分析数据导出
     * @param request 请求
     * @param response 响应
     * @param paramMap
     */
    void exportExcel2(HttpServletRequest request, HttpServletResponse response, Map<String,Object> paramMap);

    /**
     * 题目分析数据导出
     * @param request 请求
     * @param response 响应
     */
    void exportExcel3(HttpServletRequest request, HttpServletResponse response);

    /**
     * 力量大厦计算每道题正确率
     */
    JsonResponse exportExcel4();


    void exportExcelBy2024xszg_ks(HttpServletRequest request, HttpServletResponse response);
}
