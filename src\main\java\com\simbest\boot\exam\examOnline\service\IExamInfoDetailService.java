package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.model.ExamInfoDetail;

import java.util.List;
import java.util.Map;

/**
 * 用途：考试管理模块--考试答题明细service
 * 作者：gy
 * 时间: 2021-02-23 20:00
 */
public interface IExamInfoDetailService extends ILogicService<ExamInfoDetail, String> {

    /**
     *  保存答题明细集合
     *
     * @param examCode  考试编码
     */
    void saverExamInfoDetails(String examCode);


    /**
     * 异步保存数据信息
     * @param result    考试信息
     * @param allList   答题信息
     */
    void syncSaveInfo(ExamInfo result, List<Map<String, Object>> allList);

}
