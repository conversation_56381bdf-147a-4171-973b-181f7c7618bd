﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>组织人员树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <link href="../../js/jquery-ztree/css/zTreeStyle/zTreeStyle.css" rel="stylesheet" type="text/css" />
    <script src="../../js/jquery-ztree/js/jquery.ztree.core.js" type="text/javascript"></script>
    <script src="../../js/jquery-ztree/js/jquery.ztree.exhide.js" type="text/javascript"></script>
    <style type="text/css">
        .ztree li ul.line{height:auto;}
        .keyinput{width: 480px;padding: 10px;}
        #key{width: 415px;}
    </style>
</head>
<body class="page_body">
<div class="keyinput">
    <input id="key" type="text" placeholder="请输入姓名进行查询"/>
    <a class="btn a_warning " title="人员查询" style="float: right;height: 32px;padding: 0 10px;line-height: 32px;"  onclick="chooseuser()"><i class="iconfont">&#xe634;</i></a>
</div>
<ul id="userIds" class="ztree"></ul>
<ul id="orgTree"></ul>
<div class="role orgC"></div>
<script type="text/javascript">
    var gps=getQueryString();
    var setting = {
        view: {
            dblClickExpand: false,
            showLine: true,
        },
        check: {
            enable: true
        },
        data:{
            simpleData: {
                enable: true,
                idKey: "id",
                pIdKey: "parentId",
                rootPId: null
            }
        },
        callback: {
            onClick : zTreeOnClick,  //回调函数为单击操作
        }
    };
    $(function(){
        initTree();
        treeLoadSuccess();
    });
    function initTree(truename) {
        if(truename == undefined || truename == ""){
            var ajaxopts = {
                url:'action/user/user/findAllOrgUser?appcode='+web.appCode,
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    userIdzNodes = data.data;
                    var a=0;
                    for(var i in userIdzNodes){
                        userIdzNodes[i].open="false";
                        if(userIdzNodes[i].parentId=="OW19") a++;
                        // if(userIdzNodes[i].id=="00000000000000000000") //console.log(userIdzNodes[i]);
                        // if(userIdzNodes[i].id=="00000000000000000000") userIdzNodes[i].parentId="0";
                    }
                    userIdzTree = $.fn.zTree.init($("#userIds"), setting, userIdzNodes);
                    var treeObj = $.fn.zTree.getZTreeObj("userIds");
                    var allnodes = treeObj.transformToArray(treeObj.getNodes());
                    treeObj.expandNode(allnodes[0], true);
                }
            };
        }else{
            var ajaxopts = {
                url: 'action/user/user/findDimUserTree?appcode=' + web.appCode,
                contentType: "application/json; charset=utf-8",
                data: {'truename': truename},
                success: function (data) {
                    userIdzNodes = data.data;
                    var a = 0;
                    for (var i in userIdzNodes) {
                        userIdzNodes[i].open = "false";
                        if (userIdzNodes[i].parentId == "OW19") a++;
                        // if(userIdzNodes[i].id=="00000000000000000000") //console.log(userIdzNodes[i]);
                        // if(userIdzNodes[i].id=="00000000000000000000") userIdzNodes[i].parentId="0";
                    }
                    userIdzTree = $.fn.zTree.init($("#userIds"), setting, userIdzNodes);
                    var treeObj = $.fn.zTree.getZTreeObj("userIds");
                    var nodes = treeObj.getNodesByParamFuzzy("name", truename);  //根据节点数据的属性搜索，获取条件模糊匹配的节点数据 JSON 对象集合,(keywords)模糊匹配只能针对 String 类型的数据
                    /* 获取所有树节点 */
                    var allnodes = treeObj.transformToArray(treeObj.getNodes());
                    if(userIdzNodes.length < 500){
                        // 展开除第一级之外的其他节点
                        for (var i = 0, length_1 = allnodes.length; i < length_1; i++) {
                            if(allnodes[i].level == 0){
                                continue;
                            }
                            allnodes[i].open = true;
                        }
                        //展开第一级节点
                        treeObj.expandNode(allnodes[0], true);
                        if (nodes.length > 0) {
                            for(var i in nodes){
                                // treeObj.selectNode(nodes[i]); //会让节点自动滚到到可视区域内
                                var aObj = $("#" + nodes[i].tId + "_a");//树节点
                                aObj.css({'background-color':'#FFE6B0','border':'1px #FFB951 solid'})
                                // aObj.addClass('a_disabled');
                            }
                            // fuzzySearch('userIds','#key',null,true); //初始化模糊搜索方法
                        }
                    }
                    treeObj.expandNode(allnodes[0], true);
                }
            }
        }

        ajaxgeneral(ajaxopts);
    }
    //点击查询
    function chooseuser() {
        initTree($('#key').val());
    }
    //点击节点
    function zTreeOnClick(event, treeId, treeNode){
        if(treeNode.treeType=="org") return false;
        if(gps.multi==0){
            $(".role").html("");
        }
        if((getObjects("id,name",treeNode.id+","+treeNode.name)) == true){
            $(".role").append("<a name='"+treeNode.name+"' id='"+treeNode.id+"' parentId='"+treeNode.parentId+"'><font>"+treeNode.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
        }
    }
    $(document).on("click",".role a i",function(){
        $(this).parent("a").remove();
    });

    //是否有该条数据
    function getObjects(idas,keyas){
        var a=false;
        var ids=idas.split(",");
        var idkeys=keyas.split(",");
        var b = true;
        $(".role a").each(function(i,v){
            if($(v).attr('id') == idkeys[0]) {
                b = false
            }
            if(i == $(".role a").length-1){
                if(b == false){
                    top.mesAlert("提示信息","请勿重复添加！");
                }
                return b;
            }
        });
        return b;
    };
    //数据加载成功
    function treeLoadSuccess(){
        var chooseRow=top.chooseWeb[gps.name]?top.chooseWeb[gps.name].data:[];
        for(var i in chooseRow){
            if((getObjects("id,name",chooseRow[i].id+","+chooseRow[i].name)) == true){
                $(".role").append("<a name='"+chooseRow[i].name+"' id='"+chooseRow[i].id+"' reason='"+chooseRow[i].reason+"' nid='"+chooseRow[i].nid+"'><font>"+chooseRow[i].name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
            }
        }
    };
    window.getchoosedata=function(){
        var datas=[];
        $(".role a").each(function(i,v){
            var data={};
            data.id=$(v).attr("id");
            data.name=$(v).children("font").html();
            datas.push(data);
        });
        return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    }
</script>
</body>
</html>
