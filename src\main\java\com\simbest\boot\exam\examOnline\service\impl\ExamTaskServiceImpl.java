/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.examOnline.model.ExamTask;
import com.simbest.boot.exam.examOnline.repository.ExamTaskRepository;
import com.simbest.boot.exam.examOnline.service.IExamTaskService;
import com.simbest.boot.exam.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
@Slf4j
public class ExamTaskServiceImpl extends LogicService<ExamTask, String> implements IExamTaskService {

    private ExamTaskRepository repository;

    @Autowired
    public ExamTaskServiceImpl(ExamTaskRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Override
    public ExamTask taskStatusManage(String examCode) {
        // 判断当前定时器是开启状态还是关闭状态，如果是开启则关闭，如果关闭则开启
        Specification<ExamTask> spec = Specifications.<ExamTask>and()
                .eq("examCode", examCode)
                .eq("enabled", Constants.QUERY_VALID_CONDITION)
                .build();
        ExamTask one = this.findOne(spec);
        Assert.isTrue(one != null, "当前试卷不存在，请检查试卷编码");
        if (one.getTaskStatus() == 0) {
            //更改为开启状态
            one.setTaskStatus(1);
        } else {
            one.setTaskStatus(0);
        }
        this.update(one);
        // 此处对洛阳分公司满意度..试卷特殊处理，机关与分公司两套试卷 定时器状态需同步更改
        if (Constants.EXAM_CODE_BRANCH_LY.equals(one.getExamCode())) {
            Specification<ExamTask> build = Specifications.<ExamTask>and()
                    .eq("examCode", Constants.EXAM_CODE_OFFICE_LY)
                    .eq("enabled", Constants.QUERY_VALID_CONDITION)
                    .build();
            ExamTask other = this.findOne(build);
            other.setTaskStatus(one.getTaskStatus());
            this.update(other);
        }
        if (Constants.EXAM_CODE_OFFICE_LY.equals(one.getExamCode())) {
            Specification<ExamTask> build = Specifications.<ExamTask>and()
                    .eq("examCode", Constants.EXAM_CODE_BRANCH_LY)
                    .eq("enabled", Constants.QUERY_VALID_CONDITION)
                    .build();
            ExamTask other = this.findOne(build);
            other.setTaskStatus(one.getTaskStatus());
            this.update(other);
        }
        return one;
    }

}
