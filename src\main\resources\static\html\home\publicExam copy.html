<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org" style="height: 100%;">

<head>
	<title>在线考试</title>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="black" />
	<meta name="format-detaction" content="telephone=no" />
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
	<link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
		rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
	<script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
	<script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
	<script src="../../js/moaBridge_oa_wjdc.js?v=svn.revision" type="text/javascript"></script>
	<script src="../../js/aes.min.js?v=svn.revision" type="text/javascript"></script>
	<style>
		#piyueScore { width: unset; border-color: gray; }
	</style>
	<script type="text/javascript">
		var allQuestionCode = []
		var timeFlag = 0; // 提交数据的标识
		var remainTimeT; // 保存记录的计时器
		var examLists = [];//存储的试题信息
		var examAppCode = "";
		var companyName = '';
		var totalSS = 0; // 考试时间
		var questionBlankCode = ''; // 题库编码
		var singleLen = 0, multipleLen = 0, judgeLen = 0, shortLen = 0,fillLen = 0,indefLen = 0;  // 题目数量
		var singleData = [], multipleData = [], judgeData = [], shortData = [], fillData = [],indefData= [] // 答案
		var examData = {}; // 试卷模板
		var examRecordId = null; // 答题记录的id
		var currentAction = "";
		var isFinishExam = false; // 是否完成考试
        var firstIn = false
		var a = '' //拼接字符串
		var nowTimes = '' //第一次进入的时候的时间戳

		var AesKey = "dW5uR,Yml#y%PeLG";// AES 秘钥
		var CBCIV = "Be*Kn0xJ&XHc(Jl0";// AES-128-CBC偏移量
		var CBCOptions = {// 加密选项
			iv: CryptoJS.enc.Utf8.parse(CBCIV),
			mode:CryptoJS.mode.CBC,
			padding: CryptoJS.pad.Pkcs7
		}
		/**
		 * AES加密（CBC模式，需要偏移量）
		 * @param data
		 * @returns {*}
		 */
		function encrypt(data){
			var key = CryptoJS.enc.Utf8.parse(AesKey);
			var secretData = CryptoJS.enc.Utf8.parse(data);
			var encrypted = CryptoJS.AES.encrypt(
				secretData, 
				key, 
				CBCOptions
			);
			return encrypted.toString();
		}

		$(function () {
			$("#examOverDialog").dialog({ closed: true });
			var userAgent = navigator.userAgent;
			/**单点配置**/
			var username = "";
			var gps = getQueryString();
			if (gps.type == "piyue") {
				$("#submit").hide()
				$(".examTime").hide()
			}
			if (gps.type != "piyue") {
				$(".pleasePiyue").hide()
				$("#piyueScore").hide()
				$(".submitPiyueScore").hide()
			}
			// 页面认证问题
			if (gps.from == "oa") {
				ajaxgeneral({
					url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.uid,
					async: false,
					success: function (ress) {
						username = ress.data.username;
					}
				});
			} else if (gps.access_token) {//手机办公
				ajaxgeneral({
					url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.currentUserCode,
					async: false,
					success: function (ress) {
						username = ress.data.username;
						if (ress.data.belongCompanyTypeDictValue == '01') {
							companyName = '省公司';
						}
						if (ress.data.belongCompanyTypeDictValue == '02') {
							companyName = ress.data.belongCompanyName;
						}
						if (ress.data.belongCompanyTypeDictValue == '03') {
							companyName = ress.data.belongCompanyNameParent;
						}
					}
				});
			} else {
				getCurrent();
				username = web.currentUser.username;
			}
			if (gps.actionType && gps.actionType == "secrecyJoin") { // 已办
				$(".submitBtn").hide();
			} else {
				//禁用鼠标右边
				document.oncontextmenu = function () {
					getparent().mesShow("温馨提示", "请手动答题", 2000, 'red');
					return false;
				};
				//禁用ctrl+v功能
				document.onkeydown = function () {
					if (event.ctrlKey && window.event.keyCode == 86) {
						getparent().mesShow("温馨提示", "请手动答题", 2000, 'red');
						return false;
					}
				};
			}

			// 秒数格式化为hh:mm:ss
			function countdown(totalSS) {
				var hh = Math.floor(totalSS / 3600).toString().length < 2 ? '0' + Math.floor(totalSS / 3600) : Math.floor(totalSS / 3600);
				var mm = Math.floor((totalSS % 3600) / 60).toString().length < 2 ? '0' + Math.floor((totalSS % 3600) / 60) : Math.floor((totalSS % 3600) / 60);
				var ss = Math.floor((totalSS % 3600) % 60).toString().length < 2 ? '0' + Math.floor((totalSS % 3600) % 60) : Math.floor((totalSS % 3600) % 60);

				var nowTime = hh + ':' + mm + ':' + ss;
				return nowTime
			}

			// 题目序号和答案序号格式化,0题目转为汉字，1选项转为大写英文字母
			function formatNumber(type, num) { // 0题目序号  1选项序号
				num = parseInt(num);
				var res = '';
				if (type == 0) {
					var arr1 = new Array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九');
					var arr2 = new Array('', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿');//可继续追加更高位转换值
					if (!num || isNaN(num)) {
						return "零";
						return "零";
					}
					var english = num.toString().split("")
					var result = "";
					for (var i = 0; i < english.length; i++) {
						var des_i = english.length - 1 - i;//倒序排列设值
						result = arr2[i] + result;
						var arr1_index = english[des_i];
						result = arr1[arr1_index] + result;
					}
					//将【零千、零百】换成【零】 【十零】换成【十】
					result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');
					//合并中间多个零为一个零
					result = result.replace(/零+/g, '零');
					//将【零亿】换成【亿】【零万】换成【万】
					result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
					//将【亿万】换成【亿】
					result = result.replace(/亿万/g, '亿');
					//移除末尾的零
					result = result.replace(/零+$/, '')
					//将【零一十】换成【零十】
					//result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
					//将【一十】换成【十】
					result = result.replace(/^一十/g, '十');
					res = result;

				} else if (type == 1) {
					res = String.fromCharCode((num - 1) + 65);
				}
				return res;
			}

			// 获取试卷模板
			var tempCurrentUserCode = gps.currentUserCode ? gps.currentUserCode : ""
			ajaxgeneral({
				url: 'action/examAttribute/constructExamLayout?currentUserCode=' + tempCurrentUserCode,
				data: { "examAppCode": gps.examAppCode },
				contentType: "application/json; charset=utf-8",
				success: function (res) {
					for (var i in res.data.singleQuestionList) {//单选
						allQuestionCode.push(res.data.singleQuestionList[i].questionCode)
					}
					for (var i in res.data.moreQuestionList) {//多选
						allQuestionCode.push(res.data.moreQuestionList[i].questionCode)
					}
					for (var i in res.data.judgeQuestionList) {//判断
						allQuestionCode.push(res.data.judgeQuestionList[i].questionCode)
					}
					for (var i in res.data.shortAnswerQuestionList) {//简答
						allQuestionCode.push(res.data.shortAnswerQuestionList[i].questionCode)
					}
					for (var i in res.data.fillingQuestionList) {//填空
						allQuestionCode.push(res.data.fillingQuestionList[i].questionCode)
					}
					for (var i in res.data.indefiniteQuestionList) {//不定项
						allQuestionCode.push(res.data.indefiniteQuestionList[i].questionCode)
					}

					$(".explain").html(res.data.examName);
					examAppCode = res.data.examAppCode;
					examLists = res.data.singleQuestionList;
					questionBlankCode = res.data.questionBankCode;
					currentAction = "test";
					if (gps.type == "piyue") {
						username = gps.username
					}
					// 当前用户是否有未完成试卷
					ajaxgeneral({
						url: 'action/examInfo/findExamInfo',
						type: "POST",
						data: { publishUsername: username, examCode: gps.examCode, examAppCode: gps.examAppCode },
						contentType: "application/json; charset=utf-8",
						dataType: "json",
						success: function (result) {
							if (result.data){
								examRecordId = result.data.id; // 未完成试卷id
								var examRecord = result.data.examRecord?result.data.examRecord.split(','):[]; // 题目编号
								var examAnswer = result.data.examAnswer?result.data.examAnswer.split(','):[]; // 保存的答案
								singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案

								// 匹配已选择的答案
								function matchAnswer(lists, mark) {
									for (var i = 0; i < examRecord.length; i++) {
										for (var n = 0; n < lists.length; n++) {
											if (examRecord[i] == lists[n].questionCode) {
												if (mark != 'shortAnswer') {
													if(mark == 'fillAnswer'){
													}else{
														var examAnswerOptions
														if (examAnswer[i]) {
															examAnswerOptions = examAnswer[i].split('/');
															for (var ii = 0; ii < examAnswerOptions.length; ii++) {
																for (var nn = 0; nn < lists[n].answerList.length; nn++) {
																	if (examAnswerOptions[ii] == lists[n].answerList[nn].answerCode) {
																		lists[n].answerList[nn].isSelected = true;
																	} else {
																		if (!lists[n].answerList[nn].isSelected) {
																			lists[n].answerList[nn].isSelected = false;
																		}
																	}
																}
															}
														}
													}
												} else {
													lists[n].answerCode = examAnswer[i];
												}
											}
										}
									}
									return lists;
								}
								matchAnswer(res.data.singleQuestionList, 'single');
								matchAnswer(res.data.moreQuestionList, 'multiple');
								matchAnswer(res.data.judgeQuestionList, 'judge');
								matchAnswer(res.data.shortAnswerQuestionList, 'shortAnswer');
								matchAnswer(res.data.indefiniteQuestionList, 'indef');
							} 

							if (res.data.singleQuestionList && res.data.singleQuestionList.length > 0) {
								singleLen = res.data.singleQuestionList.length;
								for (var i = 0; i < res.data.singleQuestionList.length; i++) {
									for (var j = 0; j < res.data.singleQuestionList[i].answerList.length; j++) {
										if (res.data.singleQuestionList[i].answerList[j].isSelected) {
											singleData.push({
												questionCode: res.data.singleQuestionList[i].answerList[j].questionCode,
												examAnswer: res.data.singleQuestionList[i].answerList[j].answerCode
											});
										}
									}
								}
							}
							if (res.data.moreQuestionList && res.data.moreQuestionList.length > 0) {
								multipleLen = res.data.moreQuestionList.length;
								for (var i = 0; i < res.data.moreQuestionList.length; i++) {
									var options = [];
									for (var j = 0; j < res.data.moreQuestionList[i].answerList.length; j++) {
										if (res.data.moreQuestionList[i].answerList[j].isSelected) {
											options.push(res.data.moreQuestionList[i].answerList[j].answerCode);
										}
									}
									if (options.length > 0) {
										multipleData.push({
											questionCode: res.data.moreQuestionList[i].questionCode,
											examAnswer: options.join("/")
										});
									}
								}
							}
							if (res.data.judgeQuestionList && res.data.judgeQuestionList.length > 0) {
								judgeLen = res.data.judgeQuestionList.length;
								for (var i = 0; i < res.data.judgeQuestionList.length; i++) {
									for (var j = 0; j < res.data.judgeQuestionList[i].answerList.length; j++) {
										if (res.data.judgeQuestionList[i].answerList[j].isSelected) {
											judgeData.push({
												questionCode: res.data.judgeQuestionList[i].answerList[j].questionCode,
												examAnswer: res.data.judgeQuestionList[i].answerList[j].answerCode
											});
										}
									}
								}
							}
							if (res.data.shortAnswerQuestionList && res.data.shortAnswerQuestionList.length > 0) {
								shortLen = res.data.shortAnswerQuestionList.length;
								for (var i = 0; i < res.data.shortAnswerQuestionList.length; i++) {
									if (res.data.shortAnswerQuestionList[i].answerCode) {
										shortData.push({
											questionCode: res.data.shortAnswerQuestionList[i].questionCode,
											examAnswer: res.data.shortAnswerQuestionList[i].answerCode
										});
									}
								}
							}
							if (res.data.fillingQuestionList && res.data.fillingQuestionList.length > 0) {
								fillLen = res.data.fillingQuestionList.length;
								for (var i = 0; i < res.data.fillingQuestionList.length; i++) {
									if (res.data.fillingQuestionList[i].answerCode) {
										fillData.push({
											questionCode: res.data.fillingQuestionList[i].questionCode,
											examAnswer: res.data.fillingQuestionList[i].answerCode
										});
									}
								}
							}
							if (res.data.indefiniteQuestionList && res.data.indefiniteQuestionList.length > 0) {
								indefLen = res.data.indefiniteQuestionList.length;
								for (var i = 0; i < res.data.indefiniteQuestionList.length; i++) {
									var options = [];
									for (var j = 0; j < res.data.indefiniteQuestionList[i].answerList.length; j++) {
										if (res.data.indefiniteQuestionList[i].answerList[j].isSelected) {
											options.push(res.data.indefiniteQuestionList[i].answerList[j].answerCode);
										}
									}
									if (options.length > 0) {
										multipleData.push({
											questionCode: res.data.indefiniteQuestionList[i].questionCode,
											examAnswer: options.join("/")
										});
									}
								}
							}
							examData = res.data;
							if (result.data && result.data.isFinishExam) { //已完成考试
								isFinishExam = true;
							} else {  //未完成考试
								isFinishExam = false;
							}
							if (result.data && result.data.isFinishExam) { //已完成考试
								showQuestions('reTest', res.data);
								$(".submitBtn").hide();
								ajaxgeneral({
									url: 'action/examAttribute/computeScore?userName=' + username +'&examCode=' + gps.examCode +'&examAppCode=' + gps.examAppCode ,
									success: function (res) {
										if(res.data){
											$('#scoreNum span').text(res.data.score)
											if (gps.type != "piyue") {
												$("#closeDialog").dialog({ closed: false });
											}
										}
									}
								})
							} else {  //未完成考试
								if(result.data.drop){//弃考
									$("#examAbandonDialog").dialog({ closed: false });
									$("#examAbandonBtns").click(function () {
										window.close()
									});
								}else{
									var setTime = ''
									ajaxgeneral({
										url: 'action/examAttribute/findOne',
										contentType: "application/json; charset=utf-8",
										data:{examAppCode: gps.examAppCode},
										success: function (res) {
											if(res.data){
												setTime = res.data.setTime * 60
												$('#setTime').text(res.data.setTime)
												$("#examInitDialog").dialog({ closed: false });
											}
										}
									})
									$("#examInityes").click(function () {
										showQuestions('test', res.data);
										$(".submitBtn").show();
										if (remainTimeT) clearInterval(remainTimeT);
										if (gps.type != "piyue") {
											remainTimeT = setInterval(ajaxInterval, 1000); //每隔10秒保存一次数据
										}
										totalSS = parseInt(result.data?result.data.residueTime:setTime)
										if (totalSS > 0 && !isFinishExam) {
											timeOut(totalSS);
										} else {
											$(".examTime").hide();
										}
										$("#examInitDialog").dialog({ closed: true });
									});
									$("#examInitno").click(function () {
										window.close()
									});

								}
							}
							if(res.data.fillingQuestionList.length>0){
								for(var j in res.data.fillingQuestionList){
									for(var v in examRecord){
										if(res.data.fillingQuestionList[j].questionCode ==  examRecord[v]){
											var examAnswerArr = examAnswer[v].split(a)
											$('.fill'+v).find('.fipt').each(function(index, element){
												$(this).val(examAnswerArr[index])
												widthChange(this)
											});
										}
									}	
								}
							}
						}
					})
				}
			})

			// 每隔10秒保存一次数据
			function ajaxInterval() {
				// 单选
				singleData = [];
				for (var i = 0; i < $(".singleQues .main").length; i++) {
					if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
						singleData.push({
							questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
							examAnswer: $(".singleQues .main").eq(i).find("input[type='radio']:checked").val()
						});
					}else {
						singleData.push({
							questionCode: $(".singleQues .main").eq(i).find("input[type='radio']").attr("name"),
							examAnswer: null
						});
					}
				}
				// 多选
				multipleData = [];
				for (var i = 0; i < $(".multipleQues .main").length; i++) {
					var mulAnswer = []; // 每道多选题选中的答案
					if ($(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
						$(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
							mulAnswer.push($(this).val());
						});
						multipleData.push({
							questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
							examAnswer: mulAnswer.join('/')
						});
					} else {
						multipleData.push({
							questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']").attr("name"),
							examAnswer: null
						});
					}
				}
				// 判断
				judgeData = [];
				for (var i = 0; i < $(".judgeQues .main").length; i++) {
					if ($(".judgeQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
						judgeData.push({
							questionCode: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
							examAnswer: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").val()
						});
					} else {
						judgeData.push({
							questionCode: $(".judgeQues .main").eq(i).find("input[type='radio']").attr("name"),
							examAnswer: null
						});
					}
				}
				//简答
				shortData = [];
				for (var i = 0; i < $(".shortAnswer .main").length; i++) {
					if ($(".shortAnswer .main").eq(i).find("textarea").val() != "") {
						shortData.push({
							questionCode: $(".shortAnswer .main").eq(i).find("textarea").attr("id"),
							examAnswer: $(".shortAnswer .main").eq(i).find("textarea").val()
						});
					}else {
						shortData.push({
							questionCode: $(".shortAnswer .main").eq(i).find("textarea").attr("id"),
							examAnswer: null
						});
					}
				}
				// 填空
				fillData = [];
				for (var i = 0; i < $(".fillAnswer .main").length; i++) {
					var fillAnswer = []; 
					$(".fillAnswer .main").eq(i).find(".fipt").each(function (index) {
						fillAnswer.push($(this).val());
					});
					fillData.push({
						questionCode: $(".fillAnswer .main").eq(i).find("h6").attr("id"),
						examAnswer: fillAnswer.join(a)
					});
				}
				// 不定项
				indefData = [];
				for (var i = 0; i < $(".indefQues .main").length; i++) {
					var indefAnswer = []; // 每道不定项选题选中的答案
					if ($(".indefQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
						$(".indefQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
							indefAnswer.push($(this).val());
						});
						indefData.push({
							questionCode: $(".indefQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
							examAnswer: indefAnswer.join('/')
						});
					} else {
						indefData.push({
							questionCode: $(".indefQues .main").eq(i).find("input[type='checkbox']").attr("name"),
							examAnswer: null
						});
					}
				}

				if (currentAction == "test") {
					timeFlag = timeFlag + 1;
					if (timeFlag >= 10) {
						var totalData = singleData.concat(multipleData, judgeData, shortData,fillData,indefData);
						var answerArr = []
						for(var  j in allQuestionCode){
							for(var v in totalData){
								if(allQuestionCode[j] == totalData[v].questionCode){
									answerArr[j] = totalData[v].examAnswer
								}
							}
						}
						examAnswerArry  = answerArr
						if (singleData.length > 0 || multipleData.length > 0 || judgeData.length > 0 || shortData.length  || fillData.length > 0 || indefData.length > 0) {
							timeFlag = 0;
							var saveExamDate = {
								examCode: gps.examCode,
								examAppCode: gps.examAppCode,
								publishUsername: username,
								examRecord: allQuestionCode.join(','),
								examAnswer: examAnswerArry.join(','),
								id: examRecordId,
								residueTime: totalSS, // 秒
								salt:nowTimes,
							}
							//console.log(saveExamDate,'间隔执行');
							ajaxgeneral({
								url: 'action/examInfo/saveExamSalt',
								data: {str:encrypt(JSON.stringify(saveExamDate))},
								loading: false,
								contentType: "application/json; charset=utf-8",
								success: function (res) {
									examRecordId = res.data.id;
								},
								sError:function(res){
									if(res.message=='已在另一终端打开, 将在5秒后关闭'){
										setTimeout(function(){
											window.close()
										},5000)
									}
								}
								
							})
						}
					}
				}
			}

			function transNum(num){
				if(num==1){ return '一' }
				if(num==2){ return '二' }
				if(num==3){ return '三' }
				if(num==4){ return '四' }
				if(num==5){ return '五' }
				if(num==6){ return '六' }
			}
			// 渲染显示试卷
			function showQuestions(type, data) { // type的值：test测试；reTest重测
				if (data) {
					var titFlag = 0; // 标题序号
					var qid = 1;     //题号
					var qid2 = 1;    
					var qid3 = 1;
					var qid4 = 1;
					var qid5 = 1;
					var qid6 = 1;
					var qid7 = 1;

					var qidFlog1 = false; //类型标题
					var qidFlog2 = false;
					var qidFlog3 = false;
					var qidFlog4 = false;
					var qidFlog5 = false;

					var list = [];
					var questions = list.concat(data.singleQuestionList, data.moreQuestionList, data.judgeQuestionList, data.shortAnswerQuestionList,data.indefiniteQuestionList)
					for(var k in data.fillingQuestionList){
						questions.push(data.fillingQuestionList[k])
					}

					for (var i = 0; i < questions.length; i++) {
						//单选
						if (questions[i].questionType == 'single') {
							$("<div>").addClass("single").appendTo($(".questions"));
							$('.single').eq(0).html('<span style="margin-left:25px;font-weight:600">'+ transNum(qid6) +'、单选题（共'+data.singleQuestionList.length+'题，每题'+data.singleQuestionList[0].questionScore+'分）</span>')
							var part = $("<div>").addClass("part singleQues").appendTo($(".questions"));
							//var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、单选题").appendTo(part);
							var main = $("<div>").addClass("main").appendTo(part);
							// var h6 = $("<h6 class="+questions[i].id+">").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
							var h6 = $("<h6 class=" + questions[i].id + ">").html(qid + "、" + questions[i].questionName.replace(/\(/g,'(&nbsp;&nbsp;&nbsp;&nbsp;')).appendTo(main);
							var ul = $("<ul>").appendTo(main);
							if (questions[i].answerList && questions[i].answerList.length > 0) {
								for (var j = 0; j < questions[i].answerList.length; j++) {
									if (type == "test") { // 测试
										if (questions[i].answerList[j].isSelected) {
											var li = $("<li>").addClass("active").appendTo(ul);
											var input = $("<input>").attr({
												type: 'radio',
												id: questions[i].answerList[j].id,
												name: questions[i].answerList[j].questionCode,
												value: questions[i].answerList[j].answerCode,
												checked: true
											}).appendTo(li);
										} else {
											var li = $("<li>").appendTo(ul);
											var input = $("<input>").attr({
												type: 'radio',
												id: questions[i].answerList[j].id,
												name: questions[i].answerList[j].questionCode,
												value: questions[i].answerList[j].answerCode
											}).appendTo(li);
										}
									} else if (type == "reTest") { // 重测
										var li = $("<li>");
										var input = $("<input>").attr({
											type: 'radio',
											id: questions[i].answerList[j].id,
											name: questions[i].answerList[j].questionCode,
											value: questions[i].answerList[j].answerCode
										});
										if (questions[i].answerList[j].isSelected) { // 已填的答案   isSelected
											li = $("<li>").addClass("active red");
											input = $("<input>").attr({
												type: 'radio',
												id: questions[i].answerList[j].id,
												name: questions[i].answerList[j].questionCode,
												value: questions[i].answerList[j].answerCode,
												checked: true
											});
										}
										if (questions[i].answerList[j].isCorrect) { // 正确答案
											li = $("<li>").removeClass("red").addClass(" green");
										}
										$(input).appendTo(li);
										$(li).appendTo(ul);
									}
									var label = $("<label>").attr("for", questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode + "：" + questions[i].answerList[j].answerContent).appendTo(li);
								}
							}
							qid++;
						}
						//多选
						if (questions[i].questionType == 'more') {
							if(!qidFlog1){
								qid6++;
								qidFlog1 = true
							}
							$("<div>").addClass("multiple").appendTo($(".questions"));
							$('.multiple').eq(0).html('<span style="margin-left:25px;font-weight:600">'+ transNum(qid6) +'、多选题（共'+data.moreQuestionList.length+'题，每题'+data.moreQuestionList[0].questionScore+'分，少选得'+(data.moreQuestionList[0].questionScore)/2+'分，多选错选不得分）</span>')
							titFlag += 1;
							var part = $("<div>").addClass("part multipleQues").appendTo($(".questions"));
							//var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、多选题").appendTo(part);
							var main = $("<div>").addClass("main").appendTo(part);
							// var h6 = $("<h6>").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
							var h6 = $("<h6>").html(qid2 + "、" + questions[i].questionName.replace(/\(/g,'(&nbsp;&nbsp;&nbsp;&nbsp;')).appendTo(main);
							var ul = $("<ul>").addClass("clearfix").appendTo(main);
							for (var j = 0; j < questions[i].answerList.length; j++) {
								if (type == "test") { // 测试
									if (!questions[i].answerList[j].isSelected) {
										var li = $("<li>").appendTo(ul);
										var input = $("<input>").attr({
											type: 'checkbox',
											id: questions[i].answerList[j].id,
											name: questions[i].answerList[j].questionCode,
											value: questions[i].answerList[j].answerCode
										}).appendTo(li);
									} else {
										var li = $("<li>").addClass(" active").appendTo(ul);
										var input = $("<input>").attr({
											type: 'checkbox',
											id: questions[i].answerList[j].id,
											name: questions[i].answerList[j].questionCode,
											value: questions[i].answerList[j].answerCode,
											checked: true
										}).appendTo(li);
									}
								} else if (type == "reTest") { // 重测
									var li = $("<li>")
									var input = $("<input>").attr({
										type: 'checkbox',
										id: questions[i].answerList[j].id,
										name: questions[i].answerList[j].questionCode,
										value: questions[i].answerList[j].answerCode
									});
									if (questions[i].answerList[j].isSelected) { // 已填的答案
										li = $("<li>").addClass("active red");
										input = $("<input>").attr({
											type: 'checkbox',
											id: questions[i].answerList[j].id,
											name: questions[i].answerList[j].questionCode,
											value: questions[i].answerList[j].answerCode,
											checked: true
										});
									}
									if (questions[i].answerList[j].isCorrect) { // 正确答案
										li = $("<li>").removeClass(" red").addClass(" green");
									}
									$(input).appendTo(li);
									$(li).appendTo(ul);
								}
								var label = $("<label>").attr("for", questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode + "：" + questions[i].answerList[j].answerContent).appendTo(li);
							}
							qid2++;
						}
						//判断
						if (questions[i].questionType == 'judge') {
							if(!qidFlog2){
								qid6++;
								qidFlog2 = true
							}
							$("<div>").addClass("judge").appendTo($(".questions"));
							$('.judge').eq(0).html('<span style="margin-left:25px;font-weight:600">'+ transNum(qid6) +'、判断题（共'+data.judgeQuestionList.length+'题，每题'+data.judgeQuestionList[0].questionScore+'分）</span>')
							titFlag += 1;
							var part = $("<div>").addClass("part judgeQues").appendTo($(".questions"));
							//var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、多选题").appendTo(part);
							var main = $("<div>").addClass("main").appendTo(part);
							// var h6 = $("<h6>").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
							var h6 = $("<h6>").html(qid3 + "、" + questions[i].questionName.replace(/\(/g,'(&nbsp;&nbsp;&nbsp;&nbsp;')).appendTo(main);
							var ul = $("<ul>").addClass("clearfix").appendTo(main);

							for (var j = 0; j < questions[i].answerList.length; j++) {
								if (type == "test") { // 测试
									if (!questions[i].answerList[j].isSelected) {
										var li = $("<li>").appendTo(ul);
										var input = $("<input>").attr({
											type: 'radio',
											id: questions[i].answerList[j].id,
											name: questions[i].answerList[j].questionCode,
											value: questions[i].answerList[j].answerCode
										}).appendTo(li);
									} else {
										var li = $("<li>").addClass(" active").appendTo(ul);
										var input = $("<input>").attr({
											type: 'radio',
											id: questions[i].answerList[j].id,
											name:questions[i].answerList[j].questionCode,
											value: questions[i].answerList[j].answerCode,
											checked: true
										}).appendTo(li);
									}
								} else if (type == "reTest") { // 重测
									var li = $("<li>")
									var input = $("<input>").attr({
										type: 'radio',
										id: questions[i].answerList[j].id,
										name: questions[i].answerList[j].questionCode,
										value: questions[i].answerList[j].answerCode
									});
									if (questions[i].answerList[j].isSelected) { // 已填的答案
										li = $("<li>").addClass("active red");
										input = $("<input>").attr({
											type: 'radio',
											id: questions[i].answerList[j].id,
											name:questions[i].answerList[j].questionCode,
											value: questions[i].answerList[j].answerCode,
											checked: true
										});
									}
									if (questions[i].answerList[j].isCorrect) { // 正确答案
										li = $("<li>").removeClass(" red").addClass(" green");
									}
									$(input).appendTo(li);
									$(li).appendTo(ul);
								}
								var label = $("<label>").attr("for", questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode == "true" ? "正确" : "错误").appendTo(li);
									// var label = $("<label>").attr("for", questions[i].answerList[j].id).html(questions[i].answerList[j].questionCode).appendTo(li);
							}
							qid3++;

						}
						// 简答
						if (questions[i].questionType == 'shortAnswer') {
							if(!qidFlog3){
								qid6++;
								qidFlog3 = true
							}
							$("<div>").addClass("short").appendTo($(".questions"));
							$('.short').eq(0).html('<span style="margin-left:25px;font-weight:600">'+ transNum(qid6) +'、简答题</span>')
							titFlag += 1;
							var part = $("<div>").addClass("part shortAnswer").appendTo($(".questions"));
							//var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"：简答题").appendTo(part);
							var main = $("<div>").addClass("main shortAnswer").appendTo(part);
							// var h6 = $("<h6>").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
							var h6 = $("<h6>").html(qid4 + "、" + questions[i].questionName).appendTo(main);
							var pObj = $("<p>").appendTo(main);
							if (type == "test") { // 测试
								if (questions[i].answerCode) {
									var textarea = $("<textarea onkeyup='textareaAutoHeight(this)'>").attr({ id: questions[i].questionCode }).html(questions[i].answerCode).appendTo(pObj);
								} else {
									var textarea = $("<textarea onkeyup='textareaAutoHeight(this)'>").attr({ id: questions[i].questionCode }).appendTo(pObj);
								}
							} else if (type == "reTest") { // 重测
								var textarea = $("<textarea onkeyup='textareaAutoHeight(this)'>").attr({ id: questions[i].questionCode }).html(questions[i].answerCode).appendTo(pObj);
							}
							qid4++;
						}
						//填空
						if(questions[i].questionType == 'filling'){
							if(!qidFlog4){
								qid6++;
								qidFlog4 = true
							}
							// var fillingAns = questions[i].answerList.length>0?questions[i].answerList[0].answerContent.split(" "):[]  //赋值回显
							$("<div>").addClass("filling").appendTo($(".questions"));
							$('.filling').eq(0).html('<span style="margin-left:25px;font-weight:600">'+ transNum(qid6) +'、填空题</span>')
							var fillingAns =[]  //赋值回显
							titFlag += 1;
							var part = $("<div>").addClass("part fillAnswer").appendTo($(".questions"));
							//var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"：简答题").appendTo(part);
							var main = $("<div>").addClass("main fillAnswer").appendTo(part);
							// var h6 = $("<h6>").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
							var h6 = $("<h6>").attr({ id: questions[i].questionCode }).addClass("fill"+i).html(qid5 + "、" + questions[i].questionName.replace(/\[\[\]\]/g, '<input class="fipt" oninput="widthChange(this)" type="text" />')).appendTo(main);
							var pObj = $("<p>").appendTo(main);
							if (type == "test") { // 测试
								$('.fill'+i).find('.fipt').each(function(index, element){
									$(this).val(fillingAns[index])
								});
							} else if (type == "reTest") { // 重测
								$('.fill'+i).find('.fipt').each(function(index, element){
									$(this).val(fillingAns[index])
								});
							} 
							qid5++;
						}

						//不定项
						if (questions[i].questionType == 'indefinite') {
							if(!qidFlog5){
								qid6++;
								qidFlog5 = true
							}
							$("<div>").addClass("indef").appendTo($(".questions"));
							$('.indef').eq(0).html('<span style="margin-left:25px;font-weight:600">'+ transNum(qid6) +'、不定项题（共'+data.indefiniteQuestionList.length+'题，每题'+data.indefiniteQuestionList[0].questionScore+'分，少选得'+(data.indefiniteQuestionList[0].questionScore)/2+'分，多选错选不得分）</span>')
							titFlag += 1;
							var part = $("<div>").addClass("part indefQues").appendTo($(".questions"));
							//var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、多选题").appendTo(part);
							var main = $("<div>").addClass("main").appendTo(part);
							// var h6 = $("<h6>").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
							var h6 = $("<h6>").html(qid7 + "、" + questions[i].questionName.replace(/\(/g,'(&nbsp;&nbsp;&nbsp;&nbsp;')).appendTo(main);
							var ul = $("<ul>").addClass("clearfix").appendTo(main);
							for (var j = 0; j < questions[i].answerList.length; j++) {
								if (type == "test") { // 测试
									if (!questions[i].answerList[j].isSelected) {
										var li = $("<li>").appendTo(ul);
										var input = $("<input>").attr({
											type: 'checkbox',
											id: questions[i].answerList[j].id,
											name: questions[i].answerList[j].questionCode,
											value: questions[i].answerList[j].answerCode
										}).appendTo(li);
									} else {
										var li = $("<li>").addClass(" active").appendTo(ul);
										var input = $("<input>").attr({
											type: 'checkbox',
											id: questions[i].answerList[j].id,
											name: questions[i].answerList[j].questionCode,
											value: questions[i].answerList[j].answerCode,
											checked: true
										}).appendTo(li);
									}
								} else if (type == "reTest") { // 重测
									var li = $("<li>")
									var input = $("<input>").attr({
										type: 'checkbox',
										id: questions[i].answerList[j].id,
										name: questions[i].answerList[j].questionCode,
										value: questions[i].answerList[j].answerCode
									});
									if (questions[i].answerList[j].isSelected) { // 已填的答案
										li = $("<li>").addClass("active red");
										input = $("<input>").attr({
											type: 'checkbox',
											id: questions[i].answerList[j].id,
											name: questions[i].answerList[j].questionCode,
											value: questions[i].answerList[j].answerCode,
											checked: true
										});
									}
									if (questions[i].answerList[j].isCorrect) { // 正确答案
										li = $("<li>").removeClass(" red").addClass(" green");
									}
									$(input).appendTo(li);
									$(li).appendTo(ul);
								}
								var label = $("<label>").attr("for", questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode + "：" + questions[i].answerList[j].answerContent).appendTo(li);
							}
							qid7++;
						}
						$('textarea').each(function(i,v){
							textareaAutoHeight(v);
						}); 
					}
					if (isFinishExam) { // 已完成考试禁用表单
						$(".questions input,.questions textarea").attr("disabled", true);
						$("#submit").hide();
					} else {
						$(".questions input,.questions textarea").attr("disabled", false);
						if (gps.type != "piyue") {
							$("#submit").show();
						}
					}
				}else {
					getparent().mesShow("温馨提示", "试卷获取失败,请联系系统管理员!!!", 2000, 'red');
				}
			}

			// 点“提交”:或者答题的情况，如果答完就提交，没答完就提示
			$("#submit").click(function () {
				// 单选和判断的高亮、isSelected字段控制
				if ($(this).attr("type") && $(this).attr("type") == "radio") {
					if (!$(this).parent("li").hasClass("active")) {
						$(this).parent("li").addClass(" active");
						$(this).parent("li").siblings().removeClass(" active");
						// 重测时isSelected字段表示上次已选择的选项
						var partClass = $(this).parents(".part").attr("class").split(" ")[1];
						var big_index = $(this).parents(".main").index("." + partClass + " .main"); // 在大题中的索引
						var small_index = $(this).parents().index(); // 在小题中的索引
						if ($(this).parents(".part").hasClass("singleQues")) {
							for (var i = 0; i < examData.singleQuestionList.length; i++) {
								for (var j = 0; j < examData.singleQuestionList[i].answerList.length; j++) {
									examData.singleQuestionList[i].answerList[j].isSelected = false;
								}
							}
							examData.singleQuestionList[big_index].answerList[small_index].isSelected = true;
						} else if ($(this).parents(".part").hasClass("judgeQues")) {
							for (var i = 0; i < examData.judgeQuestionList.length; i++) {
								for (var j = 0; j < examData.judgeQuestionList[i].answerList.length; j++) {
									examData.judgeQuestionList[i].answerList[j].isSelected = false;
								}
							}
							examData.judgeQuestionList[big_index].answerList[small_index].isSelected = true;
						}
					}
				};

				// 多选的高亮、isSelected控制
				if ($(this).attr("type") && $(this).attr("type") == "checkbox") {
					if (!$(this).parent("li").hasClass("active")) {
						$(this).parent("li").addClass(" active");
					} else {
						$(this).parent("li").removeClass("active");
					}
					for (var i = 0; i < examData.moreQuestionList.length; i++) {
						for (var j = 0; j < examData.moreQuestionList[i].answerList.length; j++) {
							if ($(".multipleQues .main").eq(i).find("li").eq(j).hasClass("active")) {
								examData.moreQuestionList[i].answerList[j].isSelected = true;
							} else {
								examData.moreQuestionList[i].answerList[j].isSelected = false;
							}
						}
					}
				};

				// 单选
				singleData = [];
				for (var i = 0; i < $(".singleQues .main").length; i++) {
					if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
						singleData.push({
							questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
							examAnswer: $(".singleQues .main").eq(i).find("input[type='radio']:checked").val()
						});
					}
				}
				// 多选
				multipleData = [];
				for (var i = 0; i < $(".multipleQues .main").length; i++) {
					var mulAnswer = []; // 每道多选题选中的答案
					if ($(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
						$(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
							mulAnswer.push($(this).val());
						});
						multipleData.push({
							questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
							examAnswer: mulAnswer.join('/')
						});
					}
				}
				// 判断
				judgeData = [];
				for (var i = 0; i < $(".judgeQues .main").length; i++) {
					if ($(".judgeQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
						judgeData.push({
							questionCode: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
							examAnswer: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").val()
						});
					}
				}
				//简答
				shortData = [];
				for (var i = 0; i < $(".shortAnswer .main").length; i++) {
					if ($(".shortAnswer .main").eq(i).find("textarea").val() != "") {
						shortData.push({
							questionCode: $(".shortAnswer .main").eq(i).find("textarea").attr("id"),
							examAnswer: $(".shortAnswer .main").eq(i).find("textarea").val()
						});
					}
				}
				// 填空
				fillData = [];
				for (var i = 0; i < $(".fillAnswer .main").length; i++) {
					var fillAnswer = []; 
					if ($(".fillAnswer .main").eq(i).find(".fipt").val() != "") {
						$(".fillAnswer .main").eq(i).find(".fipt").each(function (index) {
							fillAnswer.push($(this).val());
						});
						fillData.push({
							questionCode: $(".fillAnswer .main").eq(i).find("h6").attr("id"),
							examAnswer: fillAnswer.join(a)
						});
					}
				}
				// 不定项的高亮、isSelected控制
				if ($(this).attr("type") && $(this).attr("type") == "checkbox") {
					if (!$(this).parent("li").hasClass("active")) {
						$(this).parent("li").addClass(" active");
					} else {
						$(this).parent("li").removeClass("active");
					}
					for (var i = 0; i < examData.indefiniteQuestionList.length; i++) {
						for (var j = 0; j < examData.indefiniteQuestionList[i].answerList.length; j++) {
							if ($(".indefQues .main").eq(i).find("li").eq(j).hasClass("active")) {
								examData.indefiniteQuestionList[i].answerList[j].isSelected = true;
							} else {
								examData.indefiniteQuestionList[i].answerList[j].isSelected = false;
							}
						}
					}
				};

				if (singleData.length + multipleData.length + judgeData.length + shortData.length + fillData.length + indefData.length == singleLen + multipleLen + judgeLen + shortLen + fillLen + indefLen) {
					$("#submitDialog").dialog({ closed: false });  //全部完成
				} else {
					getparent().mesShow("温馨提示", "您有试题还未作答，请检查!", 2000, 'red');
				}
			});

			$("#sureSubmit").click(function () { submitData(); });

			function submitData() {
				clearInterval(remainTimeT);
				var questionCodeArry = [];
				var examAnswerArry = [];
				var totalData = singleData.concat(multipleData, judgeData, shortData,fillData,indefData);
				for (var i = 0; i < totalData.length; i++) {
					questionCodeArry.push(totalData[i].questionCode);
					examAnswerArry.push(totalData[i].examAnswer);
				}
				var datas={
					examCode: gps.examCode,
					examAppCode: gps.examAppCode,
					publishUsername: username,
					examRecord: allQuestionCode.join(','),
					examAnswer: examAnswerArry.join(','),
					questionBlankCode: questionBlankCode,
					residueTime: totalSS
				}
				//console.log(datas,'手动提交');
				ajaxgeneral({
					url: 'action/examInfo/submitExamSalt',// 调用判断方法 实际不判断直接保存记录
					data:{str:encrypt(JSON.stringify(datas))},
					contentType: "application/json; charset=utf-8",
					success: function (res) {
						if (remainTimeT) clearInterval(remainTimeT);
						$("#scoreDialog h5").html("提交成功！");
						$("#yes").show();
						moaBridge.close();
						// window.close();
						location.reload()
						$("#submitDialog").dialog({ closed: true });
						$("#scoreDialog").dialog({ closed: false });
					}
				})
			}

			function timeOut() {//考试倒计时
				$('.examTime').show()
				if (gps.type != "piyue") {
					if (totalSS > 0) {
						if(!firstIn){ //第一次进入请求一下这个接口
							// nowTimes = encrypt(new Date().getTime())
							ajaxInterval()
							nowTimes = new Date().getTime()
							var questionCodeArry = [];
							var examAnswerArry = [];
							var totalData = singleData.concat(multipleData, judgeData, shortData, fillData,indefData);
							for (var i = 0; i < totalData.length; i++) {
								questionCodeArry.push(totalData[i].questionCode);
								examAnswerArry.push(totalData[i].examAnswer);
							}
							firstIn = true
							var saveExamD={
								examCode: gps.examCode,
								examAppCode: gps.examAppCode,
								publishUsername: username,
								examRecord: allQuestionCode.join(','),
								examAnswer: examAnswerArry.join(','),
								id: examRecordId,
								residueTime: totalSS, // 秒
								salt:nowTimes,
								isVisit:true
							}
							//console.log(saveExamD,'首次初始化');
							ajaxgeneral({
								url: 'action/examInfo/saveExamSalt',
								data: {str:encrypt(JSON.stringify(saveExamD))},
								loading: false,
								contentType: "application/json; charset=utf-8",
								success: function (res) {},
								sError:function(res){
									if(res.message=='已在另一终端打开, 将在5秒后关闭'){
										setTimeout(function(){
											window.close()
										},5000)
									}
								}
							})
						}
						totalSS--;
						$(".examTime h3").html(countdown(totalSS));
						setTimeout(timeOut, 1000);
					} else {
						clearInterval(remainTimeT);
						var questionCodeArry = [];
						var examAnswerArry = [];
						var totalData = singleData.concat(multipleData, judgeData, shortData, fillData,indefData);
						for (var i = 0; i < totalData.length; i++) {
							questionCodeArry.push(totalData[i].questionCode);
							examAnswerArry.push(totalData[i].examAnswer);
						}
						var datas = {
							examCode: gps.examCode,
							examAppCode: gps.examAppCode,
							publishUsername: username,
							examRecord: allQuestionCode.join(','),
							examAnswer: examAnswerArry.join(','),
							questionBlankCode: questionBlankCode,
							residueTime: totalSS,
						}
						//console.log(datas,'自动提交');
						ajaxgeneral({
							url: 'action/examInfo/submitExamSalt',// 调用判断方法 实际不判断直接保存记录
							data:{str:encrypt(JSON.stringify(datas))} ,
							contentType: "application/json; charset=utf-8",
							success: function (res) {
								if (remainTimeT) clearInterval(remainTimeT);
							}
						})
						$("#examOverDialog").dialog({ closed: false });
					}
				}
			};

			// 全答对时关闭弹框
			$("#yes").click(function () {
				$("#scoreDialog").dialog({ closed: true });
				if (gps.actionType && gps.actionType == "secrecyTask") {
					top.dialogClose("detail");
				} else {
					var userAgent = navigator.userAgent;
					if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
						// window.location.href = "about:blank";
						window.history.back();
					} else {
						window.opener = null;
						window.open('', '_self');
					}
					window.close();
					moaBridge.close();
				}
			});

			$("#examOver").click(function () {
				$("#examOverDialog").dialog({ closed: true });
				location.reload()
			});

			// 试卷已完成时，关闭页面
			$("#closeBtns button").click(function () {
				$('#closeDialog').dialog('close');
				if (gps.actionType && gps.actionType == "secrecyJoin") {
					top.dialogClose("detail");
				} else {
					var userAgent = navigator.userAgent;
					if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
						window.history.back();
					} else {
						window.opener = null;
						window.open('', '_self');
					}
					moaBridge.close();
				}
			})
			// 提交批阅分数
			$(".submitPiyueScore").click(function () {
				$(".submitPiyueScore").removeClass("canSubmit");
				ajaxgeneral({
					url: "action/examInfo/updateExamInfo",
					data: { "id": gps.piyueId, "score": $("#piyueScore").val() },
					contentType: "application/json; charset=utf-8",
					success: function (data) {
					}
				});
			})
            // 考试结束弹窗关闭回调 
			$('#examOverDialog').dialog({
				onClose: function(){
					location.reload()
				}
			});

			// 考试初始化弹窗关闭回调 
			$('#examInitDialog').dialog({
				onClose: function(){
					window.close()
				}
			});

			// 考试弃考弹窗关闭回调 
			$('#examAbandonDialog').dialog({
				onClose: function(){
					window.close()
				}
			});		    
		})

		function widthChange(param){
			var str=$(param).val()
			var len=str.length
			var reg=/[\u4e00-\u9fa5]/g;
			var matches=str.match(reg)
			var hanLen = matches===null?0:matches.length //获取汉字个数，一个汉字占1.5个字节，其他占一个字节
			var endLen=(len+parseInt(hanLen/1.5)+2)
			$(param).css("width",(endLen*10>=188?endLen*10:188)+"px")//*10 一个字节占10px
		}
	</script>
	<style type="text/css">
		/*公共样式*/
		.clearfix:after { content: '.'; display: block; height: 0; line-height: 0; clear: both; visibility: hidden; }
		.clearfix { zoom: 1; }
		.w15 { width: 15%; }
		/*页面样式*//*背景颜色*/
		body { background-image: url("../../images/home/<USER>"); background-repeat: no-repeat; background-size: 100% 100%;margin: 0px; padding: 0px; }
		.intext { text-indent: 0.15rem; margin: 0.3rem auto; }

		.wrapper {
			width: 92%;
			margin: 0 auto;
			color: #000;
			/* height: 650px; */
			height: 80%;
			margin-top: 7.5%;
			padding-bottom: 3%;
			background-color: #f0b3b3;
			border-radius: 20px;
			position: relative;
			border: 2px solid #e7ff5e;
			overflow: auto;
		}

		.yz {
			width: 120px;
			height: 180px;
			background-image: url("../../images/home/<USER>"); background-repeat: no-repeat; background-size: 100% 100%;
			position: absolute;
			bottom: 10%;
			right: 7%; 
			z-index: 99;
		
		}
		.header { text-align: center; }
		.header, .header img { width: 100%; }
		.details { width: 100%; padding: 10px; font-size: 16px;position: relative;}
		.explain { margin-top: 10px; font-weight: bolder; font-size: 20px; text-align: center; width: 98%; margin: 0 auto; color: #D90000; }
		.questions { padding: 25px 0; }
		.questionType { font-size: 20px; font-weight: bold; line-height: 1.2; margin-top: 20px; }
		.main { padding: 0 50px; }
		.main ul { padding: 0px; }
		.shortAnswer .main ul { padding: 0 10px; }
		/* .main ul { background: #F5F5F5; } */
		.main h6 { font-size: 16px; line-height: 1.5; margin: 10px 0;}
		.main li { line-height: 1.5; margin:5px 20px 5px 0; display: inline-block; font-weight: 400; }
		.main li.fl { margin-top: 0; }
		.main li input[type=radio]:checked:before { background: #D90000; }
		.main .active input:focus { outline-color: red; }
		.main .active { color: #000; }
		.main .green { color: #05955c; }
		.main .red { color: #E11414; }
		.main input { width: auto; }
		.main label { margin-left: 10px; }
		.shortAnswer .main textarea { min-height: 160px; font-size: 14px; width: 70%; background-color: #f0b3b3;border-color: #000;border-radius: 10px;}
		.icon-duihao1 { font-size: 16px; margin-left: 4px; }
		.icon-cuo1 { font-size: 14px; font-weight: bold; margin-left: 4px; }
		.submitBtn,
		.submitPiyueScore { border: 0; outline: 0; width: 90px; height: 36px; background: #B4B4B4; 
			border-radius: 4px; font-size: 14px; color: #fff; margin: 10px 0 0 60px; letter-spacing: 2px;}
		.submitBtn:active { opacity: .85; }
		.canSubmit { background-color: #E83333; }
		.dialog h5 { font-size: 15px; font-weight: bold; text-align: center; }
		.forceSubmitDialog p { font-size: 14px; font-weight: bold; text-align: center; margin-top: 20px; }
		.scoreDialog p { font-size: 12px; text-align: center; }
		.submitBtns button { border: 0; outline: 0; padding: 0; margin: 0; height: 32px; font-size: 12px; 
			color: #fff; text-align: center; border-radius: 4px; padding: 0 20px !important; }
		.submitBtns .gray { background-color: #B4B4B4; }
		.submitBtns .red { background-color: #E11414; }
		/* .remainTime { font-size: 15px; font-weight: bold; margin-top: 20px; text-align: right; } */
		.examTime { position: fixed; top: 30px; right: 40px; font-size: 24px;color: #fff }
		.examTime h3 { color: #fff }
		.fipt{ font-size: 16px; border: none; border-bottom: 1px solid #000; padding: 0  5px;background-color: #f0b3b3; }

		/* 隐藏滚动条 */
		.wrapper::-webkit-scrollbar {
			width: 0.5em; /* 设置滚动条宽度 */
			background-color: transparent; /* 设置滚动条背景颜色为透明 */
		}

		/* 隐藏滚动条轨道 */
		.wrapper::-webkit-scrollbar-track {
			background-color: transparent; /* 设置滚动条轨道背景颜色为透明 */
		}

		/* 隐藏滚动条滑块 */
		.wrapper::-webkit-scrollbar-thumb {
			background-color: transparent; /* 设置滚动条滑块背景颜色为透明 */
		}
	</style>
</head>

<body style="height: 100%;">
	<div class="yz"></div>
	<div class="wrapper">
		<div class="details">
			<div class="questions"> </div>
			<!-- 提交 -->
			<button class="submitBtn hide" id="submit" style="background-color:#df4246">提交</button>
			<span style="margin-left: 20px" class="pleasePiyue">请打分：</span>
			<input id="piyueScore" oninput="myInput()"></input>
			<button class="submitPiyueScore">提交批阅</button>
			<!-- 提交对话框 -->
			<div id="submitDialog" class="easyui-dialog dialog" title="温馨提示" closed="true"
				data-options="modal:true,buttons:'#submitBtns'" style="width:400px;height:200px;padding:10px">
				<h5>您已答完所有题，确认提交？</h5>
			</div>
			<div class="btns submitBtns" id="submitBtns" style="text-align:center;">
				<button class="easyui-linkbutton gray" onclick="$('#submitDialog').dialog('close')">取消</button>
				<button id="sureSubmit" class="easyui-linkbutton red">是的确认提交！</button>
			</div>
			<!-- 提交成功对话框 -->
			<div id="scoreDialog" class="easyui-dialog dialog scoreDialog" title="温馨提示" closed="true"
				data-options="modal:true,buttons:'#scoreBtns'" style="width:400px;height:200px;padding:10px">
				<h5></h5>
			</div>
			<div class="submitBtns scoreBtns" id="scoreBtns" style="text-align:center;">
				<button id="yes" class="easyui-linkbutton red hide">确定</button>
			</div>
			<!-- 考试结束对话框 -->
			<div id="examOverDialog" class="easyui-dialog dialog examOverDialog" title="温馨提示" closed="true"
				data-options="modal:true,buttons:'#examOverBtns'" style="width:400px;height:200px;padding:10px">
				<h5>答题时间已到，试卷已自动提交！</h5>
			</div>
			<div class="submitBtns" id="examOverBtns" style="text-align:center;">
				<button id="examOver" class="easyui-linkbutton red ">确定</button>
			</div>
			<!-- 打开试卷时，试卷已完成，关闭页面 -->
			<div id="closeDialog" class="easyui-dialog dialog closeDialog" title="温馨提示" closed="true"
				data-options="modal:true,buttons:'#closeBtns'" style="width:400px;height:200px;padding:10px">
				<h5>您已完成竞赛答题，感谢您的参与！</h5>
				<h5 id="scoreNum">您的分数是:<span></span></h5>
			</div>
			<div class="submitBtns" id="closeBtns" style="text-align:center;">
				<button class="easyui-linkbutton red">确定</button>
			</div>
			<!-- 考试开始前对话框 -->
			<div id="examInitDialog" class="easyui-dialog dialog examInitDialog" title="温馨提示" closed="true"
				data-options="modal:true,buttons:'#examInitBtns'" style="width:450px;height:225px;padding:10px">
				<h5>每人仅有一次答题的机会，<span id="setTime"></span>分钟后自动交卷，开始考试10分钟后未答题视为自动弃考，答题过程中请勿关闭或刷新页面，您可以点击“提交”按钮交卷，请确认是否开始正式答题？</h5>
			</div>
			<div class="submitBtns" id="examInitBtns" style="text-align:center;">
				<button id="examInityes" class="easyui-linkbutton red ">确定</button>
				<button id="examInitno" class="easyui-linkbutton" style="background-color: #e49797;color:#000">取消</button>
			</div>

			<!-- 弃考弹窗 -->
			<div id="examAbandonDialog" class="easyui-dialog dialog examAbandonDialog" title="温馨提示" closed="true"
				data-options="modal:true,buttons:'#examAbandonBtns'" style="width:400px;height:200px;padding:10px">
				<h5>答题已开始10分钟，未开始正式答题，视为自动弃考！</h5>
			</div>
			<div class="submitBtns" id="examAbandonBtns" style="text-align:center;">
				<button id="examAbandon" class="easyui-linkbutton red ">确定</button>
			</div>




		</div>
		<div class="examTime hide">
			考试剩余: <h3></h3>
		</div>
	</div>
	<script>
		function myInput() {
			var limitNum = $("#piyueScore").val();
			var reg = /^(0|([1-9]\d{0,1})|100)$/;
			if (reg.test(limitNum) == false) {
				$("#piyueScore").val("");
				$(".submitPiyueScore").removeClass("canSubmit");
				return false;
			} else {
				$("#piyueScore").val(limitNum);
				$(".submitPiyueScore").addClass("canSubmit");
			}
		}
	</script>
</body>

</html>