<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>    
    <meta name="format-detaction" content="telephone=no"/>    
    <title>洛阳分公司各部门服务支撑力度调查问卷</title>
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"  rel="stylesheet"/>
    <link href="/simbestui/js/themes/default/easyui.css?v=svn.revision"  rel="stylesheet"/>
    <link href="/simbestui/js/themes/icon.css?v=svn.revision"  rel="stylesheet"/>
    <link href="/simbestui/css/public.css?v=svn.revision"  rel="stylesheet"/>
    <link href="/simbestui/css/pages.css?v=svn.revision"  rel="stylesheet"/>
    <script src="/simbestui/js/jquery.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/jquery.easyui.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision"  type="text/javascript"></script>
    <script src="../../js/moaBridge_oa_wjdc.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/jquery.zjsfile.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/jquery.aduq.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/jquery.process.js?v=svn.revision"  type="text/javascript"></script>
    <script type="text/javascript">
        var timeFlag = 0; // 提交数据的标识
        var remainTimeT; // 保存记录的计时器
        var annualQuarterCode = "";//提交的题目所属考试code
        var flagNum=true;
        $(function(){
            var userAgent = navigator.userAgent;
            /**单点配置**/            
            var username = "";
            var gps = getQueryString();
            if(gps.actionType && gps.actionType=="secrecyJoin"){ // 已办
                $(".submitBtn").hide();
                $(".submitBtn").show();
            }else{
                $(".submitBtn").show();
                //禁用鼠标右边
                document.oncontextmenu = function(){
                    getparent().mesShow("温馨提示","请手动答题",2000,'red');
                    return false;
                };
                //禁用ctrl+v功能
                document.onkeydown = function(){
                    if (event.ctrlKey && window.event.keyCode==86){

                        getparent().mesShow("温馨提示","请手动答题",2000,'red');
                        return false;
                    }
                };
            }
            // 页面认证问题
            if(gps.from=="oa"){
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.uid,
                    async: false,
                    success: function (ress) {
                        username = ress.data.username;
                    }
                });
            }else if(gps.access_token){//手机办公
                // alert('获取用户信息');
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.currentUserCode,
                    async: false,
                    success: function (ress) {
                        username = ress.data.username;
                    }
                });
            }else {
                getCurrent();
                username = web.currentUser.username;
            }
            var questionBlankCode = ''; // 题库编码
            var singleLen = 0, multipleLen = 0, judgeLen = 0, shortLen = 0; // 题目数量
            var saveSingleData = [], multipleData = [], judgeData = [], shortData = []; // 答案
            var examData = {}; // 试卷模板
            var examRecordId = null; // 答题记录的id
            var currentAction = "";
            var isFinishExam = false; // 是否完成考试

            ajaxgeneral({//获取试卷名称
                url: "action/annualQuarter/getAnnualQuarterInfo?annualQuarterCode=lyxf&createdTime=no",
                data: {"examAppCode": "lyxf"},
                contentType: "application/json; charset=utf-8",
                success: function (ress) {
                    annualQuarterCode = ress.data.annualQuarterCode;
                }
            });            

            // 题目序号和答案序号格式化,0题目转为汉字，1选项转为大写英文字母
            function formatNumber(type,num) { // 0题目序号  1选项序号
                num = parseInt(num);
                var res = '';
                if (type == 0) {
                    var arr1 = new Array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九');
                    var arr2 = new Array('', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿');//可继续追加更高位转换值
                    if (!num || isNaN(num)) {
                        return "零";
                    }
                    var english = num.toString().split("")
                    var result = "";
                    for (var i = 0; i < english.length; i++) {
                        var des_i = english.length - 1 - i;//倒序排列设值
                        result = arr2[i] + result;
                        var arr1_index = english[des_i];
                        result = arr1[arr1_index] + result;
                    }
                    //将【零千、零百】换成【零】 【十零】换成【十】
                    result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');
                    //合并中间多个零为一个零
                    result = result.replace(/零+/g, '零');
                    //将【零亿】换成【亿】【零万】换成【万】
                    result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
                    //将【亿万】换成【亿】
                    result = result.replace(/亿万/g, '亿');
                    //移除末尾的零
                    result = result.replace(/零+$/, '')
                    //将【零一十】换成【零十】
                    //result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
                    //将【一十】换成【十】
                    result = result.replace(/^一十/g, '十');
                    res = result;

                } else if (type == 1) {
                    res = String.fromCharCode((num-1)+65);
                }
                return res;
            }
            // 获取试卷模板
            ajaxgeneral({
                url: 'action/examAttribute/constructExamLayout',
                data: {"examAppCode": "lyxf"},
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    ajaxgeneral({//获取试卷名称
                        url: "action/annualQuarter/getAnnualQuarterInfo?annualQuarterCode=lyxf&createdTime=no",
                        data: {"examAppCode": "lyxf"},
                        contentType: "application/json; charset=utf-8",
                        success: function (ress) {
                            $(".explain").html(ress.data.annualQuarterInfo);
                        }
                    });
                    questionBlankCode = res.data.questionBankCode;
                    currentAction = "test";

                    // 当前用户是否有未完成试卷
                    ajaxgeneral({
                        url: 'action/examAnswerNotes/findAllSaveAnswer?annualQuarterCode=' + annualQuarterCode,
                        type: "POST",
                        data:{
                            publishUsername: username,
                            annualQuarterCode:annualQuarterCode
                        },
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        success: function (result) {
                            if(result.data.length!=0) {

                                // 匹配已选择的答案
                                function matchAnswer(lists,answer){
                                    for(var n = 0; n < answer.length; n++){// n 是查询到答案的标记
                                            for (var m = 0; m < lists.length; m++) {// m 是所有题目的标记
                                                if (answer[n].questionCode == lists[m].answerList[1].questionCode) {
                                                    for(var i = 0; i < 5; i++){
                                                        if (lists[m].answerList[i].answerCode == answer[n].usernameAnswer) {
                                                            res.data.singleQuestionList[m].answerList[i].isSelected = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    return lists;
                                }
                                matchAnswer(res.data.singleQuestionList,result.data);
                            }else{
                            }
                            //如果有单选
                            if(res.data.singleQuestionList && res.data.singleQuestionList.length>0){
                                singleLen = res.data.singleQuestionList.length;
                                for(var i = 0;i < res.data.singleQuestionList.length; i++){
                                    for(var j = 0;j < res.data.singleQuestionList[i].answerList.length; j++){
                                        if(res.data.singleQuestionList[i].answerList[j].isSelected){
                                            saveSingleData.push({
                                                questionCode: res.data.singleQuestionList[i].answerList[j].questionCode,
                                                examAnswer: res.data.singleQuestionList[i].answerList[j].answerCode
                                            });
                                        }
                                    }
                                }
                            }
                            // 设置提交按钮高亮是否显示
                            //if (saveSingleData.length>71) {//全部选择
                            if (saveSingleData.length==80) {//全部选择
                                $("#submit").addClass(" canSubmit");
                            } else {
                                $("#submit").removeClass("canSubmit");
                            }
                            examData = res.data;
                            if(result.data.length != 0 ){ //已完成考试
                                if (result.data[0].answerStatus === 1) {
                                     $("#submit").hide();
                                }
                            }

                            ajaxgeneral({//获取试卷名称
                                url: "action/annualQuarter/getAnnualQuarterInfo?annualQuarterCode=lyxf&createdTime=no",
                                data: {"examAppCode": "lyxf"},
                                contentType: "application/json; charset=utf-8",
                                success: function (ress) {
                                    $(".explain").html(ress.data.annualQuarterInfo);
                                    annualQuarterCode = ress.data.annualQuarterCode;
                                }
                            });
                            
                            if(result.data.length != 0){ //已完成考试
                                if (result.data[0].answerStatus === 1) {
                                    showQuestions('test', res.data);
                                    $(".submitBtn").hide();
                                    $("#closeDialog").dialog({"closed":false});
                                    $("li > input").attr("disabled",true);
                                }else if (result.data[0].answerStatus === 0){
                                    showQuestions('test', res.data);
                                    if(remainTimeT) clearInterval(remainTimeT);
                                    remainTimeT = setInterval(ajaxInterval,15000); //每隔30秒保存一次数据
                                }
                            }else{  //未完成考试
                                    showQuestions('test', res.data);
                                    if(remainTimeT) clearInterval(remainTimeT);
                                    remainTimeT = setInterval(ajaxInterval,15000); //每隔30秒保存一次数据
                            }
                        }
                    })
                }
            })



            // 每隔30秒保存一次数据
            function ajaxInterval() {
                if(currentAction == "test"){
                    saveSingleData = [];
                    $("ul",".singleQues .main").each(function(i,v){
                        if ($(v).find("input[type='radio']:checked").length > 0) {
                            saveAnswerScore = $(".singleQues .main").eq(i).parent("div").children("span").attr("answerScore");
                            switch(saveAnswerScore){
                                case "A":
                                    saveAnswerScore = 5;
                                    break;
                                case "B":
                                    saveAnswerScore = 4;
                                    break;
                                case "C":
                                    saveAnswerScore = 3;
                                    break;
                                case "D":
                                    saveAnswerScore = 2;
                                    break;
                                case "E":
                                    saveAnswerScore = 1;
                                    break;
                            }              
                            saveSingleData.push({
                                questionCode: $(v).eq(0).children("li").find("input[type='radio']:checked").attr("name"),
                                usernameAnswer: $(v).eq(0).children("li").find("input[type='radio']:checked").val(),
                                annualQuarterCode:annualQuarterCode,
                                examCode:"lyxf",
                                answerScore:saveAnswerScore
                            });
                        }
                    })
                    if(saveSingleData.length>0){
                        ajaxgeneral({
                            url: 'action/examAnswerNotes/saveAllAnswer?annualQuarterCode='+annualQuarterCode,
                            data: {
                                answerStatus:0,
                                questionList:saveSingleData,
                            },
                            loading: false,
                            contentType: "application/json; charset=utf-8",
                            success: function (res) {
                            }
                        })
                    }
                }
            }
            // 显示试卷
            function showQuestions(type,data){ // type的值：test测试,reTest重测
                if(data){
                    var titFlag = 0; // 标题序号
                    var qid=1;
                    var list=data.singleQuestionList;
                    var questions=list;
                    questions.sort(function (a,b) {return a.questionOrder-b.questionOrder })
                    //单选
                    for(var i=0;i<questions.length;i++){
                        //单选题
                        if(questions[i].questionType=='single'){
                            var tNum = i%8+1;
                            if (i%8 === 0) {
                                var singleBox = $("<div>").addClass("singleBox").appendTo($(".questions"));
                                switch(Math.floor(i/8)){
                                    case 1:
                                        var h3 = $("<h3>").html("对党委办公室评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 2:
                                        var h3 = $("<h3>").html("对人力资源部评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 3:
                                        var h3 = $("<h3>").html("对市场经营部评价调查问卷").appendTo(singleBox);
                                        break;
                                    // case 4:
                                    //     var h3 = $("<h3>").html("对客户服务中心评价调查问卷").appendTo(singleBox);
                                    //     break;
                                    case 4:
                                        var h3 = $("<h3>").html("对网络部评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 5:
                                        var h3 = $("<h3>").html("对政企客户部评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 6:
                                        var h3 = $("<h3>").html("对综合部评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 7:
                                        var h3 = $("<h3>").html("对工会评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 8:
                                        var h3 = $("<h3>").html("对客户服务部评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 9:
                                        var h3 = $("<h3>").html("对纪委办公室评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 10:
                                        var h3 = $("<h3>").html("对工程建设中心评价调查问卷").appendTo(singleBox);
                                        break;
                                    default:
                                        var h3 = $("<h3>").html("对财务部评价调查问卷").appendTo(singleBox);
                                }
                            }

                            var part = $("<div>").addClass("part singleQues").appendTo($(".singleBox:last-child"));
                            var main = $("<div>").addClass("main singleQues").appendTo(part);
                            var answerScore = $("<span>").addClass("answerScore").appendTo(part);
                            var h6 = $("<h6>").html(tNum+"."+questions[i].titleDescription).appendTo(main);
                            var ul = $("<ul>").appendTo(main);
                            if(questions[i].answerList && questions[i].answerList.length>0){
                                for(var j=0;j<questions[i].answerList.length;j++){
                                    if(type == "test"){ // 测试
                                        if(questions[i].answerList[j].isSelected){
                                            var li = $("<li>").addClass("active").appendTo(ul);
                                            var input = $("<input>").attr({
                                                type:'radio',
                                                id:questions[i].answerList[j].id,
                                                name:questions[i].answerList[j].questionCode,
                                                value:questions[i].answerList[j].answerCode,
                                                checked:true
                                            }).appendTo(li);
                                        }else{
                                            var li = $("<li>").appendTo(ul);
                                            var input = $("<input>").attr({
                                                type:'radio',
                                                id:questions[i].answerList[j].id,
                                                name: questions[i].answerList[j].questionCode,
                                                value:questions[i].answerList[j].answerCode
                                            }).appendTo(li);
                                        }
                                    }else if(type == "reTest"){ // 重测
                                        var li = $("<li>");
                                        var input = $("<input>").attr({
                                            type:'radio',
                                            id:questions[i].answerList[j].id,
                                            name:questions[i].answerList[j].questionCode,
                                            value:questions[i].answerList[j].answerCode
                                        });
                                        if(questions[i].answerList[j].isSelected){ // 已填的答案   isSelected
                                            li = $("<li>").addClass("active red");
                                            input = $("<input>").attr({
                                                "type":'radio',
                                                "id":questions[i].answerList[j].id,
                                                "name":questions[i].answerList[j].questionCode,
                                                "value":questions[i].answerList[j].answerCode,
                                                "checked":true
                                            });
                                        }
                                        if(questions[i].answerList[j].isCorrect){ // 正确答案
                                            li = $("<li>").removeClass("red").addClass(" green");
                                        }

                                        $(input).appendTo(li);
                                        $(li).appendTo(ul);
                                    }
                                    var label = $("<label>").attr("for", questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode + "：" + questions[i].answerList[j].answerContent).appendTo(li);
                                    for (var index = 0; index < questions[i].answerList.length; index++) {
                                        if (questions[i].answerList[index].isSelected == true) {
                                            $("ul:last").parent("div").parent("div").children("span").attr("answerScore",(5-index)).html('分数：'+(5-index));
                                        }                                        
                                    }
                                }
                            }
                            qid++;
                        }
                    }


                    // input和textarea的事件
                    $("input").on("click",function() {
                        // 单选和判断的高亮、isSelected字段控制
                        if ($(this).attr("type") && $(this).attr("type") == "radio") {
                            if (!$(this).parent("li").hasClass("active")) {
                                $(this).parent("li").addClass(" active");
                                //选择答案后在右侧显示分数
                                switch(this.value){
                                    case 'A':
                                        $(this).parent("li").parent("ul").parent("div").parent("div").children("span").attr("answerScore","5").html('分数：5');
                                        break;
                                    case 'B':
                                        $(this).parent("li").parent("ul").parent("div").parent("div").children("span").attr("answerScore","4").html('分数：4');
                                        break;
                                    case 'C':
                                        $(this).parent("li").parent("ul").parent("div").parent("div").children("span").attr("answerScore","3").html('分数：3');
                                        break;
                                    case 'D':
                                        $(this).parent("li").parent("ul").parent("div").parent("div").children("span").attr("answerScore","2").html('分数：2');
                                        break;
                                    case 'E':
                                        $(this).parent("li").parent("ul").parent("div").parent("div").children("span").attr("answerScore","1").html('分数：1');
                                        break;
                                }
                                $(this).parent("li").siblings().removeClass(" active");

                                // 重测时isSelected字段表示上次已选择的选项
                                var partClass = $(this).parents(".part").attr("class").split(" ")[1];

                                var big_index = $(this).parents(".main").index("." + partClass + " .main"); // 在大题中的索引
                                var small_index = $(this).parents().index(); // 在小题中的索引
                                if ($(this).parents(".part").hasClass("singleQues")) {
                                    for (var i = 0; i < examData.singleQuestionList.length; i++) {
                                        for (var j = 0; j < examData.singleQuestionList[i].answerList.length; j++) {
                                            examData.singleQuestionList[i].answerList[j].isSelected = false;
                                        }
                                    }
                                    examData.singleQuestionList[big_index].answerList[small_index].isSelected = true;
                                } else if ($(this).parents(".part").hasClass("judgeQues")) {
                                    for (var i = 0; i < examData.judgeQuestionList.length; i++) {
                                        for (var j = 0; j < examData.judgeQuestionList[i].answerList.length; j++) {
                                            examData.judgeQuestionList[i].answerList[j].isSelected = false;
                                        }
                                    }
                                    examData.judgeQuestionList[big_index].answerList[small_index].isSelected = true;
                                }
                            }
                        };
                        // 单选
                        if ($(this).parents(".main").hasClass("singleQues")) {
                            saveSingleData = [];
                            for (var i = 0; i < $(".singleQues .main").length; i++) {
                                if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                    answerScore = $(".singleQues .main").eq(i).parent("div").children("span").attr("answerScore");
                                    switch(answerScore){
                                        case "A":
                                            answerScore = 5;
                                            break;
                                        case "B":
                                            answerScore = 4;
                                            break;
                                        case "C":
                                            answerScore = 3;
                                            break;
                                        case "D":
                                            answerScore = 2;
                                            break;
                                        case "E":
                                            answerScore = 1;
                                            break;
                                    }
                                    saveSingleData.push({
                                        questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                        usernameAnswer: $(".singleQues .main").eq(i).find("input[type='radio']:checked").val(),
                                        examCode: "lyxf",
                                        answerScore: answerScore
                                    });
                                }
                            }
                        }
                        // // 设置提交按钮高亮是否显示
                        // if (saveSingleData.length == 80) {//全部选择
                        //     $("#submit").addClass(" canSubmit");
                        // } else {
                        //     $("#submit").removeClass("canSubmit");
                        // }
                        // 设置提交按钮高亮是否显示
                        var singleBoxNum = 0
                        $(".questions .singleBox").each(function (i, v) {
                            if ($(v).children("h3").eq(0).css("display") != "none") {
                                singleBoxNum++
                            }
                        })
                        if (saveSingleData.length == (singleBoxNum * 8)) {//全部选择
                            $("#submit").addClass(" canSubmit");
                        } else {
                            $("#submit").removeClass("canSubmit");
                        }
                    });
                }
                else{
                    getparent().mesShow("温馨提示","试卷获取失败,请联系系统管理员!!!",2000,'red');
                }
            }

            // 点“提交”
            $("#submit").click(function(){
                if ($("#submit").hasClass("canSubmit")) {
                    $("#submitDialog").dialog({closed:false});
                }else{
                    getparent().mesShow("温馨提示","请答完所有题目!!!",2000,'red');
                }
            });

            $("#sureSubmit").click(function(){
                submitData();
            });

            // 提交答案
            function submitData(){
                clearInterval(remainTimeT);
                saveSingleData = [];
                    $("ul",".singleQues .main").each(function(i,v){
                        if ($(v).find("input[type='radio']:checked").length > 0) {
                            saveAnswerScore = $(".singleQues .main").eq(i).parent("div").children("span").attr("answerScore");
                            switch(saveAnswerScore){
                                case "A":
                                    saveAnswerScore = 5;
                                    break;
                                case "B":
                                    saveAnswerScore = 4;
                                    break;
                                case "C":
                                    saveAnswerScore = 3;
                                    break;
                                case "D":
                                    saveAnswerScore = 2;
                                    break;
                                case "E":
                                    saveAnswerScore = 1;
                                    break;
                            }              
                            saveSingleData.push({
                                questionCode: $(v).eq(0).children("li").find("input[type='radio']:checked").attr("name"),
                                usernameAnswer: $(v).eq(0).children("li").find("input[type='radio']:checked").val(),
                                annualQuarterCode:annualQuarterCode,
                                examCode:"lyxf",
                                answerScore:saveAnswerScore
                            });
                        }
                    })
                //if (saveSingleData.length>71) {//全部选择
                if (saveSingleData.length>0) {//全部选择
                        ajaxgeneral({
                            url: 'action/examAnswerNotes/saveAllAnswer?annualQuarterCode='+annualQuarterCode,
                            data: {
                                answerStatus:1,
                                questionList:saveSingleData,
                            },
                            loading: false,
                            contentType: "application/json; charset=utf-8",
                            success: function (res) {
                                if(remainTimeT) clearInterval(remainTimeT);

                                $("#scoreDialog h5").html("提交成功！");
                                $("#yes").show();
                                $(".submitBtn").hide();
                                $("#submitDialog").dialog({closed:true});
                                $("#scoreDialog").dialog({closed:false});
                            }
                        })
                    }
            }

            // 全答对时关闭弹框
            $("#yes").click(function(){
                $("#scoreDialog").dialog({closed:true});
                if(gps.actionType && gps.actionType=="secrecyTask"){
                    top.dialogClose("detail");
                }else{
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                    moaBridge.close();
                }
            });
 
            function close_page() {
                     var userAgent = navigator.userAgent;
                          if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") !=-1) {
                                    window.history.back();
                                    window.close();
                            }else if(userAgent.indexOf('Android') > -1 || userAgent.indexOf('Linux') > -1){
                                    window.history.back();
                                    window.close();
                            }else {
                                    window.opener = null;
                                    window.close();
                            }
                        }



            // 试卷已完成时，关闭页面
            $("#closeBtns button").click(function(){
                $('#closeDialog').dialog('close');

                if(gps.actionType && gps.actionType=="secrecyJoin"){
                    top.dialogClose("detail");
                }else{
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1 || userAgent.indexOf("MSIE 6.0") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                    moaBridge.close();
                }

            })
        })
    </script>
    <style>
        body {
            background-image: url("survey.jpg");
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover;
            opacity: 0.8;
            margin: 0px;
            padding: 0px;
        }
        .wrapper{width:100%;margin:0 auto;background-color:#fff;color:#000;}
        .details{width:100%;padding:10px 10px 60px 10px;font-size:1rem;}
        .explain{line-height:34px;margin-top:10px;font-weight: bolder;font-size: 1.5rem;text-align: center;}
        .questions{padding-bottom: 20px}
        .questionType{font-size:1.3rem;font-weight:bold;line-height:1.2;margin-top:20px;}
        ul {width: 100%;}
        .main,.main ul{padding:0 21px; display: inline-block; }
        .shortAnswer .main ul{padding:0 10px;}
        .main h6{font-size:1.1rem;;line-height:1.5;margin:25px 0 0;font-weight: 400;}
        .questions h3{line-height:1.5;font-weight: 600;text-align: center;margin-top: 25px;}
        h3 {font-size: 1.35rem;}
        .main li{ margin-left:10px; line-height:1.5;}
        .main span{display:block ;line-height:1.5;margin-left: 45px;}
        .main li.fl{margin-top:0;}
        .main .active{color:orange;}
        .main .green{color:#09DB87;}
        .main .red{color:#E11414;}
        .main input{width:auto;}
        .main label{margin-left:10px; font-size: 1rem;}
        .main span{font-size: 1rem; display: inline-block;}
        .shortAnswer .main textarea{min-height:160px;font-size:1rem;}
        .icon-duihao1{font-size:1rem;margin-left:4px;}
        .icon-cuo1{font-size:1rem;font-weight:bold;margin-left:4px;}
        .submitBtn{border:0;outline:0;width:90px;height:36px;background:#B4B4B4;border-radius:4px;font-size:1rem;color:#fff;margin:10px 0 0 60px;letter-spacing:2px;float: right;}
        .submitBtn:active{opacity:.85;}
        .canSubmit{background-color:#E83333;}
        .part span{color: red;display: block;margin-left: 50px;}
        .dialog h5{font-size:1rem;font-weight:bold;text-align:center;margin-top:10px;}
        .forceSubmitDialog p{font-size:1rem;font-weight:bold;text-align:center;margin-top:20px;}
        .scoreDialog p{font-size:1rem;text-align:center;}

        .submitBtns button{border:0;outline:0;padding:0;margin:0;height:32px;font-size:1rem;color:#fff;text-align:center;border-radius:4px;padding:0 20px!important;}
        .submitBtns .gray{background-color:#B4B4B4;}
        .submitBtns .red{background-color:#E11414;}
        .remainTime{font-size:1rem;font-weight:bold;margin-top:20px;text-align:right;}
        .clearfix>li div:nth-child(2){margin-top:10px}
        .clearfix>li div:nth-child(2) input{width:75%}
        .main>h6{text-indent:21px;}
    </style>
</head>
<body style="height: 100%;" >
<div class="wrapper">
    <div class="details">
        <p class="explain"></p>
        <div class="questions">

        </div>
        <!-- 提交 -->
        <button class="submitBtn" id="submit">提交</button>
        <!-- 提交对话框 -->
        <div id="submitDialog" class="easyui-dialog dialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#submitBtns'" style="width:400px;height:200px;padding:10px"><h5>您已答完所有题，确认提交？</h5></div>
        <div class="btns submitBtns" id="submitBtns" style="text-align:center;">
            <button class="easyui-linkbutton gray" onclick="$('#submitDialog').dialog('close')">取消</button>
            <button id="sureSubmit" class="easyui-linkbutton red">是的确认提交！</button>
        </div>
        <!-- 提交成功对话框 -->
        <div id="scoreDialog" class="easyui-dialog dialog scoreDialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#scoreBtns'" style="width:400px;height:200px;padding:10px">
            <h5>您已完成调查问卷，感谢您的参与！</h5>
            <!--<p>（注：简答题不计入合格分数，只计入参考分值）</p>-->
        </div>
        <div class="submitBtns scoreBtns" id="scoreBtns" style="text-align:center;">
            <button id="yes" class="easyui-linkbutton red hide">确定</button>
        </div>
        <!-- 打开试卷时，试卷已完成，关闭页面 -->
        <div id="closeDialog" class="easyui-dialog dialog closeDialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#closeBtns'" style="width:400px;height:200px;padding:10px">
            <h5>您已完成调查问卷，感谢您的参与！</h5>
        </div>
        <div class="submitBtns" id="closeBtns" style="text-align:center;">
            <button class="easyui-linkbutton red">确定</button>
        </div>
    </div>
</div>
</body>
</html>