/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.repository;/**
 * Created by KZH on 2019/10/8 16:06.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.knowledge.model.UsAnswerRecord;
import com.simbest.boot.exam.knowledge.model.UsInvitations;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:06
 * @desc
 **/
public interface UsAnswerRecordRepository extends LogicRepository<UsAnswerRecord, String> {

    @Query(value = "SELECT t. * FROM us_answer_record  t WHERE t.enabled=1 AND TO_CHAR(t.created_time, 'yyyy-MM-dd') = :createdTime  AND t.exam_code= :dailyQuestionBankCode  AND t.work_type= :workType  AND t.creator= :creator  ORDER BY t.created_time ASC " ,
            nativeQuery = true)
    List<UsAnswerRecord> findTodyAnswerRecordByWorkType(@Param("createdTime") String createdTime,@Param("dailyQuestionBankCode") String dailyQuestionBankCode,@Param("workType") String workType,@Param("creator") String creator);

    /**
     * 查询答题记录--人人对战使用
     * @param pmInsId
     * @return
     */
    @Query(value = "select t.*" +
            "  from us_answer_record t" +
            " where t.enabled = 1" +
            "   and t.pm_ins_id =:pmInsId " ,
            nativeQuery = true)
    List<UsAnswerRecord> findRecordByPmInsId(@Param("pmInsId") String pmInsId);


    /**
     * 查询答题记录--人人对战使用--抽题目判断
     * @param invitaitonId
     * @return
     */
    @Query(value = "select t.*" +
            "  from us_answer_record t" +
            " where t.enabled = 1" +
            "  and t.INVITATION_ID=:invitaitonId " ,
            nativeQuery = true)
    List<UsAnswerRecord> findRecordByInvitaitonId(@Param("invitaitonId") String invitaitonId);

    @Query(value = "select t.*" +
            "  from us_answer_record t" +
            " where t.enabled = 1" +
            "   and t.exam_code  = :examCode  AND t.ansewers_user_name = :ansewersUserName " ,
            nativeQuery = true)
    List<UsAnswerRecord> findAnswerRecordByUser(@Param("examCode") String examCode, @Param("ansewersUserName") String ansewersUserName);
}
