package com.simbest.boot.exam.util;

import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.sys.model.SysDictValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @用途:
 * @作者：zsf
 * @时间: 2019/1/7
 */
@Slf4j
@Component
public class DictValueTool {

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    /**
     * 根据类型查找该类型的值
     * @param type 类型
     * @return
     */
    public List<Map<String,Object>> getByType( String type) {
        /**设置返回值**/
        List<Map<String,Object>> list = null;

        /**当类型不为空时查询该类型下的value值**/
        if (StringUtils.isNotEmpty( type )){
            Map<String,Object> map = Maps.newHashMap();
            String sql = " select t.* from SYS_DICT_VALUE t where  t.enabled=1 and t.dict_type =:dictType order by t.display_order";
            map.put("dictType",type);
            list = customDynamicWhere.queryNamedParameterForList( sql, map);
        }

        return list;
    }
}
