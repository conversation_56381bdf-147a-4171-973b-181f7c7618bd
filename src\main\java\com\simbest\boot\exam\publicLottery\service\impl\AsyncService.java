package com.simbest.boot.exam.publicLottery.service.impl;

import cn.hutool.core.map.MapUtil;
import com.simbest.boot.cmcc.a4.response.JsonResponse;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.TextBodyRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.function.Function;

import static com.simbest.boot.config.MultiThreadConfiguration.MULTI_THREAD_BEAN;

/**
 * @ClassName: AsyncService
 * @description:
 * @author: ZHAOBO
 * @create: 2024-05-10 20:56
 */
@Component
@Slf4j
public class AsyncService {

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Async(MULTI_THREAD_BEAN)
    public void sendUrl (String username) {
        String url = "http://************:8088/exam/action/publicLottery/drawLottery/sso?appcode=exam&uid=";
//        String url = "http://***********:10063/exam/action/publicLottery/drawLottery/sso?appcode=exam&uid=";
        JsonResponse response = httpPost.apply(url + rsaEncryptor.encrypt(username)).asBean(JsonResponse.class);
        if (response.getErrcode() == 0 ) {
            Map<String , Object> data = (Map<String , Object>) response.getData();
            log.error("人：{} , 号：{}" , MapUtil.getStr(data , "username") , MapUtil.getStr(data , "num") );
        } else {
            log.error("请求错误：{}" , response.getMessage());
        }
    }

    /**
     * httpPost
     */
    private static final Function<String, TextBodyRequest> httpPost = (String u) -> {
        TextBodyRequest textBodyRequest = new TextBodyRequest(u);
        RestTemplate restTemplate = new RestTemplate();
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setReadTimeout(60000); // 设置读取超时时间（毫秒）
        factory.setConnectTimeout(60000); // 设置连接超时时间（毫秒）
        restTemplate.setRequestFactory(factory);
        textBodyRequest.setRestTemplate(restTemplate);
        return textBodyRequest;
    };

}
