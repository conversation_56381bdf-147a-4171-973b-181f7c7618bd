package com.simbest.boot.exam.briefDistribution.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.briefDistribution.model.OrganizationValue;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface OrganizationValueRepository extends LogicRepository<OrganizationValue, String> {
    List<OrganizationValue> findOrganizationValuesByGroupTypeAndStatus(@Param("groupType") String groupType,@Param("status") String status);
}
