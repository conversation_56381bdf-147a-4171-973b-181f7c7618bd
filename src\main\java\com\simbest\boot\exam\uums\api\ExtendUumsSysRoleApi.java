package com.simbest.boot.exam.uums.api;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.util.UumsUtil;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @title: ExtendUumsSysRoleApi
 * @projectName exam
 * @description: 扩展uums中的角色管理API
 * @date 2021/6/28  11:52
 */
@Component
@Slf4j
public class ExtendUumsSysRoleApi extends UumsSysRoleApi {

    private static final String USER_MAPPING = "/action/sys/role/";
    private static final String SSO = "/sso";

    @Autowired
    private UumsUtil uumsUtil;


}
