/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.web;/**
 * Created by KZH on 2019/10/8 16:09.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsAnswerRecord;
import com.simbest.boot.exam.knowledge.service.IUsAnswerRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:09
 * @desc
 **/
@Api(description = "答题记录表", tags = {"答题记录表控制器"})
@Slf4j
@RestController
@RequestMapping(value = "/action/usAnswerRecord")
public class UsAnswerRecordController extends LogicController<UsAnswerRecord, String> {

    private IUsAnswerRecordService usAnswerRecordService;

    @Autowired
    public UsAnswerRecordController(IUsAnswerRecordService usAnswerRecordService) {
        super(usAnswerRecordService);
        this.usAnswerRecordService = usAnswerRecordService;
    }
    @ApiOperation(value = "获取答题记录列表接口", notes = "获取答题记录列表接口")
    @PostMapping(value = {"/getRecordList", "/sso/getRecordList", "/api/getRecordList"})
    public JsonResponse getRecordList(@RequestParam(required = false, defaultValue = "1") int page,
                                      @RequestParam(required = false, defaultValue = "10") int size,
                                      @RequestParam(required = false ,defaultValue = "DESC") String direction,
                                      @RequestParam(required = false ,defaultValue = "createdTime") String properties,
                                      @RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                      @RequestParam(value = "currentUserCode",required = false) String currentUserCode,
                                      @RequestParam(value = "workType",required = false) String workType,
                                      @RequestParam(value = "time",required = false) String time ) {
//        return usAnswerRecordService.getRecordList(page,size,direction,properties,source,currentUserCode,workType,time);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }

    @ApiOperation(value = "获取答题排名接口", notes = "获取答题排名接口")
    @PostMapping(value = {"/getSocreRanking", "/sso/getSocreRanking", "/api/getSocreRanking"})
    public JsonResponse getRecordList(@RequestParam(required = false, defaultValue = "1") int page,
                                      @RequestParam(required = false, defaultValue = "10") int size,
                                      @RequestParam(required = false ,defaultValue = "DESC") String direction,
                                      @RequestParam(required = false ,defaultValue = "createdTime") String properties,
                                      @RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                      @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
       // return usAnswerRecordService.getSocreRanking(page,size,direction,properties,source,currentUserCode);
    }


    @ApiOperation(value = "导出答题排名接口", notes = "获取答题排名接口")
    @PostMapping(value = {"/exportSocreRanking", "/sso/exportSocreRanking", "/api/exportSocreRanking"})
    public void exportSocreRanking(HttpServletRequest request, HttpServletResponse response,
                                      @RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                      @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
       // return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
        //usAnswerRecordService.exportSocreRanking(request,response,source,currentUserCode);
    }

    @ApiOperation(value = "获取答题正确次数及本次得分接口", notes = "获取答题正确次数及本次得分接口")
    @PostMapping(value = {"/getRecordRankingById", "/sso/getRecordRankingById", "/api/getRecordRankingById"})
    public JsonResponse getRecordList(@RequestParam(required = true, defaultValue = "1") String id,
                                      @RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                      @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
       // return usAnswerRecordService.getRecordRankingById(id,source,currentUserCode);
    }
}
