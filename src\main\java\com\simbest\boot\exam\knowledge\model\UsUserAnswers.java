/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.model;/**
 * Created by KZH on 2019/10/8 15:55.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:55
 * @desc 考试业务单据
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_user_answers")
@ApiModel(value = "用户答题记录表")
public class UsUserAnswers extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "W") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 500)
    @ApiModelProperty(value = "邀请ID", name = "invitaitonId", example = "2019678301426913763328")
    private String invitationId;

    @Column(length = 500)
    @ApiModelProperty(value = "主单据Id", name = "pmInsId", example = "2019678301426913763328")
    private String pmInsId;

    @Column(length = 500)
    @ApiModelProperty(value = "用户答题记录表主键id")
    private String answerRecordId;

    @Column(length = 500)
    @ApiModelProperty(value = "答题用户OA账户", name = "ansewersUserName", example = "超级管理员")
    private String ansewersUserName;

    @Column(length = 500)
    @ApiModelProperty(value = "答题用户姓名", name = "ansewersTrueName", example = "超级管理员")
    private String ansewersTrueName;

    @Column(length = 40)
    @ApiModelProperty(value = "题库编码",name = "questionBankCode",example = "A-001",required = true)
    private String questionBankCode;


    @Column(length = 2000)
    @ApiModelProperty(value = "问题ID", name = "questionId", example = "问题ID")
    private String questionId;

    @Column(length = 2000)
    @ApiModelProperty(value = "问题Code", name = "questionCode", example = "问题Code")
    private String questionCode;

    @Column(length = 500)
    @ApiModelProperty(value = "用户回答的答案", name = "chosenAnswer", example = "用户回答的答案")
    private String chosenAnswer;


    @Column(length = 500)
    @ApiModelProperty(value = "正确答案", name = "trueAnswer", example = "正确答案")
    private String trueAnswer;


    @Column(length = 500)
    @ApiModelProperty(value = "用户的回答是否正确(1:是 0:否)", name = "isCorrect", example = "用户的回答是否正确")
    private String isCorrect;


    @Column(length = 500)
    @ApiModelProperty(value = "得分", name = "score", example = "得分")
    private String score;


    @Column(length = 500)
    @ApiModelProperty(value = "用户回答这道题的时间戳", name = "answerTime", example = "用户回答这道题的时间戳")
    private String answerTime;


    @Column(length = 500)
    @ApiModelProperty(value = "答题类型", name = "workType", example = "答题类型")
    private String workType;

}
