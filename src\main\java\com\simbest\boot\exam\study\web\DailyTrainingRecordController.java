package com.simbest.boot.exam.study.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.study.model.DailyTrainingRecord;
import com.simbest.boot.exam.study.model.TrainingAnswerDetail;
import com.simbest.boot.exam.study.service.IDailyTrainingRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用途：DailyTrainingRecord领域对象名称控制器
 */
@Api(description = "DailyTrainingRecordController", tags = {"学习管理-日常训练记录"})
@Slf4j
@RestController
@RequestMapping(value="/action/study/dailyTrainingRecord")
public class DailyTrainingRecordController extends LogicController<DailyTrainingRecord, String> {

    private IDailyTrainingRecordService dailyTrainingRecordService;
    @Autowired
    public DailyTrainingRecordController(IDailyTrainingRecordService service) {
        super(service);
        this.dailyTrainingRecordService = service;
    }

    @ApiOperation(value = "每日答题", notes = "每日答题（5次）")
    @PostMapping(value = {"/dailyQuestion" , "/dailyQuestion/sso" , "/dailyQuestion/api" })
    public JsonResponse dailyQuestion(@RequestParam(defaultValue = "PC") String source ,
                                        @RequestParam(required = false ) String currentUserCode) {
        return dailyTrainingRecordService.dailyQuestion(source , currentUserCode);
    }


    @ApiOperation(value = "提交每题信息")
    @PostMapping(value = {"/submitAnswer" , "/submitAnswer/sso" , "/submitAnswer/api"})
    public JsonResponse submitAnswer(@RequestParam(defaultValue = "PC") String source ,
                                     @RequestParam(required = false ) String currentUserCode ,
                                     @RequestBody TrainingAnswerDetail detail) {
        return dailyTrainingRecordService.submitAnswer(source , currentUserCode , detail);
    }

    @PostMapping(value = {"/generateRandomQuestionsByBankCode" , "/generateRandomQuestionsByBankCode/sso" , "/generateRandomQuestionsByBankCode/api"})
    public JsonResponse generateRandomQuestionsByBankCode (String questionBankCode) {
        return JsonResponse.success(dailyTrainingRecordService.generateRandomQuestionsByBankCode(questionBankCode));
    }

}