package com.simbest.boot.exam.publicLottery.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.publicLottery.model.PublicLottery;

import java.util.List;

public interface IPublicLotteryService extends ILogicService<PublicLottery, String> {

    PublicLottery drawLottery();

    PublicLottery getLottery();

    List<PublicLottery> getLotteryList();

    void resetLottery();

    void resetLotteryPerSon(String username, boolean allDelete);
}
