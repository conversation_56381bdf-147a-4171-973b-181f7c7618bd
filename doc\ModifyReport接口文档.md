# ModifyReportController接口文档

本文档详细记录了ModifyReportController控制器的所有接口信息，包括接口名称、路径、参数、测试用例和响应示例。

## 1. ModifyReportController (错题上报控制器)

标签: 学习管理-错题上报
基础路径: `/action/modifyReport`

### 1.1 保存错题上报信息接口

- **接口名称**: 保存错题上报信息
- **接口说明**: 用户提交错题修改申请
- **请求方式**: POST
- **请求路径**: 
  - `/action/modifyReport/saveModify`
  - `/action/modifyReport/saveModify/sso`
  - `/action/modifyReport/saveModify/api`
- **请求参数**:
  | 参数名 | 类型 | 是否必填 | 默认值 | 说明 |
  | --- | --- | --- | --- | --- |
  | source | String | 否 | PC | 来源 (PC/MOBILE) |
  | currentUserCode | String | 否 | - | 当前用户编码（移动端需传） |
  | map | Map<String, Object> | 是 | - | 上报信息对象 |

- **map参数说明**:
  | 参数名 | 类型 | 是否必填 | 说明 |
  | --- | --- | --- | --- |
  | questionCode | String | 是 | 题目编码 |
- **请求示例**:
  ```json
  {
    "questionCode": "A-001-1"
  }
  ```

- **响应示例**:
  ```json
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "data": {}
  }
  ```

### 1.2 查询错题上报信息接口

- **接口名称**: 查询错题上报信息
- **接口说明**: 分页查询用户提交的错题上报信息
- **请求方式**: POST
- **请求路径**: 
  - `/action/modifyReport/queryModifyReport`
  - `/action/modifyReport/queryModifyReport/sso`
  - `/action/modifyReport/queryModifyReport/api`
- **请求参数**:
  | 参数名 | 类型 | 是否必填 | 默认值 | 说明 |
  | --- | --- | --- | --- | --- |
  | page | Integer | 否 | 1 | 页码 |
  | size | Integer | 否 | 10 | 每页大小 |
  | source | String | 否 | PC | 来源 (PC/MOBILE) |
  | currentUserCode | String | 否 | - | 当前用户编码（移动端需传） |
  | report | ModifyReport | 否 | - | 查询条件对象 |

- **report对象参数说明**:
  | 参数名 | 类型 | 是否必填 | 说明 |
  | --- | --- | --- | --- |
  | questionCode | String | 否 | 题目编码 |
  | questionGroupName | String | 否 | 题目分组名称 |
  | status | String | 否 | 状态 (0:上报, 1:采纳, 2:拒绝采纳) |

- **请求示例**:
  ```json
  {
    "username": "hadmin",
    "questionGroupName": "计算机基础",
    "status": "0"
  }
  ```

- **响应示例**:
  ```json
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "data": {
      "content": [
        {
          "id": "WQC892069679764439040",
          "username": "hadmin",
          "questionId": "EQ20250724110858",
          "questionCode": "A-001-1",
          "questionType": "single",
          "questionGroupName": "计算机基础",
          "questionContent": "下列选项中，哪个是Java的关键字？",
          "userAnswer": "B",
          "modifyContent": "我认为答案应该是A",
          "status": "0",
          "createdDate": "2025-08-14T14:30:00",
          "lastUpdatedDate": "2025-08-14T14:30:00"
        }
      ],
      "totalElements": 1,
      "totalPages": 1,
      "numberOfElements": 1
    }
  }
  ```

### 1.4 查询题目详细信息接口

- **接口名称**: 查询题目详细信息
- **接口说明**: 根据题目ID查询题目的详细信息，包括题目内容和答案选项
- **请求方式**: POST
- **请求路径**: 
  - `/action/modifyReport/findQuestionById`
  - `/action/modifyReport/findQuestionById/sso`
  - `/action/modifyReport/findQuestionById/api`
- **请求参数**:
  | 参数名 | 类型 | 是否必填 | 默认值 | 说明 |
  | --- | --- | --- | --- | --- |
  | source | String | 否 | PC | 来源 (PC/MOBILE) |
  | currentUserCode | String | 否 | - | 当前用户编码（移动端需传） |
  | id | String | 是 | - | 题目ID |

- **请求示例**:
  ```
  id=EQ20250724110858
  ```

- **响应示例**:
  ```json
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "data": {
      "id": "EQ20250724110858",
      "questionCode": "A-001-1",
      "questionBankCode": "A-001",
      "questionOrder": 1,
      "questionGroupName": "计算机基础",
      "questionName": "下列选项中，哪个是Java的关键字？",
      "questionType": "single",
      "questionScore": "10",
      "questionClass": "technical",
      "titleDescription": "Java基础知识考察",
      "answerList": [
        {
          "id": "EQA001",
          "questionCode": "A-001-1",
          "answerCode": "A",
          "answerContent": "class",
          "isCorrect": true,
          "correctExplanation": "class是Java的关键字，用于定义类",
          "answerScore": 10
        },
        {
          "id": "EQA002",
          "questionCode": "A-001-1",
          "answerCode": "B",
          "answerContent": "function",
          "isCorrect": false,
          "correctExplanation": "function不是Java的关键字，是JavaScript等语言的关键字",
          "answerScore": 0
        }
      ]
    }
  }
  ```

### 1.5 修改题目信息接口

- **接口名称**: 修改题目信息
- **接口说明**: 管理员修改题目信息
- **请求方式**: POST
- **请求路径**: 
  - `/action/modifyReport/updateQuestionInfo`
  - `/action/modifyReport/updateQuestionInfo/sso`
  - `/action/modifyReport/updateQuestionInfo/api`
- **请求参数**:
  | 参数名 | 类型 | 是否必填 | 默认值 | 说明 |
  | --- | --- | --- | --- | --- |
  | source | String | 否 | PC | 来源 (PC/MOBILE) |
  | currentUserCode | String | 否 | - | 当前用户编码（移动端需传） |
  | map | Map<String, Object> | 是 | - | 题目信息对象 |

- **map参数说明**:
  | 参数名 | 类型 | 是否必填 | 说明 |
  | --- | --- | --- | --- |
  | id | String | 是 | 题目ID |
  | status | String | 是 | 状态 ，(0:上报, 1:修改, 2:不修改)   |
  | questionType | String | 否 | 题目类型 ， status为1时必填 |
  | answerCode | String | 否 | 正确答案， 多选使用/分割 ，status为1时，必填 |

- **请求示例**:
  ```json
  {
    "id": "EQ20250724110858",
    "questionName": "下列选项中，哪个是Java的关键字？",
    "titleDescription": "Java基础知识考察",
    "questionScore": "10"
  }
  ```

- **响应示例**:
  ```json
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "data": {}
  }
  ```

## 2. 数据结构说明

### 2.1 ModifyReport对象

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | String | 主键ID |
| username | String | 用户名 |
| questionId | String | 题目ID |
| questionCode | String | 题目编码 |
| questionType | String | 题目类型 |
| questionGroupName | String | 题目分组名称 |
| questionContent | String | 题目内容 |
| userAnswer | String | 用户答案 |
| modifyContent | String | 修改原因 |
| status | String | 状态 (0:上报, 1:采纳, 2:拒绝采纳) |
| createdDate | Date | 创建时间 |
| lastUpdatedDate | Date | 最后更新时间 |

### 2.2 ExamQuestion对象

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | String | 题目ID |
| questionCode | String | 题目编码 |
| questionBankCode | String | 题库编码 |
| questionOrder | Integer | 题目顺序 |
| questionGroupName | String | 题目分组名称 |
| questionName | String | 题目名称 |
| questionType | String | 题目类型 |
| questionScore | String | 题目分数 |
| questionClass | String | 题目类别 |
| titleDescription | String | 题目描述 |
| answerList | List<ExamQuestionAnswer> | 答案选项列表 |

### 2.3 ExamQuestionAnswer对象

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | String | 答案ID |
| questionCode | String | 题目编码 |
| answerCode | String | 答案编码（如A、B、C、D） |
| answerContent | String | 答案内容 |
| isCorrect | Boolean | 是否为正确答案 |
| correctExplanation | String | 正确答案解释 |
| answerScore | Integer | 答案分值 |

### 2.4 状态值说明

| 状态值 | 说明 |
| --- | --- |
| 0 | 已上报 |
| 1 | 已采纳 |
| 2 | 已拒绝 |

## 3. 错误码说明

| 错误码 | 说明 |
| --- | --- |
| 0 | 操作成功 |
| 400 | 参数错误 |
| 500 | 服务器内部错误 |