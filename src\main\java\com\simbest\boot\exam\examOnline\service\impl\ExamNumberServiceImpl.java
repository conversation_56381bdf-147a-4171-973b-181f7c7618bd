package com.simbest.boot.exam.examOnline.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.examOnline.model.ExamNumber;
import com.simbest.boot.exam.examOnline.repository.ExamNumberRepository;
import com.simbest.boot.exam.examOnline.service.IExamNumberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ExamNumberServiceImpl extends LogicService<ExamNumber,String> implements IExamNumberService {
    private ExamNumberRepository repository;
    public ExamNumberServiceImpl(ExamNumberRepository repository) {
        super(repository);
        this.repository = repository;
    }


    /**
     * 查询考试次数
     *
     * @param examCode
     * @param userName
     * @return
     */
    @Override
    public ExamNumber findExamNumber(String examCode, String userName) {
        return repository.findExamNumber(examCode,userName);
    }
}
