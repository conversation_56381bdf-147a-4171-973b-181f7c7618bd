﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>新增或修改</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var gps = getQueryString();
        //只读
        window.formReady = function () {
            formReadonly("sysRolePositionTableAddForm");
        };
        //取消只读
        window.formReadyNO = function () {
            formReadonlyNo("sysRolePositionTableAddForm");
        };
        //初始化界面
        window.initsystem = function () {
            //console.log(gps);
            $("#roleId").val(gps.roleId);
        };
        //绑定值到form
        window.bindval = function (data, pageparam) {
            //把取到的数据赋值到对应form表单
            formval(data, pageparam.dialogform.formname, pageparam.dialogform.ctable, (pageparam.dialogform.divimages || ""), pageparam.dialogform.ctablerender);
        };
        //表单校验
        window.fvalidate = function () {
            return $("#sysRolePositionTableAddForm").form("validate");
        };
    </script>
</head>
<body>
<form id="sysRolePositionTableAddForm" method="post" contentType="application/json; charset=utf-8" class="clear" cmd-select="action/role/position/findById" bindval="bindval()" initsystem="initsystem()">
    <!--........隐藏属性begin........-->
    <!--调研计划id-->
    <input id="id" name="id" type="hidden"/>
    <input id="roleId" name="roleId" type="hidden"/>
    <!--........隐藏属性end........-->
    <table border="0" cellpadding="0" cellspacing="10" width="100%">
        <tr>
            <td width="100" align="right">选择职务<font class="col_r">*</font>：</td>
            <td width="300">
                <input id="positionId" name="positionId" class="easyui-combobox" required='required' style="width: 100%; height: 32px;" data-options="
					valueField: 'id',
					ischooseall:true,
					textField: 'positionName',
					queryParams:{},
					contentType:'application/json; charset=utf-8',
					url: '/uums/action/position/position/findAllNoPage'" />
            </td>
            <td></td>
        </tr>
        <tr>
            <td width="100" align="right">显示顺序<font class="col_r">*</font>：</td>
            <td width="300">
                <input id="displayOrder" name="displayOrder" type="text" validType="zinteger"
                       class="easyui-validatebox" required='required'/><!-- 这里要弹出查询企业列表页面-->
            </td>
            <td></td>
        </tr>
    </table>
</form>
</body>
</html>
