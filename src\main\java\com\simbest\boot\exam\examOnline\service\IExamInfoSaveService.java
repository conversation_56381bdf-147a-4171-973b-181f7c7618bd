/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.examOnline.model.ExamInfoSave;

public interface IExamInfoSaveService extends ILogicService<ExamInfoSave, String> {

    ExamInfoSave saveExam(String currentUserCode, String source, ExamInfoSave o);

    //    /**
    ExamInfoSave findByNewInfo(String publishUsername, String examCode, String examAppCode);
}
