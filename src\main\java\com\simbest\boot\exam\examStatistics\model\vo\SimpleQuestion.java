package com.simbest.boot.exam.examStatistics.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * SimpleQuestion
 *
 * <AUTHOR>
 * @since 2024/4/19 10:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimpleQuestion implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "题库编码")
    private String questionBankCode;

    @ApiModelProperty(value = "题目类型")
    private String questionType;

    @ApiModelProperty(value = "题目编码")
    private String questionCode;

    @ApiModelProperty(value = "题目名称")
    private String questionName;

    @ApiModelProperty(value = "题目分数")
    private String questionScore;

    @ApiModelProperty(value = "顺序")
    private Integer questionOrder;

    private List<SimpleQuestionAnswer> answerList;


}
