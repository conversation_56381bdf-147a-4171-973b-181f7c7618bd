<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
  <title>选择组织树</title>
  <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
    th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
    th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
  <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
      th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
      type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
      th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
      type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
      th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
      type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
      th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
      type="text/javascript"></script>
  <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
      type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
      th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
      type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
      th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
      type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
      th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
      type="text/javascript"></script>
</head>
<style>
  .ztree li ul.line{height:auto;}
  .keyinput{width: 480px;padding: 10px;}
  #key{width: 415px;}
  .clear{position: fixed;top: 10px;right: 10px; display: inline-block;width: 65px;height: 26px;line-height: 26px;text-align: center;background-color: #e34d4d;color:#fff;border-radius: 2px;z-index:9}
  .orgC{padding-top: 35px;} 
</style>
<script>
  var gps=getQueryString();
  var hasChoosed=[];
  $(function(){
    getCurrent();
    initTree();//初始化树

    $(document).on("click",".role a i",function(){//删除当前选择
      var id = $(this).parent("a").attr('id');
      for (var i = 0; i < hasChoosed.length; i++) {
        if (hasChoosed[i].id=id) {
          hasChoosed.splice(i,1);
        }
      }
      $(this).parent("a").remove();
    });

    //全部清除按钮
    $(document).on("click",".clear",function(){
      $(".role").html("");
      hasChoosed = [];
    });

  });


    //初始化树
    function initTree() {
      var ajaxopts = {
        url:"action/range/findCompanyAndChildCompany",
        data:{"summaryId":gps.summaryId},
        contentType: "application/json; charset=utf-8",
        success: function (data) {
          var companyTree = data.data;
          userTree(companyTree);
        }
      };
      ajaxgeneral(ajaxopts);
    }



  //树
  function userTree(companyTree) {
    var datas=toTreeData(companyTree,"id","orgCode","id|id,orgName|text,companyCode,levelDictValue,summaryId,orgCode,belongCompanyCode,belongDepartmentCode,displayName,companyTypeDictValue,styleDictValue");
    $("#orgTree").tree({
      lines:true,//是否显示树控件上的虚线
      treePid:'orgCode',
      queryParams:{"summaryId":gps.summaryId},
      contentType: "application/json; charset=utf-8",
      cascadeCheck:false,
      data: datas,
      fileds:'id|id,orgName|text,levelDictValue,orgCode,belongCompanyCode,,companyCode,belongDepartmentCode,summaryId,displayName,companyTypeDictValue,styleDictValue',
      animate:true,//节点在展开或折叠的时候是否显示动画效果
      onClick:function(node){
      },
      onLoadSuccess:function(node,data){
        $("#orgTree").find("ul >li:not(:has(ul))").find('.tree-title').css({'background':'whith','border':'whith','color':'black'});   // 选择最后一个节点 并添加 float:left
      },

      //选择之前
      onBeforeSelect:function(node){
        if(gps.multi==0){
          var nodes=$("#orgTree").tree("getChecked");
          for(var i in nodes){
            $("#orgTree").tree("uncheck",nodes[i].target);
          }
          $(".role").html("");
        }

        //判断是否已经有该成员信息，进行添加操作
        if(isNotChoosed(node.id)==0){
              var end=node.displayName.indexOf("\\");
              var companyName=node.displayName.slice(0,end);
              $(".role").append("<a id='"+node.id+"' companyName='"+companyName+"'><font>"+companyName+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
              hasChoosed.push(node);
          } else{
              top.mesAlert("提示", "成员已存在，无需再次添加！", 'warning');
        }
      }

    });
  }


  //判断是否已选择
  function isNotChoosed(flag){  
    for (var i = 0; i < hasChoosed.length; i++) {
      var id = hasChoosed[i].id;
      if (id==flag) {
        return 1;
      }
    }
    return 0;
  }


  //获取数据发送到上级页面
  window.getchoosedata=function(){
    for (var index = 0; index < hasChoosed.length; index++) {
      var end=hasChoosed[index].displayName.indexOf("\\");
      hasChoosed[index].companyName=hasChoosed[index].displayName.slice(0,end);
    }
    var datas=hasChoosed;
    return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};
  };
</script>
<body>
  <ul id="orgTree"></ul>
  <div class="role orgC"></div>
  <div class="clear">全部清空</div>
</body>
</html>