/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamUser;

import java.util.List;

/**
 * <strong>Title : IExamUserService</strong><br>
 * <strong>Description : 试卷人员Dao </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR> kou<PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IExamUserService extends ILogicService<ExamUser,String> {

    /**
     * 根据题库code获取试卷
     * @param questionBankCode
     * @param username
     * @return
     */
    List<ExamUser> getExamUserByQuestionBankCode(String questionBankCode,String username);

    ExamUser getExamUserByBankCode(String bankCode,String username);

    JsonResponse judgeUserWhetherHavePermission();
}
