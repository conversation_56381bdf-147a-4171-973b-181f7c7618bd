package com.simbest.boot.exam.examOnline.web;

import com.alibaba.druid.sql.visitor.functions.If;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.service.IExamSaveTimerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @desc: 根据试卷的答题试卷限制，实现自动提交答卷功能
 * @date 2021/7/13  18:58
 */
@Api(description = "根据答题时间限制自动提交试卷",tags = "根据答题时间限制自动提交试卷")
@Slf4j
@RestController
@RequestMapping(value = "/action/saveTimer")
public class ExamInfoTimerController {

    @Autowired
    private IExamSaveTimerService iExamSaveTimerService;

    @ApiOperation(value = "保存答题记录", notes = "保存答题记录")
    @PostMapping(value = {"/saveExamInfoTimer","sso/saveExamInfoTimer","api/saveExamInfoTimer"})
    public JsonResponse saveExamInfoTimer(@RequestParam(required = false) int time,
                                     @RequestParam(required = false) String source,
                                     @RequestBody(required = false) ExamInfo examInfo) {
        Boolean b = iExamSaveTimerService.saveExamInfoTimer(time, source, examInfo);
        if (b){
            return JsonResponse.success("答题试卷截止,已自动交卷");
        }
        return JsonResponse.fail("答题试卷截止，无法交卷");
    }

}
