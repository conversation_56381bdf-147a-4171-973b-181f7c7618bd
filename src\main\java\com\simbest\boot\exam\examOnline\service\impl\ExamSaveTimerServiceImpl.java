package com.simbest.boot.exam.examOnline.service.impl;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.service.IExamInfoService;
import com.simbest.boot.exam.examOnline.service.IExamSaveTimerService;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Timer;
import java.util.TimerTask;

/**
 * <AUTHOR>
 * @desc: 根据试卷答题试卷自动提交答题
 * @date 2021/7/13  18:50
 */
@Slf4j
@Service
public class ExamSaveTimerServiceImpl implements IExamSaveTimerService {

    @Autowired
    private IExamInfoService iExamInfoService;
    /**
      * @desc 根据试卷的限制时间进行自动答题操作
      * <AUTHOR>
      */
    @Override
    public Boolean saveExamInfoTimer(int time, String source, ExamInfo examInfo) {
        try {
            String currentUserName = SecurityUtils.getCurrentUserName();
            Timer timer = new Timer();
            TimerTask task = new TimerTask() {
                @Override
                public void run() {
                    JsonResponse jsonResponse = iExamInfoService.saveExamInfo(currentUserName, source, examInfo);
                }
            };
            timer.schedule(task, time*60*1000);

        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return true;
    }
}
