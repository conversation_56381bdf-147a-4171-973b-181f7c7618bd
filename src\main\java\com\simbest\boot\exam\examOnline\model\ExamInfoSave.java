/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;/**
 * Created by KZH on 2019/10/8 10:24.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * 自动保存表
 *
 * <AUTHOR>
 * @since 2023-09-11
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_info_save")
@ApiModel(value = "答题记录表")
public class ExamInfoSave extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EIS") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "剩余时间", name = "residueTime", example = "20", required = true)
    private String residueTime;

    @Column(length = 40)
    @ApiModelProperty(value = "答题次数", name = "examNumber", example = "1")
    private Integer examNumber = 0;

    @Column(length = 40)
    @ApiModelProperty(value = "考试编码", name = "examCode")
    private String examCode;

    @Column(length = 40)
    @ApiModelProperty(value = "试卷编码", name = "examAppCode", example = "hnjjwz", required = true)
    private String examAppCode;

    @Column(length = 2000)
    @ApiModelProperty(value = "答题记录", name = "examRecord", example = "A-001-1,A-001-2,A-001-3,A-001-4")
    private String examRecord;

    @Column(length = 3000)
    @ApiModelProperty(value = "答案记录", name = "examAnswer", example = "A/B/C/B,C/A,B,C/")
    private String examAnswer;


    /**
     * 20240408 中央巡视整改工作调查问卷专用  所在部门   政治面貌
     */
    @Column(length = 200)
    @ApiModelProperty(value = "所在部门")
    private String belongDept;

    @Column(length = 100)
    @ApiModelProperty(value = "政治面貌")
    private String politics ;

    @Column
    @ApiModelProperty(value = "时间戳")
    private Long timestamp;

    @Column
    @ApiModelProperty(value = "来源")
    private String source;

    @Column
    @ApiModelProperty(value = "是否弃权")
    private Boolean hasDrop = Boolean.FALSE;

    @Transient
    @ApiModelProperty(value = "时间戳加密文")
    private String salt;

    @Transient
    @ApiModelProperty(value = "每次 进入/重新进入 答题页面时记录,用来记录答题次数")
    private Boolean isVisit;

    @Transient
    @ApiModelProperty(value = "分数")
    private String score;
}
