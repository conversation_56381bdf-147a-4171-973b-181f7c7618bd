/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;/**
 * Created by KZH on 2019/10/8 10:22.
 */


import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2019-10-08 10:22
 * @desc 题目导入表
 **/
@Data
public class ExamQuestionImport {

    @ExcelVOAttribute(name = "题库编码", column = "A")
    private String questionBankCode;

    @ExcelVOAttribute(name = "题目分组" , column = "B")
    private String questionGroupName;

    @ExcelVOAttribute(name = "题目名称", column = "C")
    private String questionName;

    @ExcelVOAttribute(name = "题目类型", column = "D")
    private String questionType;

    @ExcelVOAttribute(name = "题目分数", column = "E")
    private String questionScore;

    @ExcelVOAttribute(name = "答案A", column = "F")
    private String answerA;

    @ExcelVOAttribute(name = "答案B", column = "G")
    private String answerB;

    @ExcelVOAttribute(name = "答案C", column = "H")
    private String answerC;

    @ExcelVOAttribute(name = "答案D", column = "I")
    private String answerD;

    @ExcelVOAttribute(name = "答案E", column = "J")
    private String answerE;

    @ExcelVOAttribute(name = "答案F", column = "K")
    private String answerF;

    @ExcelVOAttribute(name = "答案G", column = "L")
    private String answerG;

    @ExcelVOAttribute(name = "答案H", column = "M")
    private String answerH;

    @ExcelVOAttribute(name = "答案I", column = "N")
    private String answerI;

    @ExcelVOAttribute(name = "答案J", column = "O")
    private String answerJ;

    @ExcelVOAttribute(name = "答案K", column = "P")
    private String answerK;

    @ExcelVOAttribute(name = "答案L", column = "Q")
    private String answerL;

    @ExcelVOAttribute(name = "正确答案", column = "R")
    private String answerIsTrue;

    @ExcelVOAttribute(name = "多选最多可选答案数量", column = "S")
    private Integer maxChooseNum;

}
