package com.simbest.boot.exam.publicLottery.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.publicLottery.model.PublicLottery;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

public interface PublicLotteryRepository extends LogicRepository<PublicLottery, String> {


    @Modifying
    @Query(nativeQuery = true, value = "delete from us_public_lottery")
    int myDeleteAll();

}
