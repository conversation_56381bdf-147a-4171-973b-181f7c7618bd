package com.simbest.boot.exam.util;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 用途：消息模板，参考core包 MessageEnum类
 * 作者: jingwenhao
 * 时间: 2019/7/19  15:07
 */
public enum MessageEnum {

    /**
     * 系统消息
     **/
    //系统正在建设中，敬请期待！
    MS000000("系统正在建设中，敬请期待！"),
    //系统版本已升级至${version}，赶快体验吧！
    MS000001("系统版本已升级至${version}，赶快体验吧！"),
    //无法预览该文件！
    MS000002("无法预览该文件！"),
    //正在支付中，请勿重复操作！
    MS000003("正在${operation}，请勿重复操作！"),
    //文件上传失败，请确认后重新上传
    MS000004("${fileName} 上传失败，请确认后重新上传！"),
    //反馈提醒
    MS000005("感谢你对平台的关注与支持，我们会认真处理你的反馈，尽快修复和完善相关功能"),
    //操作成功
    MS000006("操作成功"),
    //操作失败
    MS000007("操作失败!"),
    //数据校验成功信息
    MS000008("校验通过!"),

    /**
     * 短信模板
     */
    // 网管中心监控调度系统：您收到张红旗向您下发的[]的检查任务单，任务填报截止时间为 2020-11-24 10:00:00，请及时检查。
    SMS000001("您收到${fromTruename}向您发送的【${title}】的通知信息，请您通过下方5G消息链接查看此消息，待办链接：${url}"),

    SMS000010("${trueName} , 您好！您的验证码为：${dynSmsCode},此验证码${minute}分钟内有效，请妥善保管，勿向他人泄露！"),

    ;


    private String message;

    /**
     * 私有构造方法①，不指定消息显示类型。
     *
     * @param message
     */
    MessageEnum(String message) {
        this.message = message;
    }

    /**
     * 替换参数的正则。<br>
     */
    public static final Pattern pattern = Pattern.compile("\\$\\{(\\w+)\\}");

    /**
     * 解析Enum，生成前台json格式字符串。
     *
     * @return
     */
    public static String getObjStrForJS() {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("{");
        for (MessageEnum enum1 : MessageEnum.values()) {
			/*
			 * 单个消息模板的结构如下：
			"CONFIRM_COMMIT" :{
				"id" : id,
				"message" : message
			};
			 */
            stringBuffer.append(", ");
            stringBuffer.append("\"" + enum1.name() + "\" : ");
            stringBuffer.append("{");
            stringBuffer.append("\"id\" : " + enum1.ordinal());
            stringBuffer.append(",");
            stringBuffer.append("\"message\" : \"" + enum1.message + "\"");
            stringBuffer.append("}");
        }
        // 替换第2个字符即首个逗号","。
        stringBuffer.replace(1, 2, "");
        stringBuffer.append("}");
        return stringBuffer.toString();
    }

    /**
     * 无参调用。
     *
     * @return
     */
    public String getMessage() {
        return pattern.matcher(message).replaceAll("");
    }

    /**
     * 有参调用，map内的参数请参考Enum实例的注释。
     *
     * @return
     */
    public String getMessage(CharSequence onlyParam) {
        if (onlyParam != null) {
            Matcher matcher = pattern.matcher(this.message);
            StringBuffer sbuffer = new StringBuffer();
            while (matcher.find()) {
                //将当前匹配子串替换为指定字符串，并且将替换后的子串以及其之前到上次匹配子串之后的字符串段添加到一个StringBuffer对象里，而appendTail(StringBuffer sb) 方法则将最后一次匹配工作后剩余的字符串添加到一个StringBuffer对象里。
                matcher.appendReplacement(sbuffer, onlyParam.toString());  //使用循环将句子里所有的 onlyParam 找出并替换再将内容加到sb里
            }
            matcher.appendTail(sbuffer);  //最后一次匹配后的剩余字符串加到sbuffer里
            return sbuffer.toString();
        }
        return this.getMessage();
    }

    /**
     * 有参调用，map内的参数请参考Enum实例的注释。
     *
     * @param paramMap
     * @return
     */
    public String getMessage(Map<String, ? extends Object> paramMap) {
        if (paramMap != null && !paramMap.isEmpty()) {
            Matcher matcher = pattern.matcher(this.message);
            StringBuffer sbuffer = new StringBuffer();
            while (matcher.find()) {
                if (matcher.groupCount() > 0) {
                    String paramKey = matcher.group(1); // ${xx}  返回第一组匹配到的子字符串
                    Object value = paramMap.get(paramKey);
                    matcher.appendReplacement(sbuffer, value == null ? "" : value.toString());
                }
            }
            matcher.appendTail(sbuffer);
            return sbuffer.toString();
        }
        return this.getMessage();
    }

    /**
     * 根据name获取消息
     *
     * @param
     * @return
     */
 /*   public static String getMessageByName(String prefix, String stateNum) {
        String stateNumStr = prefix + stateNum;
        for (com.simbest.boot.iservice.util.MessageEnum MessageEnum : MessageEnum.values()) {
            if (MessageEnum.name().equals(stateNumStr)) {
                return MessageEnum.getMessage();
            }
        }
        return null;
    }*/

    @Override
    public String toString() {
        return this.message;
    }



}
