package com.simbest.boot.exam.briefDistribution.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.briefDistribution.model.ApplyForm;
import com.simbest.boot.exam.briefDistribution.model.UserInfo;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface UserInfoRepository extends LogicRepository<UserInfo, String> {

    List<UserInfo> findUserInfosByBusinessIdAndEnabledIsTrue(String businessId);
}
