package com.simbest.boot.exam.examOnline.util;

import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.message.service.IMessageService;
import com.simbest.boot.exam.sms.sevice.ILoginService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用途：问卷调查工具类
 * 时间：2021/2/4
 */
@Slf4j
@Component
public class ExamOnlineTool {

    @Autowired
    private MsgPostOperatorService msgPostOperatorService;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    @Autowired
    private ILoginService iLoginService;

    @Autowired
    @Lazy
    private IExamWorkService iExamWorkService;

    @Autowired
    private IMessageService iMessageService;

    /**
     * 发送催办短信
     *
     * @param id             代办主键
     * @param transactorCode 短信接收方
     * @param examCode        考试编号
     * @param title          短信标题
     * @return 结果
     */
    public boolean sendShortMessage(String examCode,String id, String transactorCode, String title) {
        log.debug("sendShortMessage[问卷考试]--------------------->>>>>>>>>>>>推送短信开始");
        boolean flag = false;
        try {
            // 设置变量查找短信开关，是否具有发送资格
            boolean isPostMsg = false;
            /*
             * 短信开关:
             *  false 短信不发送
             *  true 发送
             */
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, "hadmin");
            //打开或关闭短信开关
//            if (simpleApp != null) {
//                isPostMsg = simpleApp.getIsSendMsg();
//            }
            // 如果具有发送资格准备发送短信参数
            boolean isPostMsgOK = false;
            if (isPostMsg) {
                // 准备发送短息
                log.debug("isPostMsg[问卷考试]开关--------------------->>>>>>>>>>>>" + isPostMsg);
                Map<String, Object> paramMap = Maps.newHashMap();
                paramMap.put("sendUser", transactorCode);
                paramMap.put("fromUser", Constants.APP_NAME);
                paramMap.put("itemSubject", title);
                paramMap.put("examCode",examCode);
                // 调用发送短信方法
                isPostMsgOK = sendSMS(paramMap);
                flag = isPostMsgOK;
            }
            // 发送失败记录
            if (!isPostMsgOK) {
                //获取当前登录人
                IUser iUser = SecurityUtils.getCurrentUser();
                LocalDateTime time = LocalDateTime.now();
                SysOperateLog operateLog = new SysOperateLog();
                operateLog.setOperateFlag("MSG");
                operateLog.setBussinessKey(id);
                operateLog.setInterfaceParam("调查问卷->发送催办短息");
                operateLog.setOperateInterface("sendShortMessage[问卷调查]");
                operateLog.setCreator(iUser.getUsername());
                operateLog.setCreatedTime(time);
                operateLog.setModifier(iUser.getUsername());
                operateLog.setModifiedTime(time);
                operateLogService.saveLog(operateLog);
                log.debug("sendShortMessage MSG Fialure>>>>" + operateLog.toString());
            }
            log.debug("sendShortMessage[问卷调查]--------------------->>>>>>>>>>>>推送短信结束");
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return flag;
    }

    /**
     * 发送短信
     *
     * @param map
     * @return
     */
    public Boolean sendSMS(Map<String, Object> map) {
        // 是否发送成功标志
        Boolean isPostMsgOK = false;
        // 发送短信操作
        try {
            // 参数不为空情况下
            if (map != null && map.size() > 0) {
                String sendUser = map.get("sendUser") != null ? map.get("sendUser").toString() : "";
                // 发送人不为空
                if (StringUtils.isNotEmpty(sendUser)) {
                    // 准备审批短信模板数据
                    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("appName", Constants.APP_NAME);
                    paramMap.put("fromUser", map.get("fromUser") != null ? map.get("fromUser").toString() : "");
                    paramMap.put("itemSubject", map.get("itemSubject") != null ? map.get("itemSubject").toString() : "");
                    String examCode = (String) map.get("examCode");
                    String title = (String) map.get("itemSubject");
                    // 准备发送短信
                    // todo 根据EXAM_CODE发送不同的短信内容 llds特殊处理 注意
                    //String msg = "【" + ExamConstants.EXAM_NAME + "】" + "：您有一份待填写的问卷调查，请及时登录员工E或在电脑上登录个人账号进行填写。";
                    //String msg = "【 满意度评价 】" + "：参与方式：登录OA系统--我的待办--“洛阳满意度评价”。活动结束时间为2021年5月30日24:00，逾期系统将自动关闭，请您及时完成问卷调查，感谢您的大力支持和配合！";

                    //http://10.92.82.161:8088/exam/html/exammanage/examlyxfevaluatePhone.html
                    //http://10.92.82.161:8088/exam/html/exammanage/examlyjgevaluatePhone.html
                    //洛阳满意度问卷   根据发送人判断要发送的短信内容
                    String lyxfWorkType="E";//县分公司
                    String lyjgWorkType="F";//洛阳机关部门
                    List<ExamWork> lyxfExamWork = iExamWorkService.findByUsernameAndWorkType(sendUser, lyxfWorkType);
                    List<ExamWork> lyjgExamWork = iExamWorkService.findByUsernameAndWorkType(sendUser, lyjgWorkType);
                    //  长链接转短链接  并拼接短链接
//                    String msg = "【 问卷考试 】" + "您好，您有一个待处理的问卷考试请及时处理！ " +
//                            "可通过门户统一待办进行处理，如没有统一待办则查询应用综合考试管理系统进行待办处理，感谢您的参与";
                      //String msg = "【公司纪检干部作风建设情况调研问卷】您好！您有一个待处理的问卷，请及时处理！请通过河南移动廉政视窗首页飘窗进行处理，感谢您的参与！";
                    //String msg = "【公司“机关化”、“衙门化”问题整改成效情况调查问卷】您好！您有一个待处理的问卷，请及时处理！请通过河南移动廉政视窗首页飘窗或者OA统一待办或者综合考试系统待办进行处理，感谢您的参与！";
                    /**从短信库里查询要发送的短信*/
                    String msg = iMessageService.findMessageContentByExamCode(examCode);
                    if(StringUtils.isEmpty(msg)){
                      /*msg = "【 问卷考试 】" + "您好，您有一个待处理的问卷考试请及时处理！ " +
                          "可通过门户统一待办进行处理，如没有统一待办则查询应用综合考试管理系统进行待办处理，感谢您的参与";*/
                    }
                    if (lyxfExamWork!=null && lyxfExamWork.size()>0&&Constants.EXAM_ANNUAL_QUARTER_LX.equals(title)) {
                        String msg1 = "【 问卷考试 】" + "您好，您有一个待处理的问卷考试请及时处理！ " +
                                "点击下方链接或通过登录员工E问卷考试弹窗或通过登录门户统一待办或通过点击门户的洛阳满意度轮播图，进行测评，感谢您对我们工作的大力支持！如已处理忽略此条信息";
                        String shortUrl = iLoginService.ConvertShortUrl("/queryDetail");
                        msg=msg1+shortUrl;
                    }
                    if (lyjgExamWork!=null &&lyjgExamWork.size()>0&&Constants.EXAM_ANNUAL_QUARTER_LJ.equals(title)){
                        String msg1 = "【 问卷考试 】" + "您好，您有一个待处理的问卷考试请及时处理！ " +
                                "点击下方链接或通过登录员工E问卷考试弹窗或通过登录门户统一待办或通过点击门户的洛阳满意度轮播图，进行测评，感谢您对我们工作的大力支持！如已处理忽略此条信息";
                        String shortUrl = iLoginService.ConvertShortUrl("/jgDetail");
                        msg=msg1+shortUrl;
                    }
                    // 准备参数
                    Map<String, Object> mapParam = Maps.newHashMap();
                    mapParam.put("sendUser", sendUser);
                    mapParam.put("msg", msg);
                    log.debug("短信参数----------------------->>>>>>>>>>>>>>>>>>mapParam" + mapParam.toString());
                    isPostMsgOK = msgPostOperatorService.postMsg(readyParams(mapParam));
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return isPostMsgOK;
    }

    /**
     * 准备短信对象
     *
     * @param map 参数 （endUser待发人，短信模板）
     * @return
     */
    private ShrotMsg readyParams(Map<String, Object> map) {
        ShrotMsg shrotMsg = new ShrotMsg();//短信对象
        Content content = new Content();
        Set<Content> contentSet = new HashSet<Content>();
        shrotMsg.setAppCode(Constants.APP_CODE);
        content.setUsername(map.get("sendUser") != null ? map.get("sendUser").toString() : "");
        content.setMsgContent(map.get("msg") != null ? map.get("msg").toString() : "");
        content.setImmediately(true);
        content.setSmsPriority(1);
        contentSet.add(content);
        shrotMsg.setContents(contentSet);
        return shrotMsg;
    }

    /**
     * 发送短信问卷
     * 调用前需判断是否可以发送短信
     *
     * @param transactorCode 短信接收方
     * @param msg            短信内容
     * @return 结果
     */
    public boolean sendShortMessageQuestInfo(String transactorCode, String msg) {
        // 如果具有发送资格准备发送短信参数
        boolean isPostMsgOK = false;
        try {
            log.info("推送短信问卷信息-->>开始");
            // 准备参数
            Map<String, Object> mapParam = Maps.newHashMap();
            mapParam.put("sendUser", transactorCode);
            mapParam.put("msg", msg);
            log.info("发送短信参数：{}", mapParam);
            // 调用发送短信方法
            isPostMsgOK = msgPostOperatorService.postMsg(readyParams(mapParam));
            log.info("推送短信问卷信息-->>结束");
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return isPostMsgOK;
    }

}
