package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.dto.AnswerStatusDto;
import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;
import com.simbest.boot.exam.examOnline.service.IExamAnswerNotesService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.templates.MessageEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-04-25
 * @desc
 **/
@Api(description = "问卷调查答题记录与统计", tags = {"问卷调查答题记录与统计控制器"})
@Slf4j
@RestController
@RequestMapping(value = "/action/examAnswerNotes")
public class ExamAnswerNotesController extends LogicController<ExamQuestionUser, String> {
    private IExamAnswerNotesService iExamAnswerNotesService;

    @Autowired
    public ExamAnswerNotesController(IExamAnswerNotesService service) {
        super(service);
        this.iExamAnswerNotesService = service;
    }

    /**
     * 保存提交的答题记录
     *
     * @param
     * @return
     */
    @ApiOperation(value = "提交/保存问卷", notes = "保存/提交的答题记录")
    @PostMapping(value = {"/saveAllAnswer", "sso/saveAllAnswer", "api/saveAllAnswer"})
    public JsonResponse saveAllAnswer(@RequestParam(required = false) String currentUserCode, @RequestParam(required = false) String source, @RequestBody AnswerStatusDto o,@RequestParam(required = false) String annualQuarterCode) {
        try {
            if (o.getAnswerStatus() == Constants.answerStatus_1) {
                return JsonResponse.success(iExamAnswerNotesService.saveAnswerNotes(currentUserCode, source, o,annualQuarterCode), "提交成功");
            }
            return JsonResponse.success(iExamAnswerNotesService.saveAnswerNotes(currentUserCode, source, o,annualQuarterCode));
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail("提交失败，考试时间已过，下次请准时参与考试噢");
        }

    }

    /**
     * 保存提交的答题记录(短信小程序)
     *
     * @param
     * @return
     */
    @ApiOperation(value = "提交/保存问卷小程序端", notes = "保存/提交的答题记录小程序端")
    @PostMapping(value = {"/saveAllAnswerApp", "sso/saveAllAnswerApp", "api/saveAllAnswerApp"})
    public JsonResponse saveAllAnswerApp(@RequestParam(required = false) String currentUserCode, @RequestParam(required = false) String source, @RequestBody AnswerStatusDto o,@RequestParam(required = false) String annualQuarterCode) {
        try {
            if (o.getAnswerStatus() == Constants.answerStatus_1) {
                return JsonResponse.success(iExamAnswerNotesService.saveAnswerNotesMB(currentUserCode, source, o,annualQuarterCode), "提交成功");
            }
            return JsonResponse.success(iExamAnswerNotesService.saveAnswerNotesMB(currentUserCode, source, o,annualQuarterCode));
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail("提交失败");
        }

    }

    /**
     * 查询用户保存的答题记录
     *
     * @return
     */
    @ApiOperation(value = "查询保存答卷", notes = "查询保存的答题")
    @PostMapping(value = {"/findAllSaveAnswer", "sso/findAllSaveAnswer", "api/findAllSaveAnswer"})
    public JsonResponse findAllSaveAnswer(@RequestParam(required = false) String annualQuarterCode) {
        return JsonResponse.success(iExamAnswerNotesService.findAnswerNotesByCreator(annualQuarterCode));
    }

    /**
     * 统计机关各部门对财务部的满意度评价结果
     * 1-6为财务部的结果
     * 7-12为党群工会
     * 13-18为人力资源部
     * 19-24为市场经营部
     * 25-30为客户服务中心
     * 31-36为网络部
     * 37-42为政企客户部
     * 43-48为综合部
     *
     * @return
     */
    @ApiOperation(value = "统计机关各部门对机关各部门满意度评价", notes = "统计机关各部门对机关各部门满意度评价")
    @PostMapping(value = {"/statisticsDepartments", "sso/statisticsDepartments", "api/statisticsDepartments"})
    public JsonResponse statisticsDepartments(@RequestParam(required = false)String annualQuarterCode) {
        try {
            return JsonResponse.success(iExamAnswerNotesService.statisticsDepartments(annualQuarterCode), "操作成功");
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(null, e.getMessage());
        }

    }

    /**
     * 统计县分公司对各部门的满意度评价结果
     * 1-9题为财务部的结果
     * 10-18题为党群工会
     * 19-27题为人力资源部
     * 28-36题为市场经营部
     * 37-45题为客户服务中心
     * 46-54题为网络部
     * 55-63题为政企客户部
     * 64-72题为综合部
     *
     * @return
     */
    @ApiOperation(value = "县分公司对机关各部门的满意度评价", notes = "县分公司对机关各部门的满意度评价")
    @PostMapping(value = {"/countyBranchStatistics", "sso/countyBranchStatistics", "api/countyBranchStatistics"})
    public JsonResponse countyBranchStatistics(@RequestParam(required = false) String annualQuarterCode) {
        try {
            return JsonResponse.success(iExamAnswerNotesService.countyBranchStatistics(annualQuarterCode), "操作成功");
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(null, e.getMessage());
        }
    }

    @ApiOperation(value = "统计机关各部门对机关各部门评价的平均值与百分制值", notes = "统计机关各部门对财务部评价的平均值与百分制值")
    @PostMapping(value = {"/statisticsDepartmentsAvg", "sso/statisticsDepartmentsAvg", "api/statisticsDepartmentsAvg"})
    public JsonResponse statisticsDepartmentsAvg(@RequestParam(required = false)String annualQuarterCode) {
        try {
            return JsonResponse.success(iExamAnswerNotesService.statisticsDepartmentsAvg(annualQuarterCode), "统计成功");
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(null, e.getMessage());
        }
    }

    @ApiOperation(value = "统计县分公司对机关各部门评价的平均值与百分制值", notes = "统计县分公司对机关各部门评价的平均值与百分制值")
    @PostMapping(value = {"/countyBranchStatisticsDepartmentsAvg", "sso/countyBranchStatisticsDepartmentsAvg", "api/countyBranchStatisticsDepartmentsAvg"})
    public JsonResponse countyBranchStatisticsDepartmentsAvg(@RequestParam(required = false)String annualQuarterCode) {
        try {
            return JsonResponse.success(iExamAnswerNotesService.countyBranchStatisticsDepartmentsAvg(annualQuarterCode), "统计成功");
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(null, e.getMessage());
        }
    }

    @ApiOperation(value = "统计结果的汇总表表头", notes = "统计结果的汇总表表头")
    @PostMapping(value = {"/statisticalSummaryTitle", "sso/statisticalSummaryTitle", "api/statisticalSummaryTitle"})
    public JsonResponse statisticalSummaryTitle(@RequestParam(required = false)String annualQuarterCode) {
        try {
            return JsonResponse.success(iExamAnswerNotesService.statisticalSummaryTitle(annualQuarterCode), null);
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(null, e.getMessage());
        }
    }

    @ApiOperation(value = "统计结果的汇总", notes = "统计结果的汇总")
    @PostMapping(value = {"/statisticalSummary", "sso/statisticalSummary", "api/statisticalSummary"})
    public JsonResponse statisticalSummary(@RequestParam(required = false)String annualQuarterCode) {
        try {
            return JsonResponse.success(iExamAnswerNotesService.statisticalSummary(annualQuarterCode), "统计成功");
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(null, e.getMessage());
        }
    }



}
