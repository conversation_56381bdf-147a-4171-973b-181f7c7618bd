package com.simbest.boot.exam.study.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.exam.examOnline.service.IExamAttributeService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionService;
import com.simbest.boot.exam.study.model.DailyTrainingRecord;
import com.simbest.boot.exam.study.model.TrainingAnswerDetail;
import com.simbest.boot.exam.study.repository.DailyTrainingRecordRepository;
import com.simbest.boot.exam.study.service.IDailyTrainingRecordService;
import com.simbest.boot.exam.study.service.ITrainingAnswerDetailService;
import com.simbest.boot.exam.study.service.IWrongQuestionCollectionService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.repository.SysDictValueRepository;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.group.UumsSysUserGroupApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用途：DailyTrainingRecord领域对象名称服务层实现
 */
@Slf4j
@Service
public class DailyTrainingRecordServiceImpl extends LogicService<DailyTrainingRecord, String> implements IDailyTrainingRecordService {

    private DailyTrainingRecordRepository repository;

    @Autowired
    public DailyTrainingRecordServiceImpl(DailyTrainingRecordRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private IExamQuestionService examQuestionService;

    @Autowired
    private IExamAttributeService attributeService;

    @Autowired
    private IExamQuestionAnswerService questionAnswerService;

    @Autowired
    private ITrainingAnswerDetailService trainingAnswerDetailService;

    @Autowired
    private IWrongQuestionCollectionService wrongQuestionCollectionService;

    @Autowired
    private SysDictValueRepository dictValueRepository;

    private static final  Integer allQuestionNum = 30;
    private static final  Integer allSingleNum = 20;
    private static final  Integer allMoreNum = 5;
    private static final  Integer allJudgeNum = 5;



    /**
     * 根据题库编码生成随机题目用于日常训练（新接口）
     *
     * @param questionBankCode 题库编码
     * @return 题目编码列表
     */
    @Override
    public List<String> generateRandomQuestionsByBankCode(String questionBankCode) {
        List<String> resultQuestionCodes = new ArrayList<>();
        
        // 定义题库1和题库2的分组规则
        Map<String, Integer> bank1GroupRules = new LinkedHashMap<>();
        bank1GroupRules.put("一把手试题", 1);
        bank1GroupRules.put("监察法", 3);
        bank1GroupRules.put("执纪审查工作规范", 3);
        bank1GroupRules.put("员工违规违纪", 1);
        bank1GroupRules.put("审查调查安全", 2);
        bank1GroupRules.put("八项规定精神", 4);
        bank1GroupRules.put("政治监督", 1);
        bank1GroupRules.put("纪律处分条例", 5);
        bank1GroupRules.put("一星题库", 6);
        bank1GroupRules.put("二星题库", 3);
        bank1GroupRules.put("三星题库", 1);
        
        Map<String, Integer> bank2GroupRules = new LinkedHashMap<>();
        bank2GroupRules.put("一把手试题", 1);
        bank2GroupRules.put("员工违规违纪", 1);
        bank2GroupRules.put("八项规定精神", 5);
        bank2GroupRules.put("纪律处分条例", 5);
        bank2GroupRules.put("一星题库", 12);
        bank2GroupRules.put("二星题库", 5);
        bank2GroupRules.put("三星题库", 1);


        Integer singleNum = 0;
        Integer moreNum = 0;
        Integer judgeNum = 0;

        try {
            // 查询题库中的题目信息
            List<Map<String, Object>> questionCodeInfos = examQuestionService.findGroupByQuestionBankCode(questionBankCode);
            if (CollectionUtil.isNotEmpty(questionCodeInfos)) {
                // 将所有题目编号信息按分组分类
                Map<String, List<String>> questionCodeInfoMap = new HashMap<>();
                for (Map<String, Object> questionCodeInfo : questionCodeInfos) {
                    String groupName = MapUtil.getStr(questionCodeInfo, "QUESTION_GROUP_NAME");
                    String codeStr = MapUtil.getStr(questionCodeInfo, "CODE_STR");
                    
                    List<String> codeList = questionCodeInfoMap.computeIfAbsent(groupName, k -> new ArrayList<>());
                    codeList.add(codeStr);
                }
                
                // 根据题库编码选择对应的分组规则
                Map<String, Integer> groupRules = "2025-jjjs-zz".equals(questionBankCode) ? bank1GroupRules : bank2GroupRules;
                // 先按分组规则抽取题目，并确保每个分组的题目数量正确
                for (Map.Entry<String, Integer> groupRule : groupRules.entrySet()) {
                    String groupName = groupRule.getKey();
                    Integer requiredCount = groupRule.getValue();

                    List<String> groupQuestions = questionCodeInfoMap.get(groupName);
                    if (groupQuestions != null && !groupQuestions.isEmpty()) {
                        // 从该分组中随机抽取指定数量的题目，确保抽取的题目符合要求
                        Integer flag = 0;
                        while (flag < requiredCount && groupQuestions.size() > 0) {
                            int randomIndex = RandomUtil.randomInt(0, groupQuestions.size());
                            String selectedQuestion = groupQuestions.remove(randomIndex);
                            if (selectedQuestion.startsWith("single#") && singleNum < allSingleNum) {
                                resultQuestionCodes.add(selectedQuestion.substring(7));
                                singleNum++;
                                flag++;
                            } else if (selectedQuestion.startsWith("more#") && moreNum < allMoreNum) {
                                resultQuestionCodes.add(selectedQuestion.substring(5)); // 去掉前缀"more#"
                                moreNum++;
                                flag++;
                            } else if (selectedQuestion.startsWith("judge#") && judgeNum < allJudgeNum) {
                                resultQuestionCodes.add(selectedQuestion.substring(6)); // 去掉前缀"judge#"
                                judgeNum++;
                                flag++;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return resultQuestionCodes;
    }

    /**
     * 生成随机题目用于日常训练
     *
     * @param username         用户名
     * @param questionBankCode 题库编码
     * @return 包含训练记录和题目的结果
     */
    @Override
    public Map<String, Object> generateRandomQuestions(String username, String questionBankCode) {
        Map<String, Object> resultMap = Maps.newHashMap();
        List<ExamQuestion> questionList = null;
        try {
            // 1. 创建训练记录
            DailyTrainingRecord record = new DailyTrainingRecord();
            record.setUsername(username);
            record.setTrainingDate(LocalDateTime.now());
            record.setTotalQuestions(allQuestionNum); // 总题数30道
            record.setRemarks("随机出题训练");
            record.setCorrectQuestions(0);
            record.setIncorrectQuestions(0);
            record.setNowSeq(0);
            record.setIsFinish("N");
            record = this.insert(record);
            resultMap.put("record" , record);

            //查询所有数据的题目编号
            List<String> finallyCodeList = generateRandomQuestionsByBankCode(questionBankCode);
            //查询题目信息
            log.warn("本次出题题目为:{}", finallyCodeList);
            if (CollectionUtil.isNotEmpty(finallyCodeList)) {
                questionList = examQuestionService.findAllByQuestionCodes(finallyCodeList);
                //查询答案信息并进行数据封装
                Map<String, List<ExamQuestionAnswer>> questionAnswerMap =  questionAnswerService.findAllByQuestionCodes(finallyCodeList);
                //循环将问题信息进行数据封装
                DailyTrainingRecord dailyTrainingRecord = record;
                questionList.stream().forEach(question -> {
                    question.setAnswerRecordId(dailyTrainingRecord.getId());
                    question.setAnswerList(questionAnswerMap.get(question.getQuestionCode()));
                    //循环保存题目信息到记录表
                    TrainingAnswerDetail answerDetail = TrainingAnswerDetail.builder()
                            .username(username)
                            .recordId(dailyTrainingRecord.getId())
                            .questionId(question.getId())
                            .questionCode(question.getQuestionCode())
                            .displayOrder(question.getQuestionOrder())
                            .build();
                    trainingAnswerDetailService.insert(answerDetail);
                });
            }
        } catch(Exception e){
            Exceptions.printException(e);
        }
        log.error("输出的题目信息为{}条" ,questionList.size());
        resultMap.put("questions" , questionList);
        return resultMap;
    }


    /**
     * 随机抽数
     * @param codeLis
     * @return
     */
    private String  randomCode(List<String> codeLis ) {
        int i = RandomUtil.randomInt(0, codeLis.size() - 1);
        return codeLis.get(i);
    }

    /**
     * 查询当前用户每日答题情况
     *
     * @param source          来源
     * @param currentUsername 账号
     * @return
     */
    @Override
    public JsonResponse dailyQuestion( String source ,  String currentUsername) {
        List<ExamQuestion> questions = null;
        Map<String , Object> resultMap = null;
        //Integer allNumber = 5 ;
        //Integer nowNumber = 0;
        //Boolean hasNum = Boolean.TRUE;
        try {
            if (StrUtil.equals(source , Constants.SOURCE_M) && StrUtil.isNotEmpty(currentUsername)) {
                loginUtils.manualLogin(currentUsername , Constants.APP_CODE);
            }
            IUser iUser = SecurityUtils.getCurrentUser();

            //判断是否那个题库
            String questionCode = "2025-jjjs-fzz";
            SysDictValue fDictValue = dictValueRepository.findByDictTypeAndName("2025-jjjs-fzz", iUser.getUsername());
            if (null == fDictValue ) {
                SysDictValue zzDictValue = dictValueRepository.findByDictTypeAndName("2025-jjjs-zz", iUser.getUsername());
                if (null == zzDictValue) {
                    return JsonResponse.fail(-1  , "暂无答题权限，请联系管理员！");
                } else {
                    questionCode = "2025-jjjs-zz";
                }
            }

            // 获取今天的开始和结束时间
            LocalDate today = LocalDate.now();
            LocalDateTime startOfDay = LocalDateTime.of(today, LocalTime.MIN);
            LocalDateTime endOfDay = LocalDateTime.of(today, LocalTime.MAX);
            Specification<DailyTrainingRecord> build = Specifications.<DailyTrainingRecord>and()
                    .eq("username", iUser.getUsername())
                    .between("trainingDate", startOfDay, endOfDay)
                    .build();
            List<DailyTrainingRecord> records = this.findAllNoPage(build);
            //nowNumber = records.size();
            Optional<DailyTrainingRecord> first = records.stream().filter(record -> StrUtil.equals(record.getIsFinish(), "N")).findFirst();
            //继续答题
            if (first.isPresent()) {
                DailyTrainingRecord dailyTrainingRecord = first.get();
                Specification<TrainingAnswerDetail> detailSpecification = Specifications.<TrainingAnswerDetail>and()
                        .eq("enabled", Boolean.TRUE)
                        .eq("recordId", dailyTrainingRecord.getId())
                        .build();
                List<TrainingAnswerDetail> details = trainingAnswerDetailService.findAllNoPage(detailSpecification , Sort.by(Sort.Direction.ASC , "displayOrder"));
                List<String> questionCodes = details.stream().map(TrainingAnswerDetail::getQuestionCode).collect(Collectors.toList());
                questions = examQuestionService.findAllByQuestionCodes(questionCodes);
                //查询答案信息并进行数据封装
                Map<String, List<ExamQuestionAnswer>> questionAnswerMap =  questionAnswerService.findAllByQuestionCodes(questionCodes);
                //循环将问题信息进行数据封装
                questions.stream().forEach(question -> {
                    question.setAnswerRecordId(dailyTrainingRecord.getId());
                    question.setAnswerList(questionAnswerMap.get(question.getQuestionCode()));
                });
                resultMap = Dict.create().set("questions" , questions).set("record" , dailyTrainingRecord);
            } else {
                resultMap = generateRandomQuestions(iUser.getUsername(), questionCode);
            }
        } catch (Exception e ) {
            Exceptions.printException(e);
        }
        Map<String , Object> dict = Dict.create().set("hasNum" , Boolean.TRUE ).set("allNumber", "∞").set("nowNumber", "");

        if (MapUtil.isNotEmpty(resultMap)) {
            dict.putAll(resultMap);
        }
        return JsonResponse.success(dict);
    }

    @Override
    public JsonResponse submitAnswer(String source, String currentUsername, TrainingAnswerDetail detail) {
        JsonResponse jsonResponse = null;
        try {
            if (StrUtil.equals(source , Constants.SOURCE_M) && StrUtil.isNotEmpty(currentUsername)) {
                loginUtils.manualLogin(currentUsername , Constants.APP_CODE);
            }
            DailyTrainingRecord record = this.findById(detail.getRecordId());
            Assert.notNull(record);
            //第几题
            record.setNowSeq(detail.getSeq());
            //最后一题 , 更新答题记录
            if ( null!= detail &&  detail.getSeq() > 28 ) {
                record.setIsFinish("Y");
            }
            //保存数据信息
            Specification<TrainingAnswerDetail> build = Specifications.<TrainingAnswerDetail>and().eq("enabled", Boolean.TRUE)
                    .eq("recordId", detail.getRecordId())
                    .eq("questionId", detail.getQuestionId())
                    .build();
            TrainingAnswerDetail one = trainingAnswerDetailService.findOne(build);
            if (one != null) {
                one.setUserAnswer(detail.getUserAnswer());
                ExamQuestion examQuestion = examQuestionService.findById(one.getQuestionId());
                if (null != examQuestion) {
                    List<ExamQuestionAnswer> allByQuestionCode = questionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
                    if (CollectionUtil.isNotEmpty(allByQuestionCode)) {
                        examQuestion.setAnswerList(allByQuestionCode);
                        String questionAnswer = allByQuestionCode.stream().filter(answer -> null != answer.getIsCorrect() && answer.getIsCorrect()).map(ExamQuestionAnswer::getAnswerCode).collect(Collectors.joining(","));
                        one.setQuestionAnswer(questionAnswer);
                        one.setIsCorrect(StrUtil.equals(questionAnswer, detail.getUserAnswer()));
                        trainingAnswerDetailService.update(one);
                        if (one.getIsCorrect()) {
                            record.setCorrectQuestions(record.getCorrectQuestions() + 1);
                        } else {
                            record.setIncorrectQuestions(record.getIncorrectQuestions() + 1);
                        }
                        //异步保存错题集
                        if (!one.getIsCorrect()) {
                            wrongQuestionCollectionService.saveWrongQuestionAsync(examQuestion, one);
                        }

                    }
                } else {
                    return JsonResponse.fail("未找到该题目");
                }

            }
            record = this.update(record);
            jsonResponse = JsonResponse.success(record);
        } catch (Exception e) {
            Exceptions.printException(e);
            jsonResponse = JsonResponse.fail(-1 , "提交失败！");
        }
        return jsonResponse;
    }

}