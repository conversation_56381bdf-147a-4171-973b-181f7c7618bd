<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>消息明细</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <link href="../../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../../css/public.css?v=svn.revision" th:href="@{/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .haveReadNum,.snum,.receiveNum{color:#22b0ef;}
    </style>
    <script type="text/javascript">
        window.fvalidate=function(){
            return formValidate("infoTableAddForm");
        };
        window.initsystem=function(){
            loadForm("infoTableAddForm");
        };
        window.bindval=function(data){
            formval(data,"infoTableAddForm");
            $(".snum").html(data.receiveNum-data.haveReadNum);
            // pageparam.listtable.data = {data:data.msgUsers};
            // loadGrid(pageparam);
        };
        window.formReady=function(data){
            formReadonly("infoTableAddForm");
        };
        $(function () {
            var gps=getQueryString();
            ajaxgeneral({
                url: 'action/applyForm/findMsgByIdNew?id=' + gps.id,
                success: function (res) {
                    $('.sendTime').text(res.data.sendTime)
                    $('.receiveNum').text(res.data.sendTotal)
                    $('.haveReadNum').text(res.data.viewedTotal)
                    $('.snum').text(res.data.todoTotal)
                }
            });
            pageparam={
                "listtable":{
                    "listname":"#groupUserTable",//table列表的id名称，需加#
                    "querycmd":"action/applyForm/findUsersInMsg?id="+gps.id,//table列表的查询命令
                    // "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "data":[],
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass":"noScroll",
                    "fitColumns":true,
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        // { title: "账号", field: "username", width: 15, align:'center'},
                        { title: "接收人", field: "taskTrueName", width: 15, align:'center'},
                        { title: "接收人OA", field: "taskUsername", width: 15, align:'center'},
                        // { title: "接收手机号", field: "preferredMobile", width: 20, align:'center',
                        //     formatter:function (value,row,index) {
                        //         if(row.receivePhoneNum){
                        //             return row.receivePhoneNum;
                        //         }else{
                        //             return row.preferredMobile;
                        //         }
                        //     }
                        // },
                        // { title: "所属分组", field: "parentName", width: 15, align:'center'},
                        { title: "所属组织", field: "displayName", width: 25, align:'center',tooltip: true},
                        { title: "是否已读", field: "status", width: 15, align:'center',
                            formatter:function (value,row,index) {
                                if(row.status == '12' ){
                                    return '已读';
                                }else{
                                    return '未读';
                                }
                            }
                        },
                        // { title: "是否转发", field: "relayFlag",width:80,align: 'center',fixed:true,
                        //     formatter:function (value,row,index) {
                        //         if(row.relayFlag){
                        //             return '是';
                        //         }else{
                        //             return '否';
                        //         }
                        //     }
                        // },
                        // { title: "转发时间", field: "receivedTime",width:140,align: 'center',tooltip: true,fixed:true,
                        //     formatter:function (value,row,index) {
                        //         if(!value){
                        //             return '--';
                        //         }else{
                        //             return "<span class='titleTooltipA'>"+timestampToTime(value)+"</span>";
                        //         }
                        //     }
                        // },
                        { title: "阅读时间", field: "modifiedTime", width: 22, align:'center',
                            // formatter:function (value,row,index) {
                            //     if(row.readTime){
                            //         return row.readTime;
                            //     }else{
                            //         return '--';
                            //     }
                            // }
                        }
                    ]]
                },
                // "dialogform": {
                //     // "dialogid": "#buttons",//对话框的id
                //     "formname": "#otherAddForm",//新增或修改对话框的formid需加#
                // },
            };
            loadGrid(pageparam);
        });
    </script>
</head>
<body class="body_page">
<form id="infoTableAddForm" bindval="bindval" initsystem="initsystem"
      contentType="application/json; charset=utf-8" cmd-select="network/message/findMsgByIdNew">
    <h4 style="text-align: center;"><span id="title"></span><span>消息明细</span></h4>
    <div>
        <span>发送时间： <span class="sendTime"></span></span>
        <span style="display: inline-block;width: 50px;"></span>
        <span>阅读情况： 发送 <span class="receiveNum"></span> 人， <span class="haveReadNum"></span> 人已读， <span class="snum"></span> 人未读</div></span>
    <div class="groupUserTable" style="margin-top: 20px;"><table id="groupUserTable"></table></div>
</form>
</body>
</html>