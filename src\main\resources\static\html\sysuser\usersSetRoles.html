﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>多用户赋角色</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"  th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"  th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"  th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .role{
            width: 275px!important;
            height: 345px!important;}
    </style>
    <script type="text/javascript">
        window.choosePersons = function ( data ) {
            var name = [];
            $ (".user_choose").empty();
            for ( var i in data.data ) {
                if ( $ ( ".user_choose a[id=" + data.data[i].id + "]" ).length == 0 ) $ ( ".user_choose" ).append ( "<a id='" + data.data[i].id + "'><font>" + data.data[i].name + "</font><i class='fr iconfont'>&#xe6ef;</i></a>" );
            }
        };
        $ ( function () {
            //所有业务角色
            ajaxgeneral ( {
                url: "action/sys/role/findAllApplicationRolesNoPage",
                contentType: "application/json; charset=utf-8",
                success: function ( data ) {
                    for ( var i in data.data ) {
                        $ ( "<a id='" + data.data[i].id + "' text='" + data.data[i].roleName + "'>" + data.data[i].roleName + "</a>" ).appendTo ( ".role_all" );
                    }
                }
            } );
            //选择用户
            $ ( ".choosePersons" ).on ( "click", function () {
                var gps = getQueryString ();
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                var href = {"multi": "1", "name": "choosePersonsVal"};
                if ( $ ( ".user_choose a" ).length > 0 ) {
                    var datas = [];
                    $ ( ".user_choose a" ).each ( function ( i, v ) {
                        var datai = {};
                        datai.id = $ ( v ).attr ( "id" );
                        datai.name = $ ( v ).find ( "font" ).text ();
                        datas.push ( datai );
                    } );
                    top.chooseWeb.choosePersonsVal = {"data": datas};
                } else {//表示新增
                    top.chooseWeb.choosePersonsVal = {"data": []};
                }
                var url = tourl ( 'html/sysuser/chooseUsers.html', href );
                // top.dialogP ( url, gps.form ? "usersSetRoles" : 'rightiframe', '选择用户', 'choosePersons', false, '800' );
                top.dialogP ( url,"usersSetRoles", '选择用户', 'choosePersons', false, '800' );
            } );
            //左移右  右移左
            $ ( document ).on ( "click", ".role_all a,.role_choose a", function () {
                $ ( ".role_choose a,.role_all a" ).removeClass ( "a_hover" );
                var divC = $ ( this ).parent ().hasClass ( "role_all" ) ? "role_choose" : "role_all";
                if ( $ ( "." + divC + " a[id=" + $ ( this ).attr ( "id" ) + "]" ).length == 0 ) {
                    $ ( this ).addClass ( "a_hover" ).appendTo ( "." + divC );
                } else {
                    $ ( this ).remove ();
                    $ ( "." + divC + " a[id=" + $ ( this ).attr ( "id" ) + "]" ).addClass ( "a_hover" );
                }
            } );
            //删除用户
            $ ( document ).on ( "click", ".user_choose a i", function () {
                $ ( this ).parent ().remove ();
            } );
            //保存
            $ ( document ).on ( "click", ".user_save", function () {
                if ( btn_sure == 1 ) {
                    var usernames = [];
                    $ ( ".user_choose a" ).each ( function ( i, v ) {
                        usernames.push ( $ ( v ).attr ( "id" ) );
                    } );
                    if ( usernames.length > 0 ) {
                        var roleids = [];
                        $ ( ".role_choose a" ).each ( function ( i, v ) {
                            roleids.push ( $ ( v ).attr ( "id" ) );
                        } );
                        if ( roleids.length > 0 ) {
                            //用户当前角色
                            btn_sure = 2;
                            ajaxgeneral ( {
                                url: "action/user/role/updateApplicationRoleListByUsernames",
                                data: {
                                    "roleIds": roleids.join ( "," ),
                                    "usernames": usernames.join ( "," ),
                                    "operationType": 1
                                },
                                //contentType:"application/json; charset=utf-8",
                                success: function ( data ) {
                                    btn_sure = 1;
                                    top.mesShow ( "温馨提示", "操作成功", 2500 );
                                }, sError: function ( data ) {
                                    btn_sure = 1;
                                }, error: function ( data ) {
                                    btn_sure = 1;
                                }
                            } );
                        } else {
                            btn_sure = 1;
                            top.mesAlert ( "温馨提示", "所选角色列表无数据!", 'warning' );
                        }
                    } else {
                        btn_sure = 1;
                        top.mesAlert ( "温馨提示", "用户列表无数据,请选择用户!", 'warning' );
                    }
                }
            } );
        } );
    </script>
</head>
<body class="body_page_uums">
<h6 class="pageTit"><font class="col_b fwb">多用户赋角色</font><i class="iconfont">&#xe66e;</i><small>为多个用户同时赋角色</small></h6>
<table border="0" cellpadding="0" cellspacing="8">
    <tr>
        <td class="lh32"><b>所选用户:</b><a class="fr btn a_primary choosePersons"><font>选择用户</font></a></td>
        <td colspan="2"><b>所有角色:</b></td>
        <td colspan="2"><b>所选角色:</b></td>
    </tr>
    <tr>
        <td>
            <div class="role user_choose"></div>
        </td>
        <td width="30" align="center"><font class="col_h"><------></font></td>
        <td>
            <div class="role role_all"></div>
        </td>
        <td width="30" align="center"><font class="col_h">>></font></br></br></br><font class="col_h"><<</font></td>
        <td>
            <div class="role role_choose"></div>
        </td>
    </tr>
    <tr>
        <td colspan="5"><a class="btn a_success fr user_save">保存</a></td>
    </tr>
</table>
</body>
</html>
