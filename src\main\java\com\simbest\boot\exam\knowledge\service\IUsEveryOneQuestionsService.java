/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
public interface IUsEveryOneQuestionsService {
    /**
     * 获取答题题目列表接口
     * @param source
     * @param username
     * @return
     */
    JsonResponse getAnswersList(String source, String username,String workType,String pmInsId,String invitaitonId);


    /**
     * 获取答题题目列表接口--查询没有答完的题目信息
     * @param source
     * @param username
     * @return
     */
    JsonResponse findSurplusExam(String source, String username,String pmInsId);

    /**
     * 开始答题接口
     * @param workType
     * @param source
     * @param currentUserCode
     * @return
     */
    JsonResponse saveRecord(String workType, String source, String currentUserCode,String pmInsId);

    /**
     * 提交答案
     * @param requestParam
     * @param source
     * @param currentUserCode
     * @return
     */
    JsonResponse saveAnswer(Map<String, Object> requestParam, String source, String currentUserCode);

    /**
     * 提交答案--最后一题的处理
     * @param requestParam
     * @param source
     * @param currentUserCode
     * @return
     */
    JsonResponse saveLastAnswer(Map<String, Object> requestParam, String source, String currentUserCode);

    /**
     * 完成答题后推送短信信息给双方
     * @param source
     * @param currentUserCode
     * @return
     */
    JsonResponse sendShortMessage(String source, String currentUserCode, List<Map<String, Object>> list, String reslut);
}
