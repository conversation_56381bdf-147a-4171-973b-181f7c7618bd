/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.model;/**
 * Created by KZH on 2019/10/8 15:55.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:55
 * @desc 考试业务单据
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_pending_task")
@ApiModel(value = "待办表")
public class UsPendingTask extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "W") //主键前缀，此为可选项注解
    private String id;

   @Column(length = 500)
    @ApiModelProperty(value = "邀请ID", name = "invitaitonId", example = "999999")
    private String invitationId;

   @Column(length = 500)
    @ApiModelProperty(value = "用户答题记录表主键id", required = true)
    private String answerRecordId;

   @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户OA账户", name = "sendUserName", example = "超级管理员")
    private String sendUserName;

   @Column(length = 500)
    @ApiModelProperty(value = "发送邀请的用户姓名", name = "sendTrueName", example = "超级管理员")
    private String sendTrueName;

   @Column(length = 500)
    @ApiModelProperty(value = "主单据Id", name = "pmInsId", example = "2019678301426913763328")
    private String pmInsId;

   @Column(length = 500)
    @ApiModelProperty(value = "待办人OA账户", name = "pendUserName", example = "hadmin")
    private String pendUserName;

   @Column(length = 500)
    @ApiModelProperty(value = "待办人姓名", name = "pendTrueName", example = "管理员")
    private String pendTrueName;




    @Column(length = 40)
    @ApiModelProperty(value = "题库编码",name = "questionBankCode",example = "A-001",required = true)
    private String questionBankCode;


    @Column(length = 2000)
    @ApiModelProperty(value = "问题ID", name = "questionId", example = "问题ID")
    private String questionId;

    @Column(length = 2000)
    @ApiModelProperty(value = "问题Code", name = "questionCode", example = "问题Code")
    private String questionCode;


    @Column(length = 200)
    @ApiModelProperty(value = "待办标题", name = "title", example = "待办标题")
    private String title;


    @Column(length = 200)
    @ApiModelProperty(value = "任务创建的时间", name = "createdAt")
    private String createdAt;

   @Column(length = 500)
    @ApiModelProperty(value = "答题用时", name = "answerTime")
    private String answerTime;



    @Column(length = 200)
    @ApiModelProperty(value = "任务的截止时间", name = "dueDate")
    private String dueDate;

    @Column(length = 200)
    @ApiModelProperty(value = "任务状态", name = "status", example = "InProgress")
    private String status;//Pending':任务待处理。 ACCEPTED':任务正在进行中   Completed':任务已完成。  Expired':任务已过期

    @Column(length = 200)
    @ApiModelProperty(value = "任务最后更新的时间戳", name = "lastUpdated", example = "任务最后更新的时间戳")
    private String lastUpdated;//核销待办/用户办理完成时间


    @Column(length = 200)
    @ApiModelProperty(value = "任务类型", name = "workTupe", example = "任务类型")
    private String workTupe;//C：人人对战

    @Column(length = 200)
    @ApiModelProperty(value = "待办类型", name = "taskTupe", example = "任务类型")
    private String taskTupe;//待办类型 ：1:邀请 2：待办

    @Column(length = 200)
    @ApiModelProperty(value = "邀请类型", name = "inviType", example = "邀请类型")
    private String inviType;//邀请类型 ：1:邀请 2：接收
}
