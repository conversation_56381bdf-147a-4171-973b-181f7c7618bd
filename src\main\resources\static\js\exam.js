// //禁用鼠标右边
// document.oncontextmenu = function(){
//     alert("页面右键已被禁用");
//     return false;
// };
// //禁用ctrl+v功能
// document.onkeydown = function(){
//     if (event.ctrlKey && window.event.keyCode==86){
//         alert("ctrl+v键已被禁用");
//         return false;
//     }
// };
// 单选
function addDRadio(questionSize){
    var newTable=document.getElementById("newTable");
    $('#newTable').empty();
    var add=[];
    //newTable.innerHTML="";
    for(var i=1;i<=questionSize;i++){
        add.push("<tr>"+
            "<td width='150px' align='left'>第"+i+"<font >题-单选题</font></td>"+
            "<td width='500px' >"+
            "题目名称："+
            "<input  id='submenuName"+ i +"'  type='text' value='' class='easyui-validatebox'"+
            "style='border-left: none;border-top: none;border-right: none;width:500px;height:32px' />"+
            "</td>"+
            // "<td>"+
            // "<input placeholder='请填写分值' type='text' value='' class='easyui-validatebox'"+
            // "style='border-left: none;border-top: none;border-right: none;width:100px;height:32px' />"+
            // "</td>"+
            "</tr>"
        );
        for(var j=1;j<=12;j++){
            var k;
            switch (j) {
                case  1: k="A";
                    break;
                case  2: k="B";
                    break;
                case  3: k="C";
                    break;
                case  4: k="D";
                    break;
                case  5: k="E";
                    break;
                case  6: k="F";
                    break;
                case  7: k="G";
                    break;
                case  8: k="H";
                    break;
                case  9: k="I";
                    break;
                case  10: k="J";
                    break;
                case  11: k="K";
                    break;
                case  12: k="L";
                    break;
            }
            add.push("<tr>" +
                "<td width='150px' align='left'>"+k+"、</td>"+
                "<td width='500px' >"+
                "<input  id='answer"+ i+j +"'  type='text' value='' class='easyui-validatebox'"+
                "style='width:500px;height:32px' />"+
                "</td>"+
                "<td>" +
                "<label for='judge1"+ i +"'>正确：</label><input class='wauto' id='correct"+ i +"' name='correct"+ i +"' value='"+k+"' type='radio'/>" +
                "</td>"+
                "</tr>");
        }
    }
    newTable.innerHTML+=add.join("");
}

/*多选渲染方法*/
function addTrRadio(questionSize){
    var newTable=document.getElementById("newTable");
    $('#newTable').empty();
    var add=[];
    //newTable.innerHTML="";
    for(var i=1;i<=questionSize;i++){
        add.push("<tr>"+
            "<td width='150px' align='left'>第"+i+"<font >题-多选题、</font></td>"+
            "<td width='500px' >"+
            "<input  id='submenuName"+ i +"'  type='text' value='' class='easyui-validatebox'"+
            "style='border-left: none;border-top: none;border-right: none;width:500px;height:32px' />"+
            "</td>"+
            // "<td>"+
            // "<input placeholder='请填写分值' type='text' value='' class='easyui-validatebox'"+
            // "style='border-left: none;border-top: none;border-right: none;width:100px;height:32px' />"+
            // "</td>"+
            "</tr>"
        );
        for(var j=1;j<=12;j++){
            var k;
            switch (j) {
                case  1: k="A";
                    break;
                case  2: k="B";
                    break;
                case  3: k="C";
                    break;
                case  4: k="D";
                    break;
                case  5: k="E";
                    break;
                case  6: k="F";
                    break;
                case  7: k="G";
                    break;
                case  8: k="H";
                    break;
                case  9: k="I";
                    break;
                case  10: k="J";
                    break;
                case  11: k="K";
                    break;
                case  12: k="L";
                    break;
            }
            add.push("<tr>" +
                "<td width='150px' align='left'>"+k+"、</td>"+
                "<td width='500px' >"+
                "<input  id='answer"+ i+j +"'  type='text' value='' class='easyui-validatebox'"+
                "style='width:500px;height:32px' />"+
                "</td>"+
                "<td>" +
                "<input class='wauto' id='correct"+ i +"' value='"+k+"' type='checkbox'/>" +
                "</td>"+
                "</tr>");
        }
    }
    newTable.innerHTML+=add.join("");
}

/*判断题渲染方法*/
function addTrJudge(questionSize){
    var newTable=document.getElementById("newTable");
    $('#newTable').empty();
    var add=[];
    //newTable.innerHTML="";
    for(var i=1;i<=questionSize;i++){
        add.push("<tr>"+
            "<td width='150px' align='left'>第"+i+"<font >题-判断题、</font></td>"+
            "<td width='500px' >"+
            "<input  id='judgeName"+ i +"'  type='text' value='' class='easyui-validatebox'"+
            "style='border-left: none;border-top: none;border-right: none;width:500px;height:32px' />"+
            "<label for='judge1"+ i +"'>正确：</label><input class='wauto' id='judge1"+ i +"' name='judge"+ i +"' value='true' type='radio'/>" +
            "<label for='judge2"+ i +"'>&nbsp&nbsp&nbsp&nbsp错误：</label><input class='wauto' id='judge2"+ i +"' name='judge"+ i +"' value='false' type='radio'/>" +
            "</td>"+
            // "<td>"+
            // "<input placeholder='请填写分值' type='text' value='' class='easyui-validatebox'"+
            // "style='border-left: none;border-top: none;border-right: none;width:100px;height:32px' />"+
            // "</td>"+
            "</tr>"
        );


    }
    newTable.innerHTML+=add.join("");
}

//填空题渲染方法
function addTrGap(questionSize){
    var newTable=document.getElementById("newTable");
    $('#newTable').empty();
    var add=[];
    //newTable.innerHTML="";
    for(var i=1;i<=questionSize;i++){
        add.push("<tr>"+
            "<td width='150px' align='left'>第"+i+"<font >题-填空题、</font></td>"+
            // "<td width='500px' >"+
            // "<input  id='shortAnswerName"+ i +"'  type='text' value='' class='easyui-validatebox'"+
            // "style='border-left: none;border-top: none;border-right: none;width:500px;height:32px' />"+
            // "</td>"+
            // // "<td>"+
            // // "<input placeholder='请填写分值' type='text' value='' class='easyui-validatebox'"+
            // // "style='border-left: none;border-top: none;border-right: none;width:100px;height:32px' />"+
            // // "</td>"+
            "</tr>"
        );
        add.push("<tr>" +
            "<td width='150px' align='left'></td>"+
            "<td width='500px' >"+
            "<textarea id='fillAnswerName"+ i +"'  class='easyui-validatebox'  style='width:550px;height:200px;resize:both;' ></textarea>"+
            "</td>"+
            "</tr>");

    }
    newTable.innerHTML+=add.join("");
}

/*简答题渲染方法*/
function addTrAnswer(questionSize){
    var newTable=document.getElementById("newTable");
    $('#newTable').empty();
    var add=[];
    //newTable.innerHTML="";
    for(var i=1;i<=questionSize;i++){
        add.push("<tr>"+
            "<td width='150px' align='left'>第"+i+"<font >题-简答题、</font></td>"+
            "<td width='500px' >"+
            "<input  id='shortAnswerName"+ i +"'  type='text' value='' class='easyui-validatebox'"+
            "style='border-left: none;border-top: none;border-right: none;width:500px;height:32px' />"+
            "</td>"+
            // "<td>"+
            // "<input placeholder='请填写分值' type='text' value='' class='easyui-validatebox'"+
            // "style='border-left: none;border-top: none;border-right: none;width:100px;height:32px' />"+
            // "</td>"+
            "</tr>"
        );
        add.push("<tr>" +
            "<td width='150px' align='left'></td>"+
            "<td width='500px' >"+
            "<textarea id='shortAnswer"+ i +"'  class='easyui-validatebox'  style='width:500px;height:250px;resize:both;' ></textarea>"+
            "</td>"+
            "</tr>");

    }
    newTable.innerHTML+=add.join("");

}

