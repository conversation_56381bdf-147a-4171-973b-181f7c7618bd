package com.simbest.boot.exam.study.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.study.model.ModifyReport;

import java.util.Map;

/**
 * @ClassName: IModifyReportService
 * @description:
 * @author: ZHAOBO
 * @create: 2025-08-14 11:22
 */
public interface IModifyReportService extends ILogicService<ModifyReport, String> {

    /**
     * 新增修正记录
     * @param map       修正数据信息
     * @return
     */
    JsonResponse saveModify(Map<String, Object> map);

    /**
     * 查询所有提交的问题信息
     * @param page      分页
     * @param size      数量
     * @param report    查询条件
     * @return
     */
    JsonResponse queryModifyReport(Integer page, Integer size, ModifyReport report);

    /**
     * 更新错题信息
     * @param id        主键id
     * @param status     类型
     * @return
     */
    JsonResponse updateStatus(String id, String status);

    /**
     * 查询题目信息
     * @param id        主键id
     * @return
     */
    JsonResponse findQuestionById(String id);

    /**
     * 修改题目信息
     * @param map       修改数据信息
     * @return
     */
    JsonResponse updateQuestionInfo(Map<String, Object> map);
}
