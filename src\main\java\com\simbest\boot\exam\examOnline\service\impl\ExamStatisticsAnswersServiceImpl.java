package com.simbest.boot.exam.examOnline.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfo;
import com.simbest.boot.exam.examOnline.repository.SummaryStatisticsRepository;
import com.simbest.boot.exam.examOnline.service.IExamRangeUserInfoService;
import com.simbest.boot.exam.examOnline.service.IExamStatisticsAnswersService;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @title: ExamStatisticsAnswersServiceImpl
 * @projectName exam
 * @description: 实时统计最近一次问卷参与考考试的人员情况
 * @date 2021/6/23  16:54
 */
@Service
@Slf4j
public class ExamStatisticsAnswersServiceImpl implements IExamStatisticsAnswersService {

    @Autowired
    private IExamWorkService iExamWorkService;

    @Autowired
    private SummaryStatisticsRepository summaryStatisticsRepository;

    @Autowired
    private IExamRangeUserInfoService iExamRangeUserInfoService;
    /**
     *功能描述 统计洛阳县分公司人员答题情况表
     * <AUTHOR>
     * @date 2021/6/23
     */
    @Override
    public List<Map<String, Object>> statisticsLYXF() {
        HashSet<String> set = new HashSet<>();
        List<Map<String, Object>> arrayList = new ArrayList<>();
        //获取分组中洛阳县分公司的人员信息
        List<ExamRangeUserInfo> byGroupIdLY = iExamRangeUserInfoService.findByGroupIdLY(Constants.LYXFCODE);
        for (ExamRangeUserInfo examRangeUserInfo : byGroupIdLY) {
            //获取组织名称
            String userOrgName = examRangeUserInfo.getUserOrgName();
            //截取组织名称
            String subUserOrgName = userOrgName.substring(0, 11);
            set.add(subUserOrgName);
        }

        //遍历存储县分公司部门的组织名称
        for (String OrgName : set) {
            Specification<ExamRangeUserInfo> condition = Specifications.<ExamRangeUserInfo>and()
                    .like( "userOrgName","%"+OrgName+"%")
                    .build();
            //查询属于这个组织名称的人员
            Iterable<ExamRangeUserInfo> allNoPage = iExamRangeUserInfoService.findAllNoPage(condition);
            //遍历这些人员
            int i=0;
            int j=0;
            HashMap<String, Object> HashMap = new HashMap<>();
            for (ExamRangeUserInfo userInfo : allNoPage) {

                //查询待办是否已处理去判断是否答题
                ExamWork examWork=iExamWorkService.findByTransactorCodeAndWorkType(userInfo.getUserName(), "E");
                if (examWork!=null){

                    String modifier = examWork.getModifier();
                    if (!modifier.equals("hadmin") && !modifier.equals("liangjie")){
                        i++;
                    }
                    if (modifier.equals("hadmin") || modifier.equals("liangjie")){
                        j++;
                    }
                }
                HashMap.put("orgName",OrgName);
                HashMap.put("answeredQuestions",i);
                HashMap.put("unansweredQuestions",j);
                double percen= 100*i/(i+j);
                String format = String.format("%.2f", percen);
//                int  round = Math.round(percen);
                HashMap.put("percentage",format+"%");
            }
            arrayList.add(HashMap);
        }


        return arrayList;
    }

    /**
     *功能描述 实时统计洛阳机关部门人员答题情况表
     * <AUTHOR>
     * @date 2021/6/23
     */
    @Override
    public List<Map<String, Object>> statisticsLYJG() {
        HashSet<String> set = new HashSet<>();
        List<Map<String, Object>> arrayList = new ArrayList<>();

        //获取分组中洛阳机关人员的组织名称信息
        List<ExamRangeUserInfo> byGroupIdLY = iExamRangeUserInfoService.findByGroupIdLY(Constants.LYJGCODE);
        for (ExamRangeUserInfo examRangeUserInfo : byGroupIdLY) {
            //获取组织名称
            String userOrgName = examRangeUserInfo.getUserOrgName();
            if (userOrgName.contains("政企客户部")){
                set.add("洛阳分公司\\政企客户部");
            }
            if (userOrgName.contains("财务部")){
                set.add("洛阳分公司\\财务部");
            }
            if (userOrgName.contains("党群工作部")){
                set.add("洛阳分公司\\党委办公室");
            }
            if (userOrgName.contains("人力资源部")){
                set.add("洛阳分公司\\人力资源部");
            }
            if (userOrgName.contains("市场经营部")){
                set.add("洛阳分公司\\市场经营部");
            }
//            if (userOrgName.contains("客户服务中心")){
//                set.add("洛阳分公司\\市场经营部\\客户服务中心");
//            }
            if (userOrgName.contains("网络部")){
                set.add("洛阳分公司\\网络部");
            }
            if (userOrgName.contains("综合部")){
                set.add("洛阳分公司\\综合部");
            }
            if (userOrgName.contains("工会")){
                set.add("洛阳分公司\\工会");
            }
            if (userOrgName.contains("客户服务部")){
                set.add("洛阳分公司\\客户服务部");
            }


        }

        //遍历存储的洛阳分公司部门的组织名称
        for (String OrgName : set) {
            Specification<ExamRangeUserInfo> condition = Specifications.<ExamRangeUserInfo>and()
                    .like( "userOrgName","%"+OrgName+"%")
                    .build();
            //查询属于这个组织名称的人员
            Iterable<ExamRangeUserInfo> allNoPage = iExamRangeUserInfoService.findAllNoPage(condition);
            //遍历这些人员
            int i=0;
            int j=0;
            HashMap<String, Object> HashMap = new HashMap<>();
            for (ExamRangeUserInfo userInfo : allNoPage) {

                //查询待办是否已处理去判断是否答题
                ExamWork examWork=iExamWorkService.findByTransactorCodeAndWorkType(userInfo.getUserName(), "F");
                if (examWork!=null){
                    String pmInsId = examWork.getPmInsId();
                    List<Map<String, Object>> list = summaryStatisticsRepository.findByPmInsId(pmInsId);
                    if (CollectionUtil.isNotEmpty(list)){
                        Map<String, Object> examRangeUserInfoMap = list.get(0);
                        String sign = MapUtil.getStr(examRangeUserInfoMap, "sign");
                        if ("1".equals(sign)){
                            i++;
                        }else {
                            j++;
                        }
                    }
//                    String modifier = examWork.getModifier();
//                    if (!modifier.equals("hadmin") && !modifier.equals("liangjie")){
//                        i++;
//                    }
//                    if (modifier.equals("hadmin") || modifier.equals("liangjie")){
//                        j++;
//                    }
                }
            }
            HashMap.put("orgName",OrgName);
            HashMap.put("answeredQuestions",i);
            HashMap.put("unansweredQuestions",j);
            double percen= 100*i/(i+j);
            String format = String.format("%.2f", percen);
            HashMap.put("percentage",format+"%");
            arrayList.add(HashMap);
        }

        return arrayList;
    }
}
