package com.simbest.boot.exam.codePatrol.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.component.repeat.lock.RequestLock;
import com.simbest.boot.exam.codePatrol.model.UsCodePatrol;
import com.simbest.boot.exam.codePatrol.service.IUsCodePatrolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: dumengfei
 * @CreateTime: 2025-03-05
 * @Description: 码上巡
 * @Version: 1.0
 */

@Slf4j
@RestController
@RequestMapping("/action/patrol")
@CrossOrigin
public class UsCodePatrolController  extends LogicController<UsCodePatrol, String> {

    private final IUsCodePatrolService service;

    public UsCodePatrolController(IUsCodePatrolService service) {
        super(service);
        this.service = service;
    }

    /**
     * 添加数据
     * @param codePatrol
     * @return
     */
    @RequestMapping(value = {
            "/addPatrol",
            "/addPatrol/sso",
            "/api/addPatrol",
            "/anonymous/addPatrol"})
    @RequestLock(expire=60)
    public JsonResponse addPatrol(@RequestBody UsCodePatrol codePatrol ) {
       return service.addPatrol(codePatrol);
    }

    /**
     * 查询试卷分类
     * @return
     */
    @RequestMapping(value = {
            "/queryPaperType",
            "/queryPaperType/sso",
            "/api/queryPaperType",
            "/anonymous/queryPaperType"})
    public JsonResponse queryPaperType( @RequestParam  String dictType){
        return service.queryPaperType( dictType);
    }

    /**
     * PC后台查询填报内容
     *
     * @param page
     * @param size
     * @param codePatrol
     * @param currentUserCode
     * @param source
     * @return
     */
    @RequestMapping(value = {
            "/queryAllPatrol",
            "/queryAllPatrol/sso",
            "/api/queryAllPatrol",
            "/anonymous/queryAllPatrol"})
    public JsonResponse queryAllPatrol(@RequestParam (defaultValue = "1") int page,
                                       @RequestParam (defaultValue = "10") int size,
                                       @RequestBody UsCodePatrol codePatrol,
                                       @RequestParam(required = false) String currentUserCode,
                                       @RequestParam(required = false,defaultValue = "PC") String source){
        return service.queryAllPatrol(page,size,codePatrol,currentUserCode,source  );
    }



}

