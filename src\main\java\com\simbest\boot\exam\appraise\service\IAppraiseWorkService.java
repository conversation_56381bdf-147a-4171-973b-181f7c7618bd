package com.simbest.boot.exam.appraise.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.appraise.model.AppraiseItem;
import com.simbest.boot.exam.appraise.model.AppraiseWork;
import com.simbest.boot.exam.appraise.model.dto.AppraiseInfoDTO;
import com.simbest.boot.exam.appraise.model.dto.AppraiseTemplateInfoDTO;
import com.simbest.boot.exam.appraise.model.vo.AppraiseWorkLayoutVO;
import com.simbest.boot.exam.appraise.model.vo.AppraiseWorkVO;

import java.util.List;

public interface IAppraiseWorkService extends ILogicService<AppraiseWork, String> {

    JsonResponse pushWork(AppraiseInfoDTO dto);

    List<AppraiseWorkVO> getWorkInfo(String username, String appraiseWorkId);

    boolean retrySendSmsMessage(String username, String appraiseWorkId);

    AppraiseWorkLayoutVO getInfo(String username, String workId);

    List<AppraiseItem> submitInfoSalt(String source, String currentUserCode, String str);

    List<AppraiseItem> submitInfo(String source, String currentUserCode, List<AppraiseItem> items);

    List<AppraiseTemplateInfoDTO> getTemplateInfo();

    String updateInterfaceUrl(String appcode, String url);
}
