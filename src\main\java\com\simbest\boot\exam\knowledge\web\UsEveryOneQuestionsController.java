/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.web;/**
 * Created by KZH on 2019/10/8 16:09.
 */

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.component.repeat.lock.RequestLock;
import com.simbest.boot.exam.knowledge.service.IUsEveryOneQuestionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:09
 * @desc
 **/
@Api(description = "人人答题", tags = {"人人答题控制器"})
@Slf4j
@RestController
@RequestMapping(value = "/action/usEveryOneQuestions")
public class UsEveryOneQuestionsController {

    @Autowired
    private IUsEveryOneQuestionsService usEveryOneQuestionsService;

    @ApiOperation(value = "获取答题题目列表接口", notes = "获取答题题目列表接口")
    @PostMapping(value = {"/getAnswersList", "/sso/getAnswersList", "/api/getAnswersList"})
    public JsonResponse getAnswersList(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                       @RequestParam(value = "currentUserCode",required = false) String currentUserCode,
                                       @RequestParam(value = "workType",required = false) String workType,
                                       @RequestParam(value = "pmInsId" ,required = true) String pmInsId,
                                       @RequestParam(value = "invitationId" ,required = true) String invitationId) {
      //  return usEveryOneQuestionsService.getAnswersList(source,currentUserCode, "C",pmInsId,invitationId);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }
    /**
     * 获取答题题目列表接口--查询没有答完的题目信息
     * @param source
     * @param username
     * @return
     */

    @ApiOperation(value = " 查询没有答完的题目信息", notes = "查询没有答完的题目信息")
    @PostMapping(value = {"/findSurplusExam", "/sso/findSurplusExam", "/api/findSurplusExam"})
    public JsonResponse findSurplusExam(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                       @RequestParam(value = "currentUserCode",required = false) String currentUserCode,
                                        @RequestParam(value = "pmInsId" ,required = true) String pmInsId,
                                        @RequestParam(required = false) String invitationId){
       // return usEveryOneQuestionsService.findSurplusExam(source,currentUserCode,pmInsId);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }



    @ApiOperation(value = "开始答题接口", notes = "开始答题接口")
    @PostMapping(value = {"/saveRecord", "/sso/saveRecord", "/api/saveRecord"})
    public JsonResponse saveRecord(@RequestParam(value = "workType" ,required = true) String workType,
                                   @RequestParam(value = "pmInsId" ,required = true) String pmInsId,
                                   @RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                   @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
        //return usEveryOneQuestionsService.saveRecord(workType,source,currentUserCode,pmInsId);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }

    @ApiOperation(value = "提交答案接口", notes = "提交答案接口")
    @PostMapping(value = {"/saveAnswer", "/sso/saveAnswer", "/api/saveAnswer"})
    public JsonResponse saveQuestion(@RequestBody Map<String,Object> requestParam,
                                   @RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                   @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
     //   return usEveryOneQuestionsService.saveAnswer(requestParam,source,currentUserCode);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }


    @ApiOperation(value = "最后一提提交答案接口", notes = "最后一提提交答案接口")
    @PostMapping(value = {"/saveLastAnswer", "/sso/saveLastAnswer", "/api/saveLastAnswer"})
    @RequestLock(expire = 10L)
    public JsonResponse saveLastAnswer(@RequestBody Map<String,Object> requestParam,
                                     @RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                     @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
        //return usEveryOneQuestionsService.saveLastAnswer(requestParam,source,currentUserCode);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }




}
