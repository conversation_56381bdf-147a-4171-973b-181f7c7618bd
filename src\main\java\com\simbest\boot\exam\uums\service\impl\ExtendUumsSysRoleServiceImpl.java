package com.simbest.boot.exam.uums.service.impl;

import cn.hutool.core.lang.Assert;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.AuthoritiesConstants;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.PageTool;
import com.simbest.boot.exam.uums.service.ExtendUumsSysRoleService;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.security.UserOrgTree;
import com.simbest.boot.util.PaginationHelp;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.role.UumsSysRoleApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @desc:  考试管理中的角色管理控制器
 * @date 2021/7/3  11:55
 */
@Service
@Slf4j
public class ExtendUumsSysRoleServiceImpl implements ExtendUumsSysRoleService {

    private static final String USER_MAPPING = "/action/sys/role/";
    private static final String SSO = "/sso";

    @Autowired
    private AppConfig config;
    @Autowired
    private RsaEncryptor encryptor;
    @Autowired
    private UumsSysRoleApi uumsSysRoleApi;
    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private PaginationHelp paginationHelp;


    /**
     * @desc  根据登录用户查询其创建的角色信息列表
     * <AUTHOR>
     */
    @Override
    public JsonResponse findRoleNameIsARoleDim(int page, int size, String roleName, String roleCode) {
        String username = SecurityUtils.getCurrentUserName();
        String newRoleCode="";
        //如果不传入roleCode则拼接查询条件
        if(roleCode==null){
             roleCode="";
             newRoleCode="ROLE_EXAM_"+roleCode;
        }

        if (!roleCode.contains("ROLE_EXAM")){
            newRoleCode="ROLE_EXAM_"+roleCode;
        }

        if (roleCode.contains("ROLE_EXAM")) newRoleCode="_"+roleCode;
        if (roleCode.contains("ROLE_EXAM_")) newRoleCode=roleCode;

        //拼接角色code，以查询关于考试管理系统的角色
        Boolean isApplicationRole=true;//业务角色
        String direction="desc";//排序规则
        String properties="id"; //排序依据字段
        log.debug("Http remote request user by username: {}", username);
        JsonResponse response =  HttpClient.post(config.getUumsAddress() + USER_MAPPING + "findRoleNameIsARoleDim"+SSO)
                .param(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username))
                .param(AuthoritiesConstants.SSO_API_APP_CODE, Constants.APP_CODE )
                .param("page", String.valueOf(page))
                .param("size", String.valueOf(size))
                .param("direction", direction)
                .param("properties", properties)
                .param("roleName", roleName)
                .param("cteator",username)
                .param("roleCode",newRoleCode)
                .param("isApplication",String.valueOf(isApplicationRole))
                .asBean(JsonResponse.class);
        if(response==null){
            log.error("--response对象为空!--");
            return null;
        }
        if (username.equals("hadmin")){
            return response;
        }

        LinkedHashMap data = (LinkedHashMap) response.getData();
        List content = (List) data.get("content");
        List<Map <String,Object>> newUserInfos = new ArrayList<>();
        if (content!=null && content.size()>0){
            for (Object o : content) {
               LinkedHashMap<String,Object>  map= (LinkedHashMap<String, Object>) o;
                String creator = (String) ((LinkedHashMap<?, ?>) o).get("creator");
                if (creator.equals(username)){
                    Map<String, Object> map1 = new HashMap<>();
                    map1.put("roleCode",map.get("roleCode"));
                    map1.put("roleName",map.get("roleName"));
                    map1.put("id",map.get("id"));
                    map1.put("isApplicationRole",map.get("isApplicationRole"));
                    newUserInfos.add(map1);
                }

            }
        }
        long listSize = newUserInfos.size();
        Pageable pageable1 = paginationHelp.getPageable(page, size, null, null);
      //  Pageable pageable1 = getPageable(page, size, null, null);
        List<Map<String, Object>> listPart = PageTool.pagination(newUserInfos, page, size);
        PageImpl resultData = new PageImpl(listPart, pageable1, listSize);
        return JsonResponse.success(resultData);
       // return response;
    }


    /**
     * @desc 新增角色时判断此角色编码是否已存在
     * <AUTHOR>
     */
    @Override
    public Boolean isHaveCode(String roleCode) {
        String username = SecurityUtils.getCurrentUserName();
        String newRoleCode="";
        if (roleCode.contains("ROLE_EXAM")){
             newRoleCode=roleCode;
        }else {
            newRoleCode="ROLE_EXAM_"+roleCode;
        }
        JsonResponse responseRole =  HttpClient.post(config.getUumsAddress() + USER_MAPPING + "isHaveCode"+SSO)
                .param( AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username))
                .param( AuthoritiesConstants.SSO_API_APP_CODE,Constants.APP_CODE )
                .param("roleCode", newRoleCode)
                .asBean(JsonResponse.class);
        Boolean data = (Boolean) responseRole.getData();
        //如果存在则返回false
        if (data){
            return false;
        }
        return true;
    }

    /**
     * @desc 新增角色
     * <AUTHOR>
     */
    @Override
    public SimpleRole createRole(SimpleRole simpleRole) {
        //获取用户传入的角色code进行判断是否需要拼接约束字段
        String newRoleCode ="";
        String roleCode = simpleRole.getRoleCode();
        //如果不包含ROLE_EXAM前缀则手动为用户填补标识
        if (roleCode.contains("ROLE_EXAM")){
            newRoleCode=roleCode;
        }else {
            newRoleCode="ROLE_EXAM_"+roleCode;
        }
        simpleRole.setRoleCode(newRoleCode);
        simpleRole.setIsApplicationRole(true);
        simpleRole.setDisplayOrder(1);
        Boolean haveCode = uumsSysRoleApi.isHaveCode(Constants.APP_CODE, simpleRole.getRoleCode());
        if (haveCode) return null;
        return uumsSysRoleApi.addRole(Constants.APP_CODE, simpleRole);
    }


    /**
      * @desc 根据角色id查询角色信息
      * <AUTHOR>
      */
    @Override
    public SimpleRole findRoleById(String id) {
        return uumsSysRoleApi.findById(id, Constants.APP_CODE);
    }

    /**
      * @desc 角色信息修改  只提供修改角色名称
      * <AUTHOR>
      */
    @Override
    public SimpleRole updateRoleInfo(SimpleRole simpleRole) {

        //如果用户在修改角色Code的时候不满足考试管理系统角色code的约束则进行手动约束
      /*  String roleExamCode="";
        if (simpleRole.getRoleCode().contains("ROLE_EXAM")){
            roleExamCode=simpleRole.getRoleCode();
        }else {
            roleExamCode="ROLE_EXAM_"+simpleRole.getRoleCode();
        }
        simpleRole.setRoleCode(roleExamCode);*/
        return uumsSysRoleApi.updateRole(Constants.APP_CODE, simpleRole);

    }

    /**
      * @desc 根据ID逻辑删除角色
      * <AUTHOR>
      */
    @Override
    public boolean delRoleInfo(String id) {
        SimpleRole simpleRole = new SimpleRole();
        //在规定用户创建的角色code携带前缀标识
        simpleRole.setId(id);
        return uumsSysRoleApi.delRole(Constants.APP_CODE, simpleRole);
    }

    /**
      * @desc 批量删除角色
      * <AUTHOR>
      */
    @Override
    public boolean delRoleInfos(String[] ids) {

        try {
            for (String id : ids) {
                SimpleRole simpleRole = new SimpleRole();
                //在规定用户创建的角色code携带前缀标识
                simpleRole.setId(id);
                uumsSysRoleApi.delRole(Constants.APP_CODE, simpleRole);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * @desc 模糊查询出人所在的组织树
     * <AUTHOR>
     */
    @Override
    public Set<UserOrgTree> findDimUserTree(SimpleUser truename) {

        Map<String, Object> map = new HashMap<>();
        map.put("truename",truename.getTruename());
        Set<UserOrgTree> dimUserTree = uumsSysUserinfoApi.findDimUserTree(map, Constants.APP_CODE);
        return dimUserTree;
    }
}
