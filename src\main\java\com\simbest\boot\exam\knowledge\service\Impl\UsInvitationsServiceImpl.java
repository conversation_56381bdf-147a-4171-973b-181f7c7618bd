/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service.Impl;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsInvitations;
import com.simbest.boot.exam.knowledge.model.UsPendingTask;
import com.simbest.boot.exam.knowledge.model.UsQuizSessions;
import com.simbest.boot.exam.knowledge.model.UsUserScore;
import com.simbest.boot.exam.knowledge.repository.UsInvitationsRepository;
import com.simbest.boot.exam.knowledge.repository.UsUserScoreRepository;
import com.simbest.boot.exam.knowledge.service.*;
import com.simbest.boot.exam.mainbills.model.UsPmInstence;
import com.simbest.boot.exam.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.exam.mainbills.service.impl.UsPmInstenceServiceImpl;
import com.simbest.boot.exam.util.BelongInfoTool;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.DateUtil;
import com.simbest.boot.exam.util.FormatConversion;
import com.simbest.boot.exam.wfquey.web.QueryDictValueController;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.xkzhangsan.time.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.h2.mvstore.DataUtils;
import org.hibernate.validator.internal.IgnoreForbiddenApisErrors;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class UsInvitationsServiceImpl extends LogicService<UsInvitations, String> implements IUsInvitationsService {

    private UsInvitationsRepository usInvitationsRepository;

    private String param1 = "/acton/usInvitations";
    @Autowired
    private UsPmInstenceServiceImpl pmInstenceService;

    @Autowired
    public UsInvitationsServiceImpl(UsInvitationsRepository usInvitationsRepository) {
        super(usInvitationsRepository);
        this.usInvitationsRepository = usInvitationsRepository;

    }

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IUsPmInstenceService usPmInstenceService;

    @Autowired
    private IUsInvitationsService usInvitationsService;

    @Autowired
    private ISysDictValueService sysDictValueService;

    @Autowired
    private IUsPendingTaskService usPendingTaskService;

    @Autowired
    private UsQuizSessionsServiceImpl usQuizSessionsService;

    @Autowired
    private LoginUtils loginUtils;


    @Autowired
    IUsEveryOneQuestionsService usEveryOneQuestionsService;

    /**
     * A邀请B
     *
     * @param sendUserName 发送者ID
     * @param recUserName 接收者ID
     * @return 邀请对象
     */
    @Override
    public JsonResponse sendInvitation(String source, String username,String sendUserName, String recUserName) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(username,Constants.APP_CODE);
        }
        IUser iUser = SecurityUtils.getCurrentUser();
        // 设置题库编码
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();//题库编码字典获取方式
        Integer everyoneQuestionCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneQuestionCount").get(0).getValue());//人人答题数量
        Integer everyoneAnswerCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneAnswerCount").get(0).getValue());//人人答题次数
        Integer everyoneTimeOut =Integer.valueOf(sysDictValueService.findByDictType("everyoneTimeOut").get(0).getValue());//人人答题超时时间

        UUID uuid = UUID.randomUUID();
        // 去除UUID中的"-"
        String pmInsId = "C"+uuid.toString().replaceAll("-", "");
        String currentDay = DateUtil.getCurrentStr();
        // 检查发送者的邀请次数
        List<UsInvitations> sendList = usInvitationsRepository.countExamWorkByTask(currentDay, sendUserName);
        if (sendList.size()>everyoneAnswerCount) {
            return  JsonResponse.fail("当日邀请次数已用完，请明日尝试邀请");
        }

        // 检查接收者当天的对战次数
        List<UsInvitations> reciveList = usInvitationsRepository.countExamWorkByTask(currentDay, recUserName);
        if (reciveList.size() >=everyoneAnswerCount) {
            return  JsonResponse.fail("当日接收者对战次数已用完，请明日尝试邀请");
        }

        // 创建邀请
        UsInvitations invitation = new UsInvitations();

        BelongInfoTool.setBelongCompanyAndDepartment(invitation);
        invitation.setBelongOrgCode(iUser.getBelongOrgCode());
        invitation.setBelongOrgName(iUser.getAuthOrgs().iterator().next().getDisplayName());
        invitation.setQuestionBankCode(knowledgeQuestionBankCode);//题库编码
//        // 设置问题ID
//       invitation.setQuestionId("问题ID");
//        // 设置问题Code
//       invitation.setQuestionCode("问题Code");
        // 设置发送邀请的用户OA账户
        SimpleUser sendUser = uumsSysUserinfoApi.findByUsernameFromCurrent(sendUserName, Constants.APP_CODE);
        invitation.setSendUserName(sendUserName);
        // 设置发送邀请的用户姓名
        invitation.setSendTrueName(sendUser.getTruename());
        // 设置接受邀请的用户OA账户
        SimpleUser reciveUser = uumsSysUserinfoApi.findByUsernameFromCurrent(recUserName, Constants.APP_CODE);
        invitation.setRecUserName(recUserName);
        // 设置接受邀请的用户姓名
        invitation.setRecTrueName(reciveUser.getTruename());
        // 设置邀请状态
        invitation.setStatus("PENDING"); // 'Pending'：待接收, 'Accepted'：已接受, 'Expired'：过期, 'refuse'：拒绝
        // 设置邀请创建时间
        invitation.setCreatedAt(DateUtil.getCurrentTimestamp());
        // 设置邀请过期时间
        Date overTime=DateUtil.addMinutes(everyoneTimeOut);
        // 定义日期格式，例如："yyyy-MM-dd HH:mm:ss"
        String pattern = "yyyy-MM-dd HH:mm:ss";
        // 创建一个SimpleDateFormat对象，指定模式
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        // 使用SimpleDateFormat对象格式化Date对象，得到格式化的日期字符串
        String formattedDate = simpleDateFormat.format(overTime);
        invitation.setExpiresAt(formattedDate);
        // 设置邀请接受时间
       invitation.setAcceptedAt("");
       invitation.setPmInsId(pmInsId);

        invitation = usInvitationsService.insert(invitation);
        LocalDateTime createdTime = invitation.getCreatedTime();
        int year = createdTime.getYear();
        String  title=year+"年合规知识竞赛-人人对战"+"-"+invitation.getSendTrueName()+"邀请"+invitation.getRecTrueName()+"答题";
        invitation.setMessage(title);
        usInvitationsService.update(invitation);
        // 创建接收者的待办任务
        UsPendingTask receiverTask = new UsPendingTask();
        receiverTask.setSendUserName(invitation.getSendUserName());
        receiverTask.setSendTrueName(invitation.getSendTrueName());
        receiverTask.setInvitationId(invitation.getId());
        receiverTask.setPendUserName(recUserName);//待办人OA
        receiverTask.setPendTrueName(invitation.getRecTrueName());//待办人姓名
        receiverTask.setQuestionBankCode(knowledgeQuestionBankCode);//题库编码
        receiverTask.setBelongOrgCode(iUser.getBelongOrgCode());
        receiverTask.setBelongOrgName(iUser.getAuthOrgs().iterator().next().getDisplayName());
        receiverTask.setTitle(title);
        receiverTask.setCreatedAt(DateUtil.getCurrentTimestamp());//任务创建的时间
        // 设置邀请过期时间
        Date taskOverTime=DateUtil.addMinutes(10);
        // 使用SimpleDateFormat对象格式化Date对象，得到格式化的日期字符串
        String taskOverDate = simpleDateFormat.format(taskOverTime);
        receiverTask.setDueDate(taskOverDate);//任务的截止时间
        receiverTask.setStatus("PENDING");//Pending':任务待处理。 InProgress':任务正在进行中   Completed':任务已完成。  Expired':任务已过期
        receiverTask.setWorkTupe("C");//C：人人对战
        receiverTask.setTaskTupe("1");//待办类型 ：1:邀请 2：待办
        receiverTask.setPmInsId(pmInsId);
        receiverTask.setInviType("1");//邀请类型 ：1:邀请 2：接收
        usPendingTaskService.insert(receiverTask);
        usPendingTaskService.saveOpenTodo(receiverTask);//推送统一待办
        //推送短信
        //【2024年度合规知识竞赛】您好，今天是2024年11月18日，【赵博】向您发起“人人对战”邀请，请您10分钟内确认是否接收，超时将自动拒绝本次邀请，感谢您的参与和支持！
        String msgResult="【2024年度合规知识竞赛】您好，【"+sendUser.getTruename()+"】向您发起“人人对战”邀请，请您10分钟内确认是否接收，超时将自动拒绝本次邀请，感谢您的参与和支持！";
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        Map map = new HashMap();
        map.put("PARTICIPANT", recUserName);
        map.put("PARTI_NAME", invitation.getRecTrueName());
        map.put("RECEIPT_TITLE", "2024年合规知识竞赛");
        list.add(map);
        usEveryOneQuestionsService.sendShortMessage(source, iUser.getUsername(), list, msgResult);

        UsPmInstence usPmInstence = new UsPmInstence();
        usPmInstence.setPmInsId(pmInsId);
        usPmInstence.setPmInsTitle(receiverTask.getTitle());
        usPmInstence.setPmInsType("C");
        usPmInstence.setSign("0");
        BelongInfoTool.setBelongCompanyAndDepartment(usPmInstence);
        usPmInstence.setBelongOrgCode(iUser.getBelongOrgCode());
        usPmInstence.setBelongOrgName(iUser.getAuthOrgs().iterator().next().getDisplayName());
        usPmInstenceService.insert(usPmInstence);
        return JsonResponse.success(receiverTask);
    }

    /**
     * B接受邀请
     *
     * @param invitationId 邀请ID
     * @return 邀请对象
     */
    @Override
    public JsonResponse acceptInvitation(String source, String username,String invitationId) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(username,Constants.APP_CODE);
        }
        IUser iUser = SecurityUtils.getCurrentUser();
        // 设置题库编码
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();//题库编码字典获取方式
        Integer everyoneQuestionCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneQuestionCount").get(0).getValue());//人人答题数量
        Integer everyoneAnswerCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneAnswerCount").get(0).getValue());//人人答题次数
        Integer everyoneTimeOut =Integer.valueOf(sysDictValueService.findByDictType("everyoneTimeOut").get(0).getValue());//人人答题超时时间
        // 查找邀请
        UsInvitations invitation = usInvitationsService.findById(invitationId);
        if(null==invitation) {
            return JsonResponse.fail("暂无查询到此次邀请，请确认邀请信息是否已过期");
        }

        // 检查邀请状态
        if (!invitation.getStatus().equals("PENDING")) {
            return JsonResponse.fail("请确认此次邀请信息是否过期");
        }

        // 检查邀请是否过期
//        try {
//            Boolean aBoolean = DateUtil.compireTime(invitation.getExpiresAt());
//            if(!aBoolean){
//                return JsonResponse.fail("请确认此次邀请信息已过期");
//            }
//        } catch (ParseException e) {
//            throw new RuntimeException(e);
//        }

        // 创建对战会话
        UsQuizSessions quizSession = new UsQuizSessions();
        // 设置发送邀请的用户OA账户
        quizSession.setSendUserName(invitation.getSendUserName());
        // 设置发送邀请的用户姓名
        quizSession.setSendTrueName(invitation.getSendTrueName());
        // 设置接受邀请的用户OA账户
        quizSession.setRecUserName(invitation.getRecUserName());
        // 设置接受邀请的用户姓名
        quizSession.setRecTrueName(invitation.getRecTrueName());
        // 设置对战开始时间
        quizSession.setStartTime(DateUtil.getCurrentTimestamp());
        // 设置对战结束时间
        // 设置邀请过期时间
        Date date = new Date(); // 示例中直接使用当前时间
        // 使用 DateUtil 类的 addMinutes 方法给 everyoneTimeOut 增加一定的分钟数
        Date overTime = DateUtil.addMinutes(date, everyoneTimeOut); // 假设增加30分钟
        // 定义日期格式，例如："yyyy-MM-dd HH:mm:ss"
        String pattern = "yyyy-MM-dd HH:mm:ss";
        String formattedDate= DateUtil.getDate(overTime, pattern);
        try {
            // 将 Date 对象格式化为字符串
            quizSession.setEndTime(formattedDate);
        } catch (Exception e) {
            // 捕获可能发生的任何异常
            e.printStackTrace();
        }
        // 设置对战状态
        quizSession.setStatus("ONGOING"); // ONGOING：对战正在进行中 COMPLETED：对战结束
//        // 设置A得分
//        quizSession.setScoreUserA(100);
//        // 设置B得分
//        quizSession.setScoreUserB(80);
        // 设置创建时间
        quizSession.setCreatedAt(DateUtil.getCurrentTimestamp());
        // 设置更新时间
        quizSession.setUpdatedAt(DateUtil.getCurrentTimestamp());
        quizSession.setInvitationId(invitationId);
        usQuizSessionsService.insert(quizSession);
        // 更新邀请的会话ID
        invitation.setSessionId(quizSession.getId());
        // 更新邀请状态
        invitation.setStatus("ACCEPTED");
        invitation = usInvitationsService.update(invitation);
        //更新邀请待办状态
        UsPendingTask pendingTaskByInvitaitonId = usPendingTaskService.findPendingTaskByPmInsId(invitation.getPmInsId());
        pendingTaskByInvitaitonId.setStatus("ACCEPTED");
        pendingTaskByInvitaitonId.setTaskTupe("2");
        usPendingTaskService.update(pendingTaskByInvitaitonId);
        /*pendingTaskByInvitaitonId.setDueDate(DateUtil.getCurrentTimestamp());
        usPendingTaskService.cancleOpenTodo(pendingTaskByInvitaitonId);
        pendingTaskByInvitaitonId.setEnabled(false);
        pendingTaskByInvitaitonId.setRemovedTime(LocalDateTime.now());
        usPendingTaskService.update(pendingTaskByInvitaitonId);
        // 创建接收者的待办任务
        UsPendingTask receiverTask = new UsPendingTask();
        receiverTask.setInvitationId(invitation.getId());
        receiverTask.setSendUserName(invitation.getSendUserName());
        receiverTask.setSendTrueName(invitation.getSendTrueName());
        receiverTask.setPendUserName(invitation.getRecUserName());//待办人OA
        receiverTask.setPendTrueName(invitation.getRecTrueName());//待办人姓名
        receiverTask.setQuestionBankCode(invitation.getQuestionBankCode());//题库编码
        receiverTask.setTitle("2024年合规知识竞赛人人对战");
        receiverTask.setCreatedAt(DateUtil.getCurrentTimestamp());//任务创建的时间
        BelongInfoTool.setBelongCompanyAndDepartment(receiverTask);
        // 设置邀请过期时间
        Date taskOverTime=DateUtil.addMinutes(everyoneTimeOut);
        // 使用SimpleDateFormat对象格式化Date对象，得到格式化的日期字符串
        receiverTask.setDueDate(formattedDate);//任务的截止时间
        receiverTask.setStatus("ACCEPTED");//Pending':任务待处理。 InProgress':任务正在进行中   Completed':任务已完成。  Expired':任务已过期
        receiverTask.setWorkTupe("C");//C：人人对战
        receiverTask.setTaskTupe("2");//待办类型 ：1:邀请 2：待办
        receiverTask.setInviType("1");//邀请类型 ：1:邀请 2：接收
        UUID uuid = UUID.randomUUID();
        // 去除UUID中的"-"
        String pmInsId = "C"+uuid.toString().replaceAll("-", "");
        receiverTask.setPmInsId(pmInsId);
        UsPendingTask usPendingTask1 = usPendingTaskService.insert(receiverTask);
        UsPmInstence usPmInstence = new UsPmInstence();
        usPmInstence.setPmInsId(pmInsId);
        usPmInstence.setPmInsTitle(receiverTask.getTitle());
        usPmInstence.setPmInsType("C");
        usPmInstence.setSign("0");
        BelongInfoTool.setBelongCompanyAndDepartment(usPmInstence);
        usPmInstence.setBelongOrgCode(iUser.getBelongOrgCode());
        usPmInstence.setBelongOrgName(iUser.getAuthOrgs().iterator().next().getDisplayName());
        usPmInstenceService.insert(usPmInstence);
        usPendingTaskService.saveOpenTodo(usPendingTask1);//推送统一待办*/
        // 创建发送者的答题待办任务
        UsPendingTask senderTask = new UsPendingTask();
        senderTask.setInvitationId(invitation.getId());
        senderTask.setSendUserName(invitation.getRecUserName());
        senderTask.setSendTrueName(invitation.getRecTrueName());
        senderTask.setPendUserName(invitation.getSendUserName());//待办人OA
        senderTask.setPendTrueName(invitation.getSendTrueName());//待办人姓名
        senderTask.setQuestionBankCode(invitation.getQuestionBankCode());//题库编码
        LocalDateTime createdTime = invitation.getCreatedTime();
        int year = createdTime.getYear();
        String  title=year+"年合规知识竞赛-人人对战"+"-"+invitation.getSendTrueName()+"邀请"+invitation.getRecTrueName()+"答题";
        senderTask.setTitle(title);
        senderTask.setCreatedAt(DateUtil.getCurrentTimestamp());//任务创建的时间
        senderTask.setDueDate(formattedDate);//任务的截止时间
        senderTask.setStatus("ACCEPTED");//Pending':任务待处理。 ACCEPTED':任务正在进行中   Completed':任务已完成。  Expired':任务已过期
        senderTask.setWorkTupe("C");//C：人人对战
        senderTask.setTaskTupe("2");//待办类型 ：1:邀请 2：待办
        senderTask.setInviType("2");//邀请类型 ：1:邀请 2：接收
        UUID uuidS = UUID.randomUUID();
        // 去除UUID中的"-"
        String pmInsIds = "C"+uuidS.toString().replaceAll("-", "");
        senderTask.setPmInsId(pmInsIds);
        BelongInfoTool.setBelongCompanyAndDepartment(senderTask);
        UsPendingTask usPendingTask = usPendingTaskService.insert(senderTask);
        UsPmInstence usPmInstence1 = new UsPmInstence();
        usPmInstence1.setPmInsId(pmInsIds);
        usPmInstence1.setPmInsTitle(senderTask.getTitle());
        usPmInstence1.setPmInsType("C");
        usPmInstence1.setSign("0");
        BelongInfoTool.setBelongCompanyAndDepartment(usPmInstence1);
        usPmInstence1.setBelongOrgCode(iUser.getBelongOrgCode());
        usPmInstence1.setBelongOrgName(iUser.getAuthOrgs().iterator().next().getDisplayName());
        usPmInstenceService.insert(usPmInstence1);
        usPendingTaskService.saveOpenTodo(usPendingTask);//推送统一待办
        return JsonResponse.success(invitation);
    }

    /**
     * B拒绝邀请
     *
     * @param source
     * @param username
     * @param invitationId
     * @return
     */
    @Override
    public JsonResponse refauseInvitation(String source, String username, String invitationId) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(username,Constants.APP_CODE);
        }
        // 设置题库编码
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();//题库编码字典获取方式
        Integer everyoneQuestionCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneQuestionCount").get(0).getValue());//人人答题数量
        Integer everyoneAnswerCount =Integer.valueOf(sysDictValueService.findByDictType("everyoneAnswerCount").get(0).getValue());//人人答题次数
        Integer everyoneTimeOut =Integer.valueOf(sysDictValueService.findByDictType("everyoneTimeOut").get(0).getValue());//人人答题超时时间

        // 查找邀请
        UsInvitations invitation = usInvitationsService.findById(invitationId);
        if(null==invitation) {
            return JsonResponse.fail("暂无查询到此次邀请，请确认邀请信息是否已过期");
        }
        // 检查邀请状态
        if (invitation.getStatus().equals("REFUSE")) {
            return JsonResponse.fail("请确认此次邀请信息是否已被拒绝");
        }
        invitation.setStatus("REFUSE");//设置为拒绝
        // 返回发送者的邀请次数
        invitation.setEnabled(false);
        invitation.setRemovedTime(LocalDateTime.now());
        usInvitationsService.update(invitation);
        //核销待办
        List<UsPendingTask> pendingTaskByInvitaitonId = usPendingTaskService.findPendingTaskByInvitaitonId(invitation.getId());
        if(CollectionUtil.isNotEmpty(pendingTaskByInvitaitonId)) {
            for (UsPendingTask usPendingTask : pendingTaskByInvitaitonId) {
                usPendingTask.setStatus("COMPLETED");
                usPendingTask.setLastUpdated(DateUtil.getCurrentTimestamp());
                usPendingTaskService.update(usPendingTask);
                usPendingTaskService.cancleOpenTodo(usPendingTask);//核销统一待办
            }
        }

        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 查询当日所有预约信息
     * 每天12点取消当日所有预约信息
     *
     * @param currentDay
     * @return
     */
    @Override
    public List<UsInvitations> cancleInvitations(String source, String username,String currentDay) {
        return usInvitationsRepository.cancleInvitations(currentDay);
    }

    /**
     * 查询已邀请接受但是未答完题的情况
     *
     * @param currentDay
     * @return
     */
    @Override
    public List<UsInvitations> cancleInvitationsAcc(String source, String username,String currentDay) {
        return usInvitationsRepository.cancleInvitationsAcc(currentDay);
    }


    /**
     * 夜间核销待办任务
     * 核销包含两部门
     * 1、核销统一待办、待办状态变更
     * 2、当日所有邀请信息变更为过期
     */
    @Override
    public JsonResponse expirePendingTasks(String source, String username,String yesterday) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(username,Constants.APP_CODE);
        }
        if(StringUtil.isEmpty(yesterday)){
            yesterday = DateUtil.getYesterday();
        }
        //查询当日所有未接受
        List<UsInvitations> usInvitationsList = usInvitationsService.cancleInvitations(source,username,yesterday);
        if(CollectionUtil.isNotEmpty(usInvitationsList)) {
            for (UsInvitations usInvitations : usInvitationsList) {
                usInvitations.setStatus("EXPIRED");
                usInvitationsService.update(usInvitations);
            }
        }
        //查询当日已接受但是未答完题的
        List<UsInvitations> usInvitationsList1 = usInvitationsService.cancleInvitationsAcc(source,username,yesterday);
        if(CollectionUtil.isNotEmpty(usInvitationsList1)) {
            for (UsInvitations usInvitations : usInvitationsList1) {
                usInvitations.setStatus("EXPIRED");
                usInvitationsService.update(usInvitations);
            }
        }
        List<UsQuizSessions> usQuizSessionsList = usQuizSessionsService.cancleQuizSessions(yesterday);
        if(CollectionUtil.isNotEmpty(usQuizSessionsList)) {
            for (UsQuizSessions usQuizSessions : usQuizSessionsList) {
                usQuizSessions.setStatus("COMPLETED");
                usQuizSessionsService.update(usQuizSessions);
            }
        }

        List<UsPendingTask> pendingTasksList = usPendingTaskService.cancleTask(yesterday);
        if(CollectionUtil.isNotEmpty(pendingTasksList)){
            for (UsPendingTask usPendingTask : pendingTasksList) {
                usPendingTask.setStatus("EXPIRED");
                usPendingTaskService.update(usPendingTask);
                usPendingTaskService.cancleOpenTodo(usPendingTask);//核销统一待办
            }
        }
        return  JsonResponse.defaultSuccessResponse();
    }

    /**
     * 邀请过期处理
     */
    @Override
    public JsonResponse expireInvitations(String source, String username) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(username,Constants.APP_CODE);
        }
        try {
            String currentStr = DateUtil.getCurrentStr();
            List<UsInvitations> invitationsList = usInvitationsService.findTimeOutByStatus();
            if (CollectionUtil.isNotEmpty(invitationsList)) {
                for (UsInvitations usInvitations : invitationsList) {
                    if (!DateUtil.compireTime(usInvitations.getExpiresAt())) {
                        usInvitations.setStatus("EXPIRED");
                        // 返回发送者的邀请次数
                        usInvitations.setEnabled(false);
                        usInvitations.setRemovedTime(LocalDateTime.now());
                        usInvitationsService.update(usInvitations);
                        UsPendingTask usPendingTask = usPendingTaskService.findPendingTaskByPmInsId(usInvitations.getPmInsId());
                        //去除统一待办和待办
                        usPendingTaskService.cancleOpenTodo(usPendingTask);
                        usPendingTask.setStatus("EXPIRED");
                        usPendingTask.setEnabled(false);
                        usPendingTask.setRemovedTime(LocalDateTime.now());
                        usPendingTaskService.update(usPendingTask);
                    }
                }
            }
        } catch (ParseException e) {
            log.error("Error parsing date", e);
            throw new RuntimeException(e);
        }
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 查询指定日期指定状态的数邀请信息
     *
     * @param currentDay
     * @param status
     * @return
     */
    @Override
    public List<UsInvitations> findByStatus(String source, String username,String currentDay, String status) {
        return usInvitationsRepository.findByStatus(currentDay,status);
    }

    /**
     * 查询被邀请人列表信息
     *
     * @param currentDay
     * @param recUserName
     * @return
     */
    @Override
    public List<UsInvitations> InvitedTask(String currentDay, String recUserName) {
        return usInvitationsRepository.InvitedTask(currentDay,recUserName);
    }

    /**
     * 查询超出指定时间内的邀请信息
     *
     * @return
     */
    @Override
    public List<UsInvitations> findTimeOutByStatus() {
        return usInvitationsRepository.findTimeOutByStatus(LocalDateTime.now().minusMinutes(10L));
    }

    /**
     *
     * @param source
     * @param currentUserCode
     * @return
     */
    @Override
    public JsonResponse getInvitationList(String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        List<Map<String , Object>> usInvitations = usInvitationsRepository.findByUserName(currentUser.getUsername(), DateUtil.getCurrentStr());
        if (CollectionUtil.isNotEmpty(usInvitations)) {
            usInvitations = FormatConversion.formatConversion(usInvitations);
            for (Map<String , Object> map : usInvitations) {
                DateTime dt = new DateTime(DateUtil.parseLongTimestamp(MapUtil.getLong(map , "createdTime")));
                map.put("createdTime" , dt.toString("yyyy-MM-dd HH:mm:ss") );
            }
        }
        return JsonResponse.success(usInvitations);
    }

    @Override
    public List<UsInvitations> findByPmInsId(String pmInsId) {
        List<UsInvitations> usInvitations = usInvitationsRepository.findByPmInsId(pmInsId);
        return usInvitations;
    }

    /**
     * 根据邀请人和被邀请人查询剩余邀请次数
     * A邀请B,只要B接受，则A与B都将少一次邀请次数
     * 也可用来查询指定日期指定人员剩余多少邀请次数
     *
     * @param currentDay
     * @param sendUserName
     * @return
     */
    @Override
    public List<UsInvitations> countExamWorkByTask(String source, String username,String currentDay, String sendUserName) {
        return usInvitationsRepository.countExamWorkByTask(currentDay, sendUserName);
    }
    
}
