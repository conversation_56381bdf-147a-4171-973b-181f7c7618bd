package com.simbest.boot.exam.sms.sevice.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mzlion.easyokhttp.HttpClient;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.constants.AuthoritiesConstants;
import com.simbest.boot.exam.publicLottery.model.PublicLotteryPerson;
import com.simbest.boot.exam.publicLottery.service.IPublicLotteryPersonService;
import com.simbest.boot.exam.sms.sevice.ILoginService;
import com.simbest.boot.exam.util.AppConstants;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.MessageEnum;
import com.simbest.boot.security.IAuthService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleConfig;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.util.CodeGenerator;
import com.simbest.boot.util.SpringContextUtil;
import com.simbest.boot.util.encrypt.Des3Encryptor;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.redis.RedisUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppConfigApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2020/8/12</strong><br>
 * <strong>Modify on : 2020/8/12</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Service
@Slf4j
public class LoginServiceImpl implements ILoginService {

    @Autowired
    private UumsSysAppConfigApi uumsSysAppConfigApi;

    @Autowired
    private RsaEncryptor encryptor;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private Des3Encryptor des3Encryptor;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private SpringContextUtil springContextUtil;
    @Autowired
    private IPublicLotteryPersonService publicLotteryPersonServiceImpl;


    @Override
    public int sendMsg(String phone) {
        int i = 0;
        String lockKey = null;
        try {
            //短信发送成功后，2分钟内有效,不能重复发送
            String cacheKey = RedisUtil.SMS_VERIFICATION_CODE_PRIFIEX + phone;
            Boolean keyExist = RedisUtil.hasKey(cacheKey);
            //key 已存在，返回操作频繁提示
            if (keyExist) {
                return 2;
            } else {
                //用于防止未发送短信之前重复发送操作，发送短信之前，先将该手机号锁保存到redis中，发送短信成功或者失败，都删除该锁
                lockKey = RedisUtil.SMS_VERIFICATION_CODE_PRIFIEX + "lock:" + phone;
                Boolean lockKeyExist = RedisUtil.hasKey(lockKey);
                if (lockKeyExist) {
                    return 2;
                } else {
                    RedisUtil.set(lockKey, phone, 120);
                }
            }
            Map<String, Object> map = Maps.newHashMap();
            map.put("preferredMobile", phone);
            map.put("enabled", true);
            loginUtils.adminLogin();
            List<SimpleUser> list = uumsSysUserinfoApi.findAllNoPage(Constants.APP_CODE, map);
            if (CollectionUtil.isNotEmpty(list)) {
                SimpleUser iUser = list.get(0);
                String profile = springContextUtil.getActiveProfile();
                String dynSmsCode = CodeGenerator.randomInt(6);
                if (!ApplicationConstants.PRD.equalsIgnoreCase(profile)) {//如果是测试环境，默认验证码为666666
                    dynSmsCode = "666666";
                    RedisUtil.genAndSaveAppSMSCode(phone, dynSmsCode, 600);
//                    //清除手机号码锁
                    RedisUtil.expire(lockKey, 0, TimeUnit.SECONDS);
                    i = 1;
                    return i;
                }
                if ("hadmin3".equalsIgnoreCase(iUser.getUsername())) {//如果是hadmin3，默认验证码为666666
                    dynSmsCode = "666666";
                }
                log.warn("验证码为【{}】", dynSmsCode);
                Map<String, Object> params = Maps.newHashMap();
                params.put("appName", Constants.APP_NAME);
                params.put("trueName", iUser.getTruename());
                params.put("minute", "10");
                params.put("dynSmsCode", dynSmsCode);
                String msgContent = MessageEnum.SMS000010.getMessage(params);
                ShrotMsg shrotMsg = new ShrotMsg();//短信发送实体对象
                Set<Content> contentSet = new HashSet<Content>();
                Content content = new Content();//短信内容实体对象
                shrotMsg.setAppCode(Constants.APP_CODE);
                content.setUsername(iUser.getUsername());
                content.setMsgContent(msgContent);
                content.setImmediately(true);
                content.setSmsPriority(1);
                Set<String> resMsgPhones = Sets.newHashSet();
                resMsgPhones.add(phone);
                content.setPhoneNums(resMsgPhones);
                contentSet.add(content);
                shrotMsg.setContents(contentSet);
                String msgJson = JacksonUtils.obj2json(shrotMsg);
                String msgEncyptorJson = this.des3Encryptor.encrypt(msgJson);
                log.debug("MsgPostOperatorService>>>>>>>postMsg>>>>>短信发送加密串>>>" + msgEncyptorJson);
                SimpleConfig simpleConfig = uumsSysAppConfigApi.findAppConfigByStyle("umsc", "hadmin", "common");
                JsonResponse response = HttpClient.post(simpleConfig.getAddress() + "/msg/inner/sendMtMsg/sso")
                        .param("loginuser", rsaEncryptor.encrypt("hadmin"))
                        .param("appcode", Constants.APP_CODE)
                        .param("sendMtMsgJson", msgEncyptorJson)
                        .asBean(JsonResponse.class);
                int ret = response.getErrcode();
                log.warn("发送短信结果【{}】", ret);
                //int ret = 0;
                if (ret == 0) {
                    //RedisUtil.setEx(AppConstants.SMS_CODE_KEY + phone, dynSmsCode, AppConstants.DYN_LOGIN_MSG_TIME, TimeUnit.MINUTES);
                    RedisUtil.genAndSaveAppSMSCode(phone, dynSmsCode, 600);
//                    //清除手机号码锁
                    RedisUtil.expire(lockKey, 0, TimeUnit.SECONDS);
                    i = 1;
                }
            } else {
                return 0;
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            //清除号码锁
            RedisUtil.expire(lockKey, 0, TimeUnit.SECONDS);
            i = -1;
        }
        return i;
    }

    /**
     * 发送验证码 仅限特定人员
     *
     * @param phone 手机号
     */
    @Override
    public int sendMsgForOA(String phone) {
        List<PublicLotteryPerson> list = publicLotteryPersonServiceImpl.findAllNoPage(Specifications.<PublicLotteryPerson>and()
                .eq("preferredMobile", phone).build());
        if (CollectionUtils.isEmpty(list)) return 0;

        return this.sendMsg(phone);
    }


    @Override
    public String getShortUrl(String url) {
        String shortUrl = "";
        HashMap<Object, Object> params = Maps.newHashMap();
        params.put("appCode", AppConstants.APP_CODE);
        params.put("url", url);
        String jsonStr = JacksonUtils.obj2json(params);
        //远程调用zhfw接口
        IUser iUser = SecurityUtils.getCurrentUser();
        if (iUser == null) {
            loginUtils.adminLogin();
            iUser = SecurityUtils.getCurrentUser();
        }
        log.debug("开始进行长短连接转换");
        SimpleConfig simpleConfig = uumsSysAppConfigApi.findAppConfigByStyle("iservice", "hadmin", "common");
        Map resultMap = HttpClient.textBody(simpleConfig.getAddress() + "/action/sms/getShortUrl/sso?" +
                        //Map resultMap = HttpClient.textBody( "http://***********:8001/zhfw/action/sms/convertShortUrl/sso?" +
                        AuthoritiesConstants.SSO_API_APP_CODE + "=" + AppConstants.APP_CODE + "&" +
                        AuthoritiesConstants.SSO_API_USERNAME + "=" + encryptor.encrypt(iUser.getUsername()))
                .json(jsonStr).asBean(Map.class);
        log.warn("-----接口返回值为【{}】", JacksonUtils.obj2json(resultMap));
        //若接口成功errorCode=0
        Map data = resultMap != null ? JacksonUtils.json2obj(JacksonUtils.obj2json(resultMap.get("data")), LinkedHashMap.class) : null;
        if (data != null && "0".equals(data.get("errorCode"))) {
            shortUrl = MapUtil.getStr(data, "destUrl");
        }
        return shortUrl;
    }

    @Override
    public String ConvertShortUrl(String url) {
        String shortUrl = "";
        HashMap<Object, Object> params = Maps.newHashMap();
        params.put("appCode", AppConstants.APP_CODE);
        params.put("url", url);
        String jsonStr = JacksonUtils.obj2json(params);
        //远程调用zhfw接口
        IUser iUser = SecurityUtils.getCurrentUser();
        if (iUser == null) {
            loginUtils.adminLogin();
            iUser = SecurityUtils.getCurrentUser();
        }
        log.debug("开始进行长短连接转换");
        SimpleConfig simpleConfig = uumsSysAppConfigApi.findAppConfigByStyle("iservice", "hadmin", "common");
        Map resultMap = HttpClient.textBody(simpleConfig.getAddress() + "/action/sms/convertShortUrl/sso?" +
                        //Map resultMap = HttpClient.textBody( "http://***********:8001/zhfw/action/sms/convertShortUrl/sso?" +
                        AuthoritiesConstants.SSO_API_APP_CODE + "=" + AppConstants.APP_CODE + "&" +
                        AuthoritiesConstants.SSO_API_USERNAME + "=" + encryptor.encrypt(iUser.getUsername()))
                .json(jsonStr).asBean(Map.class);
        log.warn("-----接口返回值为【{}】", JacksonUtils.obj2json(resultMap));
        //若接口成功errorCode=0
        Map data = resultMap != null ? JacksonUtils.json2obj(JacksonUtils.obj2json(resultMap.get("data")), LinkedHashMap.class) : null;
        if (data != null && "0".equals(data.get("errorCode"))) {
            shortUrl = MapUtil.getStr(data, "destUrl");
        }
        return shortUrl;
    }


    @Override
    public Map<String, Object> getPhone(String token) {
        //-----------------------1、获取第三方登录时的手机号-----------------
        Map<String, Object> phoneMap = Maps.newHashMap();
        //远程调用zhfw接口
        IUser iUser = SecurityUtils.getCurrentUser();
        log.debug("开始进行获取电话号码");
        SimpleConfig simpleConfig = uumsSysAppConfigApi.findAppConfigByStyle("iservice", "hadmin", "common");
        Map resultMap = com.simbest.boot.util.http.client.HttpClient.post(simpleConfig.getAddress() + "/action/sms/getPhoneNum/sso")
                .param(AuthoritiesConstants.SSO_API_APP_CODE, AppConstants.APP_CODE)
                .param(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt("hadmin"))
                .param("appCode", AppConstants.APP_CODE)
                .param("token", token).asBean(Map.class);
        log.warn("-----接口返回值为【{}】", JacksonUtils.obj2json(resultMap));
        //若接口成功errorCode=0
        Map data = JacksonUtils.json2obj(JacksonUtils.obj2json(resultMap.get("data")), LinkedHashMap.class);
        String basePhone = "";
        if (data != null && "0".equals(data.get("errorCode"))) {
            basePhone = MapUtil.getStr(data, "basePhone");
        }
        //----------------------2、获取用户在系统中录入的电话----------------
        SimpleUser user = uumsSysUserinfoApi.findByKey(basePhone, IAuthService.KeyType.preferredMobile, "exam");
        String phoneNum = basePhone;
        String phone = rsaEncryptor.encrypt(phoneNum);
        if (user != null) {
            phoneMap.put("basePhone", phoneNum);
            phoneMap.put("phone", phone);
        }
        return phoneMap;
    }

    /**
     * 校验验证码
     *
     * @param phone phone
     * @param code  code
     * @return 校验结果
     */
    @Override
    public boolean checkVerifyCode(String phone, String code) {
        return RedisUtil.validateAppSMSCode(phone, code);
    }

}
