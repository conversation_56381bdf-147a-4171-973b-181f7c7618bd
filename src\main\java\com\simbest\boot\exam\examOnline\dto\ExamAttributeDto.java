package com.simbest.boot.exam.examOnline.dto;

import com.simbest.boot.exam.examOnline.model.ExamAttribute;
import com.simbest.boot.exam.examOnline.model.ExamAttributeQuestion;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @desc:
 * @date 2021/7/5  14:08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "试卷-题目传输试题")
public class ExamAttributeDto {
    //试卷信息
    private ExamAttribute examAttribute;

    //选择固定题目集合信息
    private List<ExamAttributeQuestion> examAttributeQuestions;

}
