package com.simbest.boot.exam.mainbills.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.mainbills.model.UsPmInstence;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface UsPmInstenceRepository extends LogicRepository<UsPmInstence,String> {

    String sql1 = "select * from us_pm_instence a WHERE a.pm_ins_id=:pmInsId";
    @Query(value = sql1,nativeQuery = true)
    UsPmInstence findByPmInsId(@Param("pmInsId") String pmInsId);


    @Query(value = "select t.* from US_PM_INSTENCE t INNER JOIN US_EXAM_WORK ue  on( t.pm_ins_id=ue.pm_ins_id) where ue.is_todo_flag=0",
            nativeQuery = true)
    List<UsPmInstence> findByPmInsIdTitle(@Param("title") String title, @Param("sign") String sign);

    @Transactional
    @Modifying
    @Query(
            value = "update us_pm_instence set enabled=0 where id = :id",
            nativeQuery = true
    )
    int deleteByFromId(@Param("id") Long id);

    /**
     * 获取当前待办的信息
     * @param username  当前登陆人
     * @param id        流程id
     * @return
     */
    @Query(
            value = "select t.process_inst_id , t.business_key , t.process_def_name , t.process_def_id , s.activity_def_id , t.current_state , t.receipt_code , t.id , s.work_item_id  from WF_WORKITEM_MODEL s ,  act_business_status t where  t.process_inst_id = s.process_inst_id and s.end_time is null and s.participant = :username and t.id = :id ",
            nativeQuery = true
    )
    Map<String, Object> getTodoParams(@Param("username") String username, @Param("id") String id);
}
