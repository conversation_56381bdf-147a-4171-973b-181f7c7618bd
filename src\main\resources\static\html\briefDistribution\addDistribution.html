<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>简报派发</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <!-- <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=${svn.revision}}" type="text/javascript"></script> -->

    <script src="../../js/kindeditor/kindeditor.min.js?v=-1" th:src="@{/js/kindeditor/kindeditor.min.js?v=${svn.revision}}" type="text/javascript"></script>

            <style>
                .chooseFrom{display: inline-block;width: 100px;height:25px;line-height: 25px;text-align: center;color:#fff;border-radius: 3px;font-size: 14px;cursor: pointer;margin-right: 10px;background: #0086CF;}
                .titleType{font-size: 16px;}
                .base,.users,.transpondUsers{border:solid 1px #E0E0E0;border-radius: 3px;margin-top: 10px;}
                .boxTop{height: 30px;line-height: 30px;background: #F5F5F5;padding:0 10px;cursor: pointer;}
                .userBox{width: 220px;border: solid 1px #71c8f8;display: inline-block;padding:10px;border-radius: 3px;background: rgb(214, 228, 246);position: relative;cursor: pointer;margin: 10px 20px;vertical-align: top}
                .userBox2{width: 95%;}

                .otherBox{width: 210px;border: solid 1px #71c8f8;display: inline-block;padding:10px;border-radius: 3px;background: rgb(214, 228, 246);position: relative;margin: 10px 20px;vertical-align: top;cursor: pointer;}
                .icon-guanbi{position: absolute;right: -11px;top: -11px;color:red;width: 16px;height: 16px;}
                .userBox>p{overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
                .addUserBtn{height: 30px;line-height: 30px;width: 100px;color:#fff;background:#9CC715;display:none;border-radius: 3px;text-align: center;margin-right: 20px;cursor: pointer;}
                .uploadImage{width: 300px; overflow:hidden; text-overflow:ellipsis;}
                .uploadImageI{width: 300px; overflow:hidden; text-overflow:ellipsis;}
                .addressee{padding:20px 0 10px 15px;}
                .otherBox p{ overflow:hidden;white-space: nowrap;text-overflow: ellipsis;}
            </style>
            <script type="text/javascript">
                var msgOrgList = [];        //选择的组织
                var msgPositionList = [];   //选择职位
                var msgUsers = [];          //选择人员
                var msgGroupList = [];     //选择分组
                var sendTypeFlag = 'person';//发送方式标志
                var isChange = false;//是否是手动切换发送方式
                getCurrent()
                var gps = getQueryString();
                $(function () {
                    // if(gps.type=='transpond'){//转发模式
                    //     $("#infoTableAddForm").attr('cmd-update',"network/message/forwardMessage");
                    // }
                    loadForm("infoTableAddForm");
                    // $('.textareaContent').hide();//隐藏

                    if(gps.mytype == 'edit'){
                        $(".save").hide()
                        $(".transpond").hide()
                        $(".edit").show()
                    }
                    if(gps.mytype == 'transpond'){
                        $(".save").hide()
                        $(".edit").hide()
                        $(".transpond").show()
                    }

                    pageparam={
                        "listtable":{
                            "listname":"#groupUserTable",//table列表的id名称，需加#
                            "data":[],
                            "nowrap": true,//把数据显示在一行里,默认true
                            "styleClass":"noScroll",
                            "fitColumns":true,
                            "frozenColumns":[],//固定在左侧的列
                            "columns":[[//列
                                { title: "账号", field: "username", width: 15, align:'center'},
                                { title: "姓名", field: "truename", width: 15, align:'center'},
                                { title: "接收手机号", field: "preferredMobile", width: 20, align:'center',
                                    formatter:function (value,row,index) {
                                        if(row.receivePhoneNum){
                                            return row.receivePhoneNum;
                                        }else{
                                            return row.preferredMobile;
                                        }
                                    }
                                },
                                { title: "所属分组", field: "parentName", width: 15, align:'center'},
                                { title: "所属组织", field: "orgDisplayName", width: 22, align:'center'},
                                { title: "操作", field: "op", width: 15, align:'center',
                                    formatter:function (value,row,index) {
                                        var g=
                                            "<a href='#' class='operateBlue updateothers' displayOrder='"+index+"' username='"+row.username+"' truename='"+row.truename+"' preferredMobile='"+row.preferredMobile+"' receivePhoneNum='"+row.receivePhoneNum+"' parentName='"+row.parentName+"' orgDisplayName='"+row.orgDisplayName+"'>编辑</a>"
                                            +"<span class='operateLine'>|</span>"
                                            +"<a  href='#' class='del operateRed' onclick='deleteBase("+index+")' deleteid='" + row.id + "'>删除</a>";
                                        var gdel = "<a  href='#' class='del operateRed' onclick='deleteBase("+index+")' deleteid='" + row.id + "'>删除</a>";
                                        if(row.userType==1){
                                            return gdel;
                                        }else{
                                            return g;
                                        }
                                    }},
                            ]],
                            "pagination":false
                        },
                    };
                    pageparam.listtable.data = {data:[]};
                    loadGrid(pageparam);
                    if(gps.id){
                        getDetail();
                    }
                    if(gps.type=='read'){
                        formReadonly("infoTableAddForm");
                        $(".chooseFrom").hide();
                        $('.delete').remove();
                    }
                    //添加人员
                    $(".addOtherPerson").on('click',function (){
                        var users = '';
                        for (var i = 0; i < msgUsers.length; i++) {
                            users = users +','+ msgUsers[i].username;
                        }
                        if(gps.mytype == 'transpond'){
                            top.orgCode = web.currentUser.belongOrgCode
                        }else{
                            top.orgCode = ''
                        }
                        if (sendTypeFlag != 'person') {
                            if (onSelectSubmit()) {
                                $.messager.confirm('确认','警告，选择其他方式发送信息将会清空已选接收方',function(r){
                                    if (r){
                                        top.dialogP('html/message/infopage/addOtherPerson.html?hasChoosed='+users, window.name, '选择人员', 'addOtherPerson', false, 700, 400);
                                    }
                                });
                            }else{
                                top.dialogP('html/message/infopage/addOtherPerson.html?hasChoosed='+users, window.name, '选择人员', 'addOtherPerson', false, 700, 400);
                            }
                        }else{
                            top.dialogP('html/message/infopage/addOtherPerson.html?hasChoosed='+users, window.name, '选择人员', 'addOtherPerson', false, 700, 400);
                        }
        
                    });
        
                    //添加组织
                    $(".addOtherOrg").on('click',function (){
                        if (sendTypeFlag != 'org') {
                            if (onSelectSubmit()) {
                                $.messager.confirm('确认','警告，选择其他方式发送信息将会清空已选接收方',function(r){
                                    if (r){
                                        top.dialogP('html/message/infopage/addOtherOrg.html?', window.name, '选择组织', 'addOtherOrg', false, 700, 400);
                                    }
                                });
                            }else{
                                top.dialogP('html/message/infopage/addOtherOrg.html?', window.name, '选择组织', 'addOtherOrg', false, 700, 400);
                            }
                        }else{
                            top.dialogP('html/message/infopage/addOtherOrg.html?', window.name, '选择组织', 'addOtherOrg', false, 700, 400);
                        }
                    });
        
                    //添加分组
                    $(".addOtherGroup").on('click',function (){
                        var users = '';
                        for (var i = 0; i < msgUsers.length; i++) {
                            users = users +','+ msgUsers[i].username;
                        }
                        if (sendTypeFlag != 'group') {
                            if (onSelectSubmit()) {
                                $.messager.confirm('确认','警告，选择其他方式发送信息将会清空已选接收方',function(r){
                                    if (r){
                                        top.dialogP('html/message/infopage/chooseGroup.html?hasChoosed='+users, window.name, '选择分组', 'addOtherGroup', false, 700, 400);
                                    }
                                });
                            }else{
                                top.dialogP('html/message/infopage/chooseGroup.html?hasChoosed='+users, window.name, '选择分组', 'addOtherGroup', false, 700, 400);
                            }
                        }else{
                            top.dialogP('html/message/infopage/chooseGroup.html?hasChoosed='+users, window.name, '选择分组', 'addOtherGroup', false, 700, 400);
                        }
        
                    });
        
                    //添加职位
                    $(".addOtherPosition").on('click',function (){
                        if (sendTypeFlag != 'position') {
                            if (onSelectSubmit()) {
                                $.messager.confirm('确认','警告，选择其他方式发送信息将会清空已选接收方',function(r){
                                    if (r){
                                        top.dialogP('html/message/infopage/addOtherPosition.html?=', window.name, '选择职位', 'addOtherPosition', false, 700, 400);
                                    }
                                });
                            }else{
                                top.dialogP('html/message/infopage/addOtherPosition.html?=', window.name, '选择职位', 'addOtherPosition', false, 700, 400);
                            }
                        }else{
                            top.dialogP('html/message/infopage/addOtherPosition.html?=', window.name, '选择职位', 'addOtherPosition', false, 700, 400);
                        }
                    });
        
                    $(document).on('click','.updateothers',function (){
                        var username = $(this).css("username");
                        var truename = $(this).css("truename");
                        var preferredMobile = $(this).css("preferredMobile");
                        var receivePhoneNum = $(this).css("receivePhoneNum");
                        var parentName = $(this).css("parentName");
                        var orgDisplayName = $(this).css("orgDisplayName");
                        var displayOrder = $(this).css("displayOrder");
                        top.dialogP('html/message/infopage/addOtherPerson.html?type=update&username='+username+'&displayOrder='+displayOrder+'&receivePhoneNum='+receivePhoneNum, window.name, '编辑人员', 'addOtherPerson', false, 700, 400);
                    });
        
                    //人员窗口回调
                    window.addOtherPerson = function (data){
                        var choose = data.data;
                        //清空其他方式选择的收信方
                        sendTypeFlag = "person";
                        msgOrgList = [];
                        msgPositionList = [];
                        //遍历选择的数据和已有数据是否重复
                        for (var i = 0; i < choose.length; i++) {
                            for (var j = 0; j < msgUsers.length; j++) {
                                if (choose[i].username == msgUsers[j].username) {
                                    msgUsers.splice(j,1);
                                    j--;
                                }
                            }
                        }
                        msgUsers = msgUsers.concat(choose);
                        getUsers("person",msgUsers);
                    };
        
                    //按职位添加
                    window.addOtherPosition = function(data){
                        var choose = data.data;
                        //清空其他方式选择的收信方
                        sendTypeFlag = "position";
                        msgOrgList = [];
                        msgUsers = [];
                        msgGroupList = [];
                        //遍历选择的数据和已有数据是否重复
                        for (var i = 0; i < choose.length; i++) {
                            for (var j = 0; j < msgPositionList.length; j++) {
                                if (choose[i].orgCode == msgPositionList[j].orgCode) {
                                    msgPositionList.splice(j,1);
                                    j--;
                                }
                            }
                        }
                        msgPositionList = msgPositionList.concat(choose);
                        getUsers("position",msgPositionList);
                    };
        
                    //按组织添加
                    window.addOtherOrg = function(data){
                        var choose = data.data;
                        //清空其他方式选择的收信方
                        sendTypeFlag = "org";
                        msgGroupList = [];
                        msgUsers = [];
                        msgPositionList = [];
                        //遍历选择的数据和已有数据是否重复
                        for (var i = 0; i < choose.length; i++) {
                            for (var j = 0; j < msgOrgList.length; j++) {
                                if (choose[i].orgCode == msgOrgList[j].orgCode) {
                                    msgOrgList.splice(j,1);
                                    j--;
                                }
                            }
                        }
                        msgOrgList = msgOrgList.concat(choose);
                        getUsers("org",msgOrgList);
                    };
        
                    //按分组添加
                    window.addOtherGroup = function(data){
                        var choose = data.data;
                        //清空其他方式选择的收信方
                        sendTypeFlag = "group";
                        msgOrgList = [];
                        msgUsers = [];
                        msgPositionList = [];
                        //遍历选择的数据和已有数据是否重复
                        for (var i = 0; i < choose.length; i++) {
                            for (var j = 0; j < msgGroupList.length; j++) {
                                if (choose[i].username) {
                                    if (choose[i].username == msgGroupList[j].username) {
                                        msgGroupList.splice(j,1);
                                        j--;
                                    }
                                }else{
                                    if (choose[i].groupId == msgGroupList[j].groupId) {
                                        msgGroupList.splice(j,1);
                                        j--;
                                    }
                                }
        
                            }
                        }
                        msgGroupList = msgGroupList.concat(choose);
                        getUsers("group",msgGroupList);
                    };
        
                    //按组织查看
                    window.readOrg = function(data){
                        console.log(data);
                    };
                    //按职位查看
                    window.readPosition = function(data){
                        console.log(data);
        
                    };
                    //按分组查看
                    window.readGroup = function(data){
                        console.log(data);
                    };
        
                    $("#sendType").combobox({//根据选择框动态改变选人接口
                        onLoadSuccess:function(param){
                            //gps.id存在说明时查看或编辑，此时第一次进页面不执行onchange
                            if(gps.id){
                                isChange = false;
                            }else{//没有id说明时新建消息，给默认值'按人员发送'
                                $('#sendType').combobox('setValue', 'person');
                            }
                        },
                        onSelect:function(param,v){
                            isChange = true;//手动选择时，isChange改为true
                        },
                        onChange:function (newVal,oldVal) {
                            $(".addOtherGroup").css("display","none");
                            $(".addOtherOrg").css("display","none");
                            $(".addOtherPosition").css("display","none");
                            $(".addOtherPerson").css("display","none");
                            // if (newVal == 'person') {
                            //     $(".addOtherPerson").css("display","inline-block");
                            // }else if(newVal == 'org'){
                            //     $(".addOtherOrg").css("display","inline-block");
                            // }else if(newVal == 'position'){
                            //     $(".addOtherPosition").css("display","inline-block");
                            // }else if(newVal == 'group'){
                            //     $(".addOtherGroup").css("display","inline-block");
                            // }

                            
                            if (newVal == 'person') {
                                //置空
                                msgOrgList = [];        //选择的组织
                                msgPositionList = [];   //选择职位
                                msgUsers = [];          //选择人员
                                msgGroupList = [];     //选择分组
                                $('.usersBox').html('');
                                $(".addOtherPerson").css("display","inline-block");
                            }else{
                                //置空
                                msgOrgList = [];        //选择的组织
                                msgPositionList = [];   //选择职位
                                msgUsers = [];          //选择人员
                                msgGroupList = [];     //选择分组
                                $('.usersBox').html('');
                                ajaxgeneral({
                                    url: 'action/organizationValue/findList',
                                    success: function (res) {
                                        getUsers(newVal,res.data,null)
                                    }
                                });
                            }

                          



                            // setTimeout(function () {
                            //     if(isChange){
                            //         //如果当前选中的有值
                            //         if(onSelectSubmit()){
                            //             top.mesConfirm('提示','警告，选择其他方式发送信息将会清空已选接收方',function(){
                            //                 //置空
                            //                 msgOrgList = [];        //选择的组织
                            //                 msgPositionList = [];   //选择职位
                            //                 msgUsers = [];          //选择人员
                            //                 msgGroupList = [];     //选择分组
                            //                 $('.usersBox').html('');
                            //             },function () {
                            //                 isChange = false;
                            //                 //如果取消的话，把下拉的值恢复为原来的值
                            //                 $("#sendType").combobox('setValue', oldVal);
                            //                 $(".addOtherGroup").css("display","none");
                            //                 $(".addOtherOrg").css("display","none");
                            //                 $(".addOtherPosition").css("display","none");
                            //                 $(".addOtherPerson").css("display","none");
                            //                 // if (oldVal == 'person') {
                            //                 //     $(".addOtherPerson").css("display","inline-block");
                            //                 // }else if(oldVal == 'org'){
                            //                 //     $(".addOtherOrg").css("display","inline-block");
                            //                 // }else if(oldVal == 'position'){
                            //                 //     $(".addOtherPosition").css("display","inline-block");
                            //                 // }else if(oldVal == 'group'){
                            //                 //     $(".addOtherGroup").css("display","inline-block");
                            //                 // }

                            //                 if (newVal == 'person') {
                            //                     $(".addOtherPerson").css("display","inline-block");
                            //                 }else{
                            //                     ajaxgeneral({
                            //                         url: 'action/organizationValue/findList',
                            //                         success: function (res) {
                            //                             console.log(res);
                            //                             getUsers(newVal,res.data,null)
                            //                         }
                            //                     });
                            //                 }
                            //             });
                            //         }
                            //     }
                            // },50)
                        }
                    })
        
                    //提交
                    $(".save").on('click',function () {
                        // $('.hideContent').remove();
                        if(formValidate("infoTableAddForm")){
                            demoHidden(['save'],true);
                            formsubmit("infoTableAddForm");
                            setTimeout(function(){
                                top.window.insertDistribution.window.dialogClosed()
                            },1000)
                        }else{
                            demoHidden(['save'],false);
                        }
                    });


                    // 编辑
                    $(".edit").on('click',function () {
                        if(formValidate("infoTableAddForm")){
                            var data = getFormValue("infoTableAddForm")
                            ajaxgeneral({
                                url:"action/applyForm/update?source=PC",
                                data:data,
                                contentType:"application/json; charset=utf-8",
                                success:function(datas){
                                    top.window.insertDistribution.window.dialogClosed()
                                }
                            });
                        }
                    });

                    // 转办
                    $(".transpond").on('click',function () {
                        if(formValidate("infoTableAddForm")){
                            var data = getFormValue("infoTableAddForm")
                            ajaxgeneral({
                                url:"action/applyForm/forward?id="+ data.id + '&userStr=' + data.receiveUsers + '&sendType=' + data.sendType,
                                success:function(datas){
                                    top.window.insertDistribution.window.dialogClosed()
                                }
                            });
                        }
                    });

                    //关闭
                    $(".formCloseMy").on('click',function () {
                        top.window.insertDistribution.window.dialogClosed()
                    });
        
                    //下拉折叠
                    $(".boxTop").on('click',function () {
                        if($(this).find('.iconfont').hasClass('icon-shang')){
                            $(this).find('.iconfont').removeClass("icon-shang").addClass('icon-xia');
                            $(this).next().show();
                        }else{
                            $(this).find('.iconfont').removeClass("icon-xia").addClass('icon-shang');
                            $(this).next().hide();
                        }
                    });
        
                    //删除选择人员
                    $(document).on('click','.delete',function (e) {
                        e.stopPropagation();
                        $(this).parent().remove();
                        if (sendTypeFlag == 'person') {
                            msgUsers.splice(e.target.id,1);
                            getUsers(sendTypeFlag,msgUsers);
                        }else if(sendTypeFlag == 'position'){
                            msgPositionList.splice(e.target.id,1);
                            getUsers(sendTypeFlag,msgPositionList);
                        }else if(sendTypeFlag == 'org'){
                            msgOrgList.splice(e.target.id,1);
                            getUsers(sendTypeFlag,msgOrgList);
                        }else if(sendTypeFlag == 'group'){
                            msgGroupList.splice(e.target.id,1);
                            getUsers(sendTypeFlag,msgGroupList);
                        }
        
                    });
        
                    // //点击用户弹窗
                    // $(document).on('click','.updateUser',function (){
                    //     if(gps.type=='read'){
                    //         return false;
                    //     }else{
                    //         var username = $(this).attr("username");
                    //         var truename = $(this).attr("truename");
                    //         var preferredMobile = $(this).attr("preferredMobile");
                    //         var receivePhoneNum = $(this).attr("receivePhoneNum");
                    //         var parentName = $(this).attr("parentName");
                    //         var orgDisplayName = $(this).attr("orgDisplayName");
                    //         var displayOrder = $(this).attr("displayOrder");
                    //         top.dialogP('html/message/infopage/updateOtherPerson.html?type=update&username='+username+'&displayOrder='+displayOrder+'&receivePhoneNum='+receivePhoneNum, window.name, '编辑人员', 'addOtherPerson', false, 700, 580);
                    //     }
                    // });
        
                    //点击组织弹窗
                    $(document).on('click','.updateOrg',function (){
                        var orgId = $(this).attr("orgId");
                        var id = $(this).attr("id");
                        top.dialogP('html/message/infopage/personList.html?orgId='+orgId+'&id='+id+'&type='+gps.type, window.name, '查看组织', 'readOrg', false, 700, 500);
                    });
        
                    //点击职位弹窗
                    $(document).on('click','.updatePosition',function (){
                        var positionId = $(this).attr("positionId");
                        var id = $(this).attr("id");
                        top.dialogP('html/message/infopage/personList.html?positionId='+positionId+'&id='+id+'&type='+gps.type, window.name, '查看职位', 'readPosition', false, 700, 500);
                    });
        
                    //点击分组弹窗
                    $(document).on('click','.updateGroup',function (){
                        var groupId = $(this).attr("groupId");
                        var id = $(this).attr("id");
                        top.dialogP('html/message/infopage/personList.html?groupId='+groupId+'&id='+id+'&type='+gps.type, window.name, '查看分组', 'readGroup', false, 700, 500);
                    });
                });
              
                function deleteBase(e) {
                    $("#groupUserTable").datagrid("deleteRow",e);
                    //删除一行后重新加载一次表格，解决deleteRow方法删除第一行后行号错位bug
                    var rows=$("#groupUserTable").datagrid('getRows');
                    pageparam.listtable.data.data = rows;
                    loadGrid(pageparam);
                };
                function getDetail() {
                    if(gps.come){
                        $(".users").remove();
                    }
                    ajaxgeneral({
                        url: "action/applyForm/getById?id="+gps.id,
                        contentType: "application/json; charset=utf-8",
                        success: function (res) {
                            // res.data.sendType = 'person'
                            // console.log(res.data);

                            formval(res.data,"infoTableAddForm");
                            $('#sendType').combobox('setValue',res.data.sendType);
                            //如果是转发，隐藏状态
                            if(gps.type=='transpond'){
                                $('.transpondHide').hide();
                                $(".save").show();
                                //根据发送类型获取要渲染的数据
                                sendTypeFlag = res.data.sendType;
                                if (sendTypeFlag == 'person') {
                                    readData = res.data.msgUsers;
                                    msgUsers = res.data.msgUsers;
                                }else if(sendTypeFlag == 'position'){
                                    readData = res.data.msgPositionList;
                                    msgPositionList = res.data.msgPositionList;
                                }else if(sendTypeFlag == 'org'){
                                    readData = res.data.msgOrgList;
                                    msgOrgList = res.data.msgOrgList;
                                }else if(sendTypeFlag == 'group'){
                                    readData = res.data.msgGroupList;
                                    var readDataUsers = res.data.msgUsers;
                                    msgGroupList = res.data.msgGroupList;
                                }
                                getUsers(sendTypeFlag,readData,readDataUsers);
                            }else{
                                //根据发送类型获取要渲染的数据
                                sendTypeFlag = res.data.sendType;
                                if (sendTypeFlag == 'person') {
                                    // readData = res.data.msgUsers;
                                    // msgUsers = res.data.msgUsers;
                                    readData = res.data.userList;
                                    msgUsers = res.data.userList;
                                }else if(sendTypeFlag == 'position'){
                                    readData = res.data.msgPositionList;
                                    msgPositionList = res.data.msgPositionList;
                                }else if(sendTypeFlag == 'org'){
                                    readData = res.data.msgOrgList;
                                    msgOrgList = res.data.msgOrgList;
                                }else if(sendTypeFlag == 'group'){
                                    readData = res.data.msgGroupList;
                                    var readDataUsers = res.data.msgUsers;
                                    msgGroupList = res.data.msgGroupList;
                                }
                                getUsers(sendTypeFlag,readData,readDataUsers);
                            }
                  
                                // $('.textareaContent').show();
                                // $('.kindeditor').addClass('hideContent');
                                // $('.textareaContent').removeClass('hideContent');
                            
                        }
                    });
                };
        
                function beforeSubmit(data) {//提交数据
                    data.sendType = $('#sendType').combobox('getValue');
                    // data.msgPositionList = msgPositionList;//选择的职位信息
                    // data.msgUsers = msgUsers;//选择的人员信息
                    // data.msgOrgList = msgOrgList;//选择的组织信息
                    // data.msgGroupList = msgGroupList;//选择的分组信息
                    var receiveUsers =''
                    if(msgUsers.length > 0){
                        for (var i = 0; i < msgUsers.length; i++) {
                            receiveUsers += msgUsers[i].username + ',';
                        }
                        data.receiveUsers = receiveUsers.substring(0, receiveUsers.length - 1);  
                    }

                    if(msgGroupList.length > 0){
                        for (var i = 0; i < msgGroupList.length; i++) {
                            receiveUsers += msgGroupList[i].userName + ',';
                        }
                        data.receiveUsers = receiveUsers.substring(0, receiveUsers.length - 1);
                    }

                    if(msgUsers.length==0&&msgGroupList.length == 0){
                        top.mesAlert("提示", "请选择接收人", 'warning');
                        return false;
                    }else{
                        return true;
                    }
                };
        
                //在选择收信人前判断当前是否有收信人
                function onSelectSubmit() {
                    if (msgOrgList.length != 0) {
                        return true;
                    }
                    if (msgPositionList.length != 0) {
                        return true;
                    }
                    if (msgUsers.length != 0) {
                        return true;
                    }
                    if (msgGroupList.length != 0) {
                        return true;
                    }
                    return false;
                }
        
        
                //成功提交后关闭页面
                function submitcallback() {
                    setTimeout(function () {
                        demoHidden(['save'],false);
                        window.opener.loadList();
                        window.winClose();
                    },2000)
                    demoHidden(['save'],true);
                };
        
                // 好像是为了防止二次点击
                function demoHidden(arr,type){
                    for(var i=0;i<arr.length;i++){
                        if(type==true){
                            $("."+arr[i]).css({"pointer-events":"none",'opacity':'0.6','-khtml-opacity':'0.6','-moz-opacity':'0.6','filter':'alpha(opacity=60)',"filter":'progid:DXImageTransform.Microsoft.Alpha(opacity=70)'});
                        }else{
                            $("."+arr[i]).css({"pointer-events":"auto",'opacity':'1','-khtml-opacity':'1','-moz-opacity':'1','filter':'alpha(opacity=100)',"filter":'progid:DXImageTransform.Microsoft.Alpha(opacity=100)'});
                        }
                    }
                };
        
        
                function getUsers(type,data,readDataUsers){
                    if (type == 'group') {//先对数组排序使分组在前人员在后
                        var groupUser = [];
                        var groupList = [];
                        for (var groupIndex = 0; groupIndex < data.length; groupIndex++) {
                            if (data[groupIndex].dataType == 'user') {
                                groupUser.push(data[groupIndex]);
                            }else{
                                groupList.push(data[groupIndex]);
                            }
                        }
                        data = groupUser.concat(groupList);
                        msgGroupList = data;
                    }
                    $('.usersBox').html('');
                    for (var i = 0; i < data.length; i++) {
                        var myarr = []
                        if(type=='position'){//职位
                            var html = "<div class=\"otherBox updatePosition\" id='"+data[i].id+"'  positionId='"+data[i].positionId+"' preferredMobile='"+data[i].preferredMobile+"' receivePhoneNum='"+data[i].receivePhoneNum+"' orgDisplayName='"+data[i].orgDisplayName+"' displayOrder='"+i+"'>\n" +
                                "                <i id='"+i+"' class=\"iconfont icon-guanbi delete\"></i>\n" +
                                "                <p>职务名称：<span class=\"name\">"+data[i].positionName+"</span></p>\n" +
                                "                <p>公司职位：<span class=\"companyName\">"+data[i].positionCompanyName+"</span></p>\n" +
                                "            </div>";
                        }else if(type=='group'){//分组
                            if (data[i].groupId) {//判断后对分组和人员分开渲染
                                var html = "<div class=\"otherBox updateGroup\" id='"+data[i].id+"'  groupId='"+data[i].groupId+"'>\n" +
                                    "                <i id='"+i+"' class=\"iconfont icon-guanbi delete\"></i>\n" +
                                    "                <p>组织名称：<span class=\"name\">"+data[i].groupName+"</span></p>\n" +
                                    "            </div>";
                            }else{
                                //领导层手机号不显示****
                                var receivePhoneNum = data[i].receivePhoneNum?data[i].receivePhoneNum:data[i].preferredMobile;
                                receivePhoneNum = data[i].belongDepartmentCode=='4772338661636601428'?'***********':receivePhoneNum;

                               
                                for(var i in data){
                                    var item = data[i]
                                    myarr.push(item.userTrueName)
                                }



                                var html = "<div class=\"userBox userBox2 updateUser\"  username='"+data[i].username+"' truename='"+data[i].userTrueName+"' preferredMobile='"+data[i].preferredMobile+"' receivePhoneNum='"+data[i].receivePhoneNum+"' parentName='"+data[i].parentName+"' orgDisplayName='"+data[i].orgDisplayName+"' displayOrder='"+i+"'>\n" +
                                    // "                <i id='"+i+"' class=\"iconfont icon-guanbi delete\"></i>\n" +
                                    "                <p id=\"dd\">姓名：<span class=\"name\">"+myarr.join()+"</span></p>\n" +
                                    // "                <p>组织：<span class=\"org\">"+data[i].orgDisplayName+"</span></p>\n" +
                                    // "                <p>手机号：<span class=\"preferredMobile\">"+receivePhoneNum+"</span></p>\n" +
                                    "            </div>";
                            }
                        }else if(type=='person'){//人
                            //领导层手机号不显示****
                            var receivePhoneNum = data[i].receivePhoneNum?data[i].receivePhoneNum:data[i].preferredMobile;
                            receivePhoneNum = data[i].belongDepartmentCode=='4772338661636601428'?'***********':receivePhoneNum;
                            var belongDepartmentName = data[i].orgDisplayName?data[i].orgDisplayName:data[i].belongDepartmentName;
                            var html = "<div class=\"userBox updateUser\" username='"+data[i].username+"' truename='"+data[i].truename+"' preferredMobile='"+data[i].preferredMobile+"' receivePhoneNum='"+data[i].receivePhoneNum+"' parentName='"+data[i].parentName+"' orgDisplayName='"+data[i].orgDisplayName+"' displayOrder='"+i+"'>\n" +
                                "                <i id='"+i+"' class=\"iconfont icon-guanbi delete\"></i>\n" +
                                "                <p>姓名：<span class=\"name\">"+data[i].truename+"</span></p>\n" +
                                // "                <p>组织：<span class=\"org\">"+data[i].orgDisplayName?data[i].orgDisplayName:data[i].belongDepartmentName+"</span></p>\n" +
                                "                <p>组织：<span class=\"org\">"+belongDepartmentName+"</span></p>\n" +
                                "                <p>手机号：<span class=\"preferredMobile\">"+receivePhoneNum+"</span></p>\n" +
                                "            </div>";
                        }else if(type=='org'){//组织
                            var html = "<div class=\"otherBox updateOrg\" id='"+data[i].id+"' orgId='"+data[i].orgCode+"' orgCode='"+data[i].orgCode+"'  orgName='"+data[i].orgName+"' displayOrder='"+i+"'>\n" +
                                "                <i id='"+i+"' class=\"iconfont icon-guanbi delete\"></i>\n" +
                                "                <p>组织名称：<span class=\"name\">"+data[i].orgName+"</span></p>\n" +
                                "            </div>";
                        }
                        $('.usersBox').prepend(html);
                        $('#dd').tooltip({    
                            position: 'bottom',    
                            content: '<span style="color:#fff">'+myarr+'</span>',    
                            onShow: function(){        
                                $(this).tooltip('tip').css({backgroundColor: '#666', borderColor: '#666' });
                            }
                        });
                    }
        
                    //如果readDataUsers有值，说明按分组发送中有单独人员存在)
                    console.log(readDataUsers)
                    if(readDataUsers){
                        for (var i = 0; i < readDataUsers.length; i++) {
                            //领导层手机号不显示****
                            var receivePhoneNum = readDataUsers[i].receivePhoneNum?readDataUsers[i].receivePhoneNum:readDataUsers[i].preferredMobile;
                            receivePhoneNum = readDataUsers[i].belongDepartmentCode=='4772338661636601428'?'***********':receivePhoneNum;
                            var htmlusers = "<div class=\"userBox updateUser\" username='"+readDataUsers[i].username+"' truename='"+readDataUsers[i].truename+"' preferredMobile='"+readDataUsers[i].preferredMobile+"' receivePhoneNum='"+readDataUsers[i].receivePhoneNum+"' parentName='"+readDataUsers[i].parentName+"' orgDisplayName='"+readDataUsers[i].orgDisplayName+"' displayOrder='"+i+"'>\n" +
                                "                <i id='"+i+"' class=\"iconfont icon-guanbi delete\"></i>\n" +
                                "                <p>姓名：<span class=\"name\">"+readDataUsers[i].truename+"</span></p>\n" +
                                "                <p>组织：<span class=\"org\">"+readDataUsers[i].orgDisplayName+"</span></p>\n" +
                                "                <p>手机号：<span class=\"preferredMobile\">"+receivePhoneNum+"</span></p>\n" +
                                "            </div>";
                        }
                        $('.usersBox').append(htmlusers);
                    }
                    if(gps.type=='read'){
                        formReadonly("infoTableAddForm");
                        idReadonly('sendType');
                        $(".addUserBtn").hide();
                        $('.delete').remove();
                    }
                }
        
            </script>
        </head>
        <!--<body class="body_page">-->
        <body class="body_page" style="padding-top:62px;">
        <!-- 原确定按钮位置 -->
        <input id="id" name="id" type="hidden" />
        <div class="users">
            <div class="boxTop">收信人<span><i class='fr iconfont down icon-xia'></i></span></div>
            <div class="addressee">
                <span>发送方式：</span>
                <!-- <input id="sendType" name="sendType" class="easyui-combobox " style="width:150px; height: 32px;" data-options="
                    valueField: 'value',
                    panelHeight:'auto',
                    textField: 'name',
                    editable:false,
                    queryParams:{'dictType':'sendType'},
                    contentType:'application/json; charset=utf-8',
                    url:web.rootdir+'sys/dictValue/findDictValue'" /> -->
            
                    <input id="sendType" name="sendType" class="easyui-combobox " style="width:150px; height: 32px;" data-options="
                        valueField: 'value',
                        panelHeight:'auto',
                        textField: 'name',
                        editable:false,
                        data:[{name:'按人员发送',value:'person'},{name:'按分组发送',value:'group'},]" />
                


                <div class="addUserBtn addOtherGroup">选择分组</div>
                <div class="addUserBtn addOtherOrg">选择组织</div>
                <div class="addUserBtn addOtherPosition">选择职位</div>
                <div class="addUserBtn addOtherPerson">添加人员</div>
                <div style="margin: 10px;">
                    <div class="usersBox"></div>
                </div>
            </div>
        
        </div>
        <form id="infoTableAddForm" cmd-insert="action/applyForm/create" cmd-update="action/applyForm/update"
              contentType="application/json; charset=utf-8" cmd-select="network/message/pcFindMsgById" beforeSubmit="beforeSubmit" submitcallback="submitcallback">
            <!-- 消息信息 -->
            <div class="base">
                <div class="boxTop">基础信息 <span><i class='fr iconfont down icon-xia'></i></span></div>
                <table border="0" cellpadding="0" cellspacing="10" width="100%">
                    <input name="id" id="id" style="display: none;"/>
                    <tr>
                        <td width="30" align="right"><font class="col_r">*</font>标题：</td>
                        <td width="100">
                            <input class="easyui-validatebox" name="title" id="title" required style="width: 300px;"/>
                        </td>
                        <td class="transpondHide" width="30" align="right"><font class="col_r">*</font>状态：</td>
                        <td class="transpondHide" width="100">
                            <input id="status" name="status" type="text" class="cselectorRadio" values="2|待发送,0|立即发送" required/>
                        </td>
                        <!-- <td width="50" align="right"><font class="col_r">*</font>短信小程序：</td>
                        <td width="100">
                            <input id="smsAppFlag" name="smsAppFlag" type="text" class="cselectorRadio" values="true|是,false|否" conchange="changeSmsAppFlag" required/>
                        </td> -->
                    </tr>
                    <tr>
                        <td width="30" align="right" style="vertical-align: top;padding-top: 10px;"><font class="col_r">*</font>内容：</td>
                        <td width="400" colspan="5" id="contentBox">
                            <textarea id="content" name="content" class="kindeditor"  style="width:95%;min-height:550px;" required="true"></textarea>
                            <!-- <textarea id="content" name="content" class="textareaContent hideContent"  style="width:95%;min-height:550px;" required="false"></textarea> -->
                        </td>
                    </tr>
                    <!-- <tr>
                        <td align="right" width="30" valign="top"  class="lh32" style="vertical-align: top;">上传附件：</td>
                        <td colspan="0" valign="top">
                            <input id="sysFiles" name="sysFiles" type="text" file="true" mulaccept="true" multiple="true" isDisk="true"
                                   class="cselectorImageUpload" btnmsg="<i class='iconfont'
                               title='添加'>&#xe641;</i>"
                                   href="sys/file/uploadProcessFiles"/>
                        </td>
                    </tr> -->
                </table>
            </div>
        
        
            <!-- 视口外阴影效果 -->
            <div style="position: relative;display: none;">
                <div style="margin: -35px 20px 0;position: absolute;right: 20px;">
                    <span href="#" class="chooseFrom chooseGroup">选择分组</span>
                    <span href="#" class="chooseFrom ml10 addOtherPerson">添加人员</span>
                </div>
                <div class="groupUserTable" style="margin: 50px 20px 0 20px;">
                    <table id="groupUserTable"></table>
                </div>
            </div>
        
            <!-- 确定提交按钮 -->
            <div class="pageInfo2">
                <a class="btn a_r4 mr10 a_blue transpond hide">转办</a>
                <a class="btn a_r4 mr10 a_blue save">确定</a>
                <a class="btn a_r4 mr10 a_blue edit hide">确定</a>
                <a class="btn formCloseMy a_none">关闭</a>
            </div>
        </form>
        </body>
        </html>