<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8" />
<title>播放视频</title>
<!--引用百度地图API-->
<style type="text/css">
	html,body,*{margin:0;padding:0;box-sizing:border-box;}
	video{max-width:100%;}
</style>
</head>
<body onload="initVideo();">
</body>
<script type="text/javascript">
	function getParam(name) {
		return location.href.match(new RegExp('[?&]' + name + '=([^?&]+)', 'i')) ? decodeURIComponent(RegExp.$1) : '';
	}
	var srcParam = getParam('url');
	var widthParam = getParam('width');
	var heightParam = getParam('height');
	// var u = navigator.userAgent;
	// var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
	// var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
	// if(isiOS || isAndroid){
	// 	widthParam=window.screen.availWidth-(20 * (window.screen.availWidth / 320))-2;
	// }
	if(location.href.indexOf("hfx.net/")>-1){
		var ht=location.href.split("/simbestui/")[0];
		srcParam=srcParam.replace("http://************:8088",ht);
		widthParam=document.body.clientWidth-3;
		heightParam-=20;
	}
	//创建视频：
	function initVideo(){		
		var html='<video webkit-playsinline="true" playsinline="true" x5-video-player-type="h5" x5-video-player-fullscreen="true" x5-playsinline="true" x-webkit-airplay="true" x5-video-orientation="portraint" width="'+widthParam+'" height="'+heightParam+'" style="border:1px solid #e0e0e0;margin:0 auto;" controls="controls">'
					+'<source src="'+srcParam+'" type="video/mp4" />'
					+'<source src="'+srcParam+'" type="video/ogg" />'
					+'<source src="'+srcParam+'" type="video/webm" />'
					+'<object data="'+srcParam+'" width="'+widthParam+'" height="'+heightParam+'">'
						+'<embed src="'+srcParam+'" width="'+widthParam+'" height="'+heightParam+'" />'
					+'</object>'
				+'</video>';
		document.body.innerHTML=html;
	};
</script>
</html>