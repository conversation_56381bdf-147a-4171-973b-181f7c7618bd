package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ISystemService;
import com.simbest.boot.exam.examOnline.model.ExamRange;
import org.springframework.data.domain.Page;

import java.util.Iterator;
import java.util.List;

/**
 * 用途：考试信息模块--考试范围service
 * 作者：gy
 * 时间: 2021-02-01 10:44
 */
public interface IExamRangeService extends ISystemService<ExamRange, String> {
    /**
     *  保存考试范围信息
     */
    void saveExamRange(String summaryId, List<ExamRange> rangeList);

    /**
     * 根据考试汇总信息外键、公司编码获取当前人归属考试范围
     */
    ExamRange findByCompanyCodeAndSummaryId(String summaryId, String companyCode);

    /**
     * 根据考试汇总信息外键获取考试范围
     */
    Iterator<ExamRange> findBySummaryId(String summaryId);

    /**
     * 保存考试范围采用的试卷
     */
    ExamRange saveRangePaper(String id, String examPaperCode);

    /**
     * 查询考试范围信息
     */
    List<ExamRange> findRangeList(ExamRange o);

    /**
     *  根据考试编码获取考试范围信息
     */
    String findPaperCodeByExamCode(String examCode);
}
