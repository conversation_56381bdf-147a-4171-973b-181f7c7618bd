package com.simbest.boot.exam.appraise.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.constants.ApplicationConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.time.LocalDateTime;

/**
 * Appraise
 *
 * <AUTHOR>
 * @since 2024/1/24 9:21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity(name = "us_appraise_work")
@ApiModel(value = "评价工单表")
public class AppraiseWork extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UAW") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty("评价人")
    private String username;

    @Column(length = 40)
    @ApiModelProperty("评价人")
    private String truename;

    @Column(length = 40)
    @ApiModelProperty("评价人-phone")
    private String phone;

    @Column(length = 40)
    @ApiModelProperty("评价工单code")
    private String code;

    @Column(length = 40)
    @ApiModelProperty("评价模板code")
    private String templateCode;

    @Column
    @ApiModelProperty("评价时间")
    @JsonFormat(pattern = ApplicationConstants.FORMAT_DATE_TIME, timezone = ApplicationConstants.FORMAT_TIME_ZONE)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime appraiseDate;

    @Column(length = 10)
    @ApiModelProperty("评价状态 0 未评价 1 已评价")
    private String status;

    @Column(length = 10)
    @ApiModelProperty("短信推送状态 0 未推送 1 已推送成功 2 推送失败 3 已重推成功")
    private String smsPushStatus;

    @Column(length = 10)
    @ApiModelProperty("dict推送状态 0 未推送 1 推送成功")
    private String dictPushStatus;

    @Column(length = 10)
    @ApiModelProperty("总评分")
    private String score;

    @Column(length = 40)
    @ApiModelProperty("被评价人")
    private String appraiseUsername;

    @Column(length = 40)
    @ApiModelProperty("被评价人")
    private String appraiseTruename;

    @Column(length = 40)
    @ApiModelProperty("被评价的工单Id")
    private String appraiseWorkId;

    @Column(length = 255)
    @ApiModelProperty("被评价的工单标题")
    private String appraiseWorkTitle;

}
