<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>答案详情</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            getCurrent();
            var pageparam={
                "listtable":{
                    /*"idFiled":"ID", 用于自定义id的情况，当接口查出的id不为id，比如是ID时，在这里设置字段名 */
                    "listname":"#examAnswerTable",//table列表的id名称，需加# menuExpalinTable
                    "querycmd":"action/examQuestionAnswer/findAllByQuestionCodePage?questionCode="+gps.questionCode,//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "fitColumns": true,//自动扩大或缩小列的尺寸以适应表格的宽度并且防止水平滚
                    "frozenColumns":[[
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "题目编码", field: "questionCode", width: 150,sortable: true, tooltip: true,align:"center"},
                        { title: "答案内容", field: "answerContent", width: 300,sortable: true, tooltip: true,align:"center"},
                        { title: "是否为正确答案", field: "isCorrect", width: 150,sortable: true, tooltip: true,align:"center",
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                if(value == true) {
                                    return "是";
                                }else{
                                    return "否";
                                }
                            }},
                        {
                            field: "opt", title: "操作", width: 150, rowspan: 1,sortable: true, tooltip: true,align:"center",//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g ="<a href='#' class='showDialog' showDialogindex='" + index + "'>【修改答案】</a>";
                                return g;
                            }
                        }
                    ] ]

                },
                "dialogform":{
                    "dialogid":"#buttons",//对话框的id
                    "formname":"#examAnswerTableAddForm",//新增或修改对话框的formid需加#
                    //"insertcmd":"action/examQuestionResult/importQuestionBank",//新增命令
                    "updatacmd":"action/examQuestionAnswer/update",//修改命令
                    "onSubmit":function(data){
                        return true;
                    }
                }
            };
            loadGrid(pageparam);
        });
        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data,isupdate){
            if(isupdate){
                $('.update-readonly').hide();
            }else{
                $('.update-readonly').show();
            }
        };
        //回调函数
        window.programaInfo=function(data){
        };

    </script>
</head>
<body class="body_page">
<!--searchform-->

<!--table-->
<div class="examAnswerTable" ><table id="examAnswerTable"></table></div>
<!--新增修改的dialog页面-->
<div id="buttons" title="修改" class="easyui-dialog" style="width:950px;height:450px;">
    <form id="examAnswerTableAddForm" method="post" contentType="application/json; charset=utf-8"  onSubmit="onSubmit()" >
        <input id="id" name="id" type="hidden" />
        <table border="0" cellpadding="0" cellspacing="10">
            <tr>
                <td width="100" align="right">题目编码</td>
                <td colspan="3">
                    <input id="questionCode" name="questionCode" type="text"  class="easyui-validatebox"  readonly="readonly" /></td>
            </tr>
            <tr>
                <td width="100" align="right"><font class="col_r">*</font>答案内容</td>
                <td colspan="3">
                    <input id="answerContent" name="answerContent" type="text" class="easyui-validatebox"  required='required' /></td>
            </tr>
            <tr>
                <td width="100" align="right"><font class="col_r">*</font>对应选项</td>
                <td colspan="3">
                    <select class="easyui-combobox" data-options="panelHeight:'auto',editable:false" id="isCorrect"  name="isCorrect"  style="width: 150px; height: 32px;">
                        <option selected value="">--请选择--</option>
                        <option  value="true">是</option>
                        <option  value="false">否</option>

                    </select>
            </tr>


        </table>
    </form>
</div>
</body>
</html>
