<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>用户管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
		$(function(){
			//showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
			var pageparam={
				"listtable":{
					"listname":"#userTable",//table列表的id名称，需加#
					"querycmd":"action/user/user/findRoleNameIsARoleDim",//table列表的查询命令
					"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
					"nowrap": true,//把数据显示在一行里,默认true					
					"checkboxall":true,
					"queryParams":{"accountNonExpired":"true","accountNonLocked":"true","credentialsNonExpired":"true","status":"0","enabled":1},
					"frozenColumns":[[
						{ field: "ck",checkbox:true}
					]],//固定在左侧的列
					"columns":[[//列   
						{ title: "登录标识", field: "username", width: 60},
						{ title: "用户姓名", field: "truename", width: 60},
						{ title: "员工号", field: "employeeNumber", width: 60 },//排序sortable: true
						{ title: "移动电话", field: "preferredMobile", width: 60 },
						{ title: "所属公司", field: "orgName", width: 120},
						{
							field: "opt", title: "操作", width: 100, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
							formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
								var g = "<a href='#' class='showDialog' showDialogindex='" + index + "'>【查看】</a>"
								+"<a href='#' contentType='application/json; charset=utf-8' delete='action/admin/authority/sysuser/delete' deleteid='"+row.id+"'>【删除】</a>"
								+"<a class='openDialog' idname='id' id='" + row.id + "'>【特殊授权】</a>";
								return g;
							}
						}
					] ],
					"pagerbar": [{
						id:"deleteall",
						iconCls: 'icon-remove',
						text:"批量删除&nbsp;"
					}],
					"deleteall":{//批量删除deleteall.id要与pagerbar.id相同
						"id":"deleteall",
						"url":"action/admin/authority/sysuser/deletes",
						"contentType":"application/json; charset=utf-8"
					}
				},
				"dialoglisttable":{
					"dialogid":"#buttons2",//对话框的id
					"buttons":[{
						text:"确认",
						handler:function(){
							var obj = $('#buttons2').dialog('options');
							var urlstr = obj["queryParams"];
							if(btn_sure==1){
								btn_sure=2;
								var aa=$("#permissionIds").tree("getChecked");
								var ids=[]; 
								for(var i in aa){
									ids.push(aa[i].key);
								}
								web.ajaxgeneral({
									url:"action/admin/authority/sysuser/createSysUserPermission",
									data:{"id":urlstr.id,"permissionIds":ids.join(" ")},
									success:function(data){
										$.messager.show({
											title:"提示信息",
											msg:data.message,
											timeout:2500,
											showType:"slide",
											style:{
												right:'',
												top:10,
												bottom:''
											}
										});
										$("#buttons2").dialog("close");//关闭对话框
									},sError:function(data){
										btn_sure=1;
									},error:function(data){
										btn_sure=1;
									}
								});
							}
						}
					},{
						text:"关闭",
						handler:function(){
							$("#buttons2").dialog("close");
						}
					}],
					"dialogurl":"sysUserAuthorization.html"//对话框页面的路径
				},
				"dialogform":{
					"dialogid":"#buttons",//对话框的id
					"formname":"#userTableAddForm",//新增或修改对话框的formid需加#
					"onSubmit":function(param){//在请求加载数据之前触发。返回false可以停止该动作
						param.sysOrg={"id":$("#oid").combogrid("getValue"),"orgName":$("#orgName").val()};
						return true;
					},
					"insertcmd":"action/admin/authority/sysuser/create",//新增命令
					"updatacmd":"action/admin/authority/sysuser/update"//修改命令
				}
			};
			loadGrid(pageparam);
			$(".userchooseorg").on("click",function(){
				//第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
				//第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550   
				var href={"multi":"0","name":"userChooseOrgVal"};
				var chooseRow=top.chooseWeb.userChooseOrgVal?top.chooseWeb.userChooseOrgVal.data:[];
				if($("#orgId").val()!=""){
					if(chooseRow.length==0 || (chooseRow.length>0 && $("#orgId").val()!=chooseRow[0].orgCode)){						
						var datai={};
						datai.orgCode=$("#orgId").val();
						datai.displayName=$("#orgName").val();
						chooseRow.push(datai);
						top.chooseWeb.userChooseOrgVal={"data":chooseRow};
					}
				}else{//表示新增
					top.chooseWeb.userChooseOrgVal={"data":[]};
				}
				var url=tourl('html/sysorg/sysOrgTree.html?',href);			
				top.dialogP(url,'rightiframe','选择部门','userChooseOrg',false,'800');
			});
		});
		//选择上级组织
		window.userChooseOrg=function(data){
			if(data.data.length>0){
				if($("#orgId").val()!=data.data[0].orgCode){
					$("#orgId").val(data.data[0].orgCode);
					$("#orgName").val(data.data[0].displayName);				
					orgInit(data.data[0].orgCode)
				}
			}
		};
		//初始化对话框
		function orgInit(orgcode){
			$("#positionName").combobox({
				valueField: 'id',  
				ischooseall:true,	
				multiple:true,				
				textField: 'positionName', 
				queryParams:{'orgCode':orgcode?orgcode:''},
				onSelect:function(data){
					$("#positionId").val($("#positionName").combobox("getValues"));
				},
				onUnselect:function(data){
					$("#positionId").val($("#positionName").combobox("getValues"));
				},
				url: '/uums/action/position/position/findPositionByCompany'
			});	
		};
		//form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
		function beforerender(data,isupdate){
			if(isupdate){
				$('.update-readonly').hide();
			}else{
				$('.update-readonly').show();
			}
		};
	</script>
</head>
<body class="body_page_uums">
<h6 class="pageTit"><font class="col_b fwb">用户管理</font><i class="iconfont">&#xe66e;</i><small>用户的增删改查和授权</small></h6>
<!--searchform-->
<form id="userTableQueryForm"> 
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">用户姓名：</td><td width="150"><input name="username" type="text" value="" /></td>
            <td width="90" align="right">移动电话：</td><td width="150"><input name="phone" type="text" value="" /></td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fl searchtable"><span>查询</span></a>   
					<a class="btn a_success showDialog fr"><span>新增</span></a>
                </div>
                </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="userTable"><table id="userTable"></table></div>
<!--dialog-->
<div id="buttons2" title="特殊授权" class="easyui-dialog" style="width:800px;height:350px;"></div>
<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:750px;height:550px;">
<form id="userTableAddForm" method="post" contentType="application/json; charset=utf-8" beforerender="beforerender()">
	<input id="id" name="id" type="hidden" />
	<table border="0" cellpadding="0" cellspacing="6">
        <tr class="update-readonly">
            <td width="140" align="right">登录标识：</td>
            <td colspan="3" width="160">
                <input id="username" name="username" type="text" class="easyui-validatebox" required='required'/>
            </td>
        </tr>
		<tr>
            <td width="140" align="right">用户姓名：</td>
            <td colspan="3" width="160">
                <input id="truename" name="truename" type="text" class="easyui-validatebox" required='required' />
            </td>
        </tr>
		<tr class="update-readonly">
            <td width="140" align="right">密码：</td>
            <td width="160">
                <input id="password" name="password" type="password" class="easyui-validatebox" validType="password"/>
            </td>
            <td width="120" align="right">重复密码：</td>
            <td width="160">
                <input id="password1" name="password1" type="password" class="easyui-validatebox" validType="equals['#password']"/>
            </td>
        </tr>
		<tr>
            <td width="140" align="right">移动电话：</td>
            <td width="160">
                <input id="preferredMobile" name="preferredMobile" type="text" class="easyui-validatebox" validType="phone" />
            </td>
            <td width="120" align="right">电子邮箱：</td>
            <td width="160">
                <input id="email" name="email" type="text" class="easyui-validatebox" validType="email" />
            </td>
        </tr>
		<tr>
			<td width="140" align="right">状态：</td><td width="160">
			<select class="easyui-combobox" id="status" name="status" style="width: 100%; height: 32px;" data-options="panelHeight:'auto',editable:false">
				<option selected value="">-请选择-</option>
				<option value="0">正常</option>
                <option value="1">锁定</option>
            </select>
			</td>
            <td width="140" align="right">显示顺序：</td>
            <td width="160">
                <input id="displayOrder" name="displayOrder" type="text" class="easyui-validatebox" validType="zinteger" />
            </td>
        </tr>
		<tr>
			<td width="140" align="right">开始生效时间：</td>
            <td width="160">
                <input id="startTime" name="startTime" type="text" class="easyui-datetimebox" required="true" validType="startDateCheck['endTime','startTime']" data-options="panelHeight:'auto',editable:false" style="width:100%;height:32px;"/>
            </td>
			<td width="120" align="right">结束生效时间：</td>
            <td width="160">
                <input id="endTime" name="endTime" type="text" class="easyui-datetimebox" required="true" validType="endDateCheck['startTime','endTime']" data-options="panelHeight:'auto',editable:false" style="width:100%;height:32px;"/>
            </td>
        </tr>
    </table>
	<h6 class="ctableT"><strong>所属部门</strong></h6>
	<table border="0" cellpadding="0" cellspacing="0" class="ctable w100" id="orgTargets" pathsave="orgTargets">			
			<thead>
				<tr>
					<td width="40" class="hide">id</td>
					<td width="40" class="hide">remark</td>
					<td width="160">部门</td>
					<td width="160">职务</td>
					<td width="30"><a class="a_add_btn col_l" dialogname="orgTargets" title="新增/修改所属部门"><i class="iconfont">&#xe641;</i></a></td>
				</tr>
			</thead>
			<thead class="trow hide">
				<tr>
					<td path="orgId" class="hide"></td>
					<td path="positionId" class="hide"></td>
					<td path="orgName"></td>
					<td path="positionName"></td>
					<td width="30"><a class='fl a_mod_btn mr10' dialogname="orgTargets" title="新增/修改所属部门"><i class='iconfont'>&#xe6fe;</i></a><a class='fl a_del_btn col_l'><i class='iconfont'>&#xe6ef;</i></a></td>
				</tr>
			</thead>
			<tbody></tbody>
		</table>	
</form>
</div>
<!--dialog-->
<div id="orgTargetsDialog" title="新增或修改所属部门" class="easyui-dialog" data-options="closed:true" style="width:420px;height:350px;">
<form id="orgTargetsForm" initsystem="orgInit()">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="80" align="right">部门：</td>
            <td width="160"><input id="orgId" type="hidden"/><input id="positionId" type="text"/>
                <input id="orgName" name="orgName" type="text" required='required' />
            </td>
			<td>
                <a class="btn a_warning userchooseorg"><i class="iconfont">&#xe634;</i></a>
				<!--第一个参数是页面的url,第二个参数是当前页面对话框的id+"F",第三个参数选择人界面的标题,第四个参数是选择人完成之后的回调函数名称-->
			</td>
        </tr>
        <tr>
            <td width="80" align="right">职务：</td>
            <td width="160">
                <input id="positionName" name="positionName" type="text" style="width:100%;height:32px;" required='required' />
            </td>
			<td></td>
        </tr>
    </table>
</form>	
</div>
</body>
</html>
