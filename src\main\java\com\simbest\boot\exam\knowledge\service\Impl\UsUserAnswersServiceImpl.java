/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service.Impl;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.exam.knowledge.model.UsAnswerRecord;
import com.simbest.boot.exam.knowledge.model.UsUserAnswers;
import com.simbest.boot.exam.knowledge.repository.UsUserAnswersRepository;
import com.simbest.boot.exam.knowledge.service.IUsAnswerRecordService;
import com.simbest.boot.exam.knowledge.service.IUsUserAnswersService;
import com.simbest.boot.exam.util.FormatConversion;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class UsUserAnswersServiceImpl extends LogicService<UsUserAnswers, String> implements IUsUserAnswersService {

    private UsUserAnswersRepository userAnswersRepository;

    @Autowired
     IUsAnswerRecordService usAnswerRecordService;

    @Autowired
    IExamQuestionAnswerService iExamQuestionAnswerService;
    private String param1 = "/acton/usPendingTask";

    @Autowired
    public UsUserAnswersServiceImpl(UsUserAnswersRepository userAnswersRepository) {
        super(userAnswersRepository);
        this.userAnswersRepository = userAnswersRepository;

    }

    @Override
    public JsonResponse getRecordAnswersList(String id) {
        UsAnswerRecord usAnswerRecord = usAnswerRecordService.findById(id);
        if (usAnswerRecord==null){
            return JsonResponse.fail("未查询到答题记录");
        }
        List<Map<String, Object>> mapList = userAnswersRepository.findUsUserAnswerListByAnswerRecordId(id , SecurityUtils.getCurrentUserName());
        mapList = FormatConversion.formatConversion(mapList);
        for (Map<String, Object> map : mapList) {
            String questionCode = map.get("questionCode").toString();
            List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(questionCode);
            map.put("answerList",examQuestionAnswerList);
        }
        return JsonResponse.success(mapList);
    }

    /**
     * 查询答题记录
     * 用于续答
     *
     * @param pmInsId
     * @param ansewersUserName
     * @param questionBankCode
     * @return
     */
    @Override
    public List<UsUserAnswers> findAnswerUserNameInfo(String pmInsId, String ansewersUserName, String questionBankCode) {
        return userAnswersRepository.findAnswerUserNameInfo(pmInsId, ansewersUserName, questionBankCode);
    }

    @Override
    public Map<String, Object> getSocreRankingByAnswerRecordId(String id) {
        return userAnswersRepository.getSocreRankingByAnswerRecordId(id);
    }

    /**
     * @param currentDay
     * @param workType
     * @param questionBankCode
     * @param invitaitonId
     * @param ansewersUserName
     * @return
     */
    @Override
    public List<UsUserAnswers> getRightCount(String currentDay, String workType, String questionBankCode, String invitaitonId, String ansewersUserName) {
        return userAnswersRepository.getRightCount(currentDay, workType, questionBankCode, invitaitonId, ansewersUserName);
    }

    /**
     * 获取答题记录的答题情况
     * 考试记录新增分数
     *
     * @param answerRecordId
     * @param ansewersUserName
     * @return
     */
    @Override
    public List<UsUserAnswers> getRightCountToAnalyse(String answerRecordId, String ansewersUserName) {
        return userAnswersRepository.getRightCountToAnalyse(answerRecordId, ansewersUserName);
    }

    @Override
    public List<UsUserAnswers> getUsUserAnswersByRecordId(String recordId) {
        return  userAnswersRepository.findUsUserAnswersByAnswerRecordId(recordId);
    }

    @Override
    public List<Map<String, Object>> getTimeByRecordId(String id) {
        return userAnswersRepository.getTimeByRecordId(id);
    }

/**
*
 * @param workType
 * @param questionBankCode
 * @param invitaitonId
 * @param ansewersUserName
 * @return
*/
    @Override
    public List<Map<String, Object>> getTimeByRecordByUser(String workType, String questionBankCode, String invitaitonId, String ansewersUserName) {
        return userAnswersRepository.getTimeByRecordByUser(workType, questionBankCode, invitaitonId, ansewersUserName);
    }

    @Override
    public List<UsUserAnswers> findUsUserAnswersByQuestionIdAndAnswerRecordId(String questionId, String answerRecordId) {
        return userAnswersRepository.findUsUserAnswersByQuestionIdAndAnswerRecordId(questionId,answerRecordId);
    }

    @Override
    public Map<String, Object> findListByAnswerRecordId(String id) {
        return userAnswersRepository.findListByAnswerRecordId( id);
    }


    /**
     * 查询答题次数，用于判断是否A\B双方已经全部答完
     *
     * @param currentDay
     * @param workType
     * @param questionBankCode
     * @param ansewersUserName
     * @return
     */
    @Override
    public List<UsUserAnswers> findAnswerUserNameCount(String currentDay, String workType, String questionBankCode, String ansewersUserName) {
        return userAnswersRepository.findAnswerUserNameCount(currentDay, workType, questionBankCode, ansewersUserName);
    }

    @Override
    public List<Map<String, Object>> getSocreRanking(String knowledgeQuestionBankCode) {
       return userAnswersRepository.getSocreRanking(knowledgeQuestionBankCode);
    }
    @Override
    public List<Map<String, Object>> getSocreRankingNew(String knowledgeQuestionBankCode) {
        return userAnswersRepository.getSocreRankingNew(knowledgeQuestionBankCode);
    }
    @Override
    public Map<String, Object> getSelfSocreRanking(String knowledgeQuestionBankCode,String answerUserName) {
        return userAnswersRepository.getSelfSocreRanking(knowledgeQuestionBankCode,answerUserName);
    }

}
