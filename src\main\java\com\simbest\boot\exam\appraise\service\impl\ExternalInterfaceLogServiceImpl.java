package com.simbest.boot.exam.appraise.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.appraise.model.ExternalInterfaceLog;
import com.simbest.boot.exam.appraise.repository.ExternalInterfaceLogRepository;
import com.simbest.boot.exam.appraise.service.IExternalInterfaceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ExternalInterfaceLogServiceImpl extends LogicService<ExternalInterfaceLog, String> implements IExternalInterfaceLogService {

    private final ExternalInterfaceLogRepository repository;

    public ExternalInterfaceLogServiceImpl(ExternalInterfaceLogRepository repository) {
        super(repository);
        this.repository = repository;
    }


}
