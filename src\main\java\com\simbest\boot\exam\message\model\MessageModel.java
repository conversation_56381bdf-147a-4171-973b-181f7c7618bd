package com.simbest.boot.exam.message.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @createTime  2021-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_message_model")
@ApiModel(value = "短信内容实体")
public class MessageModel extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UMM") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 1000,nullable = false)
    @ApiModelProperty(name = "发送短信内容")
    private String messageContent;

    @Column(length = 200)
    @ApiModelProperty(name = "考试编码")
    private String examCode;

    //此表用于方便修改发送短信的内容。
}