<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
    xmlns:th="http://www.thymeleaf.org">
<head>
  <title>新增</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
  <!--
  <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
  -->
  <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
  <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
  <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<style type="text/css">
        .addUserBtn{height: 30px;line-height: 30px;width: 100px;color:#fff;background:#9CC715;border-radius: 3px;text-align: center;float: right;margin: 5px 10px; cursor: pointer;}
        .userBox{width: 135px;height: 55px;border: solid 1px #71c8f8;display: inline-block;padding:5px;border-radius: 3px;background: rgb(214, 228, 246);position: relative;margin: 10px 15px;font-size: 13px; vertical-align: top}
        .icon-shanchu{position: absolute;right: -10px;top: -5px;color:red;width: 26px;height: 16px;font-size: 12px;}
</style>
<body class="page_body">



<script type="text/javascript">
  var gps=getQueryString();
  var msgPerson = [];

  $(function(){
    if (gps.id === '1'){
      $(".lymyd").show();
    }else if(gps.id === '5'){
      $(".lymyd").show();
    }
    pageparam={
        "listtable":{
          "listname":"#groupListTable",//table列表的id名称，需加#
          "querycmd":"action/rangeUserInfo/findAllUserInfoByGroupId?groupId="+gps.id,
          "contentType":"application/json; charset=utf-8",
          "data":{},
          "checkboxall":true,        
          "fitColumns":true,
          "frozenColumns":[[
          { field: "ck",checkbox:true}
          ]],//固定在左侧的列
          "columns":[[//列
            { title: "人员名称", field: "TRUENAME", width: 15, align:'center'},
            { title: "OA账号", field: "USERNAME", width: 15, align:'center'},
            { title: "所在组织", field: "DISPLAY_NAME", width: 15, align:'center'},
            { title: "操作", field: "op", width: 5, align:'center',
              formatter:function (value,row,index) {
                var g="<a  href='#' class='del operateRed' delete='action/rangeUserInfo/delById?id=" +row.ID+ "' >【删除】</a>";
                return g;
              }
            },
          ]],
					"pagerbar": [{
						id:"id",
						iconCls: 'icon-remove',
						text:"批量删除&nbsp;"
					}],
          "deleteall":{                                           //批量删除                    deleteall.id要与pagerbar.id相同,传参数方式默认为kv
            id:"id",
            url:"action/rangeUserInfo/delByIds",
            contentType:"application/json; charset=utf-8"
          },
          "onSubmit":function(a){
          },  
        },
      };
      loadGrid(pageparam);

  });


  //添加组织信息
  $(document).on('click','.addPerson',function(e){
    //ie8不兼容event、target；以下写法用来兼容ie8
    var event = e || window.event;
    var target = event.target||event .srcElement;
    top.dialogP('html/examrange/editPersonTree.html',window.name,'组织清单','addUser',false,'650','600');
  })

  window.addUser = function (data) {
    data = data.data;
    var userArr = [];//新人员
    (function () {
      if (data) {
        for (var i = 0; i < data.length; i++) {
          var userInfo = {};
          userInfo.userName = data[i].id;
          userInfo.groupId = gps.id;
          userArr.push(userInfo);
        }        
      }
    })();
    var oldData = [];//旧人员
    (function () {
      if (pageparam.listtable.data.data) {
        for (var index = 0; index < pageparam.listtable.data.data.length; index++) {
          oldData.push(pageparam.listtable.data.data[index].USERNAME);
        }
      }
    })()
    //去重
    var newData = {};
    (function (userArr,oldData) {
      if (oldData&&userArr) {
        for (var index = 0; index < oldData.length; index++) {
          for (var i = 0; i < userArr.length; i++) {
            if (userArr[i].userName === oldData[index]) {
              userArr.splice(i,1);
              i--;
            }            
          }
        }
      }
    })();
    

    ajaxgeneral({
      url: "action/rangeUserInfo/saveExamRangeUserInfo?groupId="+gps.id,
      data:userArr,
      contentType: "application/json; charset=utf-8",
      success: function (res) {
        loadGrid(pageparam);
      }
    })
  }



  $(document).on('click','.reset',function (){
    formreset('groupListTableQueryForm');
  });

  //返回数据
  window.getchoosedata=function(){
    var datas = {};
    return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
  };

    
</script>

  <div class="table_searchD">
    <form id="groupListTableQueryForm" >
      <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr hidden class="lymyd">
          <td >用户名：</td>
          <td ><input id="truename" name="truename" type="text" /></td>
          <td >部门：</td>
          <td ><input id="displayName" name="displayName" type="text" /></td>
        </tr>

        <tr class="public">
          <td  width="30">OA账号：</td>
          <td  width="70"><input id="username" name="username" type="text" /></td>
          <td  width="30"></td>
          <td width="150">
            <div class="w100">
              <a class="btn fl searchtable"><span>查询</span></a>
              <a class="btn ml10 mr10 reset"><span>重置</span></a>
              <a class="btn  addUserBtn addPerson" style="margin: 0"><span>添加人员</span></a>
              <!-- <a class="btn a_green fr showDialogTop"  openlayer="html/examrange/editGroup.html" listPageName="Range" width="680" height="400">新建</a> -->
            </div>
          </td>
        </tr>


      </table>
    </form>
  </div>

  <div class="groupListTable">
    <table id="groupListTable">
    </table>
  </div>
</body>
</html>
