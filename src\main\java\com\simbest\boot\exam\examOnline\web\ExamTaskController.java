/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamTask;
import com.simbest.boot.exam.examOnline.service.IExamTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api
@RestController
@RequestMapping(value = "action/task/manager")
public class ExamTaskController extends LogicController<ExamTask, String> {

    private IExamTaskService service;

    @Autowired
    public ExamTaskController(IExamTaskService service) {
        super(service);
        this.service = service;

    }

    @ApiOperation(value = "定时器开关", notes = "定时器开关")
    @PostMapping(value = {"/taskStatusManage", "sso/taskStatusManage"})
    public JsonResponse taskStatusManage(@RequestParam String examCode) {
        try {
            return JsonResponse.success(service.taskStatusManage(examCode), "操作成功");
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(null, "操作失败");
        }

    }

}
