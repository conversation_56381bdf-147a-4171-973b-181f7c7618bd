package com.simbest.boot.exam.study.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;

import org.hibernate.annotations.GenericGenerator;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.awt.geom.QuadCurve2D;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 错题集实体类，用于记录用户的错题信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "wrong_question_collection")
@ApiModel(value = "WrongQuestionCollection", description = "错题集表")
public class WrongQuestionCollection extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "WQC") //主键前缀，此为可选项注解
    @ApiModelProperty(value = "主键ID", example = "1")
    private String id;

    @Column(name = "username", length = 50)
    @ApiModelProperty(value = "用户名", example = "hadmin")
    private String username;

    @Column(name = "question_id", length = 40)
    @ApiModelProperty(value = "题目ID", example = "EQ20250724110858")
    private String questionId;

    @Column(name = "question_code", length = 250)
    @ApiModelProperty(value = "题目编码", example = "A-001-1")
    private String questionCode;

    @Column(name = "question_type", length = 50)
    @ApiModelProperty(value = "题目类型", example = "single")
    private String questionType;

    @Column(name = "question_group_name", length = 200)
    @ApiModelProperty(value = "题目分组名称", example = "计算机基础")
    private String questionGroupName;

    @Column(name = "question_content", length = 2000)
    @ApiModelProperty(value = "题目内容", example = "下列选项中，哪个是Java的关键字？")
    private String questionContent;

    @Column(name = "user_answer", length = 2000)
    @ApiModelProperty(value = "用户答案", example = "B")
    private String userAnswer;

    @Column(name = "correct_answer", length = 2000)
    @ApiModelProperty(value = "正确答案", example = "A")
    private String correctAnswer;

    @Column(name = "wrong_count")
    @ApiModelProperty(value = "错误次数", example = "3")
    private Integer wrongCount;

    @Column(name = "last_wrong_time")
    @ApiModelProperty(value = "最后错误时间")
    private LocalDateTime lastWrongTime;

    @Transient
    private ExamQuestion question;

}