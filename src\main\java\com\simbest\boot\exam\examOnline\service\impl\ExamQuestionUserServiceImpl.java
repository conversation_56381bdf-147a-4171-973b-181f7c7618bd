/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;
import com.simbest.boot.exam.examOnline.repository.ExamQuestionUserRepository;
import com.simbest.boot.exam.examOnline.service.IExamQuestionUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <strong>Title : ExamQuestionUserServiceImpl</strong><br>
 * <strong>Description : 人员试卷题目Service </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Service
@Slf4j
public class ExamQuestionUserServiceImpl extends LogicService<ExamQuestionUser,String> implements IExamQuestionUserService {
    private ExamQuestionUserRepository repository;

    @Autowired
    public ExamQuestionUserServiceImpl (ExamQuestionUserRepository repository){
        super(repository);
        this.repository=repository;

    }

    @Override
    public List<ExamQuestionUser> getExamQuestionUserByBankCode(String bankCode,String username) {
        return this.findAllNoPage(Specifications.<ExamQuestionUser>and()
                .eq("bankCode", bankCode)
                .eq("creator", username)
                .eq("enabled", true)
                .build());
    }

    @Override
    public List<ExamQuestionUser> getExamQuestionUserByBankCode(String[] questionCode,String bankCode,String username) {
        return this.findAllNoPage(Specifications.<ExamQuestionUser>and()
                .in("questionCode", Lists.newArrayList(questionCode))
                .eq("bankCode", bankCode)
                .eq("creator", username)
                .eq("enabled", true)
                .build());
    }
}
