package com.simbest.boot.exam.background.service.impl;

import com.google.common.collect.Lists;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.exam.background.model.AttachmentConstants;
import com.simbest.boot.exam.background.model.ImagePath;
import com.simbest.boot.exam.background.repository.FileExtendRepository;
import com.simbest.boot.exam.background.service.IFileExtendService;
import com.simbest.boot.exam.background.service.ImagePathService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exceptions.AppRuntimeException;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.util.AppFileUtil;
import com.simbest.boot.util.CodeGenerator;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.encrypt.UrlEncryptor;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

import static com.simbest.boot.sys.service.impl.SysFileService.FILE_ERROR;

/**
 * 作用：附件扩展类
 * 作者：zsf
 * 时间：2018/07/28
 */
@Slf4j
@Service
public class FileExtendServiceImpl extends LogicService<SysFile, String> implements IFileExtendService {


    private FileExtendRepository fileExtendRepository;

    @Autowired
    private AppFileUtil appFileUtil;

    @Autowired
    private AppConfig config;

    @Autowired
    private RsaEncryptor rsaUtil;

    @Autowired
    private UrlEncryptor urlEncode;

    @Autowired
    private AttachmentConstants attachmentConstants;

    @Autowired
    public FileExtendServiceImpl(FileExtendRepository repository) {
        super(repository);
        this.fileExtendRepository = repository;
    }

    @Autowired
    private ImagePathService imagePathService;



    /**
     * 上传视频到共享存储
     */
    @Override
    public List<SysFile> uploadProcessFiles(Collection<MultipartFile> multipartFiles) {
        List<SysFile> sysFileList = Lists.newArrayList();
        try {
            sysFileList = appFileUtil.uploadFiles(prepareDirectory(),multipartFiles);
            for (SysFile sysFile : sysFileList) {
                String mobileFilePath = config.getAppHostPort() + sysFile.getFilePath();
                //保存共享存储能访问的路径
                String filePath = sysFile.getFilePath();
                String preFilePath = attachmentConstants.getPreFilePath();
                String substring = filePath.substring(preFilePath.length());
                String filePath2=config.getAppHostPort() + substring;//路径
                sysFile.setMobileFilePath(config.getAppHostPort() + substring);
                this.insert(sysFile);

             //   sysFile.setDownLoadUrl(sysFile.getDownLoadUrl().concat("?id=" + sysFile.getId()));
                //修改下载URL，追加ID
//                sysFile.setApiFilePath(config.getAppHostPort() + "/" + Constants.APP_CODE + sysFile.getApiFilePath().concat("?id=" + sysFile.getId()));
//                sysFile.setAnonymousFilePath(config.getAppHostPort() + "/" + Constants.APP_CODE + sysFile.getAnonymousFilePath().concat("?id=" + sysFile.getId()));
                sysFile.setApiFilePath(config.getAppHostPort() + "/"+Constants.APP_CODE + sysFile.getApiFilePath().concat("?id=" + sysFile.getId()));
                sysFile.setAnonymousFilePath(config.getAppHostPort() + "/"+Constants.APP_CODE + sysFile.getAnonymousFilePath().concat("?id=" + sysFile.getId()));
                sysFile.setDownLoadUrl(sysFile.getDownLoadUrl()+"?id="+sysFile.getId());
                //保存真实路径
                /*sysFile.setApiFilePath(mobileFilePath);
                 sysFile.setAnonymousFilePath(mobileFilePath);*/
                ImagePath imagePath = new ImagePath();
                imagePath.setUseNow(false);
                imagePath.setExamAppCode(sysFile.getFileName());
                imagePath.setDownLoadUrl(sysFile.getMobileFilePath());
              // List<ImagePath> imagePaths = new ArrayList<>();
                //   imagePaths.add(imagePath);
          //      imagePathService.saveImagePath(imagePaths);
           //     return imagePath;
            //    this.update(sysFile);
            }
        } catch (Exception e) {
            throw new AppRuntimeException(String.format(FILE_ERROR, e.getMessage()));
        }
      return   sysFileList;
    }

    private String prepareDirectory() {

        String username = SecurityUtils.getCurrentUserName();
        String directory = StringUtils.removeEnd(  username + ApplicationConstants.SLASH
                + CodeGenerator.systemUUID() + ApplicationConstants.SLASH
                  , ApplicationConstants.SLASH);
        log.debug("上传路径地址为【{}】", directory);
        return directory;
    }





}
