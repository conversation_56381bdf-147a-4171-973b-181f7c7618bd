/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;
import com.simbest.boot.exam.examOnline.service.IExamQuestionUserService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <strong>Title : ExamQuestionUserController</strong><br>
 * <strong>Description : 人员试卷题目Controller </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Api
@RestController
@RequestMapping(value = "action/examQuestionUser")
public class ExamQuestionUserController extends LogicController<ExamQuestionUser,String> {
    private IExamQuestionUserService service;

    @Autowired
    public ExamQuestionUserController(IExamQuestionUserService service){
        super(service);
        this.service=service;

    }
}
