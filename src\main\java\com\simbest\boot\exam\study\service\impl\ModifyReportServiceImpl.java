package com.simbest.boot.exam.study.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionService;
import com.simbest.boot.exam.study.model.ModifyReport;
import com.simbest.boot.exam.study.repository.ModifyReportRepository;
import com.simbest.boot.exam.study.service.IModifyReportService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ModifyReportServiceImpl
 * @description:
 * @author: ZHAOBO
 * @create: 2025-08-14 11:22
 */
@Service
@Slf4j
public class ModifyReportServiceImpl extends LogicService<ModifyReport, String> implements IModifyReportService {

    private ModifyReportRepository repository;

    public ModifyReportServiceImpl(ModifyReportRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private IExamQuestionService examQuestionService;

    @Autowired
    private IExamQuestionAnswerService examQuestionAnswerService;


    /**
     * 新增修正记录
     *
     * @param map 修正数据信息
     * @return
     */
    @Override
    public JsonResponse saveModify(Map<String, Object> map) {
        String questionCode = MapUtil.getStr(map, "questionCode");
        String modifyContent = MapUtil.getStr(map, "modifyContent");
        Assert.notNull(modifyContent, "修正内容不能为空！");
        /*Assert.notNull(questionCode, "题目编码不能为空！");
        String userAnswer = MapUtil.getStr(map, "userAnswer");
        Assert.notNull(userAnswer, "修正答案不能为空！");*/

        ExamQuestion examQuestion = examQuestionService.findQuestionAndAnswer(questionCode);
        Assert.notNull(examQuestion , "查询题目信息失败！");
        IUser iUser = SecurityUtils.getCurrentUser();
        ModifyReport modifyReport = ModifyReport.builder()
                .questionCode(examQuestion.getQuestionCode())
                .questionId(examQuestion.getId())
                .questionType(examQuestion.getQuestionType())
                .questionGroupName(examQuestion.getQuestionGroupName())
                .questionContent(examQuestion.getQuestionName())
/*                .userAnswer(userAnswer)*/
                .modifyContent(modifyContent)
                .status("0")
                .username(iUser.getUsername())
                .truename(iUser.getTruename())
                .displayOrgName(iUser.getAuthOrgs().iterator().next().getDisplayName())
                .build();
        this.insert(modifyReport);
        return JsonResponse.success( 1 , "提交成功");
    }

    /**
     * 查询所有提交的问题信息
     *
     * @param page   分页
     * @param size   数量
     * @param report 查询条件
     * @return
     */
    @Override
    public JsonResponse queryModifyReport(Integer page, Integer size, ModifyReport report) {
        Pageable pageable = this.getPageable(page, size, Sort.Direction.DESC.toString(), "createdTime");
        Specification<ModifyReport> build = Specifications.<ModifyReport>and()
                .eq("enabled", Boolean.TRUE)
                .like(StrUtil.isNotEmpty(report.getQuestionGroupName()), "questionGroupName", "%" + report.getQuestionGroupName() + "%")
                .like(StrUtil.isNotEmpty(report.getQuestionContent()), "questionContent", "%" + report.getQuestionContent() + "%")
                .eq("status", "0")
                .build();
        Page<ModifyReport> all = this.findAll(build, pageable);
        return JsonResponse.success(all);
    }

    /**
     * 更新错题信息
     *
     * @param id     主键id
     * @param status 类型
     * @return
     */
    @Override
    public JsonResponse updateStatus(String id, String status) {
        Assert.notNull(id , "主键不能为空！");
        Assert.notNull(status , "处理状态不能为空！");
        ModifyReport modifyReport = this.findById(id);
        Assert.notNull(modifyReport , "查询反馈信息失败！");
        modifyReport.setStatus(status);
        //更正题目答案
        if (StrUtil.equals(status , "1")) {
            ExamQuestion questionAndAnswer = examQuestionService.findQuestionAndAnswer(modifyReport.getQuestionCode());
            Assert.notNull(questionAndAnswer , "获取题目信息失败！");
            List<String> answerArray = Arrays.asList(modifyReport.getUserAnswer().split(","));
            questionAndAnswer.getAnswerList().forEach(answer -> {
                if (answerArray.contains(answer.getAnswerCode())) {
                    answer.setIsCorrect(Boolean.TRUE);
                } else {
                    answer.setIsCorrect(Boolean.FALSE);
                }
                examQuestionAnswerService.update( answer);
            });
        }
        this.update(modifyReport);
        return JsonResponse.success( 1 , "处理成功！");
    }

    /**
     * 查询题目信息
     *
     * @param id 主键id
     * @return
     */
    @Override
    public JsonResponse findQuestionById(String id) {
        ModifyReport modifyReport = this.findById(id);
        Assert.notNull(modifyReport , "查询反馈信息失败！");
        ExamQuestion questionAndAnswer = examQuestionService.findQuestionAndAnswer(modifyReport.getQuestionCode());
        Assert.notNull(questionAndAnswer , "获取题目信息失败！");
        modifyReport.setQuestion(questionAndAnswer);
        return JsonResponse.success(questionAndAnswer);
    }

    /**
     * 修改题目信息
     *
     * @param map 修改数据信息
     * @return
     */
    @Override
    public JsonResponse updateQuestionInfo(Map<String, Object> map) {
        String id = MapUtil.getStr(map, "id");
        String status = MapUtil.getStr(map, "status");
        String questionType = MapUtil.getStr(map, "questionType");
        String answerCode = MapUtil.getStr(map, "answerCode");
        Assert.notNull(id, "主键不能为空！");
        Assert.notNull(status, "处理状态不能为空！");
        ModifyReport modifyReport = this.findById(id);
        Assert.notNull(modifyReport , "查询反馈信息失败！");
        modifyReport.setStatus(status);
        //更正题目答案
        if (StrUtil.equals(status , "1")) {
            Assert.notNull(questionType, "题目类型不能为空！");
            Assert.notNull(answerCode, "正确答案不能为空！");
            ExamQuestion questionAndAnswer = examQuestionService.findQuestionAndAnswer(modifyReport.getQuestionCode());
            Assert.notNull(questionAndAnswer , "获取题目信息失败！");
            questionAndAnswer.setQuestionType(questionType);
            examQuestionService.update(questionAndAnswer);
            List<String> answerArray = Arrays.asList(answerCode.split(","));
            questionAndAnswer.getAnswerList().forEach(answer -> {
                if (answerArray.contains(answer.getAnswerCode())) {
                    answer.setIsCorrect(Boolean.TRUE);
                } else {
                    answer.setIsCorrect(Boolean.FALSE);
                }
                examQuestionAnswerService.update( answer);
            });
        }
        this.update(modifyReport);
        return JsonResponse.success( 1 , "处理成功！");
    }


}
