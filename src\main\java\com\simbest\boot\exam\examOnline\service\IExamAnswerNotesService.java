package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.dto.AnswerStatusDto;
import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2021-04-25
 * @desc 单选问卷调查答题记录与统计Service
 **/
public interface IExamAnswerNotesService extends ILogicService<ExamQuestionUser, String> {

    /**
     * 记录用户答题记录
     *
     * @param
     * @return
     */
    List<ExamQuestionUser> saveAnswerNotes(String currentUserCode, String source, AnswerStatusDto o,String annualQuarterCode);

    /**
     * 记录用户答题记录(短信小程序端)
     *
     * @param
     * @return
     */
    List<ExamQuestionUser> saveAnswerNotesMB(String currentUserCode, String source, AnswerStatusDto o,String annualQuarterCode);

    /**
     * 查询用户保存的答题记录
     *
     * @param
     * @return
     */
    List<ExamQuestionUser> findAnswerNotesByCreator(String annualQuarterCode);


    /**
     * 统计机关各部门对机关各部门的满意度评价结果
     *
     * @return
     */
    List<Map<String, Object>> statisticsDepartments(String annualQuarterCode);

    /**
     * 统计县分公司对机关各部门的满意度评价结果
     *
     * @param
     * @return
     */
    List<Map<String, Object>> countyBranchStatistics(String annualQuarterCode);

    /**
     * 统计机关各部门对机关各部门的满意度评价总平均分以及百分制分值
     *
     * @return
     */
    List<Map<String, Object>> statisticsDepartmentsAvg(String annualQuarterCode);

    /**
     * 县分公司对机关各部门的满意度评价总平均分以及百分制分值
     *
     * @return
     */
    List<Map<String, Object>> countyBranchStatisticsDepartmentsAvg(String annualQuarterCode);

    /**
      * 统计汇总表
      * <AUTHOR>
      * @date 2021/6/29
      */
    List<Map<String, Object>> statisticalSummary(String annualQuarterCode);

    /**
     * 统计汇总表表头
     * <AUTHOR>
     * @date 2021/6/29
     */
    List<Map<String, Object>> statisticalSummaryTitle(String annualQuarterCode);

    /**
     * 根据当前人判断是否答过题目，去判断是否需要推送统一待办
     */
    ExamQuestionUser findByCreator(String creator,String annualQuarterCode);


}
