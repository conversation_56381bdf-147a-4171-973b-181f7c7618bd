<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>综合管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <meta charset="UTF-8">
    <!-- <meta name="ctx" th:content="${#httpServletRequest.getContextPath()}" />-->
    <!-- Tether core CSS -->
    <link href="fonts/iconfont/iconfont.css" th:href="@{/fonts/iconfont/iconfont.css}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js" th:src="@{http://************:8088/simbestui/js/jquery.min.js}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jsencrypt.min.js" th:src="@{http://************:8088/simbestui/js/jsencrypt.min.js}" type="text/javascript"></script>
    <link href="http://************:8088/simbestui/css/public.css" th:href="@{http://************:8088/simbestui/css/public.css}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/login.css" th:href="@{http://************:8088/simbestui/css/login.css}" rel="stylesheet"/>
    <script type="text/javascript">
        var encrypt = new JSEncrypt();
        var key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC+K3y4fL71dFhFYC9c9bea9wPH" + "\r" +
            "youU86VI0nI1GtDiMbSd3/mFcf/Z14hixordW8W8Q0BftncjcbIOHOeHDK074hpV" + "\r" +
            "bMdJTgadisuksX1fISp5CXa5ETsDcHa6usb1wGd2EFSo8ws5Jfi5oGZVgRzF3YLI" + "\r" +
            "KgxYn+NZu7cvHOD0GwIDAQAB" + "\r";
        encrypt.setPublicKey(key);
        function check(){
            var val=encrypt.encrypt($("#password").val());
            var passW=encodeURI(val);
            $("#password").val(passW);
            return true;
        };
        function refreshCaptcha() {
            $("#captcha").attr("src","/exam/captcha?t=" + Math.random());
        }
    </script>
</head>
<body>
<div class="logo_wrap" path="综合管理系统">
    <div class="logo"><img src="../images/logo.png" th:src="@{/images/logo.png}" alt="综合管理系统"/></div>
</div>
<!--login-->
<form id="login" method="POST" th:action="@{/uumslogin}" onsubmit="return check()">
    <input id="appcode" name="appcode" type="hidden" value="exam"/>
    <table cellspacing="15" cellpadding="0" border="0" class="login_tab">
        <tr><td colspan="2" align="center"><h5 class="login_tit">用户登录</h5></td></tr>
        <tr><td colspan="2"><font color="red"><span th:if="${param.loginError}" th:text="${session.SPRING_SECURITY_LAST_EXCEPTION.message}"></span></font></td></tr>
        <tr><td colspan="2" class="td_user"><input id="username" name="username" type="text" autofocus="autofocus" placeholder="请输入用户名"/><i class="user"></i></td></tr>
        <tr><td colspan="2"><input id="password" name="password" type="password" placeholder="请输入密码"/><i class="lock"></i></td></tr>
        <tr><td width="230" class="td_code"><input id="verifyCode" name="verifyCode" type="text" placeholder="请输入验证码" /><i class="code"></i></td>
            <td width="115"><img width="115" height="38" id="captcha" src="/exam/captcha" title="点击更换" onclick="javascript:refreshCaptcha();"/></td></tr>
        <tr style="height:5px;"><td colspan="2"></td></tr>
        <tr><td colspan="2" align="center"><input type="submit" class="input_btn input_sub" value="登&nbsp;&nbsp;&nbsp;录"/></td></tr>
        <!--<tr><td align="center"><a id="submit" class="input_btn">登&nbsp;&nbsp;&nbsp;录</a></td></tr>-->
    </table>
</form>
<!--copy-->
<div class="copy">
    支撑热线：0371-60880301-1
    支撑邮箱：<EMAIL>
</div>
</body>
</html>