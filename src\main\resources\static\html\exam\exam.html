<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>考试设置</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
        type="text/javascript"></script>
    <style>
        .panel.combo-p {
            height: 100px;
            overflow-y: auto;
            border: 1px solid #e6e6e6;
        }
        .combo-panel.panel-body.panel-body-noheader {
            border: none;
        }
    </style>
    <script type="text/javascript">
        var timu = []
        $(function () {
            $('.showDialog').click(function () {
                localStorage.removeItem('itemID')
            })
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#examTable",//table列表的id名称，需加#
                    "querycmd": "action/examAttribute/findExamPaperInfo",//table列表的查询命令
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "sortOrder": 'desc',
                    "styleClass":"noScroll",
                    "columns": [[//列
                        { title: "试卷名称", field: "examName", width: 300, sortable: true, tooltip: true, align: "center" },
                        { title: "试卷编码", field: "examAppCode", width: 150, sortable: true, tooltip: true, align: "center" },
                        { title: "总题目数", field: "topicSum", width: 150, sortable: true, tooltip: true, align: "center" },
                        { title: "题库编码", field: "questionBankCode", width: 150, sortable: true, tooltip: true, align: "center" },
                        { title: "单选题比例", field: "single", width: 150, sortable: true, tooltip: true, align: "center" },
                        { title: "多选题比例", field: "more", width: 150, sortable: true, tooltip: true, align: "center" },
                        { title: "判断题比例", field: "judge", width: 150, sortable: true, tooltip: true, align: "center" },
                        { title: "填空题比例", field: "filling", width: 150, sortable: true, tooltip: true, align: "center" },
                        { title: "简答题比例", field: "shortAnswer", width: 150, sortable: true, tooltip: true, align: "center" },
                        { title: "试卷规定时间限制", field: "setTime", width: 150, sortable: true, tooltip: true, align: "center" },
                        {
                            field: "opt", title: "操作", width: 250, rowspan: 1, sortable: true, tooltip: true, align: "center",//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "<a href='#' class='read' id='" + row.id + "' readDialogindex='" + index + "'>【查看】</a>"
                                    + "<a href='#' class='addImg' id='" + row.examAppCode + "'>【考试背景图】</a>"
                                    + "<a href='#' delete='action/examAttribute/deleteById' deleteid='" + row.id + "'>【删除】</a>";
                                return g;
                            }
                        }

                    ]]
                },
                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "ctable": "maUserOrg",
                    "formname": "#examTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/examAttribute/createExamPaper",//新增命令
                    "updatacmd": "action/examAttribute/update",//修改命令
                    "onSubmit": function (data) {//在请求加载数据之前触发。返回false可以停止该动作

                        if (timu) {
                            let examAttribute = {}
                            examAttribute.examName = data.examName
                            examAttribute.examAppCode = data.examAppCode
                            examAttribute.examRemark = data.examRemark
                            examAttribute.filling = data.filling
                            examAttribute.judge = data.judge
                            examAttribute.more = data.more
                            examAttribute.setTime = data.setTime
                            examAttribute.shortAnswer = data.shortAnswer
                            examAttribute.single = data.single
                            // examAttribute.topicSum = data.topicSum
                            examAttribute.questionBankCode = data.questionBankCode
                            examAttribute.topicStyle = data.topicStyle
                            examAttribute.topicNumber = data.topicNumber
                            data.examAttribute = examAttribute
                            data.examAttributeQuestions = []
                            let a = data.examAttributeQuestions
                            for (var i = 0; i < timu.length; i++) {
                                let params = {}
                                params.examAppCode = data.examAppCode
                                params.questionBankCode = data.questionBankCode
                                params.questionCode = timu[i]
                                a.push(params)
                            }

                        }
                        return true;
                    }

                },
                "readDialog": {//查看
                    "dialogid": "#readDag",
                    "dialogedit": true,//查看对话框底部要不要编辑按钮
                    "formname": "#examTableReadForm"
                }
            };
            loadGrid(pageparam);

            // 查看试题
            $(document).on('click', '.read', function (e) {

                //ie8不兼容event、target；以下写法用来兼容ie8
                var event = e || window.event;
                var target = event.target || event.srcElement;
                top.dialogP('html/exam/examManage_read.html?type=read&id=' + target.id, window.name, '试卷详情', 'examDetail', false, '650', '600');
            })
            // 编辑试题
            $(document).on('click', '.edit', function (e) {

                //ie8不兼容event、target；以下写法用来兼容ie8
                var event = e || window.event;
                var target = event.target || event.srcElement;
                top.dialogP('html/exam/examManage_read.html?type=edit&id=' + target.id, window.name, '编辑试卷', 'examDetail', false, '650', '600');
            })

            //examDetail的回调，发送更新请求和执行重新载入函数
            window.examDetail = function (data) {//编辑考试
                //console.log(data);
                ajaxgeneral({
                    url: "action/summary/saveExamSummary",
                    data: data.data[0],
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        if (res.status == 200) {
                            formreset('examBacklogTableQueryForm');
                            $('#examBacklogTable').datagrid('reload');
                        }
                    }
                })
            }

            //试卷背景设置
            $(document).on('click', '.addImg', function (e) {
                //ie8不兼容event、target；以下写法用来兼容ie8
                var event = e || window.event;
                var target = event.target || event.srcElement;
                //console.log(target);
                top.dialogP('html/exam/bgImage.html?id=' + target.id, window.name, '试卷背景', 'examImg', true, '900', '600');
            })            
            window.examImg = function (data) {//编辑考试
                ajaxgeneral({
                    url: "action/summary/saveExamSummary",
                    data: data.data[0],
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                    }
                })
            }

            // 题库编码下拉框触发事件

            $('#questionBankCode').combobox({
                onSelect: function (param) {
                    localStorage.setItem('itemID', param.questionBankCode)
                }
            })

            // 出题方式下拉框的触发事件
            $('#topicStyle').combobox({
                onSelect: function (param) {
                    if (param.value == "random") {
                        $(".topicNumber").show()
                        $(".topicNumber input").show()
                    } else if (param.value == "fixed") {
                        $(".topicNumber").hide()
                        $(".topicNumber input").hide()
                        top.dialogP("html/exam/examFixed.html", window.name, '固定出题', 'examImport', false, '700', '700');
                    } else if (param.value == "original") {
                        $(".topicNumber").hide()
                        $(".topicNumber input").hide()
                    }
                }
            });




        });

        //表单校验
        window.fvalidate = function () {
            return $("#examTableAddForm").form("validate");
        };

        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data, isupdate) {
            if (isupdate) {
                $('.update-readonly').hide();
            } else {
                $('.update-readonly').show();
            }
        };

        function initsystem() {
            //初始化表单操作代码

        };
        //   固定出题回调
        function examImport(param) {
            timu = param.data.arr
            //console.log(param)
        }




    </script>
</head>

<body class="body_page">
    <form id="examTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="4" width="100%">
            <tr>
                <td colspan="5" width="300">
                </td>
            </tr>
            <tr>

                <td width="90" align="right">试卷编码：</td>
                <td width="150">
                    <input name="examAppCode" type="text" value="" />
                </td>

                <td width="90" align="right">试卷名称：</td>
                <td width="150">
                    <input name="examName" type="text" value="" />
                </td>

                <td width="90" align="right">题库编码：</td>
                <td width="150">
                    <input name="questionBankCode" type="text" value="" />
                </td>

                <td>
                    <div class="w100">
                        <a class="btn fl searchtable">
                            <font>查询</font>
                        </a>
                        <a class="btn a_success showDialog fr"><span>新增</span></a>
                    </div>
                </td>
                <td></td>
                <td></td>
            </tr>
        </table>
    </form>
    <!--searchform-->

    <!--table-->
    <div class="examTable">
        <table id="examTable"></table>
    </div>

    <!--dialog-->
    <div id="buttons" title="新增或修改" class="easyui-dialog" style="width:650px;height:600px;">
        <form id="examTableAddForm" method="post" contentType="application/json; charset=utf-8"
            initsystem="initsystem()">
            <input id="id" name="id" type="hidden" />
            <table border="0" cellpadding="0" cellspacing="10" width="100%">

                <tr>
                    <td width="10%" align="left">
                        <font class="col_r">*</font>试卷名称：
                    </td>
                    <td width="20%" length="100">
                        <input id="examName" name="examName" type="text" value="" class="easyui-validatebox"
                            data-options="required:true" style="width:  350px; height: 32px;" />
                    </td>
                </tr>
                <tr>
                    <td width="10%" align="left">
                        <font class="col_r">*</font>系统编号：
                    </td>
                    <td width="20%" length="100">
                        <input id="examAppCode" name="examAppCode" type="text" value="" class="easyui-validatebox"
                            data-options="required:true" style="width:  350px; height: 32px;" />
                    </td>
                </tr>
<!--                <tr>-->
<!--                    <td width="10%" align="left">-->
<!--                        <font class="col_r">*</font>题库总题目数：-->
<!--                    </td>-->
<!--                    <td width="20%" length="100">-->
<!--                        <input id="topicSum" name="topicSum" type="text" value="" class="easyui-validatebox"-->
<!--                            data-options="required:true" style="width:  350px; height: 32px;" />-->
<!--                    </td>-->
<!--                </tr>-->
                <tr>
                    <td width="10%" align="left">
                        <font class="col_r">*</font>题库编码：
                    </td>
                    <td width="20%" length="100">
                        <input id="questionBankCode" name="questionBankCode" class="easyui-combobox"
                            style="width:  350px; height: 32px;" data-options="
                                   valueField: 'questionBankCode',
                                   editable:'false',
                                   panelHeight:'auto',
                                   textField: 'questionBankName',
                                   contentType:'application/json; charset=utf-8',
                                   url: web.rootdir+'action/examQuestionBank/findAllNoPage'" />
                    </td>
                </tr>
                <tr>
                    <td width="10%" align="left">
                        <font class="col_r">*</font>出题方式：
                    </td>
                    <td width="20%" length="100">
                        <input id="topicStyle" name="topicStyle" class="easyui-combobox"
                            style="width:  350px; height: 32px;"
                            data-options="
                        valueField: 'value',
                        panelHeight:'auto',
                        textField: 'name',
                        editable:false,
                        data:[{value:'original',name:'全题库'},{value:'random',name:'随机出题'},{value:'fixed',name:'固定出题'}]" />
                    </td>
                </tr>
                <tr class="topicNumber" style="display: none">
                    <td width="10%" align="left">
                        <font class="col_r">*</font>随机出题数量：
                    </td>
                    <td width="20%" length="100">
                        <input id="topicNumber" name="topicNumber" type="text" value="" class="easyui-validatebox"
                            style="width:  350px; height: 32px;display: none" />
                        <!--                    <input id="topicNumber" name="topicNumber" type="text" value="" class="easyui-validatebox" data-options="required:true"  style="width:  350px; height: 32px;display: none"/>-->
                    </td>
                </tr>
                <tr>
                    <td width="10%" align="left">单选题比例：</td>
                    <td width="20%" length="100">
                        <input id="single" name="single" type="text" value="" class="easyui-validatebox"
                            style="width:  350px; height: 32px;" />
                    </td>
                </tr>
                <tr>
                    <td width="10%" align="left">多选题比例：</td>
                    <td width="20%" length="100">
                        <input id="more" name="more" type="text" value="" class="easyui-validatebox"
                            style="width:  350px; height: 32px;" />
                    </td>
                </tr>
                <tr>
                    <td width="10%" align="left">判断题比例：</td>
                    <td width="20%" length="100">
                        <input id="judge" name="judge" type="text" value="" class="easyui-validatebox"
                            style="width:  350px; height: 32px;" />
                    </td>
                </tr>
                <tr>
                    <td width="10%" align="left">填空题比例：</td>
                    <td width="20%" length="100">
                        <input id="filling" name="filling" type="text" value="" class="easyui-validatebox"
                            style="width:  350px; height: 32px;" />
                    </td>
                </tr>

                <tr>
                    <td width="10%" align="left">简答题比例：</td>
                    <td width="20%" length="100">
                        <input id="shortAnswer" name="shortAnswer" type="text" value="" class="easyui-validatebox"
                            style="width:  350px; height: 32px;" />
                    </td>
                </tr>

                <tr>
                    <td width="10%" align="left">
                        <font class="col_r">*</font>试卷规定时间限制(分钟)：
                    </td>
                    <td width="20%" length="100">
                        <input id="setTime" name="setTime" type="text" value="" validType="integer" class="easyui-validatebox"
                            data-options="required:true" style="width:  350px; height: 32px;" />
                    </td>
                </tr>
                <tr>
                    <td width="10%" align="left">
                        <font class="col_r">*</font>试卷简介：
                    </td>
                    <td width="20%" length="100">
                        <textarea id="examRemark" name="examRemark" class="easyui-validatebox"
                            style="width:350px;height:100px;resize:both;"></textarea>

                    </td>
                </tr>

            </table>



        </form>
    </div>

    <div id="readDag" title="试卷详情" class="easyui-dialog" style="width:650px;height:600px;">
        <form id="examTableReadForm" method="post" contentType="application/json; charset=utf-8">
            <table border="0" cellpadding="0" cellspacing="10" width="100%">

                <tr>
                    <td width="15%" align="left">试卷名称：</td>
                    <td width="20%" length="100">
                        <input type="text" class="examName" readonly="readonly" />
                    </td>
                </tr>

                <tr>
                    <td width="15%" align="left">系统编号：</td>
                    <td width="20%" length="100">
                        <input type="text" class="examAppCode" readonly="readonly" />
                    </td>
                </tr>

<!--                <tr>-->
<!--                    <td width="15%" align="left">总题目数：</td>-->
<!--                    <td width="20%" length="100">-->
<!--                        <input type="text" class="topicSum" readonly="readonly" />-->
<!--                    </td>-->
<!--                </tr>-->

                <tr>
                    <td width="15%" align="left">题库编码：</td>
                    <td width="20%" length="100">
                        <input type="text" class="questionBankCode" readonly="readonly" />
                    </td>
                </tr>
                <tr>
                    <td width="15%" align="left">出题方式：</td>
                    <td width="20%" length="100">
                        <input id="topicStyl23e" name="topicStyle" class="easyui-combobox"
                            style="width:  478px; height: 32px;"
                            data-options="
                        valueField: 'value',
                        panelHeight:'auto',
                        textField: 'name',
                        editable:false,
                        data:[{value:'original',name:'全题库'},{value:'random',name:'随机出题'},{value:'fixed',name:'固定出题'}]" />
                    </td>
                </tr>
                <tr class="topicNumber" style="display: none">
                    <td width="15%" align="left">随机出题数量：</td>
                    <td width="20%" length="100">
                        <input id="topicNumber" name="topicNumber" type="text" value="" class="easyui-validatebox"
                            style="width:  478px; height: 32px;display: none" />
                        <!--                                        <input id="topicNumber" name="topicNumber" type="text" value="" class="easyui-validatebox" data-options="required:true"  style="width:  350px; height: 32px;display: none"/>-->
                    </td>
                </tr>
                <tr>
                    <td width="15%" align="left">单选题比例：</td>
                    <td width="20%" length="100">
                        <input type="text" class="single" readonly="readonly" />
                    </td>
                </tr>

                <tr>
                    <td width="15%" align="left">多选题比例：</td>
                    <td width="20%" length="100">
                        <input type="text" class="more" readonly="readonly" />
                    </td>
                </tr>

                <tr>
                    <td width="15%" align="left">判断题比例：</td>
                    <td width="20%" length="100">
                        <input type="text" class="judge" readonly="readonly" />
                    </td>
                </tr>

                <tr>
                    <td width="15%" align="left">填空题比例：</td>
                    <td width="20%" length="100">
                        <input type="text" class="filling" readonly="readonly" />
                    </td>
                </tr>

                <tr>
                    <td width="15%" align="left">简答题比例：</td>
                    <td width="20%" length="100">
                        <input type="text" class="shortAnswer" readonly="readonly" />
                    </td>
                </tr>

                <tr>
                    <td width="15%" align="left">试卷规定时间限制：</td>
                    <td width="20%" length="100">
                        <input type="text" class="setTime" readonly="readonly" />
                    </td>
                </tr>


            </table>

        </form>
    </div>


</body>

</html>