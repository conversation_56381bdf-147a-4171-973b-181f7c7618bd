package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;
import com.sun.star.bridge.oleautomation.Decimal;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-04-25
 * @desc 单选问卷调查答题记录与统计Repository
 **/
public interface ExamAnswerNotesRepository extends LogicRepository<ExamQuestionUser, String> {


    /**
     * 查询当前人是否答过题目
     *
     * @return
     */
    @Query(value = "select * from (select * from us_exam_question_user qu  where qu.CREATOR=:creator and qu.ANNUAL_QUARTER_CODE=:annualQuarterCode and qu.ANSWER_STATUS=1 ) where rownum=1", nativeQuery = true)
    ExamQuestionUser findByCreator(@Param("creator") String creator, @Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 根据平均数计算百分制
     */
    @Query(value = "select round(:meanValue*20*1.00,2) as Centennial from dual",
            nativeQuery = true)
    Map<String, Object> calculateCentennial (@Param("meanValue") String meanValue);
    //机关各部门对机关各部门统计

    /**
     * 财务部统计
     * <p>
     * 统计机关各部门对财务部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'财务部' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-222-1','A-222-2','A-222-3','A-222-4','A-222-5','A-222-6') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> financeDepartment(@Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 党群工会统计
     * <p>
     * 统计机关各部门对党群工会6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'党群工会' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-222-7','A-222-8','A-222-9','A-222-10','A-222-11','A-222-12') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> PartyTradeUnion(@Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 人力资源部统计
     * <p>
     * 统计机关各部门对人力资源部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'人力资源部' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-222-13','A-222-14','A-222-15','A-222-16','A-222-17','A-222-18') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> humanResourcesDepartment(@Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 市场经营部统计
     * <p>
     * 统计机关各部门对市场经营部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select round(sum(t1.Avgs),2) as Avgs, round(sum(t1.Centennial),2) as Centennial, '政企客户部' as department" +
            "  from ( select avg(equ.answer_score) * 0.4 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.4, 5) as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-19','A-222-20','A-222-21','A-222-22','A-222-23','A-222-24')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName" +
            "        union all" +
            "        select avg(equ.answer_score) * 0.2 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.2, 5) as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-19','A-222-20','A-222-21','A-222-22','A-222-23','A-222-24')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName2" +
            "        union all" +
            "        select avg(equ.answer_score) * 0.2 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.2, 5) as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-19','A-222-20','A-222-21','A-222-22','A-222-23','A-222-24')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName3" +
            "        union all" +
            "        select avg(equ.answer_score) * 0.2 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.2, 5) as Centennialt" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-19','A-222-20','A-222-21','A-222-22','A-222-23','A-222-24')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName not in (:orgName, :orgName2, :orgName3)) t1",
            nativeQuery = true)
    Map<String, Object> marketingDepartment(@Param("annualQuarterCode") String annualQuarterCode, @Param("orgName") String orgName, @Param("orgName2") String orgName2, @Param("orgName3") String orgName3);

//    /**
//     * 客户服务中心统计
//     * <p>
//     * 统计机关各部门对客户服务中心6个维度的满意度评价平均值与百分制分值
//     */
//    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'客户服务中心' as department  " +
//            "from us_exam_question_user equ " +
//            "where equ.question_code IN ('A-222-25','A-222-26','A-222-27','A-222-28','A-222-29','A-222-30') " +
//            "and equ.annual_quarter_code=:annualQuarterCode "+
//            "and equ.answer_score is not null "+
//            "and equ.answer_status=1 "+
//            "and equ.answer_score!=0 ",
//            nativeQuery = true)
//    Map<String, Object> customerServiceCenter(@Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 网络部统计
     * <p>
     * 统计机关各部门对网络部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial  ,'网络部' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-222-25','A-222-26','A-222-27','A-222-28','A-222-29','A-222-30') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> networkDepartment(@Param("annualQuarterCode") String annualQuarterCode);


    @Query(value = "select round(sum(t1.Avgs),2) as Avgs, round(sum(t1.Centennial),2) as Centennial, '网络部' as department" +
            "  from ( select avg(equ.answer_score) * 0.4 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.4, 5) as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-25','A-222-26','A-222-27','A-222-28','A-222-29','A-222-30')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName" +
            "        union all" +
            "        select avg(equ.answer_score) * 0.2 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.2, 5) as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-25','A-222-26','A-222-27','A-222-28','A-222-29','A-222-30')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName2" +
            "        union all" +
            "        select avg(equ.answer_score) * 0.2 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.2, 5) as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-25','A-222-26','A-222-27','A-222-28','A-222-29','A-222-30')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName3" +
            "        union all" +
            "        select avg(equ.answer_score) * 0.2 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.2, 5) as Centennialt" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-25','A-222-26','A-222-27','A-222-28','A-222-29','A-222-30')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName not in (:orgName, :orgName2, :orgName3)) t1",
            nativeQuery = true)
    Map<String, Object> networkDepartment2(@Param("annualQuarterCode") String annualQuarterCode, @Param("orgName") String orgName, @Param("orgName2") String orgName2, @Param("orgName3") String orgName3);

    /**
     * 政企客户部统计
     * <p>
     * 统计机关各部门对政企客户部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'政企客户部' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-222-31','A-222-32','A-222-33','A-222-34','A-222-35','A-222-36') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> governmentEnterpriseCustomerDepartment(@Param("annualQuarterCode") String annualQuarterCode);


    @Query(value = "select round(sum(t1.Avgs),2) as Avgs, round(sum(t1.Centennial),2) as Centennial, '政企客户部' as department" +
            "  from ( select avg(equ.answer_score) * 0.4 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.4, 5) as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-31','A-222-32','A-222-33','A-222-34','A-222-35','A-222-36')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName" +
            "        union all" +
            "        select avg(equ.answer_score) * 0.2 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.2, 5) as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-31','A-222-32','A-222-33','A-222-34','A-222-35','A-222-36')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName2" +
            "        union all" +
            "        select avg(equ.answer_score) * 0.2 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.2, 5) as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-31','A-222-32','A-222-33','A-222-34','A-222-35','A-222-36')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName3" +
            "        union all" +
            "        select avg(equ.answer_score) * 0.2 as Avgs," +
            "                round(sum(equ.answer_score) * 20 / count(equ.answer_score) * 0.2, 5) as Centennialt" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-222-31','A-222-32','A-222-33','A-222-34','A-222-35','A-222-36')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName not in (:orgName, :orgName2, :orgName3)) t1",
            nativeQuery = true)
    Map<String, Object> governmentEnterpriseCustomerDepartment2(@Param("annualQuarterCode") String annualQuarterCode, @Param("orgName") String orgName, @Param("orgName2") String orgName2, @Param("orgName3") String orgName3);

    /**
     * 综合部统计
     * <p>
     * 统计机关各部门对综合部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial  ,'综合部' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-222-37','A-222-38','A-222-39','A-222-40','A-222-41','A-222-42') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> generalDepartment(@Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 工会统计
     * <p>
     * 统计机关各部门对综合部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial  ,'工会' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-222-43','A-222-44','A-222-45','A-222-46','A-222-47','A-222-48') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> laborUnionDepartment(@Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 客户服务部统计
     * <p>
     * 统计机关各部门对综合部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial  ,'客户服务部' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-222-49','A-222-50','A-222-51','A-222-52','A-222-53','A-222-54') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> clienteleDepartment(@Param("annualQuarterCode") String annualQuarterCode);
    //县分公司对机关各部门满意度评价统计

    /**
     * 财务部统计
     * <p>
     * 县分公司对财务部9个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'财务部' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-111-1','A-111-2','A-111-3','A-111-4','A-111-5','A-111-6','A-111-7','A-111-8','A-111-9') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> countyBranchFinanceDepartment(@Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 党群工会统计
     * <p>
     * 县分公司对党群工会6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'党群工会' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-111-10','A-111-11','A-111-12','A-111-13','A-111-14','A-111-15','A-111-16','A-111-17','A-111-18') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> countyBranchPartyTradeUnion(@Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 人力资源部统计
     * <p>
     * 县分公司对人力资源部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'人力资源' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-111-19','A-111-20','A-111-21','A-111-22','A-111-23','A-111-24','A-111-25','A-111-26','A-111-27') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> countyBranchHumanResourcesDepartment(@Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 市场经营部统计
     * <p>
     * 县分公司对市场经营部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select sum(t1.Avgs) as Avgs," +
            "       sum(t1.Centennial) as Centennial," +
            "       '市场经营部' as department" +
            "  from (select round(avg(equ.answer_score) * 0.4, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.4 as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-28', 'A-111-29', 'A-111-30', 'A-111-31', 'A-111-32', 'A-111-33', 'A-111-34', 'A-111-35', 'A-111-36')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.2 as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-28', 'A-111-29', 'A-111-30', 'A-111-31', 'A-111-32', 'A-111-33', 'A-111-34', 'A-111-35', 'A-111-36')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName =:orgName2" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.2 as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-28', 'A-111-29', 'A-111-30', 'A-111-31', 'A-111-32', 'A-111-33', 'A-111-34', 'A-111-35', 'A-111-36')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName =:orgName3" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.2 as Centennialt" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-28', 'A-111-29', 'A-111-30', 'A-111-31', 'A-111-32', 'A-111-33', 'A-111-34', 'A-111-35', 'A-111-36')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName not in (:orgName,:orgName2,:orgName3)) t1",
            nativeQuery = true)
    Map<String, Object> countyBranchMarketingDepartment(@Param("annualQuarterCode") String annualQuarterCode, @Param("orgName") String orgName, @Param("orgName2") String orgName2, @Param("orgName3") String orgName3);

//    /**
//     * 客户服务中心统计
//     * <p>
//     * 县分公司对客户服务中心6个维度的满意度评价平均值与百分制分值
//     */
//    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial  ,'客户服务中心' as department " +
//            "from us_exam_question_user equ " +
//            "where equ.question_code IN ('A-111-37','A-111-38','A-111-39','A-111-40','A-111-41','A-111-42','A-111-43','A-111-44','A-111-45') " +
//            "and equ.annual_quarter_code=:annualQuarterCode "+
//            "and equ.answer_score is not null "+
//            "and equ.answer_status=1 "+
//            "and equ.answer_score!=0 ",
//            nativeQuery = true)
//    Map<String, Object> countyBranchCustomerServiceCenter(@Param("annualQuarterCode") String annualQuarterCode);

    /**
     * 网络部统计
     * <p>
     * 县分公司对网络部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'网络部' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-111-37','A-111-38','A-111-39','A-111-40','A-111-41','A-111-42','A-111-43','A-111-44','A-111-45') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> countyBranchNetworkDepartment(@Param("annualQuarterCode") String annualQuarterCode);


    @Query(value = "select sum(t1.Avgs) as Avgs," +
            "       sum(t1.Centennial) as Centennial," +
            "       '网络部' as department" +
            "  from (select round(avg(equ.answer_score) * 0.4, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.4 as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-37','A-111-38','A-111-39','A-111-40','A-111-41','A-111-42','A-111-43','A-111-44','A-111-45')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.2 as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-37','A-111-38','A-111-39','A-111-40','A-111-41','A-111-42','A-111-43','A-111-44','A-111-45')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName =:orgName2" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.2 as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-37','A-111-38','A-111-39','A-111-40','A-111-41','A-111-42','A-111-43','A-111-44','A-111-45')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName =:orgName3" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.2 as Centennialt" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-37','A-111-38','A-111-39','A-111-40','A-111-41','A-111-42','A-111-43','A-111-44','A-111-45')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName not in (:orgName,:orgName2,:orgName3)) t1",
            nativeQuery = true)
    Map<String, Object> countyBranchNetworkDepartment2(@Param("annualQuarterCode") String annualQuarterCode, @Param("orgName") String orgName, @Param("orgName2") String orgName2, @Param("orgName3") String orgName3);

    /**
     * 政企客户部统计
     * <p>
     * 县分公司对政企客户部6个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'政企客户部' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-111-46','A-111-47','A-111-48','A-111-49','A-111-50','A-111-51','A-111-52','A-111-53','A-111-54') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> countyBranchGovernmentEnterpriseCustomerDepartment(@Param("annualQuarterCode") String annualQuarterCode);

    @Query(value = "select sum(t1.Avgs) as Avgs," +
            "       sum(t1.Centennial) as Centennial," +
            "       '政企客户部' as department" +
            "  from (select round(avg(equ.answer_score) * 0.4, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.4 as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-46','A-111-47','A-111-48','A-111-49','A-111-50','A-111-51','A-111-52','A-111-53','A-111-54')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName = :orgName" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.2 as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-46','A-111-47','A-111-48','A-111-49','A-111-50','A-111-51','A-111-52','A-111-53','A-111-54')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName =:orgName2" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.2 as Centennial" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-46','A-111-47','A-111-48','A-111-49','A-111-50','A-111-51','A-111-52','A-111-53','A-111-54')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName =:orgName3" +
            "        union all" +
            "        select round(avg(equ.answer_score) * 0.2, 2) as Avgs," +
            "               round(sum(equ.answer_score) * 20 / count(equ.answer_score), 2) * 0.2 as Centennialt" +
            "          from us_exam_question_user equ" +
            "          left join uums.v_user_org_only v" +
            "            on equ.creator = v.username" +
            "         where equ.question_code IN ('A-111-46','A-111-47','A-111-48','A-111-49','A-111-50','A-111-51','A-111-52','A-111-53','A-111-54')" +
            "           and equ.annual_quarter_code = :annualQuarterCode" +
            "           and equ.answer_score is not null" +
            "           and equ.answer_status = 1" +
            "           and equ.answer_score != 0" +
            "           and v.departmentName not in (:orgName,:orgName2,:orgName3)) t1",
            nativeQuery = true)
    Map<String, Object> countyBranchGovernmentEnterpriseCustomerDepartment2(@Param("annualQuarterCode") String annualQuarterCode, @Param("orgName") String orgName, @Param("orgName2") String orgName2, @Param("orgName3") String orgName3);

    /**
     * 综合部统计
     * <p>
     * 县分公司对综合部9个维度的满意度评价平均值与百分制分值
     */
    @Query(value = "select  round(avg (equ.answer_score),2) as Avgs,round(sum (equ.answer_score)*20/count(equ.answer_score),2) as Centennial ,'综合部' as department  " +
            "from us_exam_question_user equ " +
            "where equ.question_code IN ('A-111-55','A-111-56','A-111-57','A-111-58','A-111-59','A-111-60','A-111-61','A-111-62','A-111-63') " +
            "and equ.annual_quarter_code=:annualQuarterCode " +
            "and equ.answer_score is not null " +
            "and equ.answer_status=1 " +
            "and equ.answer_score!=0 ",
            nativeQuery = true)
    Map<String, Object> countyBranchGeneralDepartment(@Param("annualQuarterCode") String annualQuarterCode);

}
