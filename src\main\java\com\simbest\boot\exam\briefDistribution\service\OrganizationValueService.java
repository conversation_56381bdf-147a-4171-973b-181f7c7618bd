package com.simbest.boot.exam.briefDistribution.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.appraise.model.AppraiseItem;
import com.simbest.boot.exam.briefDistribution.model.OrganizationValue;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface OrganizationValueService extends ILogicService<OrganizationValue, String> {

    List<OrganizationValue> findList();

    /**
     * 组织人员导入
     * @param request
     * @param response
     */
    void importExcel(HttpServletRequest request, HttpServletResponse response);
}
