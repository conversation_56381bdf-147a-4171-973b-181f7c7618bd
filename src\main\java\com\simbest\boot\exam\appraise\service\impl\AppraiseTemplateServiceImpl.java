package com.simbest.boot.exam.appraise.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.appraise.model.AppraiseTemplate;
import com.simbest.boot.exam.appraise.repository.AppraiseTemplateRepository;
import com.simbest.boot.exam.appraise.service.IAppraiseTemplateService;
import com.simbest.boot.exam.test.util.MyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppraiseTemplateServiceImpl extends LogicService<AppraiseTemplate, String> implements IAppraiseTemplateService {

    private final AppraiseTemplateRepository repository;

    public AppraiseTemplateServiceImpl(AppraiseTemplateRepository repository) {
        super(repository);
        this.repository = repository;
    }

    /**
     * 保存所有
     * @param list list
     * @return 返回 保存后的对象
     */
    @Override
    public List<AppraiseTemplate> saveAll(List<AppraiseTemplate> list) {
        String uniqueCode = MyUtils.getUniqueCode("Temp");
        return list.stream().peek(v-> v.setCode(uniqueCode)).map(super::insert)
                .collect(Collectors.toList());
    }

}
