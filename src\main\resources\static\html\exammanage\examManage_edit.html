<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>考试维护信息</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
 <style>
   input{
     width:185px;
   }
 </style>
  <script type="text/javascript">
    $(function(){
      //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
      var gps=getQueryString();
      loadForm("examBacklogTableUpdateForm");
      //判断是否是查看方式打开
      if(gps.type=='read'){//如果是查看

        ajaxgeneral({
          url:"action/summary/findExamSummaryInfo?id="+gps.id,
          success:function(res){
            formval(res.data,"examBacklogTableUpdateForm");
          }
        });
        formReadonly("examBacklogTableUpdateForm");
          $(".noEdit").hide();
          
      }else if(gps.type=='edit'){//如果是编辑

        ajaxgeneral({
          url:"action/summary/findExamSummaryInfo?id="+gps.id,
          success:function(res){
            formval(res.data,"examBacklogTableUpdateForm");
            if (res.data.appEnabled) {
              $('.appEnabled').switchbutton("setValue",true)
            }else{
              $('.appEnabled').switchbutton("setValue",false)
            }
            if (res.data.pcEnabled) {
              $('.pcEnabled').switchbutton("setValue",true)
            }else{
              $('.pcEnabled').switchbutton("setValue",false)
            }
          }
        });
        $(".noEdit").hide();
          
      }

      //动态获取input内容用提示框展示
      $('.tips').tooltip({
      content: '<span style="color:#000"></span>',
      onShow: function(){
        $(this).tooltip('tip').css({
          backgroundColor: '#fff',
          borderColor: '#000'
        });
        var txt=$('input',this).val();
        $(this).tooltip('tip').html(txt);
      }});


      //修改验证框样式
      $('.easyui-validatebox').validatebox({    
        tipPosition: 'right',
        validateOnCreate:false
      });  
      $('.easyui-validatebox').validatebox('disableValidation');
      if(gps.type == "read") {
          idReadonly('examName')
      }
    });






    //表单校验
    window.fvalidate = function () {
        return $("#examBacklogTableUpdateForm").form("validate");
    };

    //重新加载table列表
    function loadList(){
      $('#examBacklogTableUpdateForm').datagrid('reload');
    }


    window.getchoosedata = function () {
      $('.easyui-validatebox').validatebox('enableValidation');
      if (fvalidate()) {
        //..
      }else{
        return;
      }
      var datas=[];
      var data={};
      data.id=$('.id').val();
      data.examName=$('.examName').val();
      data.examCode = $('.examCode').val();
      data.appExamUrl=$('.appExamUrl').val();
      data.appImageUrl = $('.appImageUrl').val();
      data.pcExamUrl=$('.pcExamUrl').val();
        data.smsExamUrl=$('.smsExamUrl').val();
      data.pcImageUrl = $('.pcImageUrl').val();
      data.examStartTime = $('#examStartTime').datebox('getValue');
      data.examEndTime = $('#examEndTime').datebox('getValue');
      data.weightValue = $('.weightValue').val();
      data.workType = $('.workType').val();
      if ($('.appEnabled').switchbutton("options").checked) {
        data.appEnabled = true;
      }else{
        data.appEnabled = false;
      }
      if ($('.pcEnabled').switchbutton("options").checked) {
        data.pcEnabled = true;
      }else{
        data.pcEnabled = false;
      }
      datas.push(data);
      if (data.id==null || data.id==""){
          top.mesAlert("提示", "考试名称不能为空", 'warning');
          return {"state":0};
      }
      if (data.examName==null || data.examName==""){
          top.mesAlert("提示", "考试名称不能为空", 'warning');
          return {"state":0};
      }
      if (data.examCode==null || data.examCode==""){
          top.mesAlert("提示", "考试编号不能为空", 'warning');
          return {"state":0};
      }
      // if (data.appExamUrl==null || data.appExamUrl==""){
      //     top.mesAlert("提示", "移动端考试跳转页面路径不能为空", 'warning');
      //     return {"state":0};
      // }
      // if (data.appImageUrl==null || data.appImageUrl==""){
      //     top.mesAlert("提示", "移动端端悬浮窗口图片路径不能为空", 'warning');
      //     return {"state":0};
      // }
      // if (data.pcExamUrl==null || data.pcExamUrl==""){
      //     top.mesAlert("提示", "pc端考试跳转页面路径不能为空", 'warning');
      //     return {"state":0};
      // }
      // if (data.pcImageUrl==null || data.pcImageUrl==""){
      //     top.mesAlert("提示", "pc端悬浮窗口图片路径不能为空", 'warning');
      //     return {"state":0};
      // }
      if (data.examStartTime==null || data.examStartTime==""){
          top.mesAlert("提示", "考试开始时间不能为空", 'warning');
          return {"state":0};
      }
      if (data.examEndTime==null || data.examEndTime==""){
          top.mesAlert("提示", "考试结束时间不能为空", 'warning');
          return {"state":0};
      }
      if (data.weightValue==null || data.weightValue==""){
          top.mesAlert("提示", "考试权重系数不能为空", 'warning');
          return {"state":0};
      }
      if (data.examStartTime > data.examEndTime) {
          top.mesAlert("提示", "考试开始时间不能在考试结束时间之前", 'warning');
          return {"state":0};
      }
      return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１         
        };





  </script>
</head>
<body style="padding-top: 0;">
<!--searchform-->
<div class="table_searchD">
  <form id="examBacklogTableUpdateForm">
    <table border="0" cellpadding="10" cellspacing="16" width="100%">
      <tr>
        <td width="90" hidden="true" align="right"><span class="col_r">*</span>考试id：</td>
        <td width="150" hidden="true"><input id="id" class="easyui-validatebox id" required="ture" validType="string" name="id" type="text"  /></td>

        
        <td width="90" align="right"><span class="col_r">*</span>考试名称：</td>
        <td width="150" ><input id="examName" class="easyui-validatebox examName" required="ture" validType="string" name="examName" type="text"  /></td>

        <td width="90" align="right"><span class="col_r">*</span>考试编号：</td>
        <td width="150"><input id="examCode" class="easyui-validatebox examCode" required="ture" readonly="true" validType="string" name="examCode" type="text"  /></td>

      </tr>
      <tr>

        <td width="90" align="right"><span class="col_r">*</span>移动端展示：</td>
        <td width="150" ><input id="appEnabled" class="easyui-switchbutton appEnabled" data-options="onText:'是',offText:'否'"> </td>

        <td width="90" align="right"><span class="col_r">*</span>PC端展示：</td>
        <td width="150" ><input id="pcEnabled" class="easyui-switchbutton pcEnabled" data-options="onText:'是',offText:'否'"></td>
      
      </tr>
      <tr class=" noEdit">        <!--不可修改  -->


        <td width="90"  align="right"><span class="col_r">*</span>应考人数：</td>
        <td width="150"><input id="joinExamNum" name="joinExamNum" type="text"  /></td>

        <td width="90"  align="right"><span class="col_r">*</span>实考人数：</td>
        <td width="150"><input id="actualExamNum" name="actualExamNum" type="text"  /></td>
        <td>

      </tr>
      <tr>

        <td width="90" align="right"><span class="col_r"></span>移动端考试跳转页面路径：</td>
        <td width="150"  class="tips easyui-tooltip"><input class="easyui-validatebox appExamUrl"  validType="url" id="appExamUrl" name="appExamUrl" type="text"  /></td>

        <td width="90" align="right"><span class="col_r"></span>移动端端悬浮窗口图片路径：</td>
        <td width="150"  class="tips easyui-tooltip"><input class="easyui-validatebox appImageUrl"  validType="url" id="appImageUrl" name="appImageUrl" type="text"  /></td>

      </tr>
      <tr>

        <td width="90" align="right"><span class="col_r"></span>pc端考试跳转页面路径：</td>
        <td width="150"  class="tips easyui-tooltip"><input class="easyui-validatebox pcExamUrl"  validType="url" id="pcExamUrl" name="pcExamUrl" type="text"  /></td>

        <td width="90" align="right"><span class="col_r"></span>pc端悬浮窗口图片路径：</td>
        <td width="150"  class="tips easyui-tooltip"><input class="easyui-validatebox pcImageUrl"  validType="url" id="pcImageUrl" name="pcImageUrl" type="text"  /></td>

      </tr>

        <tr>

            <td width="90" align="right"><span class="col_r"></span>sms端考试跳转页面路径：</td>
            <td width="150"  class="tips easyui-tooltip"><input class="easyui-validatebox smsExamUrl"   id="smsExamUrl" name="smsExamUrl" type="text"  /></td>

            <td width="90" align="right"></td>
            <td width="150"  class="tips easyui-tooltip"></td>

        </tr>
      <tr>

        <td width="90" align="right"><span class="col_r">*</span>考试开始时间：</td>
        <td width="150"><input id="examStartTime" class="easyui-datetimebox" name="examStartTime"
          data-options="showSeconds:true,editable:false" style="width:185px; height: 32px;"></td>

        <td width="90" align="right"><span class="col_r">*</span>考试结束时间：</td>
        <td width="150"><input id="examEndTime" class="easyui-datetimebox" name="examEndTime"
          data-options="showSeconds:true,editable:false" style="width:185px; height: 32px;"></td>

      </tr>
      <tr class="noEdit "><!--不可修改  -->

        <td width="90" align="right"><span class="col_r">*</span>待办推送状态：</td>
        <td width="150"><input id="workFlag" name="workFlag" type="text"  /></td>

        <td width="90" align="right"><span class="col_r">*</span>统一待办推送状态：</td>
        <td width="150"><input id="todoFlag" name="todoFlag" type="text"  /></td>

        <td width="90" align="right"><span class="col_r">*</span>消息推送状态：</td>
        <td width="150"><input id="messageFlag" name="messageFlag" type="text"  /></td>
          
      </tr>
      <tr>
        <td width="90" align="right"><span class="col_r">*</span>考试权重系数：</td>
        <td width="150"><input id="weightValue" class="easyui-validatebox weightValue" required="ture" validType="int" name="weightValue" type="text"  /></td>

         <td width="90" align="right">待办类型：</td>
         <td width="150"><input id="workType" class="easyui-validatebox workType" name="workType" type="text"  /></td>
      </tr>
    </table>
  </form>
</div>

  <!-- <div class="appInfoTable" style="margin-top: 20px;"><table id="appInfoTable"></table></div> -->
</body>
</html>
