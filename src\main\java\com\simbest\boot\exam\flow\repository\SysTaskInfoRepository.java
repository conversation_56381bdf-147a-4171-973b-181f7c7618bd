package com.simbest.boot.exam.flow.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.flow.model.SysTaskInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: SysTaskInfoRepository
 * @description:
 * @author: ZHAOBO
 * @create: 2024-07-09 17:31
 */
@Repository
public interface SysTaskInfoRepository extends LogicRepository<SysTaskInfo, String > {

    /**
     * 查询我的待办
     * @param pageable
     * @param title
     * @param pmInsTypes
     * @return
     */
    @Query(
            value = "select t.process_inst_id , t.task_id , t.task_username , t.task_true_name , to_char(t.start_time , 'yyyy-MM-dd HH24:mi:ss') as start_time , t.activity_def_code , t.activity_def_name , i.target_id , i.target_info , i.action_id , i.action_info , i.task_info_id , i.task_info , i.host_unit_code , i.host_unit_name    " +
                    "  from SYS_TASK_INFO           t, " +
                    "       us_application_sub_info i, " +
                    "       us_application_org_user ou " +
                    " where t.application_org_user_id = ou.id " +
                    "   and ou.application_sub_id = i.id " +
                    "   and i.target_info like  concat('%' , concat(:title , '%')) " +
                    "   and t.pm_ins_type in (:pmInsTypes) " +
                    "   and t.status = 0" +
                    "   and t.enabled = 1 " +
                    "   and ou.enabled = 1 " +
                    "   and i.enabled = 1 " +
                    "   order by t.start_time desc  " ,
            countQuery = "select count(*)    " +
                    "  from SYS_TASK_INFO           t, " +
                    "       us_application_sub_info i, " +
                    "       us_application_org_user ou " +
                    " where t.application_org_user_id = ou.id " +
                    "   and ou.application_sub_id = i.id " +
                    "   and i.target_info like  concat('%' , concat(:title , '%')) " +
                    "   and t.pm_ins_type in (:pmInsTypes) " +
                    "   and t.status = 0 " +
                    "   and t.enabled = 1 " +
                    "   and ou.enabled = 1 " +
                    "   and i.enabled = 1 " ,
            nativeQuery = true
    )
    Page<Map<String , Object>> myTaskToDo(Pageable pageable, @Param("title") String title , @Param("pmInsTypes")  List<String> pmInsTypes);


    /**
     * 查询我的待办
     * @param pageable
     * @param title
     * @param pmInsTypes
     * @return
     */
    @Query(
            value = "select t.process_inst_id , t.task_id , t.task_username , t.task_true_name , to_char(t.start_time , 'yyyy-MM-dd HH24:mi:ss') as start_time , to_char(t.end_time , 'yyyy-MM-dd HH24:mi:ss') as end_time , t.activity_def_code , t.activity_def_name , i.target_id , i.target_info , i.action_id , i.action_info , i.task_info_id , i.task_info , i.host_unit_code , i.host_unit_name    " +
                    "  from SYS_TASK_INFO           t, " +
                    "       us_application_sub_info i, " +
                    "       us_application_org_user ou " +
                    " where t.application_org_user_id = ou.id " +
                    "   and ou.application_sub_id = i.id " +
                    "   and i.target_info like  concat('%' , concat(:title , '%')) " +
                    "   and t.pm_ins_type in (:pmInsTypes) " +
                    "   and t.status = 1" +
                    "   and t.enabled = 1 " +
                    "   and ou.enabled = 1 " +
                    "   and i.enabled = 1 " +
                    "   order by t.start_time desc  " ,
            countQuery = "select count(*)    " +
                    "  from SYS_TASK_INFO           t, " +
                    "       us_application_sub_info i, " +
                    "       us_application_org_user ou " +
                    " where t.application_org_user_id = ou.id " +
                    "   and ou.application_sub_id = i.id " +
                    "   and i.target_info like  concat('%' , concat(:title , '%')) " +
                    "   and t.pm_ins_type in (:pmInsTypes) " +
                    "   and t.status = 1 " +
                    "   and t.enabled = 1 " +
                    "   and ou.enabled = 1 " +
                    "   and i.enabled = 1 " ,
            nativeQuery = true
    )
    Page<Map<String , Object>> myJoin(Pageable pageable, @Param("title") String title , @Param("pmInsTypes")  List<String> pmInsTypes);




    List<SysTaskInfo> findAllByEnabledAndPmInsId(Boolean enabled, String pmInsId);
    SysTaskInfo findAllByEnabledAndTaskId(Boolean enabled, String taskId);


    List<SysTaskInfo> findAllByEnabledAndPmInsIdAndActivityDefIdAndTaskUsernameAndStatus(Boolean enabled, String pmInsId, String activityDefId, String taskUsername, Integer status);

}
