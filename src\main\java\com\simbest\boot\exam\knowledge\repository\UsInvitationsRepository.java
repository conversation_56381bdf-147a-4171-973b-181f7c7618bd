/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.repository;/**
 * Created by KZH on 2019/10/8 16:06.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.knowledge.model.UsInvitations;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:06
 * @desc
 **/
public interface UsInvitationsRepository extends LogicRepository<UsInvitations, String> {

    /**
     * 根据邀请人和被邀请人查询剩余邀请次数
     * A邀请B,只要B接受，则A与B都将少一次邀请次数
     *
     * 也可用来查询指定日期指定人员剩余多少邀请次数
     */
    @Query(value = "SELECT *" +
            " FROM US_INVITATIONS t" +
            " WHERE t.ENABLED = 1" +
            " AND to_char(t.CREATED_TIME, 'yyyy-MM-dd') = :currentDay" +
            " AND (t.SEND_USER_NAME = :sendUserName" +
            " OR t.REC_USER_NAME = :sendUserName)", nativeQuery = true)
    List<UsInvitations> countExamWorkByTask(@Param("currentDay") String currentDay,@Param("sendUserName") String sendUserName);

    /**
     * 查询被邀请人列表信息
     * @param currentDay
     * @param sendUserName
     * @return
     */
    @Query(value = "select t.*" +
            "  from US_INVITATIONS t" +
            " where t.enabled = 1" +
            "   and to_char(t.created_time,'yyyy-MM-dd')=:currentDay" +
            "   and t.rec_user_name=:recUserName  and t.status ='PENDING'", nativeQuery = true)
    List<UsInvitations> InvitedTask(@Param("currentDay") String currentDay,@Param("recUserName") String recUserName);
    /**
     * 当天12点取消当日所有预约
     * @param currentDay
     * @return
     */

    @Query(value = "select * from US_INVITATIONS t where t.enabled=1 and to_char(t.created_time,'yyyy-MM-dd')=:currentDay and t.status in ('PENDING')", nativeQuery = true)
    List<UsInvitations> cancleInvitations(@Param("currentDay") String currentDay);

    /**
     * 查询已邀请接受但是未答完题的情况
     * @param currentDay
     * @return
     */
    @Query(value = "select t.*" +
            "  from US_INVITATIONS t,US_QUIZ_SESSIONS q" +
            " where t.enabled = 1 and q.enabled=1 " +
            "   and to_char(t.created_time, 'yyyy-MM-dd') =:currentDay" +
            "   and t.status = 'ACCEPTED'" +
            "   and t.id=q.INVITATION_ID" +
            "   and q.status='ONGOING'", nativeQuery = true)
    List<UsInvitations> cancleInvitationsAcc(@Param("currentDay") String currentDay);

    /**
     * 查询指定日期指定类型邀请
     * @param currentDay
     * @param status
     * @return
     */
    @Query(value = "select t.*" +
            "  from US_INVITATIONS t" +
            " where t.enabled = 1" +
            "   and to_char(t.created_time, 'yyyy-MM-dd') =:currentDay" +
            "   and t.status =:status", nativeQuery = true)
    List<UsInvitations> findByStatus(@Param("currentDay") String currentDay, @Param("status") String status);

    /**
     * 查询超出指定时间内的邀请信息
     * @return
     */
    @Query(value = "SELECT *" +
            "  FROM US_INVITATIONS t" +
            " WHERE t.enabled = 1" +
            " and t.created_time <= :localDateTime " +
            //"   and (SYSDATE - TO_DATE(t.created_time, 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 > 10" +
            "   and t.status in ('PENDING')", nativeQuery = true)
    List<UsInvitations> findTimeOutByStatus(@Param("localDateTime") LocalDateTime localDateTime );


    /**
     * 查询当前人发送邀请 待接收及已接收列表信息
     * @return
     */
    /*@Query(value = "SELECT *" +
            "  FROM US_INVITATIONS t" +
            " WHERE t.enabled = 1" +
            " AND to_char(t.CREATED_TIME, 'yyyy-MM-dd') = :currentDay" +
            " AND (t.SEND_USER_NAME = :sendUserName AND t.status in ('PENDING','ACCEPTED'))   " +
            " OR (t.REC_USER_NAME = :sendUserName AND t.status in ('PENDING') )  " , nativeQuery = true)
    List<UsInvitations>  findByUserName(@Param("sendUserName") String sendUserName, @Param("currentDay") String currentStr);*/


    @Query(
            value = "SELECT t.* , 'PENDING' as task_status  " +
                    "  FROM US_INVITATIONS t " +
                    " WHERE t.enabled = 1 " +
                    " AND to_char(t.CREATED_TIME, 'yyyy-MM-dd') = :currentDay" +
                    " and (t.SEND_USER_NAME =  :sendUserName or t.REC_USER_NAME = :sendUserName ) " +
                    "  AND t.status in ('PENDING')  union " +
                    " SELECT t.* , task.STATUS as task_status   " +
                    "  FROM US_INVITATIONS t , US_PENDING_TASK task  " +
                    "  WHERE t.id = task.invitation_id  " +
                    "  and  (task.pend_user_name = t.SEND_USER_NAME or task.pend_user_name = t.REC_USER_NAME  )   " +
                    "  and task.pend_user_name = :sendUserName and task.enabled = 1 and  t.enabled = 1  " +
                    " AND to_char(t.CREATED_TIME, 'yyyy-MM-dd') = :currentDay" +
                    "  and t.STATUS in ('ACCEPTED') " +
                    "  and task.STATUS in ('ACCEPTED' , 'PENDING' , 'ONGOING') " , nativeQuery = true
    )
    List<Map<String , Object>>  findByUserName(@Param("sendUserName") String sendUserName, @Param("currentDay") String currentStr);

    @Query(value = "select t.* from US_INVITATIONS t where t.enabled=1  and t.pm_ins_id =:pmInsId  ", nativeQuery = true)
    List<UsInvitations> findByPmInsId(@Param("pmInsId") String pmInsId);
}
