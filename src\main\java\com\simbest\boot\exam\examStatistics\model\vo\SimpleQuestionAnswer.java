package com.simbest.boot.exam.examStatistics.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * SimpleQuestion
 *
 * <AUTHOR>
 * @since 2024/4/19 10:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimpleQuestionAnswer implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "题目编码")
    private String questionCode;

    @ApiModelProperty(value = "答案编码")
    private String answerCode;

    @ApiModelProperty(value = "答案内容")
    private String answerContent;

    @ApiModelProperty(value = "是否为正确答案")
    private Boolean isCorrect;

    @ApiModelProperty(value = "该答案分值")
    private String answerScore;


}
