package com.simbest.boot.exam.publicLottery.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.publicLottery.model.PublicLotteryPerson;
import com.simbest.boot.exam.publicLottery.repository.PublicLotteryPersonRepository;
import com.simbest.boot.exam.publicLottery.service.IPublicLotteryPersonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PublicLotteryPersonServiceImpl extends LogicService<PublicLotteryPerson, String> implements IPublicLotteryPersonService {
    public PublicLotteryPersonRepository repository;

    public PublicLotteryPersonServiceImpl(PublicLotteryPersonRepository repository) {
        super(repository);
        this.repository = repository;
    }

}
