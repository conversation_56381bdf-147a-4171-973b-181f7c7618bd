/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.component.distributed.lock.DistributedRedisLock;
import com.simbest.boot.component.repeat.lock.RequestLock;
import com.simbest.boot.exam.examOnline.model.ExamSurvey;
import com.simbest.boot.exam.examOnline.service.IExamSurveyService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api
@RestController
@RequestMapping(value = "action/task/examSurvey")
public class ExamSurveyController extends LogicController<ExamSurvey, String> {

    private IExamSurveyService examSurveyService;

    @Autowired
    public ExamSurveyController(IExamSurveyService examSurveyService) {
        super(examSurveyService);
        this.examSurveyService = examSurveyService;

    }

    /**
     * 用户新增考试
     * 注意要处理
     *
     * @param examSurvey
     */
    @ApiOperation(value = "用户新增考试约信息", notes = "用户新增考试约信息")
    @PostMapping(value = {"/createExamSurvey", "/api/createExamSurvey", "/createExamSurvey/sso"})
    @RequestLock
    public JsonResponse createExamSurvey(@RequestBody ExamSurvey examSurvey) {
        IUser currentUser = SecurityUtils.getCurrentUser();
        try {
            DistributedRedisLock.lock(currentUser.getUsername(), 10);
            JsonResponse jsonResponse = examSurveyService.createExamSurvey(examSurvey);
        }catch (Exception e){
            Exceptions.printException(e);
        }finally {
            DistributedRedisLock.unlock(currentUser.getUsername());
        }
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 更新试卷
     *
     * @param examSurvey 包含预约ID的请求参数
     * @return 取消预约的结果
     */
    @ApiOperation(value = "用户取消预约信息", notes = "用户取消预约信息")
    @PostMapping(value = {"/updateExamSurvey", "/api/updateExamSurvey", "/updateExamSurvey/sso"})
    public JsonResponse updateExamSurvey(@RequestBody ExamSurvey examSurvey) {

        // 更新预约记录
        ExamSurvey updatedRecord = examSurveyService.update(examSurvey);
        return JsonResponse.success(updatedRecord);
    }

    /**
     * 用户删除试卷信息
     *
     * @param id 包含预约ID的请求参数
     * @return 取消预约的结果
     */
    @ApiOperation(value = "用户删除试卷信息", notes = "用户取消预约信息")
    @PostMapping(value = {"/deleteExamSurvey", "/api/deleteExamSurvey", "/deleteExamSurvey/sso"})
    public JsonResponse deleteExamSurvey(String id) {
        if(StringUtil.isNotEmpty(id)){
            examSurveyService.deleteById(id);
        }
        return JsonResponse.defaultSuccessResponse();
    }


    /**
     * 用户查询试卷信息
     *
     * @param id 包含预约ID的请求参数
     * @return 取消预约的结果
     */
    @ApiOperation(value = "用户查询试卷信息", notes = "用户查询试卷信息")
    @PostMapping(value = {"/findByExamSurvey", "/api/findByExamSurvey", "/findByExamSurvey/sso"})
    public JsonResponse 用户查询试卷信息(String id) {
        if(StringUtil.isNotEmpty(id)){
            ExamSurvey examSurvey = examSurveyService.findById(id);
            return JsonResponse.success(examSurvey);
        }
        return JsonResponse.defaultSuccessResponse();
    }



    @ApiOperation(value = "提交试卷", notes = "新版-提交试卷")
    @PostMapping(value = {"/submitExamNew", "sso/submitExamNew", "api/submitExamNew"})
    public JsonResponse submitExamNew(@RequestParam(required = false) String currentUserCode,
                                      @RequestParam(required = false, defaultValue = "PC") String source,
                                      @RequestBody(required = false) ExamSurvey examSurvey) {
        return JsonResponse.success(examSurveyService.submitExamNew(currentUserCode, source, examSurvey));
    }


    @ApiOperation(value = "查询已办信息", notes = "查询已办信息")
    @PostMapping(value = {"/findJoinDetail", "/sso/findJoinDetail", "/api/findJoinDetail"})
    public JsonResponse findByWorkType(@RequestParam String workId) {
        try {
            ExamSurvey joinDetail = examSurveyService.findJoinDetail(workId);
            return JsonResponse.success(joinDetail);
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.defaultErrorResponse();
        }
    }


}
