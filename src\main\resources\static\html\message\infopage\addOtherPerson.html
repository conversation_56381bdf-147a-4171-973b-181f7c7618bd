<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>组织人员树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<style type="text/css">
    .ztree li ul.line{height:auto;}
    .keyinput{width: 480px;padding: 10px;}
    #key{width: 415px;}
</style>
<body class="page_body">
<!-- <div class="keyinput">
    <input id="key" type="text" style="width:150px;" placeholder="请输入姓名进行查询"/>
    <a class="btn a_warning " title="人员查询" style="height: 32px;padding: 0 10px;line-height: 32px;float: none;display: inline-block;margin-top: -20px;position: relative;top:12px;"  onclick="chooseuser()"><i class="iconfont">&#xe634;</i></a>
</div> -->
<ul id="orgTree"></ul>
<div class="role orgC"></div>
<script type="text/javascript">
    var gps=getQueryString();
    var hasChoosedUsers = gps.hasChoosed;
    var isdisabled;
    var hasChoosed = '';
    $(function(){
        treeLoadSuccess();
        initTree();
        $(document).on("click",".role a i",function(){
            var str = $(this).parent("a").attr('id');
            hasChoosed = hasChoosed.replace(str, "");
            $(this).parent("a").remove();
        });
        if(gps.isClear){
            $(".role").html("");
        }
    });
    //初始化
    function initTree(truename) {
        if(truename == undefined || truename == ""){
            var ajaxopts = {
                //uums
                url:"uums/sys/userinfo/findOneStep?appcode="+web.appCode + '&orgCode=' + top.orgCode,
                data:{"appCode":web.appCode},
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    userIdzNodes = data.data;
                    userTree(userIdzNodes,'all');
                }
            };
        }else{
            var ajaxopts = {
                url: 'uums/sys/userinfo/findDimUserTree?appcode=' + web.appCode ,
                contentType: "application/json; charset=utf-8",
                data: {'truename': truename},
                success: function (data) {
                    userIdzNodes = data.data;
                    userTree(userIdzNodes,'user');
                }
            }
        }
        ajaxgeneral(ajaxopts);
    }
    //树
    function userTree(userIdzNodes,treesta) {
        if(treesta == 'all'){
            var datas=toTreeData(userIdzNodes,"id","parentId","id|id,name|text,parentId,id,treeType");
            $("#orgTree").tree({
                //checkbox:true,//是否在每一个借点之前都显示复选框
                lines:true,//是否显示树控件上的虚线
                treePid:'parentId',
                queryParams:{"appCode":web.appCode},
                contentType: "application/json; charset=utf-8",
                //cascadeCheck:false,
                // onlyLeafCheck:true,
                // onlyone:gps.multi==0?true:false,//不要乱配
                // onlyone:true,
                data: datas,
                fileds:'id,parentId,name|text,treeType',
                animate:true,//节点在展开或折叠的时候是否显示动画效果
                onClick:function(node){
                    if(node.treeType=="org"){
                        if(node.children){
                            if(node.children.length==0) top.mesAlert("提示信息","该组织无下级组织！", 'info');
                        }else{
                            ajaxgeneral({
                                url:"uums/sys/userinfo/findOneStep?appcode="+web.appCode+"&orgCode="+node.id,
                                data:{"appCode":web.appCode,"orgCode":node.id},
                                contentType: "application/json; charset=utf-8",
                                success:function(data){
                                    if(data.data.length==0){
                                        top.mesAlert("提示信息","该组织无下级数据！", 'info');
                                    }else{
                                        for(var i in data.data){
                                            data.data[i].text=data.data[i].name;
                                        }
                                        $("#orgTree").tree("append", {
                                            parent : node.target,
                                            data : data.data
                                        });
                                    }
                                }
                            });
                        }
                    }
                },
                onLoadSuccess:function(node,data){
                    $("#orgTree").find("ul >li:not(:has(ul))").find('.tree-title').css({'background':'whith','border':'whith','color':'black'});   // 选择最后一个节点 并添加 float:left
                },
                onBeforeSelect:function(node){
                    if(node.treeType=="group"||node.treeType=="org") return false;
                    if(gps.multi==0){
                        var nodes=$("#orgTree").tree("getChecked");
                        for(var i in nodes){
                            //var nodei=$("#orgTree").tree("find",nodes[i].id);
                            $("#orgTree").tree("uncheck",nodes[i].target);
                        }
                        $(".role").html("");
                    }
                    // if((getObjects("id,name",node.id+","+node.name)) == true){
                    //     $(".role").append("<a name='"+node.name+"' id='"+node.id+"' parentId='"+node.parentId+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                    // }
                    if(getObjects(node.id)&&isNotChoosed(node)){
                        if(!isdisabled){
                            isdisabled = true;//禁用
                            ajaxgeneral({
                                url:"uums/sys/userinfo/findByUsername?appcode="+web.appCode+"&username="+node.id,
                                contentType: "application/json; charset=utf-8",
                                success:function(data){
                                    node.preferredMobile = data.data.preferredMobile;
                                    $(".role").append("<a name='"+node.truename+"' id='"+node.id+"' preferredMobile='"+node.preferredMobile+"' orgDisplayName='"+node.orgDisplayName+"'v parentId='"+node.parentId+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                                    isdisabled = false;//解除禁用
                                }
                            });
                        }

                    }else{
                            top.mesAlert("提示", "成员已存在，无需再次添加！", 'warning');
                    }
                }
            });
        }else{
            var datas=toTreeData(userIdzNodes,"id","parentId","id|id,name|text,parentId,id,treeType");
            $("#orgTree").tree({
                data: datas,
                lines:true,//是否显示树控件上的虚线
                treePid:'parentId',
                fileds:'id,parentId,name|text,treeType',
                animate:true,//节点在展开或折叠的时候是否显示动画效
                onLoadSuccess:function(node,data){
                    $("#orgTree").find("ul >li:not(:has(ul))").find('.tree-title').css({'background':'rgb(255, 230, 176)','border':'1px solid rgb(255, 185, 81)','color':'#5f4c31'});   // 选择最后一个节点 并添加 float:left
                },
                onBeforeSelect:function(node){
                    if(node.treeType=="group"||node.treeType=="org") return false;
                    if(gps.multi==0){
                        var nodes=$("#orgTree").tree("getChecked");
                        for(var i in nodes){
                            //var nodei=$("#orgTree").tree("find",nodes[i].id);
                            $("#orgTree").tree("uncheck",nodes[i].target);
                        }
                        $(".role").html("");
                    }
                    // if((getObjects("id,name",node.id+","+node.name)) == true){
                    //     $(".role").append("<a name='"+node.name+"' id='"+node.id+"' parentId='"+node.parentId+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                    // }
                    if(getObjects(node.id)&&isNotChoosed(node)){
                        if(!isdisabled){
                            isdisabled = true;//禁用
                            ajaxgeneral({
                                url:"uums/sys/userinfo/findByUsername?appcode="+web.appCode+"&username="+node.id,
                                contentType: "application/json; charset=utf-8",
                                success:function(data){
                                    node.preferredMobile = data.data.preferredMobile;
                                    node.orgDisplayName = data.data.authOrgs[0].displayName;
                                    $(".role").append("<a name='"+node.truename+"' id='"+node.id+"' preferredMobile='"+node.preferredMobile+"' orgDisplayName='"+node.orgDisplayName+"' parentId='"+node.parentId+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                                    isdisabled = false;//解除禁用
                                }
                            });
                        }
                    }else{
                            top.mesAlert("提示", "成员已存在，无需再次添加！", 'warning');
                        
                    }
                }
            });
        }
    }


    //点击查询
    function chooseuser() {
        initTree($('#key').val());
    };

    function getObjects(id){
        var a = true;
        $(".role a").each(function(i,v){
            if($(v).attr('id')==id){
                a = false;
                return;
            }
        });
        return a;
    };
    //判断是否已选择
    function isNotChoosed(node){
        if(hasChoosedUsers.indexOf(node.id)!=-1){
            if(hasChoosedUsers){
                hasChoosedUsers = hasChoosedUsers + "，" +node.id;
            }else{
            hasChoosedUsers = node.id;
            }
            return false;
        }else{
            return true;
        }
    }
    //数据加载成功
    function treeLoadSuccess(){
        var chooseRow=top.chooseWeb[gps.username]?top.chooseWeb[gps.username].data:[];
        for(var i in chooseRow){
            if((getObjects("username,truename",chooseRow[i].username+","+chooseRow[i].truename)) == true){
                // $(".role").append("<a name='"+chooseRow[i].truename+"' id='"+chooseRow[i].username+"' reason='"+chooseRow[i].reason+"' nid='"+chooseRow[i].nid+"'><font>"+chooseRow[i].truename+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                $(".role").append("<a name='"+chooseRow[i].truename+"' id='"+chooseRow[i].username+"' preferredMobile='"+chooseRow[i].preferredMobile+"' orgDisplayName='"+chooseRow[i].orgDisplayName+"' parentId='"+chooseRow[i].parentId+"'><font>"+chooseRow[i].truename+"</font><i class='iconfont fr'>&#xe6ef;</i></a>")
            }
        }
    }
    //返回数据
    window.getchoosedata=function(){
        var datas=[];
        $(".role a").each(function(i,v){
            // var data={};
            // data.id=$(v).attr("id");
            // data.nid=$(v).attr("nid");
            // data.reason=$(v).attr("reason");
            // data.parentId = $(v).attr("parentId");
            // data.name=$(v).children("font").html();
            // datas.push(data);
            var data={};
            data.username=$(v).attr("id");
            data.truename=$(v).children("font").html();
            data.preferredMobile=$(v).attr("preferredMobile");
            data.belongDepartmentCode = $(v).attr("parentId");
            data.orgDisplayName = $(v).attr("orgDisplayName");
            data.receivePhoneNum=$(v).attr("receivePhoneNum");  
            data.userType=1;
            datas.push(data);
        });
        return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    };
</script>
</body>
</html>
