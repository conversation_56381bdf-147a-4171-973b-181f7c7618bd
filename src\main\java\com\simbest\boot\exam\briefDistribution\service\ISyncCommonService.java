package com.simbest.boot.exam.briefDistribution.service;

import com.simbest.boot.exam.briefDistribution.model.ApplyForm;
import com.simbest.boot.exam.codePatrol.model.UsCodePatrol;
import com.simbest.boot.sys.model.SysDict;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

public interface ISyncCommonService {


    /**
     * 创建待办
     * @param map
     * @return
     */
    public Future<Map<String, Object>> startProcess(Map<String, Object> map) ;

    /**
     * 推送待办
     * @param pmInsId  主单据id
     * @param applyForm 主单据
     */
    void createTodo(String pmInsId, ApplyForm applyForm);

    /**
     * 任务下一步
     * @param taskId    任务id
     */
    void completeTask(String taskId );

    void   pushMsg(List<String> userNames,String message);


    void   pushEmail(List<String> userNames, String password, SysDict dict, UsCodePatrol codePatrol);

}
