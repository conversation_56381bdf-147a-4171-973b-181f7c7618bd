package com.simbest.boot.exam.knowledge.task;

import com.simbest.boot.component.distributed.lock.AppRuntimeMaster;
import com.simbest.boot.component.task.AbstractTaskSchedule;
import com.simbest.boot.exam.knowledge.service.IUsInvitationsService;
import com.simbest.boot.sys.repository.SysTaskExecutedLogRepository;
import com.simbest.boot.util.security.LoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class NightCancle extends AbstractTaskSchedule {


    @Autowired
    private IUsInvitationsService usInvitationsService;
    @Autowired
    private LoginUtils loginUtils;

    public NightCancle(AppRuntimeMaster appRuntime, SysTaskExecutedLogRepository repository) {
        super(appRuntime, repository);
    }

    @Override
    @Scheduled(cron = "${time-night-cancle}")//0 0 0 * * ? 每晚2点执行
    public void checkAndExecute() {
        super.checkAndExecute(true);
        System.out.println("开始执行定时任务");
    }


    @Override
    public String execute() {
        loginUtils.adminLogin();
        String yesterday = "";
        usInvitationsService.expirePendingTasks("PC","hadmin",yesterday);
        return "";
    }

}
