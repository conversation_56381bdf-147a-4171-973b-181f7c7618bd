<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>用户管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jsencrypt.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jsencrypt.min.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        html,body{
            overflow-y:auto
        }
    </style>
    <script type="text/javascript">
        var encrypt = new JSEncrypt();
        encrypt.setPublicKey(web.key);
		$(function(){
			//showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
			var pageparam={
				"listtable":{
					"listname":"#userTable",//table列表的id名称，需加#
					"querycmd":"action/user/user/findRoleNameIsARoleDim",//table列表的查询命令
					//"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
					"nowrap": true,//把数据显示在一行里,默认true					
					"checkboxall":true,
                    //"fitColumns":false,
					//"queryParams":{"accountNonExpired":"true","accountNonLocked":"true","credentialsNonExpired":"true","status":"0","enabled":1},
					"frozenColumns":[[
						{ field: "ck",checkbox:true}
					]],//固定在左侧的列
					"columns":[[//列   
						{ title: "登录标识", field: "username", width: 90},
						{ title: "用户姓名", field: "truename", width: 70},
						// { title: "员工号", field: "employeeNumber", width: 60 },//排序sortable: true
						{ title: "移动电话", field: "preferredMobile", width: 100 },
						// { title: "所属公司", field: "belongCompanyName", width: 120},
						{ title: "所属部门", field: "belongDepartmentName", width: 200,tooltip:true},
						{ title: "是否可用", field: "enabled", width: 70,
                            formatter:function(value,row,index){
						        if (value==true){
						            return "是";
                                } else{
						            return "否";
                                }
                            }
                        },
						{
							field: "opt", title: "操作", width: 350, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
							formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
								var g = "<a href='#' class='showDialog' showDialogindex='" + index + "'>【修改】</a>";
                                if(row.enabled==true){
								    g=g+"<a href='#' contentType='application/x-www-form-urlencoded; charset=utf-8' delete='action/user/user/updateEnable?enabled=false' deleteid='"+row.id+"'>【禁用】</a>"
                                }else{
								    g=g+"<a href='#' contentType='application/x-www-form-urlencoded; charset=utf-8' delete='action/user/user/updateEnable?enabled=true' deleteid='"+row.id+"'>【启用】</a>"
                                }
								g=g+"<a class='openDialog' paras='key="+row.username+"&keyType=user&id="+row.id+"'>【功能菜单权限】</a>"
                                +"<a class='userSetRoles' id='" + row.id + "' truename='"+row.truename+"' username='"+row.username+"' >【用户角色】</a>"
                                +"<a class='checkPermission ' id='" + row.id + "' truename='"+row.truename+"' username='"+row.username+"' >【权限查询】</a>"
                                +"<a class='userOrgs' truename='"+row.truename+"' id='" + row.username + "' >【所属部门职务】</a>"
                                +"<a class='changePwd' username='"+row.username+"' id='"+row.id+"'>【修改密码】</a>";
								return g;
							}
						}
					] ],
					"onBeforeLoad":function(params){
						//params.aa="112";
					},
					"pagerbar": [{
						id:"deleteall",
						iconCls: 'icon-remove',
						text:"批量删除&nbsp;"
					}],
					"deleteall":{//批量删除deleteall.id要与pagerbar.id相同
						"id":"deleteall",
						"url":"action/user/user/deleteAllByIds",
						"contentType":"application/json; charset=utf-8"
					}
				},
				"dialoglistbtn":{
					"dialogid":"#buttons2",//对话框的id
					"buttons":[{
						text:"确认",
						handler:function(){
							var obj = $('#buttons2').dialog('options');
							var urlstr = obj["queryParams"];
							if(btn_sure==1){
								btn_sure=2;
								var aa=$("#permissionIds").tree("getChecked");
								var ids=[]; 
								for(var i in aa){
									ids.push(aa[i].id);
								}
								ajaxgeneral({
									url:"action/user/permission/updateListByUserId",
									data:{"userId":urlstr.id,"permissionIds":ids.join(","),"appId":urlstr.appId},
									success:function(data){
                                        btn_sure=1;
									    top.mesShow("温馨提示","操作成功",2500);
										$("#buttons2").dialog("close");//关闭对话框
									},sError:function(data){
										btn_sure=1;
									},error:function(data){
										btn_sure=1;
									}
								});
							}
						}
					},{
						text:"关闭",
						handler:function(){
							$("#buttons2").dialog("close");
						}
					}],
					"dialogurl":"html/syspermission/authorization.html"//对话框页面的路径
				},
				"dialogform":{
					"dialogid":"#buttons",//对话框的id
					"ctable":"userOrgs",
					"formname":"#userTableAddForm",//新增或修改对话框的formid需加#
					"onSubmit":function(param){//在请求加载数据之前触发。返回false可以停止该动作
						//param.sysOrg={"id":$("#oid").combogrid("getValue"),"orgName":$("#orgName").val()};
                        if($("#id").val()=="") {
                            var val = encrypt.encrypt($("#password").val());
                            var passW = encodeURI(val);
                            param.password = passW;
                        }
						return true;
					},
					"insertcmd":"action/user/user/create",//新增命令
					"updatacmd":"action/user/user/update"//修改命令
				}
			};
			loadGrid(pageparam);
            //操作部门职务
            $(document).on("click",".userOrgs",function(){
                var gps=getQueryString();
                var $t=$(this);
                var username=$t.attr("id");
                var url=tourl((gps.form?"uums/":"")+'html/sysuser/sysUserOrgsList.html',{"username":username});
                top.dialogP(url,window.name,$t.attr("truename")+'所属部门职务','userOrgs',true,'1000');
            });
			//用户赋角色
			$(document).on("click",".userSetRoles",function(){
                var gps=getQueryString();
				var $t=$(this);
				//第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
				//第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550   
				var href={"multi":"0","name":"userSetRolesVal","username":$t.attr("username")};
				if($t.hasClass("a_mod_org")){
					href.mod=$t.parents("tr").index();
					var orgId=$t.parents("tr").find("td[path=orgId]").text();
					var positionId=$t.parents("tr").find("td[path=positionId]").text();
					top.chooseWeb.userChooseOrgVal={"data":{"orgId":orgId,"positionId":positionId}};
				}
				var url=tourl((gps.form?"uums/":"")+'html/sysuser/userSetRoles.html',href);
				top.dialogP(url,window.name,$t.attr("truename")+'用户赋角色','userSetRoles',false,'650','500');
			});
			//查看权限
            $(document).on("click",".checkPermission",function(){
                var gps=getQueryString();
                var $t=$(this);
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                 var href={"multi":"0","name":"checkPermissionVal","username":$t.attr("username")};
                // if($t.hasClass("a_mod_org")){
                //     href.mod=$t.parents("tr").index();
                //     var orgId=$t.parents("tr").find("td[path=orgId]").text();
                //     var positionId=$t.parents("tr").find("td[path=positionId]").text();
                //     top.chooseWeb.userChooseOrgVal={"data":{"orgId":orgId,"positionId":positionId}};
                // }
                var url=tourl((gps.form?"uums/":"")+'html/sysuser/checkPermission.html',href);
                top.dialogP(url,window.name,'权限查询','checkPermission',true,'1400','900');
            });
			//重置密码
            $(".updatePassword").on("click",function(){
                //top.mesConfirm("温馨提示","确认重置该用户密码吗？",function(){
                //    getCurrent(updatePass);
                //});
                loadForm("passwordForm");
                $("#updatePassword").dialog({
                    closed:false,
                    buttons:[{
                        text: "确认",
                        handler: function () {
                            if(formValidate("passwordForm")) {
                                getCurrent(updatePass);
                            }
                        }
                    }, {
                        text: "关闭",
                        handler: function () {
                            $("#updatePassword").dialog("close");
                        }
                    }]
                });
            });
            //修改密码
            $(document).on("click","a.changePwd",function(){
                var gps = getQueryString();
                var $t = $(this);
                var id = $t.attr("id");
                $('#changePwd1').val('');
                $('#changePwd2').val('');
                $('#dd').dialog('open');
                $('#passForm').find("input,textarea").removeClass("validatebox-invalid");//把对话框里面的样式重置
                $('#passForm').find("span.textbox-invalid").removeClass("textbox-invalid");//把下拉选择框里面的样式重置

            });
            $('#dd').dialog({
                title: '修改密码',//弹出框的标题
                modal: true,//模态框
                closed: true,//默认弹出框关闭
                buttons: [{// 对话框窗口底部按钮
                    text: '确定', handler: function () {
                        //确定按钮操作
                        formsubmit('passForm','action/user/user/changeUserPassword?rsaPassword='+encodeURIComponent(getRsa($('#changePwd1').val()))+'&username='+$('.changePwd').attr('username')+'&appCode=uums');
                    }}, {
                    text: '取消', handler: function () {
                        $('#dd').dialog('close');
                    }
                }]
            });
            //解绑openId
            // $(".reserve1F").on("click",function(){
            //     top.mesConfirm("温馨提示","确认解绑该用户openId吗？",function(){
            //         ajaxgeneral({
            //             url:"action/user/user/changeUserPassword",
            //             currentUser:true,
            //             //contentType:"application/json; charset=utf-8",
            //             success:function(data){
            //                 $("#reserve1").val("");
            //             }
            //         });
            //     });
            // });
		});
		function updatePass(){
            var val=encrypt.encrypt($("#changePassword").val());
            var passW=encodeURI(val);
            ajaxgeneral({
                url:"action/user/user/changeUserPassword",
                data:{"username":web.currentUser.username,"rsaPassword":passW},
                //contentType:"application/json; charset=utf-8",
                success:function(data){
                    //top.mesShow("温馨提示","操作成功", 2000);
                    $("#updatePassword").dialog("close");
                }
            });
        };
		//用户赋角色
		window.userSetRoles=function(data){			
			//用户当前角色
            if(btn_sure==1){
                btn_sure=2;
                ajaxgeneral({
                    url:"action/user/role/updateApplicationRoleListByUsernames",
                    data:{"roleIds":data.data.join(","),"usernames":data.username,"operationType":2},
                    //contentType:"application/json; charset=utf-8",
                    success:function(data){
                        btn_sure=1;
                        top.mesShow("温馨提示","操作成功",2500);
                    },sError:function(data){
                        btn_sure=1;
                    },error:function(data){
                        btn_sure=1;
                    }
                });
            }
		};
		//查询权限
        window.checkPermission=function(data){

        };
		//form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
		function beforerender(data,isupdate){
			if(isupdate){
				$('.update-readonly').hide();
				$("#password").validatebox("disable");
			}else{
				$('.update-readonly').show();
                $("#startTime").datetimebox("setValue",getNow("yyyy-MM-dd hh:mm:ss"));
                $("#password").validatebox("enable");
			}
		};
		
		function psubmitcallback(data) {
            // top.mesAlert("提示信息",data[key]);
            var strdata = '<div style="font-size: 15px;">修改密码结果如下：</div>';
            for (var key in data)
            {
                if(data[key] == "成功"){
                    strdata += '<div style="font-size: 15px;padding-left: 28px;color: #5cb85c">'+key + ':<span>' + data[key] + '</span></div>'
                }else{
                    strdata += '<div style="font-size: 15px;padding-left: 28px;color: red">'+key + ':<span>' + data[key] + '</span></div>'
                }
            }
            top.mesAlert("提示信息",strdata);
            $('#dd').dialog('close');
        }

	</script>
</head>
<body class="body_page_uums">
<h6 class="pageTit"><font class="col_b fwb">用户管理</font><i class="iconfont">&#xe66e;</i><small>用户的增删改查和授权</small></h6>
<!--searchform-->
<form id="userTableQueryForm"> 
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">用户账号：</td><td width="150"><input name="username" type="text" value="" /></td>
            <td width="90" align="right">用户姓名：</td><td width="150"><input name="truename" type="text" value="" /></td>
            <td width="90" align="right">移动电话：</td><td width="150"><input name="preferredMobile" type="text" value="" /></td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fl searchtable"><span>查询</span></a>   
					<a class="btn a_success showDialog fr"><span>新增</span></a>
                </div>
                </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="userTable"><table id="userTable"></table></div>
<!--dialog-->
<div id="buttons2" title="用户功能菜单权限" class="easyui-dialog" style="width:700px;height:650px;"></div>
<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:1100px;height:700px;">
<form id="userTableAddForm" method="post" contentType="application/json; charset=utf-8" beforerender="beforerender()">
	<input id="id" name="id" type="hidden" />
	<table border="0" cellpadding="0" cellspacing="6" width="100%" >
        <tr class="update-readonly">
            <td width="20%" align="right"><font class="col_r">*</font>OA账号：</td>
            <td width="80" colspan="2">
                <input id="username" name="username" type="text" class="easyui-validatebox" required='required'/>
            </td>
        </tr>
		<tr>
            <td width="20%" align="right"><font class="col_r">*</font>用户姓名：</td>
            <td width="30%">
                <input id="truename" name="truename" type="text" class="easyui-validatebox" required='required' />
            </td>
            <td>
                <a class="btn a_primary updatePassword"><span>重置密码</span></a>
            </td>
            <td></td>
        </tr>
		<tr class="update-readonly">
            <td width="20%" align="right"><font class="col_r">*</font>密码：</td>
            <td width="30%">
                <input id="password" name="password" type="password" class="easyui-validatebox" validType="password"/>
            </td>
            <td width="20%" align="right">重复密码：</td>
            <td width="30%">
                <input id="password1" name="password1" type="password" class="easyui-validatebox" validType="equals['#password']"/>
            </td>
        </tr>

        <tr>
            <td width="20%" align="right"><font class="col_r">*</font>昵称：</td>
            <td width="30%">
                <input id="nickname" name="nickname" type="text" class="easyui-validatebox"  required='required'/>
            </td>
            <td width="20%" align="right">员工编号：</td>
            <td width="30%">
                <input id="employeeNumber" name="employeeNumber" type="text" class="easyui-validatebox"  />
            </td>
        </tr>

        <tr>
            <td width="140" align="right">姓氏：</td>
            <td width="160">
                <input id="lastname" name="lastname" type="text" class="easyui-validatebox" />
            </td>
            <td width="120" align="right">身份证号：</td>
            <td width="160">
                <input id="idCardNumber" name="idCardNumber" type="text" class="easyui-validatebox" validType="isCardNo" />
            </td>
        </tr>

		<tr>
            <td width="140" align="right"><font class="col_r">*</font>移动电话：</td>
            <td width="160">
                <input id="preferredMobile" name="preferredMobile" type="text" class="easyui-validatebox" validType="phone" required='required'/>
            </td>
            <td width="120" align="right">电子邮箱：</td>
            <td width="160">
                <input id="email" name="email" type="text" class="easyui-validatebox" validType="email" />
            </td>
        </tr>
		<tr>
			<td width="140" align="right">状态：</td><td width="160">
			<select class="easyui-combobox" id="enabled" name="enabled" style="width: 100%; height: 32px;" data-options="panelHeight:'auto',editable:false">
				<option value="true" selected >正常</option>
                <option value="false">锁定</option>
            </select>
			</td>
            <td width="140" align="right">显示顺序：</td>
            <td width="160">
                <input id="displayOrder" name="displayOrder" type="text" class="easyui-validatebox" validType="zinteger" />
            </td>
        </tr>

        <tr>
            <td width="140" align="right">是否是中国移动公司内部用户：</td><td width="160">
                <select class="easyui-combobox" id="isCmcc" name="isCmcc" style="width: 100%; height: 32px;" data-options="panelHeight:'auto',editable:false">
                    <option value="true" selected >是</option>
                    <option value="false">否</option>
                </select>
            </td>
            <td width="140" align="right">是否显示：</td>
            <td>
                <select class="easyui-combobox" id="isDisplay" name="isDisplay" style="width: 100%; height: 32px;" data-options="panelHeight:'auto',editable:false">
                    <option value="true" selected >是</option>
                    <option value="false">否</option>
                </select>
            </td>
        </tr>

        <tr>
            <td width="140" align="right">是否是特殊账号：</td>
            <td width="160">
                <select class="easyui-combobox" id="isSpecial" name="isSpecial" style="width: 100%; height: 32px;" data-options="panelHeight:'auto',editable:false">
                    <option value="true"  >是</option>
                    <option value="false" selected >否</option>
                </select>
            </td>
            <td width="140" align="right">在集团smap中的状态：</td>
            <td>
                <select class="easyui-combobox" id="status" name="status" style="width: 100%; height: 32px;" data-options="panelHeight:'auto',editable:false">
                    <option value="0" selected >正常</option>
                    <option value="1">锁定</option>
                    <option value="3">已删除</option>
                </select>
            </td>
        </tr>
        <tr>
            <!--<td width="100" align="right">显示名称：</td><td><input id="displayName" name="displayName" type="text" class="easyui-validatebox" required='required' /></td>-->
            <td width="100" align="right"><font class="col_r">*</font>职务级别：</td>
            <td>
                <input id="positionLevel" name="positionLevel" type="text" class="easyui-validatebox" validType="integer" required='required'/>
            </td>
            <td width="100" align="right"><font class="col_r">*</font>员工类型：</td><td>
                <input id="employeeTypeDictValue" name="employeeTypeDictValue" class="easyui-combobox" required='required' style="width: 100%; height: 32px;" data-options="
                        valueField: 'value',
                        ischooseall:true,
                        editable:false,
                        textField: 'name',
                        editable:false,
                        queryParams:{'dictType':'staffType'},
                        contentType:'application/json; charset=utf-8',
                        url: '/uums/sys/dictValue/findDictValue'" />
            </td>
            <td></td>
        </tr>

        <tr>
            <!--<td width="100" align="right">显示名称：</td><td><input id="displayName" name="displayName" type="text" class="easyui-validatebox" required='required' /></td>-->
            <td width="100" align="right">岗位职级：</td>
            <td>
                <input id="levelNumber" name="levelNumber" type="text" class="easyui-validatebox" validType="integer" />
            </td>
            <td width="100" align="right">岗位名称：</td>
            <td>
                <input id="levelName" name="levelName" type="text" class="easyui-validatebox"  />
            </td>
        </tr>

        <tr>
            <!--<td width="100" align="right">显示名称：</td><td><input id="displayName" name="displayName" type="text" class="easyui-validatebox" required='required' /></td>-->
            <td width="100" align="right"><font class="col_r">*</font>性别：</td>
            <td>
                <input id="genderDictValue" name="genderDictValue" class="easyui-combobox" required='required' style="width: 100%; height: 32px;" data-options="
                            valueField: 'value',
                            ischooseall:true,
                            editable:false,
                            textField: 'name',
                            editable:false,
                            queryParams:{'dictType':'sex'},
                            contentType:'application/json; charset=utf-8',
                            url: '/uums/sys/dictValue/findDictValue'" />
            </td>
            <td width="100" align="right"><font class="col_r">*</font>民族：</td><td>
                <input id="nationDictValue" name="nationDictValue" class="easyui-combobox" required='required' style="width: 100%; height: 32px;" data-options="
                            valueField: 'value',
                            ischooseall:true,
                            editable:false,
                            textField: 'name',
                            editable:false,
                            queryParams:{'dictType':'nation'},
                            contentType:'application/json; charset=utf-8',
                            url: '/uums/sys/dictValue/findDictValue'" />
            </td>
        </tr>

        <tr>
            <!--<td width="100" align="right">显示名称：</td><td><input id="displayName" name="displayName" type="text" class="easyui-validatebox" required='required' /></td>-->
            <td width="100" align="right">入职日期：</td>
            <td>
                <input id="entryTime" name="entryTime" type="text" class="easyui-datebox" data-options="panelHeight:'auto',editable:false" style="width:100%;height:32px;"/>
            </td>
            <td width="100" align="right">用户所属地市：</td>
            <td>
                <input id="l" name="l" type="text" class="easyui-validatebox"  />
            </td>
        </tr>

        <tr>
            <td width="140" align="right">用户职务：</td>
            <td colspan="3">
                <input id="duty" name="duty" type="text"  class="easyui-validatebox" />
            </td>
        </tr>
        <tr>
            <td width="140" align="right">业务职责编码：</td>
            <td colspan="3">
                <input id="functionName" name="functionName" type="text"  class="easyui-validatebox" />
            </td>
        </tr>
		<tr>
			<td width="140" align="right">开始生效时间：</td>
            <td width="160">
                <input id="startTime" name="startTime" type="text" class="easyui-datetimebox" validType="startDateCheck['endTime','startTime']" data-options="panelHeight:'auto',editable:false" style="width:100%;height:32px;"/>
            </td>
			<td width="120" align="right">结束生效时间：</td>
            <td width="160">
                <input id="endTime" name="endTime" type="text" class="easyui-datetimebox" validType="endDateCheck['startTime','endTime']" data-options="panelHeight:'auto',editable:false" style="width:100%;height:32px;"/>
            </td>
        </tr>
        <tr>
            <td width="140" align="right">生日：</td>
            <td width="160">
                <input id="birthday" name="birthday" type="text" class="easyui-datebox" data-options="panelHeight:'auto',editable:false" style="width:100%;height:32px;"/>
            </td>
            <td width="120" align="right">国籍：</td>
            <td width="160">
                <input id="country" name="country" type="text" class="easyui-validatebox" />
            </td>
        </tr>
        <tr>
            <!--<td width="100" align="right">显示名称：</td><td><input id="displayName" name="displayName" type="text" class="easyui-validatebox" required='required' /></td>-->
            <td width="100" align="right">政治面貌：</td>
            <td>
                <input id="religionDictValue" name="religionDictValue" class="easyui-combobox" style="width: 100%; height: 32px;" data-options="
                            valueField: 'value',
                            ischooseall:true,
                            editable:false,
                            textField: 'name',
                            queryParams:{'dictType':'political'},
                            contentType:'application/json; charset=utf-8',
                            url: '/uums/sys/dictValue/findDictValue'" />
            </td>
            <td width="100" align="right">办公电话：</td>
            <td>
                <input id="telephoneNumber" name="telephoneNumber" type="text" readonly="readonly" class="easyui-validatebox" />
            </td>
        </tr>
        <tr>
            <td width="140" align="right">微信ID(OpenId)：</td>
            <td width="160">
                <input id="reserve1" name="reserve1" type="text" readonly="readonly" class="easyui-validatebox" />
            </td>
            <td><a class="btn a_warning" onclick="$('#reserve1').val('')">清空</a></td>
        </tr>
        <tr>
            <td width="100" align="right">预留扩展字段2：</td>
            <td><input id="reserve2" name="reserve2" type="text" class="easyui-validatebox"  /></td>
            <td width="100" align="right">预留扩展字段3：</td>
            <td><input id="reserve3" name="reserve3" type="text" class="easyui-validatebox"  /></td>
        </tr>
        <tr>
            <td width="20%" align="right">预留扩展字段4(密码)：</td>
            <td colspan="3"><input id="reserve4" name="reserve4" type="text" class="easyui-validatebox"  /></td>
           <!-- <td width="100" align="right">预留扩展字段5：</td>
            <td><input id="reserve5" name="reserve5" type="text" class="easyui-validatebox"  /></td>-->
        </tr>
        <tr>
            <td width="100" align="right" valign="top">描述：</td>
            <td colspan="3">
                <textarea style="width:100%;height:120px;resize:none;" readonly="readonly" class="description"></textarea>
            </td>
        </tr>
    </table>
</form>
</div>
<!--password-->
<div id="updatePassword" title="重置密码" data-options="modal:true,closed:true" class="easyui-dialog" style="width:450px;height:350px;">
<form id="passwordForm">
    <table border="0" cellpadding="0" cellspacing="6">
        <tr>
            <td width="100" align="right"><font class="col_r">*</font>密码：</td>
            <td width="160">
                <input id="changePassword" name="password" type="password" required='required' class="easyui-validatebox" validType="password"/>
            </td>
        </tr>
        <tr>
            <td width="100" align="right">重复密码：</td>
            <td width="160">
                <input id="changePassword1" name="password1" type="password" class="easyui-validatebox" validType="equals['#changePassword']"/>
            </td>
        </tr>
    </table>
</form>
</div>
<!--password-->
<div id="dd" class="easyui-dialog" style="width:470px;height:200px;" >
    <form id="passForm" method="post" contentType="application/json; charset=utf-8" submitcallback="psubmitcallback()">
        <table style="margin-top: 10px;" border="0" cellpadding="0" cellspacing="10">
            <tbody >
                <tr>
                    <td width="100" align="right"><font class="col_r">*</font>密码：</td>
                    <td width="250">
                        <input id="changePwd1" type="password" required='required' class="easyui-validatebox" validType="password"/>
                    </td>
                </tr>
                <tr>
                    <td width="100" align="right"><font class="col_r">*</font>重复密码：</td>
                    <td width="250">
                        <input id="changePwd2" type="password" required='required' class="easyui-validatebox" validType="equals['#changePwd1']"/>
                    </td>
                </tr>
            </tbody>
        </table>
    </form>
</div>
</body>
</html>
