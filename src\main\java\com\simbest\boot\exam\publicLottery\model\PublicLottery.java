package com.simbest.boot.exam.publicLottery.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.exam.util.BelongInfoTool;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * Lottery
 *
 * <AUTHOR>
 * @since 2024/4/30 15:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "公开摇号业务表")
@Entity(name = "us_public_lottery")
@javax.persistence.Table(uniqueConstraints = {
        @UniqueConstraint(name = "num_unique", columnNames = {"num"}),
})
public class PublicLottery extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UPL") //主键前缀，此为可选项注解
    private String id;

    @Column
    @ApiModelProperty(value = "摇号号码")
    private Integer num;

    @Column(length = 40)
    @ApiModelProperty(value = "用户姓名")
    private String username;

    @Column(length = 40)
    @ApiModelProperty(value = "用户姓名")
    private String truename;

    @Column(length = 20)
    @ApiModelProperty(value = "电话")
    private String preferredMobile;

    @Version
    @Column
    @ApiModelProperty(value = "版本号")
    private Integer version;


    public void belongInfoSet(@NotNull IUser user) {
        username = user.getUsername();
        truename = user.getTruename();
        preferredMobile = user.getPreferredMobile();
        BelongInfoTool.setBelongCompanyAndDepartment(this);
    }

    public void belongInfoSet() {
        this.belongInfoSet(SecurityUtils.getCurrentUser());
    }

}
