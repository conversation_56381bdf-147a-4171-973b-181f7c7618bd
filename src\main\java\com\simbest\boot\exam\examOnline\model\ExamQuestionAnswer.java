/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;/**
 * Created by KZH on 2019/10/8 10:24.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 10:24
 * @desc 题目的正确答案表
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity(name = "us_exam_question_answer")
@ApiModel(value = "题目的正确答案表")
public class ExamQuestionAnswer extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EQA") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "题目编码", name = "questionCode", example = "A-001-1", required = true)
    private String questionCode;

    @Column(length = 250)
    @ApiModelProperty(value = "答案编码", name = "answerCode", example = "A", required = true)
    private String answerCode;

    @Column(length = 4000)
    @ApiModelProperty(value = "答案内容", name = "answerContent", example = "UDP协议", required = true)
    private String answerContent;

    @ApiModelProperty(value = "是否为正确答案", name = "isCorrect", example = "1", required = true)
    private Boolean isCorrect;

    @ApiModelProperty(value = "正确答案解释", name = "correctExplanation", example = "这是正确答案解释", required = false)
    private String correctExplanation;

    @ApiModelProperty(value = "特殊控制类型。 01 ：多选时选中后不可选其他选项")
    @Column(length = 30)
    private String specialType;

    /**
     * 为开发洛阳市县分公司对机关各部门进行满意度评价
     * 以及部门间相互满意度评价的项目添加的属性值
     * answerScore
     */
    @Column(length = 5)
    @ApiModelProperty(value = "该答案分值", name = "answerScore", example = "1~5分", required = true)
    private Integer answerScore;

    @Column(length = 200)
    @ApiModelProperty(value = "标识", name = "identification", example = "1/0")
    private String identification; //用于特殊选项控制,填空题类型中表示是否需要按顺序判题，1是0否

    @Column(length = 100)
    @ApiModelProperty(value = "父选项ID", name = "parentId")
    private String parentId;

    @Transient
    private List<ExamQuestionAnswer> examQuestionAnswers;///存放子选项
    @Transient
    private  String  answer;///存放子选项
}
