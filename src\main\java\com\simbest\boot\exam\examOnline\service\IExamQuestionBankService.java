/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service;/**
 * Created by KZH on 2019/10/8 15:13.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.examOnline.model.ExamQuestionBank;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:13
 * @desc 题库
 **/
public interface IExamQuestionBankService extends ILogicService<ExamQuestionBank,String> {

    /**
     * 根据题库编码获取题库
     * @param questionBankCode
     * @return
     */
    ExamQuestionBank findAllByQuestionBankCode(String questionBankCode);

    /**
      * 根据登录人查询题库列表
      * <AUTHOR>
      * @date 2021/6/28
      * @param
      * @return
      */
    Page<ExamQuestionBank> listByUserName(Pageable pageable, ExamQuestionBank examQuestionBank);

    /**
      * 根据登录人查询题库列表 不分页
      * <AUTHOR>
      * @date 2021/6/28
      * @param
      * @return
      */
    List<ExamQuestionBank> listByUserNameNoPage();

    /**
      * @desc 创建题库
      * <AUTHOR>
      */
    String creatExamQuestionBank(ExamQuestionBank examQuestionBank);


    /**
     * @desc 修改题库
     * <AUTHOR>
     */
    String updateExamQuestionBank(ExamQuestionBank examQuestionBank);

    /**
      * @desc 删除题库，并将其下的题目删除
      * <AUTHOR>
      */
    String delExamQuestionBank(String id);


}
