/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.repository;/**
 * Created by KZH on 2019/10/8 16:06.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.knowledge.model.UsUserAnswers;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:06
 * @desc
 **/
public interface UsUserAnswersRepository extends LogicRepository<UsUserAnswers, String> {
    @Query(value = "SELECT t. *,ueq.question_name,ueq.question_score,ueq.question_type  FROM us_user_answers  t  " +
            " left join us_exam_question ueq  ON t.question_code=ueq.question_code  " +
            "WHERE t.enabled=1 AND  t.ANSWER_RECORD_ID = :answerRecordId  and t.ansewers_user_name = :username   ORDER BY t.created_time ASC " ,
            nativeQuery = true)
  List<Map<String,Object>> findUsUserAnswerListByAnswerRecordId(@Param("answerRecordId") String answerRecordId , @Param("username") String username);

    /**
     * 查询答题记录
     * 用于续答
     * @param pmInsId
     * @param ansewersUserName
     * @param questionBankCode
     * @return
     */
    @Query(value = "select t.*" +
            "  from us_user_answers t" +
            " where t.enabled = 1" +
            "   and t.pm_ins_id =:pmInsId" +
            "   and t.ansewers_user_name =:ansewersUserName" +
            "   and t.question_bank_code=:questionBankCode " ,
            nativeQuery = true)
List<UsUserAnswers> findAnswerUserNameInfo(@Param("pmInsId") String pmInsId,@Param("ansewersUserName") String ansewersUserName,@Param("questionBankCode") String questionBankCode);


    /**
     * 查询答题次数，用于判断是否A\B双方已经全部答完
     * @param currentDay
     * @param workType
     * @param questionBankCode
     * @param ansewersUserName
     * @return
     */
    @Query(value = "select t.*" +
            "  from us_user_answers t" +
            " where t.enabled = 1" +
            "   and to_char(t.created_time,'yyyy-MM-dd')=:currentDay" +
            "   and t.work_type=:workType" +
            "   and t.question_bank_code=:questionBankCode" +
            "   and t.ansewers_user_name=:ansewersUserName " ,
            nativeQuery = true)
    List<UsUserAnswers> findAnswerUserNameCount(@Param("currentDay") String currentDay,@Param("workType") String workType,@Param("questionBankCode") String questionBankCode,@Param("ansewersUserName") String ansewersUserName);



    @Query(value = "SELECT ANSEWERS_TRUE_NAME," +
            "       BELONG_ORG_NAME," +
            "       ANSEWERS_USER_NAME," +
            "       creator," +
            "       SUM(CASE" +
            "             WHEN is_correct = 1 THEN" +
            "              TO_NUMBER(SCORE)" +
            "             ELSE" +
            "              0 " +
            "           END) AS TOTAL_SCORE" +
            "  FROM EXAM.US_USER_ANSWERS " +
            " WHERE enabled = 1 " +
            "   AND question_bank_code = :questionBankCode " +
            " GROUP BY ANSEWERS_TRUE_NAME, BELONG_ORG_NAME, ANSEWERS_USER_NAME, creator " +
            " ORDER BY TOTAL_SCORE DESC",
            nativeQuery = true)
    List<Map<String,Object>> getSocreRanking(@Param("questionBankCode") String questionBankCode);

    @Query(value = "  WITH user_time_spent AS (" +
            "    SELECT " +
            "        ansewers_user_name," +
            "        SUM(NVL(duration, 0)) AS TIME_SPENT_MINUTES" +
            "    FROM " +
            "        EXAM.US_ANSWER_RECORD" +
            "    GROUP BY " +
            "        ansewers_user_name" +
            ")," +
            "user_scores AS (" +
            "    SELECT  " +
            "        BELONG_ORG_NAME, " +
            "        ANSEWERS_USER_NAME, " +
            "        ANSEWERS_TRUE_NAME, " +
            "        SUM(CASE  " +
            "            WHEN is_correct = 1 THEN TO_NUMBER(SCORE)  " +
            "            ELSE 0  " +
            "        END) AS TOTAL_SCORE " +
            "    FROM  " +
            "        EXAM.US_USER_ANSWERS " +
            "    WHERE  " +
            "        enabled = 1 " +
            "        AND question_bank_code = :questionBankCode " +
            "    GROUP BY  " +
            "        BELONG_ORG_NAME, " +
            "        ANSEWERS_USER_NAME, " +
            "        ANSEWERS_TRUE_NAME " +
            ") " +
            "SELECT  " +
            "    us.BELONG_ORG_NAME, " +
            "    us.ANSEWERS_USER_NAME, " +
            "    us.ANSEWERS_TRUE_NAME, " +
            "    us.TOTAL_SCORE, " +
            "    uts.TIME_SPENT_MINUTES " +
            "FROM  " +
            "    user_scores us " +
            "LEFT JOIN  " +
            "    user_time_spent uts ON us.ANSEWERS_USER_NAME = uts.ansewers_user_name " +
            "ORDER BY  " +
            "    us.TOTAL_SCORE DESC,  " +
            "    uts.TIME_SPENT_MINUTES ASC ",
            nativeQuery = true)
    List<Map<String,Object>> getSocreRankingNew(@Param("questionBankCode") String questionBankCode);
    @Query(value = "SELECT " +
            "    ANSEWERS_TRUE_NAME, " +
            "    BELONG_ORG_NAME, " +
            "    ANSEWERS_USER_NAME, " +
            "    creator, " +
            "    SUM(CASE WHEN is_correct = 1 THEN TO_NUMBER(SCORE) ELSE 0 END) AS TOTAL_SCORE, " +
            "    RANK() OVER (ORDER BY SUM(CASE WHEN is_correct = 1 THEN TO_NUMBER(SCORE) ELSE 0 END) DESC) AS RANK " +
            " FROM " +
            "    EXAM.US_USER_ANSWERS " +
            " WHERE " +
            "    enabled = 1 " +
            "     AND question_bank_code = :questionBankCode  " +
            " GROUP BY " +
            "    ANSEWERS_TRUE_NAME, " +
            "    BELONG_ORG_NAME, " +
            "    ANSEWERS_USER_NAME, " +
            "    creator " +
            " HAVING " +
            "    ANSEWERS_USER_NAME = :answerUserName  " +
            " ORDER BY " +
            "    TOTAL_SCORE DESC",
            nativeQuery = true)
    Map<String,Object> getSelfSocreRanking(@Param("questionBankCode") String questionBankCode,@Param("answerUserName") String answerUserName);

    @Query(value =
            "SELECT  " +
                    "  SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) AS trueNum, " +
                    "  SUM(CASE WHEN is_correct = 1 THEN  (TO_NUMBER(SCORE))  ELSE 0 END ) AS TOTAL_SCORE " +
                    " FROM " +
                    "  EXAM.US_USER_ANSWERS " +
                    " WHERE " +
                    "  enabled = 1  AND answer_record_id = :answerRecordId " +
                    " GROUP BY " +
                    "  ANSEWERS_TRUE_NAME, " +
                    "  BELONG_ORG_NAME, " +
                    "  ANSEWERS_USER_NAME " +
                    " ORDER BY " +
                    "  TOTAL_SCORE DESC  " ,
            nativeQuery = true)
    Map<String, Object> getSocreRankingByAnswerRecordId(@Param("answerRecordId") String answerRecordId);


    /**
     * 查询此次邀请中答对多少题目
     * @param invitaitonId
     * @return
     */
    @Query(value = "select t.*" +
            "  from us_user_answers t" +
            " where t.enabled = 1" +
            "   and to_char(t.created_time,'yyyy-MM-dd')=:currentDay" +
            "   and t.work_type=:workType" +
            "   and t.question_bank_code=:questionBankCode" +
            "   and t.invitation_id=:invitaitonId  " +
            "  and t.ansewers_user_name =:ansewersUserName " +
            "  and t.is_correct = 1 and t.pm_ins_id is not null" ,
            nativeQuery = true)
    List<UsUserAnswers> getRightCount(@Param("currentDay") String currentDay,@Param("workType") String workType,@Param("questionBankCode") String questionBankCode,@Param("invitaitonId") String invitaitonId,@Param("ansewersUserName") String ansewersUserName);


    /**
     * 查询答对多少题目
     * @param answerRecordId
     * @param ansewersUserName
     * @return
     */
    @Query(value = "select t.*  from us_user_answers t " +
            " where t.enabled = 1 " +
            " and t.ANSWER_RECORD_ID=:answerRecordId" +
            " and t.is_correct = 1" +
            " and t.ANSEWERS_USER_NAME=:ansewersUserName" ,
            nativeQuery = true)
    List<UsUserAnswers> getRightCountToAnalyse(@Param("answerRecordId") String answerRecordId,@Param("ansewersUserName") String ansewersUserName);


    List<UsUserAnswers> findUsUserAnswersByAnswerRecordId(@Param("RecordId") String RecordId);

    @Query(value = "select MAX(t.ANSWER_TIME) AS maxTime ," +
            " MIN(t.ANSWER_TIME) AS minTime "+
            "  from us_user_answers t " +
            " where t.enabled = 1 " +
            "  and t.ANSWER_RECORD_ID = :answerRecordId " ,
            nativeQuery = true)
    List<Map<String, Object>> getTimeByRecordId(@Param("answerRecordId") String answerRecordId);

    List<UsUserAnswers> findUsUserAnswersByQuestionIdAndAnswerRecordId(@Param("questionId") String questionId, @Param("answerRecordId")String answerRecordId);




    @Query(value = "select MAX(t.ANSWER_TIME) AS MAXTIME ," +
            " MIN(t.ANSWER_TIME) AS MINTIME "+
            "  from us_user_answers t " +
            " where t.enabled = 1 " +
            "   and t.work_type=:workType" +
            "   and t.question_bank_code=:questionBankCode" +
            "   and t.INVITATION_ID=:invitaitonId" +
            " and t.ansewers_user_name =:ansewersUserName" ,
            nativeQuery = true)
    List<Map<String, Object>> getTimeByRecordByUser(@Param("workType") String workType,@Param("questionBankCode") String questionBankCode,@Param("invitaitonId") String invitaitonId,@Param("ansewersUserName") String ansewersUserName);
    @Query(value = "select MAX(t.ANSWER_TIME) AS MAXTIME ," +
            " MIN(t.ANSWER_TIME) AS MINTIME "+
            "  from us_user_answers t " +
            " where t.enabled = 1 " +
            " and t.answer_record_id =:answerRecordId" ,
            nativeQuery = true)
   Map<String, Object> findListByAnswerRecordId(@Param("answerRecordId") String answerRecordId);

}
