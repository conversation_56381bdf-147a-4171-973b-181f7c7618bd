package com.simbest.boot.exam.test.util;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.redis.RedisUtil;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * MyUtils
 *
 * <AUTHOR>
 * @since 2023/10/13 15:55
 */
@Slf4j
@Component
public class MyUtils {

    private static final String SELECT_BY_SQL_SELECT_TYPE = "/action/uumsSelect/selectBySqlSelectType";
    private static final String SSO = "/sso";
    private static AppConfig config;
    private static RsaEncryptor encryptor;

    public MyUtils(AppConfig config, RsaEncryptor encryptor) {
        MyUtils.config = config;
        MyUtils.encryptor = encryptor;
    }


    /**
     * 获取远程uums请求链接
     * <br/> params [uri]
     *
     * @return {@link String}
     * <AUTHOR>
     * @since 2023/6/25 11:24
     */
    public static String getUUMSUrl(String uri) {
        String loginUser = SecurityUtils.getCurrentUserName();
        String username = encryptor.encrypt(loginUser).replace("+", "%2B");
        String url = String.format(
                MyUtils.config.getUumsAddress() + uri + SSO + "?loginuser=%s&appcode=%s",
                username, Constants.APP_CODE
        );
        log.debug("Http remote request: user: {}, url: {}", loginUser, url);
        return url;
    }

    /**
     * 发送远程http请求 uums
     */
    public static List<Map<String, Object>> selectBySqlSelectType(String sqlId, List<String> params) {
        String url = MyUtils.getUUMSUrl(SELECT_BY_SQL_SELECT_TYPE) + "&appCode=" + Constants.APP_CODE + "&selectType=replacetype&sqlId=" + sqlId;
        Map<String, Object> param = new HashMap<>();
        for (int i = 0, paramsSize = params.size(); i < paramsSize; i++) {
            String key = "";
            String p = params.get(i);
            switch (i) {
                case 0:
                    key = "firstParam";
                    break;
                case 1:
                    key = "secondParam";
                    break;
                case 2:
                    key = "thirdParam";
                    break;
                case 3:
                    key = "fourthParam";
                    break;
                case 4:
                    key = "fifthParam";
                    break;
                case 5:
                    key = "sixthParam";
                    break;
            }
            param.put(key, p);
        }
        JsonResponse response = HttpClient.textBody(url).json(JacksonUtils.obj2json(param)).asBean(JsonResponse.class);
        if (response == null || response.getErrcode() != 0) {
            log.error("远程请求uums地址失败，url: {}, params: {}, response: {}", url, params, response);
        }
        return (List<Map<String, Object>>) response.getData();
    }

    /**
     * 获取一个唯一code
     *
     * @param code code前缀
     * @return 唯一code
     */
    public static String getUniqueCode(String code) {
        return String.format("%s%s", code, IdGenerator.idWorker.nextId());
    }

    /**
     * redis分布式锁
     *
     * @param key 上锁的key
     */
    public static void getRedisLock(String key) {
        getRedisLock(key, 2);
    }

    /**
     * redis分布式锁
     *
     * @param key     上锁的key
     * @param timeout 过期时间(秒)
     */
    public static void getRedisLock(String key, long timeout) {
        // ------------------------------------redis分布式锁 start-----------------------
        String value = "getLock";
        if (Objects.equals(RedisUtil.get(key), value))
            throw new IllegalStateException("业务繁忙，请等待！");
        if (RedisUtil.setIfAbsent(key, value))
            RedisUtil.expire(key, timeout, TimeUnit.SECONDS);
        // ------------------------------------redis分布式锁 end  -----------------------
    }


    @Component
    public static class LogTool {

        private static ISysOperateLogService logService;

        public LogTool(ISysOperateLogService logService) {
            LogTool.logService = logService;
        }

        /**
         * 保存日志
         *
         * @param source           来源
         * @param bussinessKey     业务key
         * @param operateInterface 接口
         * @param interfaceParam   接口参数
         * @param resultMsg        结果
         * @param errorMsg         错误信息
         */
        public static void saveLog(String source, String bussinessKey, String operateInterface, String interfaceParam, String resultMsg, String errorMsg) {
            SysOperateLog operateLog = new SysOperateLog();
            operateLog.setOperateFlag(source);
            operateLog.setBussinessKey(bussinessKey);
            operateLog.setOperateInterface(operateInterface);
            operateLog.setInterfaceParam(interfaceParam);
            operateLog.setResultMsg(resultMsg);
            operateLog.setErrorMsg(errorMsg);

            if (Objects.isNull(SecurityUtils.getCurrentUserName())) {
                operateLog.setCreator(Constants.HADMIN);
                operateLog.setModifier(Constants.HADMIN);
                operateLog.setEnabled(true);
                operateLog.setCreatedTime(LocalDateTime.now());
                operateLog.setModifiedTime(LocalDateTime.now());
            }

            logService.insert(operateLog);
        }

    }

}
