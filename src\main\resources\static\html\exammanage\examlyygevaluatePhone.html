<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org" style="height: 100%;">

<head>
    <title>在线考试</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detaction" content="telephone=no" />
   <!-- <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
          rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/moaBridge_oa_wjdc.js?v=svn.revision" type="text/javascript"></script>-->

    	<link href="../../fonts/iconfont/iconfont.css?v=svn.revision"  rel="stylesheet"/>
    	<link href="/simbestui/js/themes/default/easyui.css?v=svn.revision"  rel="stylesheet"/>
    	<link href="/simbestui/js/themes/icon.css?v=svn.revision"  rel="stylesheet"/>
    	<link href="/simbestui/css/public.css?v=svn.revision"  rel="stylesheet"/>
    	<link href="/simbestui/css/pages.css?v=svn.revision"  rel="stylesheet"/>
    	<script src="/simbestui/js/jquery.min.js?v=svn.revision"  type="text/javascript"></script>
    	<script src="/simbestui/js/jquery.easyui.min.js?v=svn.revision"  type="text/javascript"></script>
    	<script src="/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"  type="text/javascript"></script>
    	<script src="/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"  type="text/javascript"></script>
    	<script src="../../js/jquery.config.js?v=svn.revision"  type="text/javascript"></script>
    	<script src="../../js/moaBridge_oa_wjdc.js?v=svn.revision"  type="text/javascript"></script>
    	<script src="/simbestui/js/jquery.zjsfile.js?v=svn.revision"  type="text/javascript"></script>
    	<script src="/simbestui/js/jquery.aduq.js?v=svn.revision"  type="text/javascript"></script>
    	<script src="/simbestui/js/jquery.process.js?v=svn.revision"  type="text/javascript"></script>

    <style>
        #piyueScore {
            width: unset;
            border-color: gray;
        }
    </style>
    <script type="text/javascript">
        var allQuestionCode = []
        var timeFlag = 0; // 提交数据的标识
        var remainTimeT; // 保存记录的计时器
        var examLists = [];//存储的试题信息
        var examAppCode = "";
        var companyName = '';
        var totalSS = 0; // 考试时间
        var questionBlankCode = ''; // 题库编码
        var singleLen = 0, multipleLen = 0, judgeLen = 0, shortLen = 0; // 题目数量
        var singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案
        var examData = {}; // 试卷模板
        var examRecordId = null; // 答题记录的id
        var currentAction = "";
        var isFinishExam = false; // 是否完成考试
        $(function () {
            $("#examOverDialog").dialog({ closed: true });
            var userAgent = navigator.userAgent;
            /**单点配置**/
            var username = "";
            var gps = getQueryString();
            if (gps.type == "piyue") {
                $("#submit").hide()
                $(".examTime").hide()
            }
            if (gps.type != "piyue") {
                $(".pleasePiyue").hide()
                $("#piyueScore").hide()
                $(".submitPiyueScore").hide()
            }
            // 页面认证问题
            if (gps.from == "oa") {
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.uid,
                    async: false,
                    success: function (ress) {
                        // alert(ress.data)
                        web.currentUser = ress.data;
                        username = ress.data.username;

                    }
                });
            } else if (gps.access_token) {//手机办公
                // alert('获取用户信息');
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.currentUserCode,
                    async: false,
                    success: function (ress) {
                        // alert(ress.data)
                        web.currentUser = ress.data;
                        username = ress.data.username;
                        if (ress.data.belongCompanyTypeDictValue == '01') {
                            companyName = '省公司';
                        }
                        if (ress.data.belongCompanyTypeDictValue == '02') {
                            companyName = ress.data.belongCompanyName;
                        }
                        if (ress.data.belongCompanyTypeDictValue == '03') {
                            companyName = ress.data.belongCompanyNameParent;
                        }
                    }
                });
            } else {
                getCurrent();
                username = web.currentUser.username;
            }
            if (gps.currentUserCode && gps.type === "piyue") {
                ajaxgeneral({//获取评阅相关信息
                    url: "action/examInfo/findExamInfoByExamCode?creator=" + gps.currentUserCode + "&examCode=lyyg",
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function (res) {
                        if (res.data) {
                            $("#piyueScore").val(res.data.score);
                            $(".submitPiyueScore").addClass("canSubmit");
                        } else {
                            $(".submitPiyueScore").removeClass("canSubmit");
                        }
                    }
                });
            }

            if (gps.actionType && gps.actionType == "secrecyJoin") { // 已办
                $(".submitBtn").hide();
            } else {

                if (gps.type != "piyue") {
                    $(".submitBtn").show();
                }
                //禁用鼠标右边
                document.oncontextmenu = function () {
                    getparent().mesShow("温馨提示", "请手动答题", 2000, 'red');
                    return false;
                };
                //禁用ctrl+v功能
                document.onkeydown = function () {
                    if (event.ctrlKey && window.event.keyCode == 86) {
                        getparent().mesShow("温馨提示", "请手动答题", 2000, 'red');
                        return false;
                    }
                };
            }

            // 秒数格式化为hh:mm:ss
            function countdown(totalSS) {
                var hh = Math.floor(totalSS / 3600).toString().length < 2 ? '0' + Math.floor(totalSS / 3600) : Math.floor(totalSS / 3600);
                var mm = Math.floor((totalSS % 3600) / 60).toString().length < 2 ? '0' + Math.floor((totalSS % 3600) / 60) : Math.floor((totalSS % 3600) / 60);
                var ss = Math.floor((totalSS % 3600) % 60).toString().length < 2 ? '0' + Math.floor((totalSS % 3600) % 60) : Math.floor((totalSS % 3600) % 60);

                var nowTime = hh + ':' + mm + ':' + ss;
                return nowTime
            }

            // 题目序号和答案序号格式化,0题目转为汉字，1选项转为大写英文字母
            function formatNumber(type, num) { // 0题目序号  1选项序号
                num = parseInt(num);
                var res = '';
                if (type == 0) {
                    var arr1 = new Array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九');
                    var arr2 = new Array('', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿');//可继续追加更高位转换值
                    if (!num || isNaN(num)) {
                        return "零";
                        return "零";
                    }
                    var english = num.toString().split("")
                    var result = "";
                    for (var i = 0; i < english.length; i++) {
                        var des_i = english.length - 1 - i;//倒序排列设值
                        result = arr2[i] + result;
                        var arr1_index = english[des_i];
                        result = arr1[arr1_index] + result;
                    }
                    //将【零千、零百】换成【零】 【十零】换成【十】
                    result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');
                    //合并中间多个零为一个零
                    result = result.replace(/零+/g, '零');
                    //将【零亿】换成【亿】【零万】换成【万】
                    result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
                    //将【亿万】换成【亿】
                    result = result.replace(/亿万/g, '亿');
                    //移除末尾的零
                    result = result.replace(/零+$/, '')
                    //将【零一十】换成【零十】
                    //result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
                    //将【一十】换成【十】
                    result = result.replace(/^一十/g, '十');
                    res = result;

                } else if (type == 1) {
                    res = String.fromCharCode((num - 1) + 65);
                }
                return res;
            }

            // 获取试卷模板
            var tempCurrentUserCode = gps.currentUserCode ? gps.currentUserCode : ""
            ajaxgeneral({
                url: 'action/examAttribute/constructExamLayout?currentUserCode=' + tempCurrentUserCode,
                data: { "examAppCode": "lyyg" },
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    for (var i in res.data.singleQuestionList) {
                        allQuestionCode.push(res.data.singleQuestionList[i].questionCode)
                    }
                    $(".explain").html(res.data.examName);
                    examAppCode = res.data.examAppCode;
                    examLists = res.data.singleQuestionList;
                    questionBlankCode = res.data.questionBankCode;
                    currentAction = "test";
                    if (gps.type == "piyue") {
                        username = gps.username
                    }
                    // 当前用户是否有未完成试卷
                    ajaxgeneral({
                        url: 'action/examInfo/findExamInfo',
                        type: "POST",
                        data: {
                            publishUsername: username,
                            examCode: "lyyg",
                            examAppCode: "lyyg"
                        },
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        success: function (result) {
                            if (result.data != null) {
                                totalSS = parseInt(result.data.residueTime)
                                if (totalSS > 0 && !result.data.isFinishExam) {
                                    timeOut(totalSS);
                                } else {
                                    $(".examTime").hide();
                                }
                                examRecordId = result.data.id; // 未完成试卷id
                                var examRecord = result.data.examRecord.split(','); // 题目编号
                                var examAnswer = result.data.examAnswer.split(',');  // 保存的答案
                                singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案
                                // 匹配已选择的答案
                                function matchAnswer(lists, mark) {
                                    for (var i = 0; i < examRecord.length; i++) {
                                        for (var n = 0; n < lists.length; n++) {
                                            if (examRecord[i] == lists[n].questionCode) {
                                                if (mark != 'shortAnswer') {
                                                    var examAnswerOptions
                                                    if (examAnswer[i]) {
                                                        examAnswerOptions = examAnswer[i].split('/');
                                                        for (var ii = 0; ii < examAnswerOptions.length; ii++) {
                                                            for (var nn = 0; nn < lists[n].answerList.length; nn++) {
                                                                if (examAnswerOptions[ii] == lists[n].answerList[nn].answerCode) {
                                                                    lists[n].answerList[nn].isSelected = true;
                                                                } else {
                                                                    if (!lists[n].answerList[nn].isSelected) {
                                                                        lists[n].answerList[nn].isSelected = false;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                } else {
                                                    lists[n].answerCode = examAnswer[i];
                                                }
                                            }
                                        }
                                    }
                                    return lists;
                                }
                                matchAnswer(res.data.singleQuestionList, 'single');
                                matchAnswer(res.data.moreQuestionList, 'multiple');
                                matchAnswer(res.data.judgeQuestionList, 'judge');
                                matchAnswer(res.data.shortAnswerQuestionList, 'shortAnswer');
                            } else {
                                totalSS = parseInt(res.data.setTime) * 60;
                                if (totalSS > 0) {
                                    timeOut(totalSS);
                                } else {
                                    $(".examTime").hide();
                                }
                            }

                            if (res.data.singleQuestionList && res.data.singleQuestionList.length > 0) {
                                singleLen = res.data.singleQuestionList.length;
                                for (var i = 0; i < res.data.singleQuestionList.length; i++) {
                                    for (var j = 0; j < res.data.singleQuestionList[i].answerList.length; j++) {
                                        if (res.data.singleQuestionList[i].answerList[j].isSelected) {
                                            singleData.push({
                                                questionCode: res.data.singleQuestionList[i].answerList[j].questionCode,
                                                examAnswer: res.data.singleQuestionList[i].answerList[j].answerCode
                                            });
                                        }
                                    }
                                }
                            }
                            examData = res.data;
                            if (result.data && result.data.isFinishExam) { //已完成考试
                                isFinishExam = true;
                            } else {  //未完成考试
                                isFinishExam = false;
                            }
                            if (result.data && result.data.isFinishExam) { //已完成考试
                                showQuestions('reTest', res.data);
                                $(".submitBtn").hide();
                                $("li > input").attr("disabled",true);
                                if (gps.type != "piyue") {
                                    $("#closeDialog").dialog({ closed: false });
                                }
                            } else {  //未完成考试
                                showQuestions('test', res.data);
                                if (gps.type != "piyue") {
                                    $(".submitBtn").show();
                                }
                                // 设置提交按钮高亮是否显示
                                if (singleData.length == singleLen) {
                                    $("#submit").addClass(" canSubmit");
                                } else {
                                    $("#submit").removeClass("canSubmit");
                                }
                                if (remainTimeT) clearInterval(remainTimeT);
                                if (gps.type != "piyue") {
                                    remainTimeT = setInterval(ajaxInterval, 1000); //每隔5秒保存一次数据
                                }
                            }

                        }
                    })
                }
            })

            // 每隔6秒保存一次数据
            function ajaxInterval() {
                if (currentAction == "test") {
                    timeFlag = timeFlag + 1;
                    if (timeFlag >= 6) {
                        var questionCodeArry = [];
                        var examAnswerArry = [];
                        var totalData = singleData

                        for (var i = 0; i < totalData.length; i++) {
                            questionCodeArry.push(totalData[i].questionCode);
                            examAnswerArry.push(totalData[i].examAnswer);
                        }
                        // for (var i = 0; i < allQuestionCode.length; i++) {
                        //     var include = false
                        //     for (var j = 0; j < totalData.length; j++) {
                        //         if (allQuestionCode[i] == totalData[j].questionCode) {
                        //             include = true
                        //             break
                        //         }
                        //     }
                        //     if (include) {
                        //         examAnswerArry.push(totalData[j].examAnswer);
                        //     } else {
                        //         examAnswerArry.push(null);
                        //     }
                        // }
                        if (singleData.length) {
                            timeFlag = 0;
                            ajaxgeneral({
                                url: 'action/examInfo/saveExam',
                                data: {
                                    examCode: "lyyg",
                                    examAppCode: "lyyg",
                                    publishUsername: username,
                                    examRecord: questionCodeArry.join(','),
                                    examAnswer: examAnswerArry.join(','),
                                    id: examRecordId,
                                    residueTime: totalSS, // 秒
                                },
                                loading: false,
                                contentType: "application/json; charset=utf-8",
                                success: function (res) {
                                    //console.log("保存成功！");
                                    examRecordId = res.data.id;
                                }
                            })
                        }
                    }
                }
            }
            // 显示试卷
            function showQuestions(type,data){ // type的值：test测试,reTest重测
                if(data){
                    var titFlag = 0; // 标题序号
                    var qid=1;
                    var list=data.singleQuestionList;
                    var questions=list;
                    questions.sort(function (a,b) {return a.questionOrder-b.questionOrder })
                    //单选
                    for(var i=0;i<questions.length;i++){
                        //单选题
                        if(questions[i].questionType=='single'){
                            var tNum = i%2+1;
                            if (i%2 === 0) {
                                var singleBox = $("<div>").addClass("singleBox").appendTo($(".questions"));
                                switch(Math.floor(i/2)){
                                    case 1:
                                        var h3 = $("<h3>").html("对党委办公室评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 2:
                                        var h3 = $("<h3>").html("对人力资源部评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 3:
                                        var h3 = $("<h3>").html("对市场经营部评价调查问卷").appendTo(singleBox);
                                        break;
                                    // case 4:
                                    //     var h3 = $("<h3>").html("对客户服务中心评价调查问卷").appendTo(singleBox);
                                    //     break;
                                    case 4:
                                        var h3 = $("<h3>").html("对网络部评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 5:
                                        var h3 = $("<h3>").html("对政企客户部评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 6:
                                        var h3 = $("<h3>").html("对综合部评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 7:
                                        var h3 = $("<h3>").html("对工会评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 8:
                                        var h3 = $("<h3>").html("对客户服务部评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 9:
                                        var h3 = $("<h3>").html("对纪委办公室评价调查问卷").appendTo(singleBox);
                                        break;
                                    case 10:
                                        var h3 = $("<h3>").html("对工程建设中心评价调查问卷").appendTo(singleBox);
                                        break;
                                    default:
                                        var h3 = $("<h3>").html("对财务部评价调查问卷").appendTo(singleBox);
                                }
                            }

                            var part = $("<div>").addClass("part singleQues").appendTo($(".singleBox:last-child"));
                            var main = $("<div>").addClass("main singleQues").appendTo(part);
                            var answerScore = $("<span>").addClass("answerScore").appendTo(part);
                            var h6 = $("<h6>").html(tNum+"."+questions[i].titleDescription).appendTo(main);
                            var ul = $("<ul>").appendTo(main);
                            if(questions[i].answerList && questions[i].answerList.length>0){
                                for(var j=0;j<questions[i].answerList.length;j++){
                                    if(type == "test"){ // 测试
                                        if(questions[i].answerList[j].isSelected){
                                            var li = $("<li>").addClass("active").appendTo(ul);
                                            var input = $("<input>").attr({
                                                type:'radio',
                                                id:questions[i].answerList[j].id,
                                                name:questions[i].answerList[j].questionCode,
                                                value:questions[i].answerList[j].answerCode,
                                                checked:true
                                            }).appendTo(li);
                                        }else{
                                            var li = $("<li>").appendTo(ul);
                                            var input = $("<input>").attr({
                                                type:'radio',
                                                id:questions[i].answerList[j].id,
                                                name: questions[i].answerList[j].questionCode,
                                                value:questions[i].answerList[j].answerCode
                                            }).appendTo(li);
                                        }
                                    }else if(type == "reTest"){ // 重测
                                        var li = $("<li>");
                                        var input = $("<input>").attr({
                                            type:'radio',
                                            id:questions[i].answerList[j].id,
                                            name:questions[i].answerList[j].questionCode,
                                            value:questions[i].answerList[j].answerCode
                                        });
                                        if(questions[i].answerList[j].isSelected){ // 已填的答案   isSelected
                                            li = $("<li>").addClass("active red");
                                            input = $("<input>").attr({
                                                "type":'radio',
                                                "id":questions[i].answerList[j].id,
                                                "name":questions[i].answerList[j].questionCode,
                                                "value":questions[i].answerList[j].answerCode,
                                                "checked":true
                                            });
                                        }
                                        if(questions[i].answerList[j].isCorrect){ // 正确答案
                                            li = $("<li>").removeClass("red").addClass(" green");
                                        }

                                        $(input).appendTo(li);
                                        $(li).appendTo(ul);
                                    }
                                    //忽略自己部门web.currentUser.belongOrgName
                                    switch(Math.floor(i/2)){
                                        case 1:
                                            if (web.currentUser.belongDepartmentCode !== "4772396801563491841") {//党群工会
                                                $(main).hide();
                                                $(h3).hide();
                                            }
                                            break;
                                        case 2:
                                            if (web.currentUser.belongDepartmentCode !== "4772402785131648315") {//人力资源部
                                                $(h3).hide();
                                                $(main).hide();
                                            }
                                            break;
                                        case 3:
                                            if (web.currentUser.belongDepartmentCode !== "4772404434756984161") {//市场经营部
                                                // if (web.currentUser.belongOrgCode != "7898225939061096394") {//客户服务中心
                                                //     $(h3).hide();
                                                //     $(main).hide();
                                                // }
                                                $(h3).hide();
                                                $(main).hide();
                                            }
                                            break;
                                        // case 4:
                                        //     if (web.currentUser.belongOrgCode == "7898225939061096394") {//客户服务中心
                                        //         $(h3).hide();
                                        //         $(main).hide();
                                        //     }
                                        //     break;
                                        case 4:
                                            if (web.currentUser.belongDepartmentCode !== "4772405259043562720") {//网络部
                                                $(h3).hide();
                                                $(main).hide();
                                            }
                                            break;
                                        case 5:
                                            if (web.currentUser.belongDepartmentCode !== "4772399757573641549") {//政企客户部
                                                $(h3).hide();
                                                $(main).hide();
                                            }
                                            break;
                                        case 6:
                                            if (web.currentUser.belongDepartmentCode !== "4772408884043430457") {//综合部
                                                $(h3).hide();
                                                $(main).hide();
                                            }
                                            break;
                                        case 7:
                                            if (web.currentUser.belongDepartmentCode !== "1072665990768058368") {//工会
                                                $(h3).hide();
                                                $(main).hide();
                                            }
                                            break;
                                        case 8:
                                            if (web.currentUser.belongDepartmentCode !== "1072665990633840640") {//客户服务部
                                                $(h3).hide();
                                                $(main).hide();
                                            }
                                            break;
                                        case 9:
                                            if (web.currentUser.belongDepartmentCode !== "174829475866421053") {//纪委办公室
                                                $(h3).hide();
                                                $(main).hide();
                                            }
                                            break;
                                        case 10:
                                            if (web.currentUser.belongDepartmentCode !== "1165790737570865152") {//工程建设中心
                                                $(h3).hide();
                                                $(main).hide();
                                            }
                                            break;
                                        default:
                                            if (web.currentUser.belongDepartmentCode !== "4772396029303942903") {//财务部
                                                $(h3).hide();
                                                $(main).hide();
                                            }
                                    }
                                    var label = $("<label>").attr("for", questions[i].answerList[j].id).html(questions[i].answerList[j].answerCode + "：" + questions[i].answerList[j].answerContent).appendTo(li);
                                    for (var index = 0; index < questions[i].answerList.length; index++) {
                                        if (questions[i].answerList[index].isSelected == true) {
                                            $("ul:last").parent("div").parent("div").children("span").attr("answerScore",(5-index)).html('分数：'+(5-index));
                                        }
                                    }
                                }
                            }
                            qid++;
                        }
                        if(questions[i].questionType=='shortAnswer'){
                            titFlag += 1;
                            var part = $("<div>").addClass("part shortAnswer").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"：简答题").appendTo(part);
                            var main = $("<div>").addClass("main shortAnswer").appendTo(part);
                            // var h6 = $("<h6>").html(qid+"、"+questions[i].questionName+"（本题分值:"+questions[i].questionScore+"分）").appendTo(main);
                            var h6 = $("<h6>").html(qid+"、"+questions[i].questionName).appendTo(main);
                            var pObj = $("<p>").appendTo(main);
                            if(type == "test"){ // 测试
                                if(questions[i].answerCode){
                                    var textarea = $("<textarea>").attr({id:questions[i].questionCode,placeholder:"请输入不超过600个字",maxlength:600}).html(questions[i].answerCode).appendTo(pObj);
                                    var surplus = "<div class='surplusNum'>剩余 <span style='color:red' maxLength='600'>"+(600 - (questions[i].answerCode?questions[i].answerCode.length:0))+"</span> 个字</div>";
                                    $(surplus).appendTo(pObj)
                                }else{
                                    var textarea = $("<textarea>").attr({id:questions[i].questionCode,placeholder:"请输入不超过600个字",maxlength:600}).appendTo(pObj);
                                    var surplus = "<div class='surplusNum'>剩余 <span style='color:red' maxLength='600'>600</span> 个字</div>";
                                    $(surplus).appendTo(pObj)
                                }
                            }else if(type == "reTest") { // 重测
                                var textarea = $("<textarea>").attr({id:questions[i].questionCode,placeholder:"请输入不超过600个字",maxlength:600}).html(questions[i].answerCode).appendTo(pObj);
                                // var surplus = "<div class='surplusNum'>剩余 <span style='color:red' maxLength='500'>"+(500 - (questions[i].answerCode?questions[i].answerCode.length:0))+"</span> 个字</div>";
                                // $(surplus).appendTo(pObj)
                            }

                            qid++;
                        }
                    }

                    // input和textarea的事件
                    $("input").on("click",function() {
                        // 单选和判断的高亮、isSelected字段控制
                        if ($(this).attr("type") && $(this).attr("type") == "radio") {
                            if (!$(this).parent("li").hasClass("active")) {
                                $(this).parent("li").addClass(" active");
                                //选择答案后在右侧显示分数
                                switch(this.value){
                                    case 'A':
                                        $(this).parent("li").parent("ul").parent("div").parent("div").children("span").attr("answerScore","5").html('分数：5');
                                        break;
                                    case 'B':
                                        $(this).parent("li").parent("ul").parent("div").parent("div").children("span").attr("answerScore","4").html('分数：4');
                                        break;
                                    case 'C':
                                        $(this).parent("li").parent("ul").parent("div").parent("div").children("span").attr("answerScore","3").html('分数：3');
                                        break;
                                    case 'D':
                                        $(this).parent("li").parent("ul").parent("div").parent("div").children("span").attr("answerScore","2").html('分数：2');
                                        break;
                                    case 'E':
                                        $(this).parent("li").parent("ul").parent("div").parent("div").children("span").attr("answerScore","1").html('分数：1');
                                        break;
                                }
                                $(this).parent("li").siblings().removeClass(" active");

                                // 重测时isSelected字段表示上次已选择的选项
                                var partClass = $(this).parents(".part").attr("class").split(" ")[1];

                                var big_index = $(this).parents(".main").index("." + partClass + " .main"); // 在大题中的索引
                                var small_index = $(this).parents().index(); // 在小题中的索引
                                if ($(this).parents(".part").hasClass("singleQues")) {
                                    //console.log(examData)
                                    for (var i = 0; i < examData.singleQuestionList.length; i++) {
                                        for (var j = 0; j < examData.singleQuestionList[i].answerList.length; j++) {
                                            examData.singleQuestionList[i].answerList[j].isSelected = false;
                                        }
                                    }
                                    examData.singleQuestionList[big_index].answerList[small_index].isSelected = true;
                                    //console.log(examData)
                                }

                                // 单选
                                singleData = [];
                                for (var i = 0; i < $(".singleQues .main").length; i++) {
                                    if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                        singleData.push({
                                            questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                            examAnswer: $(".singleQues .main").eq(i).find("input[type='radio']:checked").val()
                                        });
                                    } else {
                                        singleData.push({
                                            questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                            examAnswer: null
                                        });
                                    }
                                }


                            }
                        };

                        // 单选
                        if ($(this).parents(".main").hasClass("singleQues")) {
                            singleData = [];
                            for (var i = 0; i < $(".singleQues .main").length; i++) {
                                if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                    answerScore = $(".singleQues .main").eq(i).parent("div").children("span").attr("answerScore");
                                    switch(answerScore){
                                        case "A":
                                            answerScore = 5;
                                            break;
                                        case "B":
                                            answerScore = 4;
                                            break;
                                        case "C":
                                            answerScore = 3;
                                            break;
                                        case "D":
                                            answerScore = 2;
                                            break;
                                        case "E":
                                            answerScore = 1;
                                            break;
                                    }
                                    singleData.push({
                                        questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                        examAnswer: $(".singleQues .main").eq(i).find("input[type='radio']:checked").val(),
                                        examCode:"lyyg",
                                        answerScore:answerScore
                                    });
                                    //console.log(singleData)
                                }
                            }
                        }


                        // 设置提交按钮高亮是否显示
                        //if (saveSingleData.length>71) {//全部选择
                        if (singleData.length == 2) {//全部选择
                            $("#submit").addClass(" canSubmit");
                        } else {
                            $("#submit").removeClass("canSubmit");
                        }
                    });
                }
                else{
                    getparent().mesShow("温馨提示","试卷获取失败,请联系系统管理员!!!",2000,'red');
                }
            }
            // 点“提交”
            $("#submit").click(function () {
                for (var i = 0; i < examLists.length; i++) {
                    for (var j = 0; j < singleData.length; j++) {
                        if (examLists[i].questionCode == singleData[j].questionCode) {
                            examLists.splice(i, 1);
                        }
                    }
                }
                $('.questions .singleQues').each(function (i, v) {
                    for (var m = 0; m < examLists.length; m++) {
                        if ($(this).find('h6').attr('class') == examLists[m].id) {
                            $(this).find('h6').css({ 'color': '#D90000' });
                        }
                    }
                })
                //console.log(singleData)
                if (singleData.length + multipleData.length + judgeData.length + shortData.length <= 0) {
                    getparent().mesShow("温馨提示", "还未开始答题", 2000, 'red');
                    // }else if(singleData.length+multipleData.length+judgeData.length+shortData.length < singleLen+multipleLen+judgeLen+shortLen){
                    //     getparent().mesShow("温馨提示","试卷未答完，请继续答题！",2000,'red');
                } else {
                    $("#submitDialog").dialog({ closed: false });
                }
            });

            $("#sureSubmit").click(function () {
                submitData();
            });

            // 提交答案
            function submitData() {
                clearInterval(remainTimeT);

                var questionCodeArry = [];
                var examAnswerArry = [];
                var totalData = singleData.concat(multipleData, judgeData, shortData);

                for (var i = 0; i < totalData.length; i++) {
                    questionCodeArry.push(totalData[i].questionCode);
                    examAnswerArry.push(totalData[i].examAnswer);
                }
                ajaxgeneral({
                    url: 'action/examInfo/submitExam',// 调用判断方法 实际不判断直接保存记录
                    data: {
                        examCode: "lyyg",
                        examAppCode: "lyyg",
                        publishUsername: username,
                        examRecord: questionCodeArry.join(','),
                        examAnswer: examAnswerArry.join(','),
                        questionBlankCode: questionBlankCode,
                        residueTime: totalSS
                    },
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        if (remainTimeT) clearInterval(remainTimeT);

                        $("#scoreDialog h5").html("提交成功！");
                        $("#yes").show();
                        moaBridge.close();
                        window.close();
                        $("#submitDialog").dialog({ closed: true });
                        $("#scoreDialog").dialog({ closed: false });
                    }
                })
            }

            //考试倒计时
            function timeOut() {
                if (gps.type != "piyue") {
                    if (totalSS > 0) {
                        totalSS--;
                        $(".examTime h3").html(countdown(totalSS));
                        setTimeout(timeOut, 1000);
                    } else {
                        clearInterval(remainTimeT);
                        var questionCodeArry = [];
                        var examAnswerArry = [];
                        var totalData = singleData.concat(multipleData, judgeData, shortData);
                        for (var i = 0; i < totalData.length; i++) {
                            questionCodeArry.push(totalData[i].questionCode);
                            examAnswerArry.push(totalData[i].examAnswer);
                        }
                        ajaxgeneral({
                            url: 'action/examInfo/submitExam',// 调用判断方法 实际不判断直接保存记录
                            data: {
                                examCode: "lyyg",
                                examAppCode: "lyyg",
                                publishUsername: username,
                                examRecord: allQuestionCode.join(','),
                                examAnswer: examAnswerArry.join(','),
                                questionBlankCode: questionBlankCode,
                                residueTime: totalSS
                            },
                            contentType: "application/json; charset=utf-8",
                            success: function (res) {
                                if (remainTimeT) clearInterval(remainTimeT);
                            }
                        })
                        $("#examOverDialog").dialog({ closed: false });
                    }
                }
            };

            // 全答对时关闭弹框
            $("#yes").click(function () {
                $("#scoreDialog").dialog({ closed: true });
                if (gps.actionType && gps.actionType == "secrecyTask") {
                    top.dialogClose("detail");
                } else {
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                    moaBridge.close();
                }
            });

            $("#examOver").click(function () {
                $("#examOverDialog").dialog({ closed: true });
                if (gps.actionType && gps.actionType == "secrecyTask") {
                    top.dialogClose("detail");
                } else {
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                    moaBridge.close();
                }
            });


            // 试卷已完成时，关闭页面
            $("#closeBtns button").click(function () {
                $('#closeDialog').dialog('close');

                if (gps.actionType && gps.actionType == "secrecyJoin") {
                    top.dialogClose("detail");
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                    moaBridge.close();
                } else {
                    var userAgent = navigator.userAgent;
                    if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                        // window.location.href = "about:blank";
                        window.history.back();
                    } else {
                        window.opener = null;
                        window.open('', '_self');
                    }
                    window.close();
                    moaBridge.close();
                }

            })
            // 提交批阅分数
            $(".submitPiyueScore").click(function () {
                $(".submitPiyueScore").removeClass("canSubmit");
                ajaxgeneral({
                    url: "action/examInfo/updateExamInfo",
                    data: { "id": gps.piyueId, "score": $("#piyueScore").val() },
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                    }
                });
            })
        })
    </script>
    <style>
        body {
            background-image: url("survey.jpg");
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover;
            opacity: 0.8;
            margin: 0px;
            padding: 0px;
        }
        .wrapper{width:100%;margin:0 auto;background-color:#fff;color:#000;}
        .details{width:100%;padding:10px 10px 60px 10px;font-size:1rem;}
        .explain{line-height:34px;margin-top:10px;font-weight: bolder;font-size: 1.5rem;text-align: center;}
        .questions{padding-bottom: 20px}
        .questionType{font-size:1.1rem;font-weight:bold;line-height:1.2;margin-top:20px;}
        ul {width: 100%;}
        .main,.main ul{padding:0 21px; display: inline-block; }
        .shortAnswer .main ul{padding:0 10px;}
        .main h6{font-size:1rem;line-height:1.5;margin:25px 0 0;font-weight: 400;}
        .questions h3{line-height:1.5;margin:40px 0 0;font-weight: 600;text-align: center;}
        h3 {font-size: 1.35rem;}
        .main li{ margin-left:10px; line-height:1.5;}
        .main span{display:block ;line-height:1.5;margin-left: 45px;}
        .main li.fl{margin-top:0;}
        .main .active{color:orange;}
        .main .green{color:#09DB87;}
        .main .red{color:#E11414;}
        .main input{width:auto;}
        .main label{margin-left:10px; font-size: 1rem;}
        .main span{font-size: 13px; display: inline-block;}
        .shortAnswer .main textarea{min-height:160px;font-size:1rem;}
        .icon-duihao1{font-size:1rem;margin-left:4px;}
        .icon-cuo1{font-size:1rem;font-weight:bold;margin-left:4px;}
        .submitBtn{border:0;outline:0;width:90px;height:36px;background:#B4B4B4;border-radius:4px;font-size:1rem;color:#fff;margin:10px 0 0 60px;letter-spacing:2px;float: right;}
        .submitBtn:active{opacity:.85;}
        .canSubmit{background-color:#E83333;}
        .part span{color: red;display: block;margin-left: 50px;}
        .dialog h5{font-size:1rem;font-weight:bold;text-align:center;margin-top:10px;}
        .forceSubmitDialog p{font-size:1rem;font-weight:bold;text-align:center;margin-top:20px;}
        .scoreDialog p{font-size:1rem;text-align:center;}

        .submitBtns button{border:0;outline:0;padding:0;margin:0;height:32px;font-size:1rem;color:#fff;text-align:center;border-radius:4px;padding:0 20px!important;}
        .submitBtns .gray{background-color:#B4B4B4;}
        .submitBtns .red{background-color:#E11414;}
        .remainTime{font-size:1rem;font-weight:bold;margin-top:20px;text-align:right;}
        .clearfix>li div:nth-child(2){margin-top:10px}
        .clearfix>li div:nth-child(2) input{width:75%}
        .main>h6{text-indent:21px;}
    </style>
</head>

<body style="height: 100%;">
<div class="wrapper">

    <div class="details">
        <p class="explain"></p>

        <div id="remainTime" class="remainTime"></div>
        <div class="questions">

        </div>
        <!-- 提交 -->
        <button class="submitBtn" id="submit">提交</button>
        <span style="margin-left: 20px" class="pleasePiyue">请打分：</span>
        <input id="piyueScore" oninput="myInput()"></input>
        <button class="submitPiyueScore">提交批阅</button>
        <!-- 提交对话框 -->
        <div id="submitDialog" class="easyui-dialog dialog" title="温馨提示" closed="true"
             data-options="modal:true,buttons:'#submitBtns'" style="width:400px;height:200px;padding:10px">
            <h5>您已答完所有题，确认提交？</h5>
        </div>
        <div class="btns submitBtns" id="submitBtns" style="text-align:center;">
            <button class="easyui-linkbutton gray" onclick="$('#submitDialog').dialog('close')">取消</button>
            <button id="sureSubmit" class="easyui-linkbutton red">是的确认提交！</button>
        </div>

        <!-- 提交成功对话框 -->
        <div id="scoreDialog" class="easyui-dialog dialog scoreDialog" title="温馨提示" closed="true"
             data-options="modal:true,buttons:'#scoreBtns'" style="width:400px;height:200px;padding:10px">
            <h5></h5>
            <!--<p>（注：简答题不计入合格分数，只计入参考分值）</p>-->
        </div>
        <div class="submitBtns scoreBtns" id="scoreBtns" style="text-align:center;">
            <button id="yes" class="easyui-linkbutton red hide">确定</button>
        </div>

        <!-- 考试结束对话框 -->
        <div id="examOverDialog" class="easyui-dialog dialog examOverDialog" title="温馨提示" closed="true"
             data-options="modal:true,buttons:'#examOverBtns'" style="width:400px;height:200px;padding:10px">
            <h5>答卷时间已到，试卷自动提交，如已过考试参与时间则自动提交失败！</h5>
            <!--<p>（注：简答题不计入合格分数，只计入参考分值）</p>-->
        </div>
        <div class="submitBtns" id="examOverBtns" style="text-align:center;">
            <button id="examOver" class="easyui-linkbutton red ">确定</button>
        </div>

        <!-- 打开试卷时，试卷已完成，关闭页面 -->
        <div id="closeDialog" class="easyui-dialog dialog closeDialog" title="温馨提示" closed="true"
             data-options="modal:true,buttons:'#closeBtns'" style="width:400px;height:200px;padding:10px">
            <h5>您已完成调查问卷，感谢您的参与！</h5>
        </div>
        <div class="submitBtns" id="closeBtns" style="text-align:center;">
            <button class="easyui-linkbutton red">确定</button>
        </div>
    </div>
</div>
<script>
    function myInput() {
        var limitNum = $("#piyueScore").val();
        var reg = /^(0|([1-9]\d{0,1})|100)$/;
        if (reg.test(limitNum) == false) {
            $("#piyueScore").val("");
            $(".submitPiyueScore").removeClass("canSubmit");
            return false;
        } else {
            $("#piyueScore").val(limitNum);
            $(".submitPiyueScore").addClass("canSubmit");
        }
    }
</script>
</body>

</html>