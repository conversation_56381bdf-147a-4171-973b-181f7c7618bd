package com.simbest.boot.exam.util;

import java.io.Serializable;

/**
 * <strong>Title :项目全局常量类，该类不允许new 继承等操作 </strong><br>
 * <strong>Description : 项目全局常量类，该类不允许new 继承等操作</strong><br>
 * <strong>Create on : 2018/6/26</strong><br>
 * <strong>Modify on : 2018/6/26</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public final class Constants implements Serializable {

    /**
     * 私有构造方法，不允许new操作
     */
    private Constants() {

    }

    /**
     * 项目code
     */
    public static final String APP_CODE = "exam";

    public static final String APP_ID = "uni_2292_ha_exam" ;

    public static final String USER_ADMIN = "hadmin" ;

    /**
     * 项目名称
     */
//    public static final String APP_NAME = "综合考试管理系统";
    public static final String APP_NAME = "综合管理平台";

    /**
     * 统一待办中系统id
     */
    public static final String APP_SYS_ID = "1106";

    /**
     * 统一待办中待办标识
     */
    public static final String APP_WORK_TYPE = "1";

    /**
     * 来源标识
     */
    public static final String SOURCE_P = "PC";

    /**
     * 来源标识
     */
    public static final String SOURCE_M = "MOBILE";

    /**
     * 小程序
     */
    public static final String SOURCE_SMS = "SMS";

    /**
     * 签订标识0未签订
     */
    public static final String SIGN_Z = "0";

    /**
     * 签订标识 1已签订
     */
    public static final String SIGN_O = "1";

    public static final String THREE = "3";
    public static final String TWO = "2";
    public static final String ONE = "1";

    public static final String ZERO = "0";

    public static final String PASS = "通过";

    public static final String NO_PASS = "不通过";

    /**
     * 省公司
     */
    public static final String PROVINCIAL_CODE = "01";

    /**
     * 地市分公司
     */
    public static final String BRANCH_CODE = "02";

    /**
     * 县/市区分公司
     */
    public static final String COUNTY_CODE = "03";

    /**
     * 时间date12:00:00
     */
    public static final String DATE_12 = "12:00:00";

    /**
     * 时间date14:00:00
     */
    public static final String DATE_14 = "14:00:00";

    /**
     * 时间date18:00:00
     */
    public static final String DATE_18 = "18:00:00";

    /**
     * 根组织
     */
    public static final String ROOT_CODE = "00000000000000000000";

    /**
     * 人员级别-员工
     */
    public static final String LEVEL_STAFF = "staff";

    /**
     * 超级管理员
     */
    public static final String HADMIN = "hadmin";

    /**
     * 人员级别-二级经理
     */
    public static final String LEVEL_MANAGER = "manager";


    public static final String TRUE = "true";

    public static final String FALSE = "false";


    /**
     * 简报派发分组类型
     */

    public static final String JBPFGROUPTYPE = "jianbaoPF";

    /**
     *简报派发 待办状态  //状态（0立即发送、1已发送、2未发送、3发送失败）
     */
    public static final String JBPF_STATUS_SENDING = "0";
    public static final String JBPF_STATUS_SENDED = "1";
    public static final String JBPF_STATUS_UNSEND = "2";
    /**
     * 起草环节
     */
    public static final String ACTIVITY_START = "exam.start";
    public static final String ACTIVITY_NAME_START = "填报反馈";
    /**
     * 派发流程
     */
    public static final String PROCESS_TYPE_P = "P";
    public static final String PROCESS_DEF_ID_P = "process_distribute_order";
    public static final String PROCESS_DEF_NAME_P = "收集流程";
    public static final String  PROFILES_ACTIVE_PRD = "prd";
    /**
     * 工单类型
    */
    public static final String ORDER_TYPE_P = "P"; //简报派发类


    /**
     * 人员级别-管理层
     */
    public static final String LEVEL_LEADER = "leader";

    public static final String A = "A";
    public static final String B = "B";
    public static final String C = "C";
    public static final String D = "D";
    public static final String E = "E";
    public static final String F = "F";
    public static final String G = "G";
    public static final String H = "H";
    public static final String I = "I";
    public static final String J = "J";
    public static final String K = "K";
    public static final String L = "L";
    public static final String Q = "Q";
    public static final String R = "R";
    public static final String Z = "Z";

    public static final String SINGLE = "single";
    public static final String MORE = "more";
    public static final String JUDGE = "judge";
    public static final String SHORTANSWER = "shortAnswer";
    public static final String FILLING = "filling";
    public static final String INDEFINITE = "indefinite"; // todo 紧急需求，添加不定项题型，在随机出题部分未做处理
    // 特殊分隔符
    public static final String SEPARATOR = String.valueOf((char) 0x01);
    public static final String PORTAL = "portal";
    public static final String HNJJWZ2 = "hnjjwz2";
    public static final String HNJJWZ3 = "hnjjwz3";


    public static final String HNJJWZ_QUESTIONBANKCODE = "A-002";
    public static final String NULL_CHAR = "";
    public static final String NULL = "null";
    public static final String SHEET_NAME = "统计结果汇总";


    public static final String COMPANY_S = "省公司";
    public static final String COMPANY_F = "分公司";

    public static final int EIGHTEEN = 18;

    public static final String EXPORT_TYPE = "exportType";
    public static final String DEPARTMENT_TYPE = "departmentType";

    public static final String POSITION_YBYG = "一般员工";
    public static final String POSITION_YYYG = "一线员工";
    public static final String POSITION_BZZ = "班组长";
    public static final String POSITION_SZR = "室主任";

    public static final String POSITION_ZR = "主任";
    public static final String POSITION_FZR = "副主任";

    public static final String POSITION_GHZX = "工会主席";
    public static final String POSITION_FGHZX = "工会副主席";

    public static final String POSITION_JL = "经理";
    public static final String POSITION_FJL = "副经理";

    public static final String POSITION_GROUP_SID = "G0411";


    /**
     * 特殊公司编码
     */
    public static final String ORG_CODE_SHENGFEIDA = "4772210096265108263";//省飞达公司
    public static final String ORG_CODE_ZHONGDUAN = "3024910528628002646";//终端公司河南分公司
    public static final Integer answerStatus_0 = 0;  //试卷未提交状态码
    public static final Integer answerStatus_1 = 1;  //试卷提交状态码

    public static final String WORK_TYPE_E = "E";//洛阳满意度相关考试
    public static final String WORK_TYPE_F = "F";//洛阳满意度相关考试
    public static final String WORK_TYPE_H = "H";//采用通用试卷模板的考试


    /**
     * 特殊处理用户
     */
    public static final String USERNAME_MANAGER1 = "liangjie"; //liangjie只能处理洛阳分公司满意度调查问卷用户管理

    /**
     * 考试编码
     */
    public static final String EXAM_CODE_BRANCH_LY = "lyxf";
    public static final String EXAM_CODE_OFFICE_LY = "lyjg";

    /**
     * @desc 洛阳满意度群组编码(不要随意更改此编码)
     * <AUTHOR>
     */
    public static final String GROUP_CODE_LYXF = "1"; //县分公司
    public static final String GROUP_CODE_LYJG = "5"; //机关部门

    /**
     * 考试的年度季度信息字符串拼接
     */
    public static final String CURR_YEAR = "年";
    public static final String CURR_MONTH_ONE = "第一季度";
    public static final String CURR_MONTH_TWO = "第二季度";
    public static final String CURR_MONTH_THR = "第三季度";
    public static final String CURR_MONTH_FOR = "第四季度";
    public static final String EXAM_ANNUAL_QUARTER_LJ = "洛阳分公司各部门协同满意度调查问卷";
    public static final String EXAM_ANNUAL_QUARTER_LX = "洛阳分公司各部门服务支撑力度调查问卷";

    /**
     * 试题编号
     */
    public static final String LYJG_QUESTION_BANK_CODE = "A-222"; //洛阳机关满意度试题编号
    public static final String LYXF_QUESTION_BANK_CODE = "A-111"; //洛阳县分满意度试题编号

    /**
     * 洛阳市机关部门满意度互评的六个维度
     */

    public static final String DIMENSION_PL2 = "PL";   //协作态度
    public static final String DIMENSION_PS2 = "PS";   //责任担当
    public static final String DIMENSION_TF2 = "TF";   //沟通协调
    public static final String DIMENSION_WE2 = "WE";   //协作质量
    public static final String DIMENSION_CA2 = "CA";   //协作效率
    public static final String DIMENSION_SC2 = "SC";   //工作对接

    //县分公司九个维度
    public static final String DIMENSION_PL = "PL";   //专业水平
    public static final String DIMENSION_PS = "PS";   //问题解决
    public static final String DIMENSION_TF = "TF";   //及时反馈
    public static final String DIMENSION_WE = "WE";   //工作效率
    public static final String DIMENSION_CA = "CA";   //咨询解答
    public static final String DIMENSION_SC = "SC";   //服务意识
    public static final String DIMENSION_PG = "PG";   //政策指导
    public static final String DIMENSION_PN = "PN";   //计划性
    public static final String DIMENSION_OE = "OE";   //总体评价
    public static final String DEPARTMENT_SCORE = "departmentScore";   //部门成绩

    /**
     * 查询条件
     */
    public static final Integer QUERY_VALID_CONDITION = 1;


    /**
     * 考试系统超级管理员
     */
    public static final String ROLE_EXAM_SUPPER = "ROLE_EXAM_SUPPER";

    /**
     * 定时任务判断日期
     *
     * <AUTHOR>
     */
    public static final String ONETIME = "0101";
    public static final String TWOTIME = "0401";
    public static final String THRTIME = "0701";
    public static final String FORTIME = "1011"; //跳开国庆假期

    /**
     * 分组编号
     *
     * <AUTHOR>
     */
    public static final String LYXFCODE = "1"; //跳开国庆假期
    public static final String LYJGCODE = "5"; //跳开国庆假期

    /**
     * @desc 出题方式
     * <AUTHOR>
     */
    public static final String TOPIC_STYLE_RANDOM_SPECIAL = "randomSpecial";//随机出题，且全类型至少1题
    public static final String TOPIC_STYLE_RANDOM_GROUP_SPECIAL = "randomGroupSpecial";//随机出题，且分组类型出题要求定制
    public static final String TOPIC_STYLE_RANDOM = "random";//随机出题方式
    public static final String TOPIC_STYLE_ORIGINAL = "original";//全题库出题方式
    public static final String TOPIC_STYLE_FIXED = "fixed";//固定出题方式
    public static final String EXAM_PERMISSION = "exam023";//考试维护菜单权限

    // exam MJSF 相关变量---------------------------------------start-------------
    public static final String MJSF = "2023mjsf_fs";
    public static final String MJSF_PFZ_SJ = "2023mjsf_pfz_sj";
    public static final String MJSF_PFZ_KS = "2023mjsf_pfz_ks";
    public static final String MJSF_PFZ_KS_TITLE = "明纪守法_普法组考试";
    // exam MJSF 相关变量---------------------------------------end  -------------


    /**
     * 答题类型(A 每日答题、B挑战答题，C:人人答题)
     */

    public static final String ANSWER_RECORD_DAILY = "A";//
    public static final String ANSWER_RECORD_CHALLENGE = "B";//
    public static final String ANSWER_RECORD_PERSION = "C";//

    /**
     * 群组--码上巡推送人员群组
     */
    public static final String EXAM_PATROL = "EXAM_PATROL";

}
