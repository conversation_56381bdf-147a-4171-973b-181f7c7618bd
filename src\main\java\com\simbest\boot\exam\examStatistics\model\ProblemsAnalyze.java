/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examStatistics.model;/**
 * Created by KZH on 2019/11/27 9:10.
 */

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2019-11-27 9:10
 * @desc 问题分析
 **/
@Data
public class ProblemsAnalyze extends Option{

    @Excel(name = "一")
    private String companyName;


    //全部人数
    @Excel(name =  "一")
    private String allpation;

    //参与人数
    @Excel(name =  "一")
    private String participation;

    //未参与人数
    @Excel(name =  "一")
    private String noParticipation;

    //参与比例
    @Excel(name =  "一")
    private String participationRatio;

    //未参与比例
    @Excel(name =  "一")
    private String noParticipationRatio;

    //二级经理参与人数
    @Excel(name =  "一")
    private String secondManager;

    //二级经理未参与人数
    @Excel(name =  "一")
    private String noSecondManager;

    //二级经理参与比例
    @Excel(name =  "一")
    private String secondManagerRatio;

    //二级经理未参与比例
    @Excel(name =  "一")
    private String noSecondManagerRatio;

    //涉及县公司
    @Excel(name =  "一")
    private String countyQuantity;

}
