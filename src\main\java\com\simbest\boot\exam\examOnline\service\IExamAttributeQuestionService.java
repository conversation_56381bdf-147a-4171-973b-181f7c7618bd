package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ISystemService;
import com.simbest.boot.exam.examOnline.model.ExamAttributeQuestion;

import java.util.List;

/**
 * <AUTHOR>
 * @desc:
 * @date 2021/7/4  11:30
 */
public interface IExamAttributeQuestionService extends ISystemService<ExamAttributeQuestion,String> {

    /**
      * @desc 保存试卷题目信息
      * <AUTHOR>
      */
    List<ExamAttributeQuestion> saveExamAttributeQuestion(List<ExamAttributeQuestion> examAttributeQuestions);

    /**
      * @desc 查询所有属于该试卷的题目
      * <AUTHOR>
      */
    List<ExamAttributeQuestion> findAllExamAttributeQuestion(String examAppCode);

    /**
      * @desc 根据试卷编码删除信息
      * <AUTHOR>
      */
    void delByExamAppCode(String examAppCode);
}
