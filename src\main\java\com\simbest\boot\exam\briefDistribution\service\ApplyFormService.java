package com.simbest.boot.exam.briefDistribution.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.briefDistribution.model.ApplyForm;
import org.springframework.data.domain.Page;

import java.util.Map;

public interface ApplyFormService extends ILogicService<ApplyForm, String> {

    Page<ApplyForm> findListByPage(int page, int size, String direction, String properties, Map<String, Object> params);

    JsonResponse create(ApplyForm applyForm);

    JsonResponse updateInfo(ApplyForm applyForm);

    JsonResponse delete(String id);

    JsonResponse getById(String applyFormId);

    /**
     * 转阅
     *
     * @param applyFormId
     * @param userStr
     * @param sendType
     * @return
     */
    JsonResponse forward(String applyFormId, String userStr, String sendType);

    /**
     * 发送待办
     * @param applyFormId
     * @return
     */
    JsonResponse send(String applyFormId);

    /**
     * 消息明细
     *
     * @param page
     * @param size
     * @param direction
     * @param properties
     * @param applyFormId
     * @return
     */
    JsonResponse findUsersInMsg(int page, int size, String direction, String properties, String applyFormId);

    JsonResponse findMsgByIdNew(String applyFormId);

    JsonResponse getOrgRangePersonData(String orgCode);

    JsonResponse getFormDetail(String id , String source);
}
