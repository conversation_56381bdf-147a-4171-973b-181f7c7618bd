/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.web.response.JsonResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
public interface IUsDailyQuestionsService  {
    /**
     * 获取答题题目列表接口
     *
     * @param recordId
     * @param source
     * @param currentUserCode
     * @return
     */
    JsonResponse getAnswersList( String source, String currentUserCode);

    JsonResponse answersRecordCheck(String source, String username);

    JsonResponse saveRecord(String workType, String source, String currentUserCode);

    JsonResponse saveAnswer(Map<String, Object> requestParam, String source, String currentUserCode);

}
