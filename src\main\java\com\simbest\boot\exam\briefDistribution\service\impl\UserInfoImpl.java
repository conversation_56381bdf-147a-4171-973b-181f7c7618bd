package com.simbest.boot.exam.briefDistribution.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.briefDistribution.model.UserInfo;
import com.simbest.boot.exam.briefDistribution.repository.UserInfoRepository;
import com.simbest.boot.exam.briefDistribution.service.UserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class UserInfoImpl extends LogicService<UserInfo, String> implements UserInfoService {


    private final UserInfoRepository repository;

    public UserInfoImpl(UserInfoRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Override
    public List<UserInfo> findByBusinessId(String businessId) {
        return repository.findUserInfosByBusinessIdAndEnabledIsTrue(businessId);
    }
}
