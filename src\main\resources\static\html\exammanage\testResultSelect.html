<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>评测结果查询</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/static/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/static/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var reloadUrl='';
        $(function(){
            //获取最近的考试信息
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#atisticsDepartments",//table列表的id名称，需加#
                    "querycmd": "action/examInfo/findTestResultSelect",//table列表的查询命令
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "sortOrder": 'desc',
                    "columns": [[//列
                        {title: "测评人员", field: "trueName", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "所在部门", field: "departmentName", width: 300,sortable: true, tooltip: true,align:"center"},
                        {title: "最终成绩", field: "score", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "答题次数", field: "examNumber", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "评测结果", field: "pass", width: 100,sortable: true, tooltip: true,align:"center"}
                    ]]
                }
            };
            loadGrid(pageparam);
            //导出
            $(document).on('click','.export',function(){
              $("#atisticsDepartmentsQueryForm").attr({"action":web.rootdir + "action/examInfo/exportTestResultSelect","method":"post"});
              $("#atisticsDepartmentsQueryForm").submit()
            });
        });
    </script>
    <style>
        .btn{
            margin-left: 10px;
        }
    </style>
</head>
<body class="body_page">
<!--searchform-->

<!--table-->
<form id="atisticsDepartmentsQueryForm">
    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td colspan="5" width="300">
            </td>
        </tr>
        <tr>
            <td width="90" align="right">测评人员：</td>
            <td width="150">
                <input name="testUserName" type="text" value=""/>
            </td>

            <td width="90" align="right">答题次数：</td>
            <td width="150">
                <input name="examNumber" type="text" value=""/></td>
            <td width="90" align="right">测评结果：</td>
            <td width="150">
                <input  name="testResult" type="text" class="easyui-combobox" editable="false" style="width:100%;height: 32px"
                data-options="valueField: 'value',
                     panelHeight:'auto',
                     textField: 'label',
                     data:[{label:'全部',value:''},{label:'不通过',value:'0'},{label:'通过',value:'1'}],
                     prompt:'--请选择--'"/>
            </td>
            <td>
                <div>
                    <a class="btn fl searchtable"><font>查询</font></a>
                    <a class="btn fl export">导出</a>
                </div>
            </td>
            <td></td>
            <td></td>
        </tr>
    </table>
</form>
<div class="atisticsDepartments" style="margin-top:10px;"><table id="atisticsDepartments"></table></div>
</body>
</html>
