/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate.doc.model;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;
import java.util.Set;

/**
 * <strong>Title : model</strong><br>
 * <strong>Description : 证书 </strong><br>
 * <strong>Create on : 2020/11/10</strong><br>
 * <strong>Modify on : 2020/11/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR> kou<PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Data
public class CertificateData {

    private Map<String, CertificateField> key2CerticateField = Maps.newConcurrentMap();

    public Map<String, CertificateField> put(CertificateField value) {
        return put(value.getKey(), value);
    }

    public Map<String, CertificateField> put(String key, CertificateField value) {
        key2CerticateField.put(key, value);
        return key2CerticateField;
    }

    public CertificateField getValue(String key) {
        return this.key2CerticateField.get(key);
    }

    public Set<String> getKeys() {
        return this.key2CerticateField.keySet();
    }

    public Map<String, CertificateField> getKey2CerticateField() {
        return key2CerticateField;
    }

    public void setKey2CerticateField(Map<String, CertificateField> key2CerticateField) {
        this.key2CerticateField = key2CerticateField;
    }
}
