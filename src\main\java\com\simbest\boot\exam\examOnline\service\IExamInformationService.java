package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ILogicService;

import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.model.ExamInformation;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IExamInformationService extends ILogicService<ExamInformation, String> {
    /**
     * 根据考试编码查询考试信息
     *
     * @param examCode
     * @param creator
     * @return
     */
    ExamInformation findExamInformationByExamCode(String examCode, String creator);

    List<ExamInformation> submitExam(String currentUserCode, String source, ExamInformation examInformation);

    ExamInformation findExamInformation(String questionCode, String publishUsername);


}
