package com.simbest.boot.exam.wfquey.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.wfquey.service.IQueryDictValueService;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.repository.SysDictValueRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用途：数据字段查询
 * 作者：zsf
 * 时间：2018/07/06
 */
@Slf4j
@Service
public class QueryDictValueImpl extends LogicService<SysDictValue, String> implements IQueryDictValueService {

    @Autowired
    private SysDictValueRepository sysDictValueRepository;


    public QueryDictValueImpl(SysDictValueRepository sysDictValueRepositorySuper) {
        super(sysDictValueRepositorySuper);
        this.sysDictValueRepository = sysDictValueRepositorySuper;
    }

    /**
     * 数据字典查询
     *
     * @param dictType
     * @return
     */
    @Override
    public List<SysDictValue> queryByType(String dictType) {
        log.debug("数据字典查询>>>>>>>>>>>>>>dictType=" + dictType);
        return sysDictValueRepository.findDictValue(dictType);
    }

}
