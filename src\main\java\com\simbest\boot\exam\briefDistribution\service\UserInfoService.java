package com.simbest.boot.exam.briefDistribution.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.briefDistribution.model.ApplyForm;
import com.simbest.boot.exam.briefDistribution.model.UserInfo;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

public interface UserInfoService extends ILogicService<UserInfo, String> {

    List<UserInfo> findByBusinessId(String id);
}
