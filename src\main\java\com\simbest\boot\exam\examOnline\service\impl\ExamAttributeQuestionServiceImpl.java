package com.simbest.boot.exam.examOnline.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.SystemService;
import com.simbest.boot.exam.examOnline.model.ExamAttributeQuestion;
import com.simbest.boot.exam.examOnline.repository.ExamAttributeQuestionRepository;
import com.simbest.boot.exam.examOnline.service.IExamAttributeQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @desc: 试卷题目业务实现类
 * @date 2021/7/4  11:35
 */
@Slf4j
@Service
public class ExamAttributeQuestionServiceImpl extends SystemService<ExamAttributeQuestion,String> implements IExamAttributeQuestionService {

    private ExamAttributeQuestionRepository repository;

    @Autowired
    public ExamAttributeQuestionServiceImpl(ExamAttributeQuestionRepository repository) {
        super(repository);
        this.repository = repository;
    }

    /**
     * @desc 保存试卷题目信息
     * <AUTHOR>
     */
    @Override
    public List<ExamAttributeQuestion> saveExamAttributeQuestion(List<ExamAttributeQuestion> examAttributeQuestions) {
       return this.saveAll(examAttributeQuestions);
    }

    /**
     * @desc 查询所有属于该试卷的题目
     * <AUTHOR>
     */
    @Override
    public List<ExamAttributeQuestion> findAllExamAttributeQuestion(String examAppCode) {
        Specification<ExamAttributeQuestion> condition = Specifications.<ExamAttributeQuestion>and()
                .eq("examAppCode",examAppCode)
                .build();
        return  repository.findAll(condition);
    }

    /**
      * @desc 根据试卷编码删除信息
      * <AUTHOR>
      */
    @Override
    public void delByExamAppCode(String examAppCode) {
        repository.deleteByExamAppCode(examAppCode);
    }
}
