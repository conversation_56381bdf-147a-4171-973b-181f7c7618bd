/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;/**
 * Created by KZH on 2019/10/17 16:38.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @create 2019-10-17 16:38
 * @desc 评测记录表
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_review")
@ApiModel(value = "评测记录表")
public class ExamReview extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ER") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "被评测人账号",name = "username",example = "hadmin",required = true)
    private String username;

    @Column(length = 250)
    @ApiModelProperty(value = "被评测人姓名",name = "truename",example = "管理员",required = true)
    private String truename;

    @Column(length = 250)
    @ApiModelProperty(value = "评测人账号",name = "reviewUsername",example = "zhangsan",required = true)
    private String reviewUsername;

    @Column(length = 250)
    @ApiModelProperty(value = "评测人姓名",name = "reviewTruename",example = "张三",required = true)
    private String reviewTruename;

    @Column(length = 250)
    @ApiModelProperty(value = "题目编码",name = "questionCode",example = "A-001-1",required = true)
    private String questionCode;

    @Column(length = 40)
    @ApiModelProperty(value = "题目得分",name = "questionScore",example = "10",required = true)
    private String questionScore;

}
