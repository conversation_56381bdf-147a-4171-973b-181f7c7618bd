/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service.Impl;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.exam.knowledge.model.UsAnswerRecord;
import com.simbest.boot.exam.knowledge.model.UsUserAnswers;
import com.simbest.boot.exam.knowledge.model.UsUserScoreRankingExcel;
import com.simbest.boot.exam.knowledge.repository.UsAnswerRecordRepository;
import com.simbest.boot.exam.knowledge.service.IUsAnswerRecordService;
import com.simbest.boot.exam.knowledge.service.IUsUserAnswersService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.FileTool;
import com.simbest.boot.exam.util.FormatConversion;
import com.simbest.boot.exam.util.PageTool;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.sql.Timestamp;

import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class UsAnswerRecordServiceImpl extends LogicService<UsAnswerRecord, String> implements IUsAnswerRecordService {


    private UsAnswerRecordRepository usAnswerRecordRepository;

    private String param1 = "/acton/UsAnswerRecord";

    @Autowired
    SysDictValueService sysDictValueService;

    @Autowired
    IUsAnswerRecordService usAnswerRecordService;

    @Autowired
    IUsUserAnswersService userAnswersService;
    @Autowired
    LoginUtils loginUtils;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private AppConfig appConfig;
    @Autowired
    public UsAnswerRecordServiceImpl(UsAnswerRecordRepository usAnswerRecordRepository) {
        super(usAnswerRecordRepository);
        this.usAnswerRecordRepository = usAnswerRecordRepository;
    }

    @Override
    public List<UsAnswerRecord> findTodyAnswerRecordByWorkType(String dailyQuestionBankCode, String workType,String username) {
        String todayTimeStr = DateUtil.getDate(new Date(), "yyyy-MM-dd");
        List<UsAnswerRecord> usAnswerRecordList=usAnswerRecordRepository.findTodyAnswerRecordByWorkType(todayTimeStr,dailyQuestionBankCode,workType,username);
        return usAnswerRecordList;
    }

    @Override
    public JsonResponse getRecordList(Integer page, Integer size,String direction,String properties,String source,String currentUserCode,String workType,String time){
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        Pageable pageable = this.getPageable(page, size, direction, properties);
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
        LocalDateTime startTime =null;
        LocalDateTime endTime =null;
        if (StrUtil.isNotEmpty(time)){
             startTime = DateUtil.dateStrFormaterLocalDateTime(time + " 00:00:00", "dateTime");
             endTime = DateUtil.dateStrFormaterLocalDateTime(time + " 23:59:59", "dateTime");
        }
        Specification<UsAnswerRecord> specification= Specifications.<UsAnswerRecord>and()
                .eq(StrUtil.isNotEmpty(workType),"workType",workType)
                .eq("creator",currentUser.getUsername())
                .eq("examCode",knowledgeQuestionBankCode)
                .between(StrUtil.isNotEmpty(time),"createdTime",startTime,endTime)
                .build();
        Page<UsAnswerRecord> pageList = this.findAll(specification,pageable);
        for (UsAnswerRecord usAnswerRecord : pageList.getContent()) {
            LocalDateTime createdTime = usAnswerRecord.getCreatedTime();
            int year = createdTime.getYear();
            if(Constants.ANSWER_RECORD_PERSION.equals(usAnswerRecord.getWorkType())){
                String  title=year+"年合规知识竞赛-人人对战"+"-"+usAnswerRecord.getSendTrueName()+"邀请"+usAnswerRecord.getRecTrueName()+"答题";
                usAnswerRecord.setTitle(title);
            }else{
                usAnswerRecord.setTitle( getTitle(usAnswerRecord.getWorkType(), year));
            }
            List<UsUserAnswers> rightCountToAnalyse = userAnswersService.getRightCountToAnalyse(usAnswerRecord.getId(), currentUser.getUsername());
            if (CollectionUtil.isNotEmpty(rightCountToAnalyse)) {
                usAnswerRecord.setRightCount(String.valueOf(rightCountToAnalyse.size()*10));
             }else{
                usAnswerRecord.setRightCount("0");
            }

            Map<String,Object>   map= userAnswersService.findListByAnswerRecordId(usAnswerRecord.getId());
            if (map.get("MAXTIME")!=null){
                DateTime maxTime = DateUtil.getJodaDateTime(map.get("MAXTIME") + "", "yyyy-MM-dd HH:mm:ss");
                DateTime minTime = DateUtil.getJodaDateTime(map.get("MINTIME") + "", "yyyy-MM-dd HH:mm:ss");
                // 将毫秒转换为分钟和秒
                Duration duration =new Duration(minTime, maxTime);
                long totalSeconds = duration.getStandardSeconds();
                long minutes = totalSeconds / 60;
                long seconds = totalSeconds % 60;
                // 格式化输出
                String formattedTime = String.format("%d.%02d", minutes, seconds);
                usAnswerRecord.setSpendTime(formattedTime);
                usAnswerRecord.setStarTime(MapUtil.getStr(map,"MINTIME"));
                usAnswerRecord.setEndTime(MapUtil.getStr(map,"MAXTIME"));
            }else {
                usAnswerRecord.setSpendTime(null);
                usAnswerRecord.setStarTime(usAnswerRecord.getCreatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                usAnswerRecord.setEndTime(null);
            }
        }
        return JsonResponse.success(pageList);
    }

    @Override
    public JsonResponse getSocreRanking(int page, int size, String direction, String properties, String source, String currentUserCode) {
        //手机端模拟登陆

        Map<String, Object> resultMap = new HashMap<>();
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        Pageable pageable = this.getPageable(page, size, direction, properties);
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
//        List<Map<String, Object>> mapList = userAnswersService.getSocreRanking(knowledgeQuestionBankCode);//
        List<Map<String, Object>> mapList = userAnswersService.getSocreRankingNew(knowledgeQuestionBankCode);//
        List<Map<String,Object>> resultList =  new ArrayList<>( );
        for (Map<String, Object> map : mapList) {
            Map<String, Object> objectObjectHashMap = new HashMap<>(map);
            Long millis=0l;
            if ( map.get("TIME_SPENT_MINUTES")!=null){
                try {
                    millis = Long.valueOf(map.get("TIME_SPENT_MINUTES").toString());
                } catch (NumberFormatException e) {
                }
            }
            Duration duration = new Duration(millis);
            long totalSeconds = duration.getStandardSeconds();
            long minutes = totalSeconds / 60;
            long seconds = totalSeconds % 60;
            String formattedTime = String.format("%d.%02d", minutes, seconds);

            objectObjectHashMap.put("TIME_SPENT_MINUTES",formattedTime);
            resultList.add(objectObjectHashMap);
        }
        // 格式化输出
//        resultList=  extracted(knowledgeQuestionBankCode, mapList);
        resultList = FormatConversion.formatConversion(resultList);

        sortAndAddRank(resultList);
        List<Map<String, Object>> collect = resultList.stream().filter(stringObjectMap -> currentUser.getUsername().equals(stringObjectMap.get("ansewersUserName"))).collect(Collectors.toList());
        Map<String, Object> selfSocreRanking =new HashMap<>();
        if (collect.size()==1){
            selfSocreRanking = collect.get(0);
        }
        List<Map<String, Object>> toplist = new ArrayList<>();
        if (resultList.size()>=30){
            for (Map<String, Object> map : resultList) {
                Integer rank = Integer.valueOf(map.get("rank").toString());
                if (rank<=30){
                    toplist.add(map);
                }else {
                    break;
                }
            }
            resultList=toplist;
        }

        List<Map<String, Object>> contentList = PageTool.pagination(resultList, page, size);
        PageImpl<Map<String, Object>> mapPage = new PageImpl<>(contentList, pageable, resultList.size());
        resultMap.put("page",mapPage);
        Map<String, Object> selfMap = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        if (selfSocreRanking!=null&&selfSocreRanking.size()>0){
            list.add(selfSocreRanking);
        }else {
            selfMap.put("ansewersUserName",currentUser.getUsername());
            selfMap.put("ansewersTrueName",currentUser.getTruename());
            selfMap.put("timeSpentMinutes","0");
            selfMap.put("belongOrgName",currentUser.getBelongOrgName());
            selfMap.put("rank","9999999");
            selfMap.put("totalScore","0");
            list.add(selfMap);
        }
        resultMap.put("self",list.get(0));
        return JsonResponse.success(resultMap);
    }

    /**
     * 处理 答题花费的时间
     * @param knowledgeQuestionBankCode
     * @param mapList
     * @return
     */
    private List<Map<String, Object>> extracted(String knowledgeQuestionBankCode, List<Map<String, Object>> mapList) {
        List<Map<String, Object>> resultList=new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            Map<String, Object> objectObjectHashMap = new HashMap<>(map);
            String ansewersUserName = MapUtil.getStr(map, "CREATOR");
            List<UsAnswerRecord> usAnswerRecordList =   usAnswerRecordService.findAnswerRecordByUser(knowledgeQuestionBankCode,ansewersUserName);
            long millis=0l;
            for (UsAnswerRecord usAnswerRecord : usAnswerRecordList) {
                List<Map<String, Object>> maps = userAnswersService.getTimeByRecordId(usAnswerRecord.getId());
                for (Map<String, Object> map2 : maps) {
                    if (map2.get("MAXTIME")!=null){
                        DateTime maxTime = DateUtil.getJodaDateTime(map2.get("MAXTIME") + "", "yyyy-MM-dd HH:mm:ss");
                        DateTime minTime = DateUtil.getJodaDateTime(map2.get("MINTIME") + "", "yyyy-MM-dd HH:mm:ss");
                        Duration duration = new Duration(minTime, maxTime);
                        millis += duration.getMillis();
                    }
                }
            }
            // 将毫秒转换为分钟和秒
            Duration duration = new Duration(millis);
            long totalSeconds = duration.getStandardSeconds();
            long minutes = totalSeconds / 60;
            long seconds = totalSeconds % 60;

            // 格式化输出
            String formattedTime = String.format("%d.%02d", minutes, seconds);
            objectObjectHashMap.put("TIME_SPENT_MINUTES",formattedTime);
            resultList.add(objectObjectHashMap);
        }
        return resultList;
    }

    @Override
    public JsonResponse getRecordRankingById(String id, String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
      Map<String, Object> mapList = userAnswersService.getSocreRankingByAnswerRecordId(id);//
        if (mapList.size()==0){
            mapList.put("TRUENUM",0);
            mapList.put("TOTAL_SCORE",0);
        }
        return JsonResponse.success(mapList);

    }

    @Override
    public void exportSocreRanking(HttpServletRequest request, HttpServletResponse response, String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        try {
            String targetFileName = new StringBuffer(appConfig.getUploadTmpFileLocation()).append(ApplicationConstants.SLASH).append("竞赛排行榜top30导出-").append(DateUtil.getDateStr("yyyyMMdd")).append(".xls").toString();
            File targetFile = new File(targetFileName);
            String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
            List<Map<String, Object>> mapList = userAnswersService.getSocreRankingNew(knowledgeQuestionBankCode);//
            List<Map<String,Object>> resultList =  new ArrayList<>( );
            for (Map<String, Object> map : mapList) {
                Map<String, Object> objectObjectHashMap = new HashMap<>(map);
                Long millis=0l;
                if ( map.get("TIME_SPENT_MINUTES")!=null){
                    millis = Long.valueOf(map.get("TIME_SPENT_MINUTES").toString());
                }
                Duration duration = new Duration(millis);
                long totalSeconds = duration.getStandardSeconds();
                long minutes = totalSeconds / 60;
                long seconds = totalSeconds % 60;
                String formattedTime = String.format("%d.%02d", minutes, seconds);
                objectObjectHashMap.put("TIME_SPENT_MINUTES",formattedTime);
                resultList.add(objectObjectHashMap);
            }
            resultList = FormatConversion.formatConversion(resultList);
            sortAndAddRank(resultList);
            if (resultList.size()>30){
                resultList=resultList.subList(0, 30);
            }
            ExcelUtil<UsUserScoreRankingExcel> exportUtil = new ExcelUtil<UsUserScoreRankingExcel>(UsUserScoreRankingExcel.class);
            List<UsUserScoreRankingExcel> sortQueryModels = JacksonUtils.json2Type(JacksonUtils.obj2json(resultList), new TypeReference<List<UsUserScoreRankingExcel>>() {
            });
            exportUtil.exportExcel(sortQueryModels, "sheet1", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), response);
        } catch (IOException e) {
            Exceptions.printException(e);
        }

    }

    /**
     * 查询答题记录，如果题目存在，则不用再次抽取题目
     *
     * @param invitaitonId
     * @return
     */
    @Override
    public List<UsAnswerRecord> findRecordByInvitaitonId(String invitaitonId) {
        return usAnswerRecordRepository.findRecordByInvitaitonId(invitaitonId);
    }

    @Override
    public List<UsAnswerRecord> findAnswerRecordByUser(String knowledgeQuestionBankCode, String ansewersUserName) {
        return usAnswerRecordRepository.findAnswerRecordByUser(knowledgeQuestionBankCode,ansewersUserName);
    }

    /**
     * 查询答题记录--人人对战使用
     *
     * @param pmInsId
     * @return
     */
    @Override
    public List<UsAnswerRecord> findRecordByPmInsId(String pmInsId) {
        return usAnswerRecordRepository.findRecordByPmInsId(pmInsId);
    }

    private  String getTitle(String workType,int year){

        switch (workType){
            case Constants.ANSWER_RECORD_DAILY :{
               return year+"年合规知识竞赛-每日答题";
            }
            case Constants.ANSWER_RECORD_CHALLENGE :{
                return year+"年合规知识竞赛-挑战答题";
            }
            case Constants.ANSWER_RECORD_PERSION :{
                return year+"年合规知识竞赛-人人对战";
            } default:return "";
        }
    }


    /**
     * 生成排名
     * @param list
     */
    private static void sortAndAddRank(List<Map<String, Object>> list) {
        // 使用流和自定义比较器进行排序
        list.sort(Comparator.<Map<String, Object>, Comparable>comparing(map -> {
                    Number totalScore = (Number) map.get("totalScore");
                    return totalScore != null ? new BigDecimal(totalScore.toString()) : BigDecimal.ZERO;
                }).reversed()
                .thenComparing(map -> {
                    String timeStr = (String) map.get("timeSpentMinutes");
                    double time = timeStr != null ? Double.parseDouble(timeStr) : 0.0;
                    return time;
                }));

        // 添加排名字段
        int rank = 1;
        BigDecimal lastTotalScore = null;
        Double lastTimeSpentMinutes = null;

        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            BigDecimal totalScore = new BigDecimal(map.get("totalScore").toString());
            double timeSpentMinutes = Double.parseDouble((String) map.get("timeSpentMinutes"));

            // 检查是否需要更新排名
            if (lastTotalScore == null || !lastTotalScore.equals(totalScore) ||
                    (lastTimeSpentMinutes != null && !lastTimeSpentMinutes.equals(timeSpentMinutes))) {
                rank = i + 1;
            }

            // 添加排名字段
            map.put("rank", rank);
            // 更新上次的值
            lastTotalScore = totalScore;
            lastTimeSpentMinutes = timeSpentMinutes;
        }
    }
}
