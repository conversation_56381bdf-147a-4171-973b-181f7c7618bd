/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.lottery.model.Jackpot;
import com.simbest.boot.exam.lottery.model.NewJackpot;
import com.simbest.boot.exam.lottery.model.RetJackpot;

/**
 * <strong>Title : INewJackpotService</strong><br>
 * <strong>Description : 新奖池Service </strong><br>
 * <strong>Create on : 2021/4/10</strong><br>
 * <strong>Modify on : 2021/4/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR> kou<PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface INewJackpotService extends ILogicService<NewJackpot,String> {

    /**
     *  根据奖项查询是否还有剩余
     * @param prize 奖项
     * @return NewJackpot
     */
    NewJackpot findJackpotIsResidue(String prize);

    /**
     *  根据随机码获取到奖池确保不出错
     * @param prize 奖项
     * @param randomNumber 随机码
     * @return NewJackpot
     */
    NewJackpot findJackpotIsResidueByRandomNumber(String prize,String randomNumber);
}
