package com.simbest.boot.exam.codePatrol.service.impl;

import com.github.wenhao.jpa.PredicateBuilder;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.briefDistribution.service.ISyncCommonService;
import com.simbest.boot.exam.codePatrol.model.UsCodePatrol;
import com.simbest.boot.exam.codePatrol.repository.UsCodePatrolRepository;
import com.simbest.boot.exam.codePatrol.service.IUsCodePatrolService;
import com.simbest.boot.exam.util.CodeGenerateUtil;
import com.simbest.boot.sys.model.SysDict;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.service.impl.SysDictService;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.sys.service.impl.SysFileService;
import com.simbest.boot.util.PaginationHelp;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


/**
 * @Author: dumengfei
 * @CreateTime: 2025-03-05
 * @Description: UsCodePatrolServiceImpl
 * @Version: 1.0
 */
@Service
@Slf4j
public class UsCodePatrolServiceImpl extends LogicService<UsCodePatrol, String> implements IUsCodePatrolService {

    private final UsCodePatrolRepository repository;

    public UsCodePatrolServiceImpl(UsCodePatrolRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private SysDictValueService dictValueService;

    @Autowired
    private SysDictService dictService;

    @Autowired
    private ISyncCommonService commonService;

    @Autowired
    private SysFileService fileService;

    @Autowired
    PaginationHelp paginationHelp;

    @Autowired
    private RsaEncryptor rsaEncryptor;



    @Autowired
    private CodeGenerateUtil  codeGenerateUtil;

    @Override
    public JsonResponse addPatrol(UsCodePatrol codePatrol) {
        codePatrol.setCreator("hadmin");
        codePatrol.setModifier("hadmin");
        String password= RandomStringUtils.randomNumeric(6);
        String code=codeGenerateUtil.generateMSXC();
        codePatrol.setCode(code);
        //对类型进行解密
        String paperType="";
        try {
            paperType=rsaEncryptor.decrypt(codePatrol.getPaperType());
        }catch (Exception e){
            return JsonResponse.fail("解析失败，请重新扫码");
        }
        if(paperType==null){
            return JsonResponse.fail("解析失败，请重新扫码");
        }
        codePatrol.setPaperType(paperType);
        codePatrol.setPassword(password);
        this.insert(codePatrol);
        List<SysFile> files = codePatrol.getFiles();

        if (files != null && files.size() > 0) {
            files.stream().forEach(file -> {
                file.setPmInsId(codePatrol.getId());
                fileService.update(file);
            });
        }
        //查询群组人员信息
        List<SysDictValue> dictValues = dictValueService.findByDictType(paperType);
        SysDict dict = dictService.findByDictType(paperType);

        if (dict != null && dictValues.size() > 0) {
            List<String> userNames = dictValues.stream().map(d -> d.getValue()).collect(Collectors.toList());
            log.error("管理员为"+userNames);
            //您好，“码上巡”平台第一组巡察组收到一份情况反映，相关材料已发送您的OA邮箱，请注意查收
            String msg=dict.getSpare1().replaceAll("一份","编号为:["+code+"]的");
            log.error("发送短信+短信内容"+msg+"，附件解压密码为："+password);
            commonService.pushMsg(userNames, msg+"，附件解压密码为："+password);
            commonService.pushEmail(userNames, password,dict,codePatrol);
        }else {
            return JsonResponse.fail("解析失败，请重新扫码");

        }

        return JsonResponse.success("提交成功");
    }

    /**
     * 查询试卷分类
     *
     * @return
     */
    @Override
    public JsonResponse queryPaperType(String dictType) {

        SysDict dictList = dictService.findByDictType(rsaEncryptor.decrypt(dictType));

        return JsonResponse.success(dictList);
    }

    /**
     * PC后台查询填报内容
     *
     * @param page
     * @param size
     * @param codePatrol
     * @param currentUserCode
     * @param source
     * @return
     */
    @Override
    public JsonResponse queryAllPatrol(int page, int size, UsCodePatrol codePatrol, String currentUserCode, String source) {
        Pageable pageable = paginationHelp.getPageable(page, size, "createdTime", "desc");
        PredicateBuilder<UsCodePatrol> predicateBuilder = Specifications.<UsCodePatrol>and()
                .eq("enabled", Boolean.TRUE)
                .eq(StringUtil.isNotEmpty(codePatrol.getPaperType()), "paperType", codePatrol.getPaperType());
        Specification<UsCodePatrol> build = predicateBuilder.build();
        Page<UsCodePatrol> codePatrols = repository.findAllActive(build, pageable);
        codePatrols.getContent().stream().forEach(v -> {
            List<SysFile> files = fileService.getFilesByPmInsId(v.getId());
            v.setFiles(files);
        });
        return JsonResponse.success(codePatrols);
    }




//
//    public static void main(String[] args) {
//        //创建一个待压缩的文件夹
//       /// String zipDirPath = appConfig.getUploadTmpFileLocation().concat(ApplicationConstants.SEPARATOR).concat("创建一个待压缩的文件夹");
//        String zipDirPath = "E:\\文档\\exam\\码上巡"; // 替换为实际文件路径
//
//        File zipDirFile = new File(zipDirPath);
//        zipDirFile.mkdirs();
//
//        //复制新文件在压缩文件下
//        File newAttachmentFile = new File("E:\\文档\\exam\\码上巡\\码上巡cosmic.xlsx");
//        FileUtil.copy(new File("E:\\文档\\exam\\码上巡cosmic.xlsx"), newAttachmentFile, Boolean.TRUE);
//
//
//        String password = "123456";
//
//
//       String path= Zip4JUtil.zip(zipDirPath, zipDirPath.concat(".zip"), password);
//
//        System.out.println(path);
//    }


}
