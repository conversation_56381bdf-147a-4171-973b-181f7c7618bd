/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.repository;/**
 * Created by KZH on 2019/10/8 16:06.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.knowledge.model.UsInvitations;
import com.simbest.boot.exam.knowledge.model.UsPendingTask;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:06
 * @desc
 **/
public interface UsPendingTaskRepository extends LogicRepository<UsPendingTask, String> {
    /**
     * 查询当日所有待办信息
     * 用于核销待办
     * @param currentDay
     * @return
     */
    @Query(value = "select t.*" +
            "  from us_pending_task t" +
            " where t.enabled = 1" +
            "   and to_char(t.created_time, 'yyyy-MM-dd') =:currentDay" +
            "   and t.status in ('PENDING','ACCEPTED')", nativeQuery = true)
    List<UsPendingTask> cancleTask(@Param("currentDay") String currentDay);

    /**
     * 根据pmInsId查询数据
     * @param pmInsId
     * @return
     */
    @Query(value = "select t.*" +
            "  from us_pending_task t" +
            " where t.enabled = 1" +
            "   and t.pm_ins_id=:pmInsId", nativeQuery = true)
    UsPendingTask findPendingTaskByPmInsId(@Param("pmInsId") String pmInsId);


    /**
     * 根据invitaitonId查询数据
     * @param invitaitonId
     * @return
     */
    @Query(value = "select t.*" +
            "  from us_pending_task t" +
            " where t.enabled = 1" +
            "   and t.invitation_id=:invitaitonId", nativeQuery = true)
    List<UsPendingTask> findPendingTaskByInvitaitonId(@Param("invitaitonId") String invitaitonId);


    /**
     * 根据invitaitonId查询数据
     * @param invitaitonId
     * @return
     */
    @Query(value = "select t.*" +
            "  from us_pending_task t" +
            " where t.enabled = 1" +
            "   and t.invitation_id=:invitaitonId" +
            "   and t.status=:status  and t.status <> 'COMPLETED' and t.TASK_TUPE='2'", nativeQuery = true)
    List<UsPendingTask> findPendingTaskByStatue(@Param("invitaitonId") String invitaitonId,@Param("status") String status);

    /**
     * 查询待办列表时间
     * @param currentDay
     * @param recUserName
     * @return
     */
    @Query(value = "select t.*" +
            "  from us_pending_task t" +
            " where t.enabled = 1" +
            "   and to_char(t.created_time,'yyyy-MM-dd')=:currentDay" +
            "   and t.pend_user_name=:recUserName" +
            "   and t.status in ('PENDING','ACCEPTED','ONGOING')", nativeQuery = true)
    List<UsPendingTask> InvitedTask(@Param("currentDay") String currentDay,@Param("recUserName") String recUserName);




}
