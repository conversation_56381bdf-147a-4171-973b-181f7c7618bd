package com.simbest.boot.exam.background.service;

import com.simbest.boot.base.service.ISystemService;
import com.simbest.boot.exam.background.model.ImagePath;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @desc: 试卷背景图片业务接口
 * @date 2021/7/15  15:40
 */
public interface ImagePathService extends ISystemService<ImagePath,String> {

    /**
      * @desc 根据试卷编码查询属于其的背景图片信息
      * <AUTHOR>
      */
    Page<ImagePath> findImagePathByExamAppCode(String examAppCode, Pageable pageable);


    /**
      * @desc 根据试卷编码查询试卷当前使用的背景图片信息
      * <AUTHOR>
      */
    ImagePath findUseImagePathByExamAppCode(String examAppCode);

    /**
      * @desc 保存试卷上传的图片信息
      * <AUTHOR>
      */
    ImagePath saveImagePath(List<Map<String, Object>> imagePaths);

    /**
      * @desc 选择其中的某一图片作为试卷背景
      * <AUTHOR>
      */
    ImagePath saveUseImagePath(ImagePath imagePath);

    /**
      * @desc 根据服务器存储的图片id主键与本系统存储图片信息表的主键进行逻辑删除图片信息
      * <AUTHOR>
      */
    void delImage(ImagePath imagePath);

}
