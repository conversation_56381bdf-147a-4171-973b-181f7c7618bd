/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate.doc.util;

import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.artofsolving.jodconverter.OfficeDocumentConverter;
import org.artofsolving.jodconverter.office.DefaultOfficeManagerConfiguration;
import org.artofsolving.jodconverter.office.OfficeManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;


/**
 * <strong>Title : DocxConverter</strong><br>
 * <strong>Description : 用于docx文件的格式转换 </strong><br>
 * <strong>Create on : 2020/11/10</strong><br>
 * <strong>Modify on : 2020/11/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Component
@DependsOn({"httpClient"})
public class DocxConverter {

    @Autowired
    private  RsaEncryptor rsaEncryptor;
    /**
     * 使用libreoffice服务，将docx格式文件转成pdf格式文件
     * @param docxPath
     * @param pdfPath
     * @param libreOfficePath
     * @return
     * @throws IOException    参数
     * File    返回类型
     */
    public static File toPdfUsingLibreOffice(String docxPath, String pdfPath, String libreOfficePath)
            throws IOException {
        File pdf = new File(pdfPath);
        File docx = new File(docxPath);


        // 测试为本地服务转换pdf
        try {
            OfficeManager officeManager = getOfficeManagerAndStartService(libreOfficePath);
            OfficeDocumentConverter converter = new OfficeDocumentConverter(officeManager);
            converter.convert(new File(docxPath), pdf);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return pdf;
    }

    public static OfficeManager getOfficeManagerAndStartService(String libreOfficePath) {
        OfficeManager officeManager = new DefaultOfficeManagerConfiguration().setOfficeHome(new File(libreOfficePath))
                .buildOfficeManager();
        startService(officeManager);

        return officeManager;
    }

    private static void startService(OfficeManager officeManager) {

        try {
            // 准备启动服务
            officeManager.start(); // 启动服务
            log.info("office转换服务启动成功");
        } catch (Exception ce) {
            log.error("office转换服务启动失败!详细信息:{}", ce);
        }
    }

    public static void main(String[] args) throws IOException {
        String inputFile = "D:\\Workspace\\IdeaWorkspace\\simbest-boot-exam\\src\\main\\resources\\tmp\\docx\\1605011861713\\8245ca71.docx";
        String outputFile = "D:\\Workspace\\IdeaWorkspace\\simbest-boot-exam\\src\\main\\resources\\tmp\\pdf\\1605011861920\\415d49c1.pdf";
        String librePath = "D:\\Program Files\\LibreOffice";
        toPdfUsingLibreOffice(inputFile, outputFile, librePath);
    }
}
