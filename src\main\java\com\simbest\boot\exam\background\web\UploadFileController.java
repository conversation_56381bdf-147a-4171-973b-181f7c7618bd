package com.simbest.boot.exam.background.web;

import com.google.common.collect.Sets;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.background.repository.FileExtendRepository;
import com.simbest.boot.exam.background.service.IFileExtendService;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.LoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @用途:
 * @作者：zsf
 * @时间: 2020/9/2
 */
@Api(description = "")
@Slf4j
@RestController
@RequestMapping(value = "/action/uploadFile")
public class UploadFileController extends LogicController<SysFile, String> {

    @Autowired
    private ISysFileService fileService;


    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private FileExtendRepository fileExtendRepository;

    @Autowired
    public UploadFileController(ISysFileService fileService) {
        super(fileService);
        this.fileService = fileService;
    }

    @Autowired
    private LoginUtils loginUtils;

    @ApiOperation(value = "传统方式上传附件（支持IE8）,支持关联流程", notes = "会保存到数据库SYS_FILE")
    @PostMapping(value = {"/uploadProcessFiles", "/uploadProcessFiles/sso", "/uploadProcessFiles/api"})
    @ResponseBody
    public void uploadFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JsonResponse jsonResponse = doUploadFile(request, response);
        String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = response.getWriter();
        out.println(result);
        out.close();
      //  return JsonResponse.success(imagePath);
    }

    /**
     * 上传文件
     */
    private JsonResponse doUploadFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Set<MultipartFile> uploadingFileSet = Sets.newHashSet();
        MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
        //优先通过指定参数名称file获取文件
        Collection<MultipartFile> uploadingFileList = mureq.getFiles("file");
        uploadingFileList.forEach(f -> uploadingFileSet.add(f));
        //再通过不指定参数名称获取文件
        Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
        multipartFiles.values().forEach(f -> uploadingFileSet.add(f));
        List<SysFile> sysFiles = fileExtendService.uploadProcessFiles(uploadingFileSet);
        JsonResponse jsonResponse;
        if (!sysFiles.isEmpty()) {
            UploadFileResponse uploadFileResponse = new UploadFileResponse();
            uploadFileResponse.setSysFiles(sysFiles);
            jsonResponse = JsonResponse.success(uploadFileResponse);
        } else {
            jsonResponse = JsonResponse.defaultErrorResponse();
        }
        return jsonResponse;
    }


    @ApiOperation(value = "删除附件")
    @PostMapping(value = {"/deleteSysFile", "/deleteSysFile/sso", "/api/deleteSysFile"})
    public JsonResponse deleteSysFile(@RequestParam String id) {
        try {
            fileExtendRepository.deleteSysFile(id);
            return JsonResponse.success(null, "删除成功");
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(null, "删除失败");
        }
    }


    @ApiOperation(value = "传统方式上传附件（支持IE8）,支持关联流程", notes = "会保存到数据库SYS_FILE")
    @PostMapping(value = {"/uploadFileH5", "/uploadFileH5/sso", "/uploadFileH5/api", "/anonymous/uploadFileH5"})
    @ResponseBody
    public void uploadFileH5(HttpServletRequest request, HttpServletResponse response) throws Exception {
        loginUtils.adminLogin();
        JsonResponse jsonResponse = doUploadFile(request, response);
        String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = response.getWriter();
        out.println(result);
        out.close();
        //  return JsonResponse.success(imagePath);
    }



    @ApiOperation(value = "REST方式上传附件,支持关联流程", notes = "会保存到数据库SYS_FILE")
    @PostMapping(value = {"/uploadFileH5New", "/uploadFileH5New/sso", "/uploadFileH5New/api", "/anonymous/uploadFileH5New"})
    @ResponseBody
    public ResponseEntity<?> uploadFileRest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        loginUtils.adminLogin();
        JsonResponse jsonResponse = doUploadFile(request, response);
        return new ResponseEntity(jsonResponse, HttpStatus.OK);
    }


}
