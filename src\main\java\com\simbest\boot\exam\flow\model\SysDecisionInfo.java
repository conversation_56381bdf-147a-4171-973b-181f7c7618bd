package com.simbest.boot.exam.flow.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Table;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * @ClassName: SysDecisionInfo
 * @description:
 * @author: ZHAOBO
 * @create: 2024-07-09 17:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity(name = "sys_decision_info")
@Table(appliesTo = "sys_decision_info", comment = "环节信息表")
@ApiModel(description = "环节信息表")
public class SysDecisionInfo  extends LogicModel {


    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "SDI") //主键前缀
    private String id;

    @ApiModelProperty(value = "流程编码")
    @Column(length = 100)
    private String processDefId;

    @ApiModelProperty(value = "流程名称")
    @Column(length = 100)
    private String processDefName;

    @ApiModelProperty(value = "环节编码")
    @Column(length = 100)
    private String activityDefId;

    @ApiModelProperty(value = "环节名称")
    @Column(length = 100)
    private String activityDefName;

    @ApiModelProperty(value = "下一环节编码")
    @Column(length = 100)
    private String nextActivityDefId;

    @ApiModelProperty(value = "下一环节名称")
    @Column(length = 100)
    private String nextActivityDefName;

    @ApiModelProperty(value = "流转编码")
    @Column(length = 100)
    private String decisionId;

    @ApiModelProperty(value = "流转名称")
    @Column(length = 100)
    private String decisionName;

    @ApiModelProperty(value = "出人配置")
    @Column(length = 1000)
    private String decisionConfig;

    @ApiModelProperty(value = "决策项排序值")
    private Integer displayOrder;

}
