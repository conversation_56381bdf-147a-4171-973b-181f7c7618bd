package com.simbest.boot.exam.todo.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.todo.model.UsTodoModel;

/**
 * @用途: 统一待办
 * @作者：zsf
 * @时间: 2018/12/27
 */
public interface IUsTodoModelService extends ILogicService<UsTodoModel,Long> {

    /**
     * 保存推送待办数据到本地
     * @param usTodoModel      待办对象
     * @return
     */
    UsTodoModel savaLocalTodoData(UsTodoModel usTodoModel);

    /**
     * 根据key查到推送待办记录
     * @param key 待办id
     * @return
     */
    UsTodoModel getTodoByKey(String key);

    /**
     * 推送统一待办
     * @param work      待办对象
     * @return
     */
    boolean openTodo(ExamWork work);

    /**
     * 注销一待办
     * @param work      待办对象
     * @return
     */
    boolean closeTodo(ExamWork work);

    UsTodoModel findAllTypeStatus(String username,String workType);

}
