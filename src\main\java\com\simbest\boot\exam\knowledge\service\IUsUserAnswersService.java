/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsUserAnswers;
import org.springframework.data.repository.query.Param;

import java.util.List;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
public interface IUsUserAnswersService extends ILogicService<UsUserAnswers,String> {


    JsonResponse getRecordAnswersList(String id);

    public List<Map<String, Object>> getSocreRanking(String knowledgeQuestionBankCode);
    public List<Map<String, Object>> getSocreRankingNew(String knowledgeQuestionBankCode);

    public Map<String, Object> getSelfSocreRanking(String knowledgeQuestionBankCode,String answerUserName) ;

        /**
         * 查询答题记录
         * 用于续答
         * @param pmInsId
         * @param ansewersUserName
         * @param questionBankCode
         * @return
         */
    List<UsUserAnswers> findAnswerUserNameInfo(String pmInsId,String ansewersUserName, String questionBankCode);

    /**
     *  查询答题次数，用于判断是否A\B双方已经全部答完
     * @param currentDay
     * @param workType
     * @param questionBankCode
     * @param ansewersUserName
     * @return
     */
    List<UsUserAnswers> findAnswerUserNameCount(String currentDay,String workType,String questionBankCode,String ansewersUserName);
    Map<String, Object> getSocreRankingByAnswerRecordId(String id);

    /**
     *
     * @param currentDay
     * @param workType
     * @param questionBankCode
     * @param invitaitonId
     * @param ansewersUserName
     * @return
     */
    List<UsUserAnswers> getRightCount(String currentDay,String workType, String questionBankCode,String invitaitonId,String ansewersUserName);


    /**
     * 获取答题记录的答题情况
     * 考试记录xin
     * @param answerRecordId
     * @param ansewersUserName
     * @return
     */
    List<UsUserAnswers> getRightCountToAnalyse( String answerRecordId, String ansewersUserName);

    List<UsUserAnswers> getUsUserAnswersByRecordId(String ecordId);

    List<Map<String, Object>> getTimeByRecordId(String id);


    List<Map<String, Object>> getTimeByRecordByUser(@Param("workType") String workType,@Param("questionBankCode") String questionBankCode,@Param("invitaitonId") String invitaitonId,@Param("ansewersUserName);") String ansewersUserName);


    List<UsUserAnswers> findUsUserAnswersByQuestionIdAndAnswerRecordId(String questionId, String answerRecordId);

    Map<String, Object> findListByAnswerRecordId(String id);
}
