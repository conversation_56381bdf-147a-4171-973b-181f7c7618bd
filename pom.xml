<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.simbest.boot</groupId>
        <artifactId>simbest-boot-parent</artifactId>
        <version>0.3</version>
        <relativePath>../simbest-boot-parent</relativePath>
    </parent>

    <groupId>com.simbest.boot</groupId>
    <artifactId>exam</artifactId>
    <version>0.1</version>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>dem01</description>

    <properties>
        <wf.version>0.1</wf.version>
        <simbest.version>0.1</simbest.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <profileActive>obuat</profileActive>

        <server.servlet.context-path>/${project.artifactId}</server.servlet.context-path>
        <server.servlet.session.timeout>14400</server.servlet.session.timeout>
        <server.servlet.session.cookie.max-age>${server.servlet.session.timeout}</server.servlet.session.cookie.max-age>
        <spring.servlet.multipart.max-file-size>10MB</spring.servlet.multipart.max-file-size>
        <spring.servlet.multipart.max-request-size>10MB</spring.servlet.multipart.max-request-size>
		<spring.datasource.type>com.alibaba.druid.pool.DruidDataSource</spring.datasource.type>
        <spring.datasource.database.type>h2</spring.datasource.database.type>
        <spring.datasource.driver-class-name>org.h2.Driver</spring.datasource.driver-class-name>
        <spring.datasource.url>jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=Oracle;AUTO_RECONNECT=TRUE;DB_CLOSE_ON_EXIT=FALSE</spring.datasource.url>
        <spring.datasource.username>sa</spring.datasource.username>
        <spring.datasource.password></spring.datasource.password>
        <spring.jpa.database-platform>org.hibernate.dialect.H2Dialect</spring.jpa.database-platform>
        <spring.datasource.sql-script-encoding>${project.build.sourceEncoding}</spring.datasource.sql-script-encoding>
        <spring.datasource.druid.initial-size>5</spring.datasource.druid.initial-size>
        <spring.datasource.druid.min-idle>10</spring.datasource.druid.min-idle>
        <spring.datasource.druid.max-active>20</spring.datasource.druid.max-active>
        <spring.datasource.druid.max-wait>60000</spring.datasource.druid.max-wait>
        <spring.datasource.druid.time-between-eviction-runs-millis>60000</spring.datasource.druid.time-between-eviction-runs-millis>
        <spring.datasource.druid.min-evictable-idle-time-millis>300000</spring.datasource.druid.min-evictable-idle-time-millis>
        <spring.datasource.druid.validation-query>SELECT 1 FROM DUAL</spring.datasource.druid.validation-query>
        <spring.datasource.druid.filter.stat.slow-sql-millis>5000</spring.datasource.druid.filter.stat.slow-sql-millis>
        <spring.datasource.druid.filter.wall.enabled>false</spring.datasource.druid.filter.wall.enabled>
        <spring.datasource.druid.stat-view-servlet.login-username>hadmin</spring.datasource.druid.stat-view-servlet.login-username>
        <spring.datasource.druid.stat-view-servlet.login-password>pass180418</spring.datasource.druid.stat-view-servlet.login-password>
        <spring.redis.cluster.nodes>localhost:6379</spring.redis.cluster.nodes>
        <spring.redis.cluster.password>123456</spring.redis.cluster.password>
        <spring.redis.cluster.max-redirects>3</spring.redis.cluster.max-redirects>
        <spring.cache.redis.key-prefix>cache:key:${project.artifactId}:</spring.cache.redis.key-prefix>
        <spring.console.enabled>true</spring.console.enabled>
        <spring.console.path>/h2-console</spring.console.path>
        <spring.jpa.show-sql>true</spring.jpa.show-sql>
        <spring.jpa.generate-ddl>true</spring.jpa.generate-ddl>
        <spring.jpa.hibernate.ddl-auto>update</spring.jpa.hibernate.ddl-auto>
        <spring.jpa.database>${spring.datasource.database.type}</spring.jpa.database>
        <spring.jpa.properties.hibernate.dialect>org.hibernate.dialect.Oracle9iDialect</spring.jpa.properties.hibernate.dialect>
        <spring.messages.encoding>${project.build.sourceEncoding}</spring.messages.encoding>
        <spring.http.encoding.charset>${project.build.sourceEncoding}</spring.http.encoding.charset>
        <spring.thymeleaf.cache>false</spring.thymeleaf.cache>
        <spring.thymeleaf.enable-spring-el-compiler>true</spring.thymeleaf.enable-spring-el-compiler>
        <spring.session.store-type>redis</spring.session.store-type>
        <spring.session.redis.cleanup-cron>0 * * * * *</spring.session.redis.cleanup-cron>
        <spring.session.redis.namespace>spring:session:${project.artifactId}</spring.session.redis.namespace>
        <management.endpoints.web.exposure.include>*</management.endpoints.web.exposure.include>

        <logback.groupId>${project.groupId}</logback.groupId>
        <logback.artifactId>${project.artifactId}</logback.artifactId>
        <app.file.upload.path>/home/<USER>/simbestboot/file/exam</app.file.upload.path>
        <app.file.upload.location>disk</app.file.upload.location>
        <app.oa.portal.token>SIMBEST_SSO</app.oa.portal.token>
        <!--主数据-->
        <app.uums.address>http://************:8088/uums</app.uums.address>
        <!--http://************:8080/uums//http://***********:8001/uums-->
        <app.swagger.address>http://localhost:8080${server.servlet.context-path}/swagger-ui.html</app.swagger.address>
        <app.druid.monitor.address>http://localhost:8080${server.servlet.context-path}/druid</app.druid.monitor.address>
        <app.actuator.monitor.address>http://localhost:8080${server.servlet.context-path}/actuator</app.actuator.monitor.address>
        <app.task.heart.test.job>0 0/5 * * * ?</app.task.heart.test.job>
        <app.captcha.enable>false</app.captcha.enable>


        <!-- 保密承诺书定时 器测试 -->
        <app.task.time.book>0 0 0 25 01 ?</app.task.time.book><!--每年01-01号产生新的保密承诺书-->
        <app.task.time.urge>0 0 14 25/1 * ?</app.task.time.urge><!--短信催办功能定时器-->
        <app.task.time.todo>0 0 8,14 * * ?</app.task.time.todo><!--统一待办推送定时器-->

        <!-- 统一待办 -->
       <!-- <cxf.openTodoUrl>http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoOpenListInfoSrv?wsdl</cxf.openTodoUrl>
        <cxf.closeTodoUrl>http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoCloseListInfoSrv?wsdl</cxf.closeTodoUrl>
        <cxf.cancelTodoUrl>http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoCancelListInfoSrv?wsdl</cxf.cancelTodoUrl>-->
        <!-- 新统一待办 -->
        <cxf.openTodoUrl>http://************:8088/ntodo/services/openTodoService?wsdl</cxf.openTodoUrl>
        <cxf.closeTodoUrl>http://************:8088/ntodo/services/closeTodoService?wsdl</cxf.closeTodoUrl>
        <cxf.cancelTodoUrl>http://************:8088/ntodo/services/cancelTodoService?wsdl</cxf.cancelTodoUrl>

        <spring.cloud.nacos.config.server-addr>nacos.ha.cmcc:8088</spring.cloud.nacos.config.server-addr>
        <spring.cloud.nacos.config.username>nacos</spring.cloud.nacos.config.username>
        <spring.cloud.nacos.config.password>Best@2008</spring.cloud.nacos.config.password>

    </properties>

    <dependencies>
        <dependency>
            <groupId>com.oceanbase</groupId>
            <artifactId>oceanbase-client</artifactId>
            <version>2.4.9</version>
        </dependency>
        <!--务必不能引入jasypt-spring-boot-starter，否则不打印日志-->

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>7.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.simbest.boot</groupId>
            <artifactId>mq</artifactId>
            <version>${simbest.version}</version>
        </dependency>

        <dependency>
            <groupId>com.simbest.boot</groupId>
            <artifactId>simbest-boot-cores</artifactId>
            <version>0.3</version>
        </dependency>
        <dependency>
            <groupId>com.simbest.boot</groupId>
            <artifactId>simbest-boot-cmcc</artifactId>
            <version>0.3</version>
        </dependency>
        <!-- 热部署模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 这个需要为 true 热部署才有效 -->
        </dependency>

        <!--邮件-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>


    <!-- 流程引擎 -->
<!--        <dependency>-->
<!--            <groupId>com.simbest.boot</groupId>-->
<!--            <artifactId>simbest-boot-wf</artifactId>-->
<!--            <version>${wf.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.simbest.boot</groupId>-->
<!--            <artifactId>simbest-boot-wfdriver</artifactId>-->
<!--            <version>0.2</version>-->
<!--        </dependency>-->


        <!--alibaba easyexcel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.xmlbeans</groupId>
                    <artifactId>xmlbeans</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.0</version>
        </dependency>

        <dependency>
            <groupId>org.jodconverter</groupId>
            <artifactId>jodconverter-core</artifactId>
            <version>4.2.2</version>
        </dependency>

        <!--springboot支持包，里面包括了自动配置类 -->
        <!-- https://mvnrepository.com/artifact/org.jodconverter/jodconverter-spring-boot-starter -->
        <dependency>
            <groupId>org.jodconverter</groupId>
            <artifactId>jodconverter-spring-boot-starter</artifactId>
            <version>4.2.2</version>
        </dependency>


        <!--jodconverter 本地支持包 -->
        <!-- https://mvnrepository.com/artifact/org.jodconverter/jodconverter-local -->
        <dependency>
            <groupId>org.jodconverter</groupId>
            <artifactId>jodconverter-local</artifactId>
            <version>4.2.2</version>
        </dependency>

        <!-- libreoffice converter for Converting docx to pdf -->
        <dependency>
            <groupId>org.artofsolving.jodconverter</groupId>
            <artifactId>jodconverter-core</artifactId>
            <version>3.0-beta-4</version>
        </dependency>

        <!-- openoffice -->
        <dependency>
            <groupId>org.openoffice</groupId>
            <artifactId>ridl</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.openoffice</groupId>
            <artifactId>unoil</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.openoffice</groupId>
            <artifactId>juh</artifactId>
            <version>2.3.0</version>
        </dependency>

        <!-- convert pdf to image -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.15</version>
        </dependency>



    </dependencies>

    <dependencyManagement>
        <dependencies>

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <addResources>false</addResources>
                    <profiles>
                        <profile>dev</profile>
                        <profile>uat</profile>
                        <profile>obuat</profile>
                        <profile>prd</profile>
                        <profile>obprd</profile>
                    </profiles>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>

        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profileActive>dev</profileActive>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <profileActive>uat</profileActive>
                <spring.datasource.database.type>oracle</spring.datasource.database.type>
                <spring.datasource.driver-class-name>oracle.jdbc.OracleDriver</spring.datasource.driver-class-name>
                <spring.datasource.url>*****************************************************= (PROTOCOL=TCP)(HOST=************)(PORT=1521)))(CONNECT_DATA=(SERVICE_NAME = orcl)))</spring.datasource.url>
                <spring.datasource.username>${project.artifactId}</spring.datasource.username>
                <spring.datasource.password>Test44Em</spring.datasource.password>
<!--                <spring.redis.cluster.nodes>************:36349,************:36359,************:36369,************:36379,************:36389,************:36399</spring.redis.cluster.nodes>-->
<!--                <spring.redis.cluster.password>Xianzai@0528</spring.redis.cluster.password>-->
                <spring.redis.config.type>dictValueRedis</spring.redis.config.type>
                <spring.redis.config.type.redis>redis-161</spring.redis.config.type.redis>
                <spring.redis.cluster.password>Xianzai@0528</spring.redis.cluster.password>

                <spring.jpa.database-platform>org.hibernate.dialect.Oracle10gDialect</spring.jpa.database-platform>
                <spring.datasource.druid.filter.wall.enabled>true</spring.datasource.druid.filter.wall.enabled>
                <!--共享存储服务器路径-->
                <app.file.upload.path>/home/<USER>/simbestboot/portal/portal</app.file.upload.path>
                <app.file.upload.pre.path>/home/<USER>/simbestboot/portal</app.file.upload.pre.path>
                <app.host.port>http://************:8088</app.host.port>

                <!-- 验证码-->
                <app.captcha.enable>false</app.captcha.enable>
            </properties>
        </profile>
        <profile>
            <id>obuat</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profileActive>obuat</profileActive>
                <!-- 验证码-->
                <app.captcha.enable>false</app.captcha.enable>

                <!-- 统一待办 -->
                <cxf.openTodoUrl>http://************:8088/ntodo/services/openTodoService?wsdl</cxf.openTodoUrl>
                <cxf.closeTodoUrl>http://************:8088/ntodo/services/closeTodoService?wsdl</cxf.closeTodoUrl>
                <cxf.cancelTodoUrl>http://************:8088/ntodo/services/cancelTodoService?wsdl</cxf.cancelTodoUrl>
            </properties>
        </profile>
        <profile>
            <id>prd</id>
            <properties>
                <profileActive>prd</profileActive>
                <spring.jpa.generate-ddl>false</spring.jpa.generate-ddl>
                <spring.datasource.database.type>oracle</spring.datasource.database.type>
                <spring.datasource.driver-class-name>oracle.jdbc.OracleDriver</spring.datasource.driver-class-name>
                <spring.datasource.url>
                    ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************))))
                </spring.datasource.url>
                <spring.datasource.username>${project.artifactId}</spring.datasource.username>
                <!--suppress UnresolvedMavenProperty -->
                <spring.datasource.password>Aug#20230802</spring.datasource.password>
                <!---->
<!--                <spring.redis.cluster.nodes>************:26379,************:26389,************:26399,************:26379,************:26389,************:26399</spring.redis.cluster.nodes>-->
<!--                <spring.redis.cluster.password>Xianzai@0528</spring.redis.cluster.password>-->
                <spring.redis.config.type>dictValueRedis</spring.redis.config.type>
                <spring.redis.config.type.redis>redis-163-169-6666</spring.redis.config.type.redis>
                <spring.redis.cluster.password>Xianzai@0528</spring.redis.cluster.password>
                <!-- Redis settings
                <spring.redis.cluster.nodes>************:26369</spring.redis.cluster.nodes>
                <spring.redis.cluster.password>Xianzai@0528</spring.redis.cluster.password>
                 -->
                <!-- BPS配置 -->
                <bps.logicName>default</bps.logicName><!-- 对应BPS应用名称 -->
                <bps.uddiHost>************</bps.uddiHost><!-- 对应BPS服务器IP ************ -->
                <bps.uddiPort>8080</bps.uddiPort><!-- 对应BPS服务器端口 -->
                <bps.uddiAdminPort>6200</bps.uddiAdminPort><!-- 对应BPS管理端口端口 -->

                <!--主数据地址-->
                <app.uums.address>http://************:8088/uums</app.uums.address>

                <spring.jpa.database-platform>org.hibernate.dialect.Oracle10gDialect</spring.jpa.database-platform>
                <spring.datasource.druid.filter.wall.enabled>true</spring.datasource.druid.filter.wall.enabled>
                <app.captcha.enable>true</app.captcha.enable>

                <app.file.upload.location>fastdfs</app.file.upload.location>
                <app.host.port>http://************:8088</app.host.port><!-- ************ / ************ -->

                <!-- 统一待办 -->
<!--                <cxf.openTodoUrl>http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoOpenListInfoSrv?wsdl</cxf.openTodoUrl>-->
<!--                <cxf.closeTodoUrl>http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoCloseListInfoSrv?wsdl</cxf.closeTodoUrl>-->
<!--                <cxf.cancelTodoUrl>http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoCancelListInfoSrv?wsdl</cxf.cancelTodoUrl>-->

                <!-- 新统一待办 -->
                <cxf.openTodoUrl>http://************:8088/ntodo/services/openTodoService?wsdl</cxf.openTodoUrl>
                <cxf.closeTodoUrl>http://************:8088/ntodo/services/closeTodoService?wsdl</cxf.closeTodoUrl>
                <cxf.cancelTodoUrl>http://************:8088/ntodo/services/cancelTodoService?wsdl</cxf.cancelTodoUrl>

                <!-- 共享存储配置-->
                <!-- 共享存储配置-->
                <app.file.upload.path>/videoAttach/videoAttach</app.file.upload.path>
                <app.file.upload.pre.path>/videoAttach</app.file.upload.pre.path>
            </properties>
        </profile>

        <profile>
            <id>obprd</id>
            <properties>
                <profileActive>obprd</profileActive>
                <!--nacos-->
                <spring.cloud.nacos.config.server-addr>10.228.113.196:8088</spring.cloud.nacos.config.server-addr>
                <spring.cloud.nacos.config.username>nacos</spring.cloud.nacos.config.username>
                <spring.cloud.nacos.config.password>Best@2008</spring.cloud.nacos.config.password>
            </properties>
        </profile>

    </profiles>

    <!--Nexus 私服-->
    <repositories>
        <repository>
            <id>releases</id>
            <url>http://10.87.57.26:8082/nexus/repository/releases/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
        <repository>
            <id>maven-thirdparty</id>
            <url>http://10.87.57.26:8082/nexus/repository/maven-thirdparty/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://10.87.57.26:8082/nexus/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://10.87.57.26:8082/nexus/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
