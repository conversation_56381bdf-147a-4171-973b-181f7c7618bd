<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>所属部门职务</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
		$(function(){
		    var gps = getQueryString();
		    $(".showDialogTop").attr("openlayer","html/sysrole/sysRolePositionInfo.html?roleId="+gps.roleId);
			//showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
			var pageparam={
				"listtable":{
					"listname":"#sysRolePositionTable",//table列表的id名称，需加#
					"querycmd":"action/role/position/findAllForAdmin",//table列表的查询命令
					"checkboxall":true,
                    "queryParams":{"roleId":gps.roleId},
                    //"pagination":false,
					"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
					"nowrap": true,//把数据显示在一行里,默认true
					"frozenColumns":[[
						{ field: "ck",checkbox:true}
					]],//固定在左侧的列
					"columns":[[//列
                        { title: "职务名称", field: "position", width: 120,
                            formatter:function(value,row,index){
                                return row.position.positionName;
                            }
                        },
						{
							field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
							formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
							    var gps=getQueryString();
								var g = "<a href='#' class='showDialogTop' listPageName='posSetF' width='700' height='400' showDialogindex='" + index + "' openlayer='html/sysrole/sysRolePositionInfo.html?roleId="+row.roleId+"' title='修改'>【修改】</a>"
								+"<a href='#' delete='action/role/position/deleteById' deleteid='"+row.id+"'>【删除】</a>";
								return g;
							}
						}
					] ],
					"pagerbar": [{
						id:"deleteall",
						iconCls: 'icon-remove',
						text:"批量删除&nbsp;"
					}],
					"deleteall":{//批量删除deleteall.id要与pagerbar.id相同
						"id":"deleteall",
						"url":"action/role/position/deleteAllByIds",
						"contentType":"application/json; charset=utf-8"
					}
				},
				"dialogform":{
					"dialogid":"#buttons",//对话框的id
					"formname":"#sysRolePositionTableAddForm",//新增或修改对话框的formid需加#
					"insertcmd":"action/role/position/create",//新增命令
					"updatacmd":"action/role/position/update"//修改命令
				}
			};
			loadGrid(pageparam);
		});
    </script>
</head>
<body>
<!--searchform-->
<form id="sysRolePositionTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
		<tr>
            <!--<td width="90" align="right">是否业务角色：</td><td width="150">
			<select class="easyui-combobox" name="isApplicationRole" style="width: 100%; height: 32px;">
                <option value="" selected>--请选择--</option>
                <option value="true">是</option>
                <option value="false">否</option>
            </select></td>
            <td width="90" align="right">角色名称：</td><td width="150"><input name="roleName" type="text" value="" /></td>-->
            <td>
                <div class="w100">
                    <!--<a class="btn a_primary fl searchtable"><span>查询</span></a>-->
					<a class="btn a_success showDialogTop fr" listPageName='posSetF' width='700' height='400' openlayer='html/sysrole/sysRolePositionInfo.html?roleId='><span>新增</span></a>
                </div>
                </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="sysRolePositionTable"><table id="sysRolePositionTable"></table></div>
</body>
</html>
