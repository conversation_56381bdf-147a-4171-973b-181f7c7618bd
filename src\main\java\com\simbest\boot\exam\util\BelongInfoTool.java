package com.simbest.boot.exam.util;

import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @createData 2020-02-12  15:41
 * @Description 简单描述：设置公司信息，主要是为修改县公司，为了后续分公司能统计到下面的县公司
 */
public class BelongInfoTool {

    /***
     * 设置基础字段
     * <br/> params [o]
     *
     * <AUTHOR>
     * @since 2023/9/11 15:50
     */
    public static <T extends WfFormModel> void setBaseField(T o) {
        setBaseField(o, SecurityUtils.getCurrentUser());
    }

    /***
     * 设置基础字段
     * <br/> params [o, iuser]
     *
     * <AUTHOR>
     * @since 2023/9/11 15:49
     */
    public static <T extends WfFormModel> void setBaseField(T o, @NonNull IUser iuser) {
        o.setBelongOrgCode(iuser.getBelongOrgCode());
        o.setBelongOrgName(iuser.getBelongOrgName());
        o.setBelongCompanyCode(iuser.getBelongCompanyCode());
        o.setBelongCompanyName(iuser.getBelongCompanyName());
        o.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
        o.setBelongDepartmentName(iuser.getBelongDepartmentName());
        o.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
    }

    /**
     * 设置所属信息
     *
     * @param wfFormModel 对象
     * @return
     */
    public static void setBelongCompanyAndDepartment(WfFormModel wfFormModel) {
        IUser iuser = SecurityUtils.getCurrentUser();
        if (iuser != null) {
            String belongCompanyTypeDictValue = iuser.getBelongCompanyTypeDictValue();
            switch (belongCompanyTypeDictValue) {
                case Constants.PROVINCIAL_CODE:
                case Constants.BRANCH_CODE:
                    wfFormModel.setBelongCompanyName(iuser.getBelongCompanyName());
                    wfFormModel.setBelongCompanyCode(iuser.getBelongCompanyCode());
                    wfFormModel.setBelongDepartmentName(iuser.getBelongDepartmentName());
                    wfFormModel.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                    break;
                case Constants.COUNTY_CODE:
                    wfFormModel.setBelongCompanyName(iuser.getBelongCompanyNameParent());
                    wfFormModel.setBelongCompanyCode(iuser.getBelongCompanyCodeParent());
                    wfFormModel.setBelongDepartmentName(iuser.getBelongCompanyName());
                    wfFormModel.setBelongDepartmentCode(iuser.getBelongCompanyCode());
                    break;
            }
            wfFormModel.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
            wfFormModel.setBelongOrgCode(iuser.getBelongOrgCode());
            wfFormModel.setBelongOrgName(iuser.getBelongOrgName());
        }
    }

    /**
     * 获取公司名称
     *
     * @return
     */
    public static String getBelongCompanyName() {
        IUser iuser = SecurityUtils.getCurrentUser();
        String belongCompanyName = "";
        if (iuser != null) {
            String belongCompanyTypeDictValue = iuser.getBelongCompanyTypeDictValue();
            switch (belongCompanyTypeDictValue) {
                case Constants.PROVINCIAL_CODE:
                case Constants.BRANCH_CODE:
                    belongCompanyName = iuser.getBelongCompanyName();
                    break;
                case Constants.COUNTY_CODE:
                    belongCompanyName = iuser.getBelongCompanyNameParent();
                    break;
            }
        }
        return belongCompanyName;
    }

    /**
     * 获取公司code
     *
     * @return
     */
    public static String getBelongCompanyCode() {
        IUser iuser = SecurityUtils.getCurrentUser();
        String belongCompanyCode = "";
        if (iuser != null) {
            String belongCompanyTypeDictValue = iuser.getBelongCompanyTypeDictValue();
            switch (belongCompanyTypeDictValue) {
                case Constants.PROVINCIAL_CODE:
                case Constants.BRANCH_CODE:
                    belongCompanyCode = iuser.getBelongCompanyCode();
                    break;
                case Constants.COUNTY_CODE:
                    belongCompanyCode = iuser.getBelongCompanyCodeParent();
                    break;
            }
        }
        return belongCompanyCode;
    }

    /**
     * 获取部门名称
     *
     * @return
     */
    public static String getBelongDepartmentName() {
        IUser iuser = SecurityUtils.getCurrentUser();
        String belongDepartmentName = "";
        if (iuser != null) {
            String belongCompanyTypeDictValue = iuser.getBelongCompanyTypeDictValue();
            switch (belongCompanyTypeDictValue) {
                case Constants.PROVINCIAL_CODE:
                case Constants.BRANCH_CODE:
                    belongDepartmentName = iuser.getBelongDepartmentName();
                    break;
                case Constants.COUNTY_CODE:
                    belongDepartmentName = iuser.getBelongCompanyName();
                    break;
            }
        }
        return belongDepartmentName;
    }

    /**
     * 获取部门code
     *
     * @return
     */
    public static String getBelongDepartmentCode() {
        IUser iuser = SecurityUtils.getCurrentUser();
        String belongDepartmentCode = "";
        if (iuser != null) {
            String belongCompanyTypeDictValue = iuser.getBelongCompanyTypeDictValue();
            switch (belongCompanyTypeDictValue) {
                case Constants.PROVINCIAL_CODE:
                case Constants.BRANCH_CODE:
                    belongDepartmentCode = iuser.getBelongDepartmentCode();
                    break;
                case Constants.COUNTY_CODE:
                    belongDepartmentCode = iuser.getBelongCompanyCode();
                    break;
            }
        }
        return belongDepartmentCode;
    }

}