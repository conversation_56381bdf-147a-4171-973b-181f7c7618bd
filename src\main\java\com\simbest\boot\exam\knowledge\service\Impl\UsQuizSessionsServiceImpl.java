/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service.Impl;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.knowledge.model.UsQuizSessions;
import com.simbest.boot.exam.knowledge.model.UsUserAnswers;
import com.simbest.boot.exam.knowledge.repository.UsQuizSessionsRepository;
import com.simbest.boot.exam.knowledge.repository.UsUserAnswersRepository;
import com.simbest.boot.exam.knowledge.service.IUsQuizSessionsService;
import com.simbest.boot.exam.knowledge.service.IUsUserAnswersService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class UsQuizSessionsServiceImpl extends LogicService<UsQuizSessions, String> implements IUsQuizSessionsService {

    private UsQuizSessionsRepository usQuizSessionsRepository;

    private String param1 = "/acton/usQuizSessions";

    @Autowired
    public UsQuizSessionsServiceImpl(UsQuizSessionsRepository usQuizSessionsRepository) {
        super(usQuizSessionsRepository);
        this.usQuizSessionsRepository = usQuizSessionsRepository;

    }

    /**
     * 查询对战未结束的情况
     *
     * @param currentDay
     * @return
     */
    @Override
    public List<UsQuizSessions> cancleQuizSessions(String currentDay) {
        return usQuizSessionsRepository.cancleQuizSessions(currentDay);
    }

    /**
     * 查询当天对战人对战情况
     * 非A即B
     * 无法判断待办答题人是邀请人还是被邀请人，可以使用不是邀请人的话那就是被邀请人的思路
     *
     * @param currentDay
     * @param sendUserName
     * @return
     */
    @Override
    public List<UsQuizSessions> checkUpdateSeeeion(String currentDay, String sendUserName) {
        return usQuizSessionsRepository.checkUpdateSeeeion(currentDay,sendUserName);
    }

    /**
     * 根据时间和邀请ID查询
     *
     * @param currentDay
     * @param invitaitonId
     * @return
     */
    @Override
    public List<UsQuizSessions> findAndUpdateSeeeion(String currentDay, String invitaitonId) {
        return usQuizSessionsRepository.findAndUpdateSeeeion(currentDay,invitaitonId);
    }
}
