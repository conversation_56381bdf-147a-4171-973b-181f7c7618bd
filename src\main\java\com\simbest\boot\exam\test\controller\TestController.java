package com.simbest.boot.exam.test.controller;

import cn.hutool.core.util.NumberUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.base.model.SystemModel;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.exam.examOnline.model.*;
import com.simbest.boot.exam.examOnline.service.*;
import com.simbest.boot.exam.test.model.Exam;
import com.simbest.boot.exam.test.util.MyUtils;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.commons.io.FileUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.persistence.Transient;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * TestController
 *
 * <AUTHOR>
 * @since 2023/9/27 9:55
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    private final IExamQuestionBankService examQuestionBankService;
    private final IExamQuestionService examQuestionService;
    private final IExamQuestionAnswerService examQuestionAnswerService;
    private final IExamAttributeService examAttributeService;
    private final IExamSummaryService examSummaryService;
    private final IExamRangeGroupService examRangeGroupService;
    private final ISysFileService sysFileService;

    public TestController(IExamQuestionBankService examQuestionBankService, IExamQuestionService examQuestionService, IExamQuestionAnswerService examQuestionAnswerService, IExamAttributeService examAttributeService, IExamSummaryService examSummaryService, IExamRangeGroupService examRangeGroupService, ISysFileService sysFileService) {
        this.examQuestionBankService = examQuestionBankService;
        this.examQuestionService = examQuestionService;
        this.examQuestionAnswerService = examQuestionAnswerService;
        this.examAttributeService = examAttributeService;
        this.examSummaryService = examSummaryService;
        this.examRangeGroupService = examRangeGroupService;
        this.sysFileService = sysFileService;
    }

    private static final String SQL_ID = "exam_073ED14BF4D4FF0AE0605C0AA1521A75";
    private static final String USER_MAPPING = "/action/uumsSelect/selectBySqlSelectType";


    /**
     * 快速创建考试
     * 1. 先调用此接口
     * 2. 在导入题目
     * tips: 考试题库编码与导入题目题库编码必须相同
     */
    @PostMapping("createdExam")
    public JsonResponse created(HttpServletRequest request,
                                HttpServletResponse response,
                                @RequestBody Exam o) {
//        String exam_name = "明纪守法_复赛_法律组";
//        String question_bank_code = "2023mjsf_fs_flz";
//        String set_time = "30";
//        String topic_style = "original";
//        String exam_start_time = "2023-09-19 00:00:00";
//        String exam_end_time = "2023-10-19 00:00:00";
//        Integer weight_value = 90;
//        String work_type = "H";

        Map<String, String> map = getExamFile(request, response, o.getExam_name(), o.getQuestion_bank_code(), o.getSet_time(), o.getTopic_style(), o.getWork_type(), o.getExam_start_time(), o.getExam_end_time(), o.getWeight_value(), o.getUsers());

        return JsonResponse.success(map);
    }

    /**
     * 快速复制考试
     */
    @PostMapping("copyExam")
    public JsonResponse copyExam(HttpServletRequest request,
                                 HttpServletResponse response,
                                 @RequestBody Exam o) {
//        Exam exam = Exam.builder()
//                .question_bank_code("2023nbxc_01")
//                .build();
        Map<String, String> map = getCopyExamFile(request, response, o);

        return JsonResponse.success(map);
    }

    public static void main(String[] args) {
//        String exam_name = "明纪守法_复赛_法律组";
//        String question_bank_code = "2023mjsf_fs_flz";
//        String set_time = "30";
//        String topic_style = "original";
//        String exam_start_time = "2023-09-19 00:00:00";
//        String exam_end_time = "2023-10-19 00:00:00";
//        Integer weight_value = 90;
//        String work_type = "H";
//        List<String> users = new ArrayList<>(Collections.singleton("wangao"));
//
//        getExamFile(exam_name, question_bank_code, set_time, topic_style, work_type, exam_start_time, exam_end_time, weight_value, users);
//


    }


    public Map<String, String> getCopyExamFile(HttpServletRequest request, HttpServletResponse response, Exam exam) {

        ExamQuestionBank questionBank = examQuestionBankService.findOne(Specifications.<ExamQuestionBank>and().eq("questionBankCode", exam.getOld_question_bank_code()).build());
        List<ExamQuestion> question = examQuestionService.findAllNoPage(Specifications.<ExamQuestion>and().eq("questionBankCode", exam.getOld_question_bank_code()).build());
        List<ExamQuestionAnswer> questionAnswer = examQuestionAnswerService.findAllNoPage(Specifications.<ExamQuestionAnswer>and().like("questionCode", String.format("%%%s%%", exam.getOld_question_bank_code())).build());
        ExamAttribute attribute = examAttributeService.findOne(Specifications.<ExamAttribute>and().eq("questionBankCode", exam.getOld_question_bank_code()).build());
        ExamSummary summary = examSummaryService.findOne(Specifications.<ExamSummary>and().like("examCode", String.format("%%%s%%", exam.getOld_question_bank_code())).build());
        ExamRangeGroup rangeGroup = examRangeGroupService.findOne(Specifications.<ExamRangeGroup>and().like("groupId", String.format("%%%s%%", exam.getOld_question_bank_code())).build());

        questionBank.setQuestionBankSize(null);
        summary.setWeightValue(summary.getWeightValue() + 1);

        String question_bank1 = getInsertSql(questionBank, "exam.us_exam_question_bank");
        String attribute1 = getInsertSql(attribute, "exam.us_exam_attribute");
        String summary1 = getInsertSql(summary, "exam.us_exam_summary");
        String rangeGroup1 = getInsertSql(rangeGroup, "exam.us_exam_range_group");
        String update = String.format("UPDATE exam.us_exam_summary t SET t.work_flag = 0, t.todo_flag = 0\n" +
                " WHERE t.exam_code LIKE '%%%s%%';", exam.getQuestion_bank_code());

        List<String> questions = Lists.newArrayList();
        List<String> question_answers = Lists.newArrayList();
        List<Map<String, Object>> group = uumsHttpRequest(exam.getOld_question_bank_code());
        question.forEach(v -> questions.add(getInsertSql(v, "exam.us_exam_question")));
        questionAnswer.forEach(v -> question_answers.add(getInsertSql(v, "exam.us_exam_question_answer")));
        String userGroup = "";
        if (!CollectionUtils.isEmpty(group))
            userGroup = get_sys_user_group(group.get(0).get("GROUP_ID").toString(), group.stream().map(v -> v.get("USERNAME").toString()).collect(Collectors.toList()));
        List<String> list0 = Arrays.asList(question_bank1, attribute1, summary1, rangeGroup1, update);

        String code = Objects.isNull(exam.getQuestion_bank_code()) ? "" : exam.getQuestion_bank_code();
        final String filename0 = String.format("%s%s_%s.txt", "copy_exam_", code, LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        final String filename1 = String.format("%s%s_%s.txt", "copy_exam_uums_", code, LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        List<String> list = new ArrayList<>();
        list.add("prompt " + filename0 + "\n");
        list.add("set feedback off\nset define off\n");
        list.addAll(list0);
        list.addAll(questions);
        list.addAll(question_answers);
        if (Objects.nonNull(exam.getQuestion_bank_code())) {
            userGroup = userGroup.replaceAll(exam.getOld_question_bank_code(), exam.getQuestion_bank_code());
            list = list.stream().map(v -> {
                String v1 = v.replaceAll(exam.getOld_question_bank_code(), exam.getQuestion_bank_code());
                return v1.replaceAll(attribute.getExamName(), exam.getExam_name());
            }).collect(Collectors.toList());
        }

        String exam_filePath = writeSqlFile(request, response, list, filename0);
        String uums_filePath = writeSqlFile(request, response, Collections.singletonList(userGroup), filename1);
        Map<String, String> map = Maps.newHashMap();
        map.put("exam_filePath", exam_filePath);
        map.put("uums_filePath", uums_filePath);
        return map;

    }

    public Map<String, String> getExamFile(HttpServletRequest request, HttpServletResponse response, String exam_name, String question_bank_code, String set_time, String topic_style, String work_type, String exam_start_time, String exam_end_time, Integer weight_value, List<String> users) {
        final String exam_code = String.format("%s_ks", question_bank_code);
        final String exam_app_code = String.format("%s_sj", question_bank_code);
        final String group_id = String.format("EXAM_%s_qz", question_bank_code);
        final String group_name = String.format("%s_群组", exam_name);
        final String ks = String.format("%s考试", exam_name);
        final String filename0 = String.format("%s%s_%s.txt", "create_exam_", question_bank_code, LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        final String filename1 = String.format("%s%s_%s.txt", "create_exam_uums_", question_bank_code, LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));

        String usExamQuestionBank = get_us_exam_question_bank(exam_name, question_bank_code);
        String usExamAttribute = get_us_exam_attribute(exam_name, question_bank_code, set_time, exam_app_code, topic_style);
        String usExamSummary = get_us_exam_summary(ks, exam_start_time, exam_end_time, exam_code, weight_value, work_type);
        String usExamRangeGroup = get_us_exam_range_group(ks, exam_app_code, group_id, group_name, exam_code, exam_name);
        List<String> list = new ArrayList<>(Arrays.asList("prompt " + filename0 + "\n", "set feedback off\nset define off\n", usExamQuestionBank, usExamAttribute, usExamSummary, usExamRangeGroup));
        String userGroup = get_sys_user_group(group_id, users);

        String exam_filePath = writeSqlFile(request, response, list, filename0);
        String uums_filePath = writeSqlFile(request, response, Collections.singletonList(userGroup), filename1);
        Map<String, String> map = Maps.newHashMap();
        map.put("exam_filePath", exam_filePath);
        map.put("uums_filePath", uums_filePath);
        return map;
    }

    public String writeSqlFile(HttpServletRequest request, HttpServletResponse response, List<String> list, String filename) {
        try {
            // 获取项目动态绝对路径
            String path = request.getServletContext().getRealPath("down");
            // 对文件名进行URL编码并拼接路径
            filename = String.format("%s\\%s", path, URLEncoder.encode(filename, StandardCharsets.UTF_8.name()));
            File file = new File(filename);

            // 覆盖文件
            FileUtils.touch(file);

            Files.write(file.toPath(), list, Charsets.UTF_8);

            return sysFileService.uploadLocalProcessFile(file, file.getName(), file.getParent(), null, null, null).getAnonymousFilePath();
            // 下载文件
//            FileTool.download(file.getPath(), response);
        } catch (Exception e) {
            Exceptions.printException(e);
            return "";
        }
    }

    // todo id更新问题
    public static String getInsertSql(Object obj, String name) {
        // 驼峰转下划线
        Function<String, String> camelCaseToSnakeCase = (String input) -> {
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < input.length(); i++) {
                char c = input.charAt(i);
                if (Character.isUpperCase(c)) {
                    result.append("_");
                    result.append(Character.toLowerCase(c));
                } else {
                    result.append(c);
                }
            }
            // To remove leading underscore if any
            if (result.charAt(0) == '_') {
                result.deleteCharAt(0);
            }
            return result.toString();
        };
        Function<String, StringBuilder> getSql = (String o) -> new StringBuilder(String.format("insert into %s (", o));
        Function<Field, String> formatF = (Field field) -> {
            try {
                field.setAccessible(true);
                Object o = field.get(obj);
                if (Objects.isNull(o)) return "null";
                if (Objects.equals(field.getName(), "id")) {
                    String o1 = o.toString();
                    String o2 = o1.substring(o1.length() - 2);
                    if (NumberUtil.isInteger(o2))
                        o = o1.substring(0, o1.length() - 2) + (Integer.parseInt(o2) + 1);
                    else
                        o = o1 + 1;
                }
                if (Objects.equals(field.getName(), "createdTime")
                        || Objects.equals(field.getName(), "modifiedTime")) {
                    o = LocalDateTime.now();
                }

                Class<?> type = field.getType();
                if (Objects.equals(type, String.class)) return String.format("'%s'", o);
                if (Objects.equals(type, Boolean.class)) return ((Boolean) o) ? "1" : "0";
                if (Objects.equals(type, LocalDateTime.class))
                    return String.format("'%s'", ((LocalDateTime) o).format(DateTimeFormatter.ofPattern(ApplicationConstants.FORMAT_DATE_TIME)));
                return o.toString();
            } catch (IllegalAccessException e) {
                Exceptions.printException(e);
                return "";
            }
        };
        Supplier<Field[]> getField = () -> {
            Field[] fields = obj.getClass().getDeclaredFields();
            List<Field> fieldList = new ArrayList<>(Arrays.asList(fields));
            if (Objects.equals(obj.getClass().getSuperclass(), LogicModel.class)) {
                fieldList.addAll(Arrays.asList(obj.getClass().getSuperclass().getDeclaredFields()));
                fieldList.addAll(Arrays.asList(obj.getClass().getSuperclass().getSuperclass().getDeclaredFields()));
            } else if (Objects.equals(obj.getClass().getSuperclass(), SystemModel.class)) {
                fieldList.addAll(Arrays.asList(obj.getClass().getSuperclass().getDeclaredFields()));
            }

            return fieldList.stream().filter(v -> Objects.isNull(v.getAnnotation(Transient.class))).toArray(Field[]::new);
        };

        StringBuilder sql = getSql.apply(name);
        Field[] fields = getField.get();
        Arrays.stream(fields).forEach(v -> sql.append(camelCaseToSnakeCase.apply(v.getName())).append(","));
        sql.deleteCharAt(sql.length() - 1);
        sql.append(") \n values (");

        Arrays.stream(fields).forEach(v -> sql.append(formatF.apply(v)).append(","));
        sql.deleteCharAt(sql.length() - 1);
        sql.append(");\n");
        return sql.toString();
    }

    public static String get_us_exam_question_bank(String EXAM_NAME, String QUESTION_BANK_CODE) {
        String sql = "insert into exam.us_exam_question_bank (ID, CREATED_TIME, MODIFIED_TIME, CREATOR, ENABLED, MODIFIER, REMOVED_TIME, QUESTION_BANK_CODE, QUESTION_BANK_NAME, QUESTION_BANK_SIZE) \n" +
                "values ('EQB'||sys_guid(), sysdate,sysdate, 'hadmin', 1, 'hadmin', null, '%s', '%s', null);\n";
        return String.format(sql, QUESTION_BANK_CODE, LocalDate.now().getYear() + EXAM_NAME);
    }

    public static String get_us_exam_attribute(String EXAM_NAME, String QUESTION_BANK_CODE, String SET_TIME, String EXAM_APP_CODE, String TOPIC_STYLE) {
        String sql = "insert into exam.us_exam_attribute (ID, CREATED_TIME, MODIFIED_TIME, CREATOR, ENABLED, MODIFIER, REMOVED_TIME, EXAM_NAME, FILLING, JUDGE, MORE, QUESTION_BANK_CODE, SET_TIME, SHORT_ANSWER, SINGLE, TOPIC_SUM, EXAM_APP_CODE, EXAM_REMARK, TOPIC_NUMBER, TOPIC_STYLE)\n" +
                "values ('EA'||sys_guid(), sysdate,sysdate, 'hadmin', 1, 'hadmin', null, '%s', null, null, null, '%s', '%s', null, null, null, '%s', '%s', null, '%s');\n";
        return String.format(sql, EXAM_NAME, QUESTION_BANK_CODE, SET_TIME, EXAM_APP_CODE, EXAM_NAME, TOPIC_STYLE);
    }

    public static String get_us_exam_summary(String EXAM_NAME, String EXAM_START_TIME, String EXAM_END_TIME, String EXAM_CODE, Integer WEIGHT_VALUE, String WORK_TYPE) {
        String sql = "insert into exam.us_exam_summary (ID, CREATED_TIME, MODIFIED_TIME, CREATOR, ENABLED, MODIFIER, REMOVED_TIME, EXAM_END_TIME, EXAM_NAME, EXAM_START_TIME, JOIN_EXAM_NUM, APP_IMAGE_URL, EXAM_CODE, MESSAGE_FLAG, PC_IMAGE_URL, TODO_FLAG, APP_EXAM_URL, PC_EXAM_URL, WORK_FLAG, APP_ENABLED, PC_ENABLED, WEIGHT_VALUE, WORK_TYPE, NUMBER_OF_MESSAGE, SMS_EXAM_URL)\n" +
                "values ('ES'||sys_guid(), sysdate,sysdate, 'hadmin', 1, 'hadmin', null, '%s', '%s', '%s', null, null, '%s', 0, null, 0, null, null, 0, 1, 1, %d, '%s', null, null);\n";
        return String.format(sql, EXAM_END_TIME, EXAM_NAME, EXAM_START_TIME, EXAM_CODE, WEIGHT_VALUE, WORK_TYPE);
    }

    public static String get_us_exam_range_group(String EXAM_NAME, String EXAM_APP_CODE, String GROUP_ID, String GROUP_NAME, String EXAM_CODE, String PAPER_NAME) {
        String sql = "insert into exam.us_exam_range_group (ID, CREATED_TIME, MODIFIED_TIME, EXAM_APP_CODE, EXAM_NAME, GROUP_ID, GROUP_NAME, EXAM_CODE, PAPER_NAME)\n" +
                "values ('ERG'||sys_guid(), sysdate,sysdate, '%s', '%s', '%s', '%s', '%s', '%s');\n";
        return String.format(sql, EXAM_APP_CODE, EXAM_NAME, GROUP_ID, GROUP_NAME, EXAM_CODE, PAPER_NAME);
    }

    public static String get_sys_user_group(String GROUP_ID, List<String> users) {
        String sql = "insert into uums.sys_user_group (ID, CREATED_TIME, MODIFIED_TIME, CREATOR, ENABLED, MODIFIER, REMOVED_TIME, GROUP_ID, SPARE1, SPARE2, USERNAME)\n" +
                "    values ('UG'||sys_guid(), sysdate,sysdate, 'hadmin', 1, 'hadmin', null, '%s', null, null, '%s');\n";
        StringBuilder builder = new StringBuilder();
        for (String USERNAME : users) {
            String format = String.format(sql, GROUP_ID, USERNAME);
            builder.append(format);
        }
        return builder.toString();
    }

    /**
     * 发送远程http请求 uums
     */
    private List<Map<String, Object>> uumsHttpRequest(String codes) {
        String url = MyUtils.getUUMSUrl(USER_MAPPING) + "&appCode=" + Constants.APP_CODE + "&selectType=replacetype&sqlId=" + SQL_ID;

        Map<String, Object> params = new HashMap<String, Object>() {{
            put("firstParam", codes);
        }};
        JsonResponse response = HttpClient.textBody(url).json(JacksonUtils.obj2json(params)).asBean(JsonResponse.class);
        if (response == null || response.getErrcode() != 0) {
            log.error("远程请求uums地址失败，url: {}, params: {}, response: {}", url, params, response);
        }
        return (List<Map<String, Object>>) response.getData();
    }

}


