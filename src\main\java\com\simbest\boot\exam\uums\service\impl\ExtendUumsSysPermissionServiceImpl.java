package com.simbest.boot.exam.uums.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.AuthoritiesConstants;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.uums.service.ExtendUumsSysPermissionService;
import com.simbest.boot.security.SimplePermission;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.permission.UumsSysPermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc:
 * @date 2021/7/3  9:55
 */
@Service
@Slf4j
public class ExtendUumsSysPermissionServiceImpl implements ExtendUumsSysPermissionService {


    private static final String USER_MAPPING = "/action/permission/permission/";
    private static final String ROLE_PERMISSION_MAPPING = "/action/role/permission/";
    private static final String SSO = "/sso";
    @Autowired
    private AppConfig config;
    @Autowired
    private RsaEncryptor encryptor;

    @Autowired
    private UumsSysPermissionApi uumsSysPermissionApi;


    /**
     * @desc  根据应用查询其下的所有权限不分页
     * <AUTHOR>
     */
    @Override
    public List<SimplePermission> findAllPermissionNoPage() {
        Map<String, Object> simplePermissionMap = new HashMap<>();
        Map<String, Object> PermissionMap = new HashMap<>();
        //查询属于考试管理系统的所有权限不分页
        PermissionMap.put("id",Constants.APP_CODE);
        simplePermissionMap.put("appId", PermissionMap);
        return uumsSysPermissionApi.findAllNoPage(Constants.APP_CODE,simplePermissionMap);
    }

    @Override
    public JsonResponse findAllNoPageForKey(String key) {
        String username = SecurityUtils.getCurrentUserName();
        log.debug("Http remote request user by username: {}", username);
        JsonResponse response =  HttpClient.post(config.getUumsAddress() + USER_MAPPING + "findAllNoPageForKey"+SSO)
                .param(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username))
                .param(AuthoritiesConstants.SSO_API_APP_CODE,Constants.APP_CODE )
                .param( "key",key)
                .param( "keyType","role")
                .param( "appId",Constants.APP_CODE)
                .asBean(JsonResponse.class);

        if(response==null){
            log.error("--response对象为空!--");
            return null;
        }
        if(!(response.getData() instanceof ArrayList)){
            log.error("--uums接口返回的类型不为ArrayList--");
            return null;
        }
        /*String json = JacksonUtils.obj2json(response.getData());
        List<SimplePermission> permissionList=JacksonUtils.json2Type(json, new TypeReference<List<SimplePermission>>(){});
        return permissionList;*/
        List data = (List) response.getData();

        return JsonResponse.success(data);
    }

    /**
     * @desc  修改角色权限信息，先删除角色所有权限，再重新添加
     * <AUTHOR>
     */
    @Override
    public JsonResponse updateListByRoleId(String permissionIds, String roleId) {
        String username = SecurityUtils.getCurrentUserName();
        log.debug("Http remote request user by username: {}", username);
        JsonResponse response =  HttpClient.post(config.getUumsAddress() + ROLE_PERMISSION_MAPPING + "updateListByRoleId"+SSO)
                .param(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username))
                .param(AuthoritiesConstants.SSO_API_APP_CODE,Constants.APP_CODE )
                .param( "permissionIds",permissionIds)
                .param( "roleId",roleId)
                .param( "appId",Constants.APP_CODE)
                .asBean(JsonResponse.class);
        return response;
    }


}
