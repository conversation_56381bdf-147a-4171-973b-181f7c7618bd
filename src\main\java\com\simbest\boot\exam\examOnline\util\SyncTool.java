package com.simbest.boot.exam.examOnline.util;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.exam.examOnline.model.ExamRangeGroup;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.mainbills.model.UsPmInstence;
import com.simbest.boot.exam.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.exam.test.util.MyUtils;
import com.simbest.boot.exam.todo.service.IUsTodoModelService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.NumRuleUtil;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.security.LoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

import static com.simbest.boot.config.MultiThreadConfiguration.MULTI_THREAD_BEAN;

/**
 * 用途：
 * 作者：gy
 * 时间: 2021-02-19 9:15
 */
@Slf4j
@Component
public class SyncTool {
    private final IUsPmInstenceService iUsPmInstenceService;

    @Lazy
    private final IExamWorkService examWorkService;
    private final IUsTodoModelService todoModelService;
    private final LoginUtils loginUtils;

    private static final String SQL_ID = "exam_073ED14BF4D4FF0AE0605C0AA1521A76";

    public SyncTool(IUsPmInstenceService iUsPmInstenceService, IExamWorkService examWorkService, IUsTodoModelService todoModelService, LoginUtils loginUtils) {
        this.iUsPmInstenceService = iUsPmInstenceService;
        this.examWorkService = examWorkService;
        this.todoModelService = todoModelService;
        this.loginUtils = loginUtils;
    }

    //    @Async(MULTI_THREAD_BEAN)
    public Future<ExamWork> createExamWork(String examCode, String username, String trueName, String companyName, String title, String workType, String examAppCode) {
        /**准备当前人待办参数**/
        //获取主单据ID
        String pmInsId = NumRuleUtil.getPmInsId(examCode);
        ExamWork work = new ExamWork();
        work.setCreateYear(DateUtil.getCurrYear());
        work.setTransactor(trueName);
        work.setTransactorCode(username);
        work.setCompanyName(companyName);
        work.setTitle(title);
        work.setPositionLevel(0);
        work.setParentCompanyCode("null");
        work.setIsPostMsg(Constants.SIGN_Z);
        work.setIsTodoFlag(Constants.SIGN_Z);
        work.setWorkType(workType);//由页面控制待办打开页面
        work.setPmInsId(pmInsId);
        work.setExamAppCode(examAppCode);//待办信息中存储试卷编码
        work.setExamCode(examCode);//待办信息中存储试卷编码

        /**保存主单据**/
        UsPmInstence usPmInstence = new UsPmInstence();
        usPmInstence.setSign(Constants.SIGN_Z);
        usPmInstence.setPmInsTitle(title);
        usPmInstence.setPmInsId(pmInsId);
        usPmInstence.setPmInsType("考试管理系统");
        iUsPmInstenceService.insert(usPmInstence);
        /**保存当前人待办**/
        ExamWork examWork = examWorkService.insert(work);
        return new AsyncResult<>(examWork);
    }

    /**
     * 通过考试群组 异步创建考试待办
     *
     * @param examCode                 考试编码
     * @param title                    试卷标题
     * @param workType                 工作类型
     * @param examRangeGroupByExamCode 考试群组
     * @return 异步创建试卷任务的Future集合
     */
    @Async(MULTI_THREAD_BEAN)
    public List<Future<ExamWork>> asyncCreateExamWorkByExamRange(String examCode, String title, String workType, Iterable<ExamRangeGroup> examRangeGroupByExamCode) {
        loginUtils.adminLogin();
        List<Future<ExamWork>> futures = new ArrayList<>();
        AtomicInteger count = new AtomicInteger(0);
        examRangeGroupByExamCode.forEach(v -> {
            List<Map<String, Object>> list = MyUtils.selectBySqlSelectType(SQL_ID, Collections.singletonList(v.getGroupId()));
            list.stream().parallel().forEach(v1 -> {
                // 准备待办参数
                String username = v1.get("USERNAME") != null ? v1.get("USERNAME").toString() : "";
                String truename = v1.get("TRUENAME") != null ? v1.get("TRUENAME").toString() : "";
                String companyname = v1.get("COMPANYNAME") != null ? v1.get("COMPANYNAME").toString() : "";
                Future<ExamWork> workFuture = asyncCreateExamWork(examCode, username, truename, companyname, title, workType, v.getExamAppCode());
                futures.add(workFuture);
            });
            count.addAndGet(list.size());
        });
        log.debug(count.get() + "个用户待办创建成功");
        return futures;
    }

    /**
     * 异步创建考试待办
     *
     * @param examCode 考试编码
     * @param username 用户名
     * @param trueName 真实姓名
     * @param companyName 公司名称
     * @param title 标题
     * @param workType 工作类型
     * @param examAppCode 考试应用码
     * @return 返回异步结果，其中包含考试工作对象
     */
    @Async(MULTI_THREAD_BEAN)
    public Future<ExamWork> asyncCreateExamWork(String examCode, String username, String trueName, String companyName, String title, String workType, String examAppCode) {
        loginUtils.adminLogin();
        return createExamWork(examCode, username, trueName, companyName, title, workType, examAppCode);
    }

    /**
     * 异步发送统一待办
     *
     * @param list 待办列表
     */
    @Async(MULTI_THREAD_BEAN)
    public void asyncSendUnifiedToDo(List<ExamWork> list) {
        if (CollectionUtils.isEmpty(list)) return;

        AtomicInteger count = new AtomicInteger(0);
        log.warn("asyncSendUnifiedToDo start >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        try {
            loginUtils.adminLogin();
            list.stream().parallel().forEach(work -> {
                //推送统一待办
                if (todoModelService.openTodo(work)) {
//                    如果推送待办成功，更新系统待办中标识
                    work.setIsTodoFlag(Constants.SIGN_O);
                    examWorkService.update(work);
                    count.addAndGet(1);
                }
            });
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        log.warn("asyncSendUnifiedToDo end 共执行【{}】次>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", count.get());
    }

    /**
     * 异步核销考试统一待办
     *
     * @param list 考试任务列表
     */
    @Async(MULTI_THREAD_BEAN)
    public void asyncDealWith(List<ExamWork> list) {
        if (CollectionUtils.isEmpty(list)) return;

        AtomicInteger count = new AtomicInteger(0);
        log.warn("asyncDealWith start >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        try {
            loginUtils.adminLogin();
            list.stream().parallel().forEach(work -> {
                //核销统一待办
                UsPmInstence byPmInsId = iUsPmInstenceService.findByPmInsId(work.getPmInsId());
                byPmInsId.setSign(Constants.SIGN_O);
                iUsPmInstenceService.update(byPmInsId);

                work.setModifiedTime(LocalDateTime.now());
                examWorkService.update(work);

                todoModelService.closeTodo(work);

                count.addAndGet(1);
            });
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        log.warn("asyncDealWith end 共执行【{}】次>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", count.get());
    }

}
