package com.simbest.boot.exam.uums.service;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.security.SimpleRole;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.security.UserOrgTree;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @desc:
 * @date 2021/7/3  11:54
 */
public interface ExtendUumsSysRoleService {
    /**
      * @desc  根据登录用户查询其创建的角色信息列表
      * <AUTHOR>
      */
    JsonResponse  findRoleNameIsARoleDim(int page,int size , String roleName,String roleCode);

    /**
      * @desc 新增角色时判断此角色编码是否已存在
      * <AUTHOR>
      */
    Boolean   isHaveCode(String roleCode);

    /**
      * @desc 新增角色
      * <AUTHOR>
      */
    SimpleRole  createRole(SimpleRole simpleRole);

    /**
      * @desc 根据角色id查询角色信息
      * <AUTHOR>
      */
    SimpleRole  findRoleById(String id);

    /**
      * @desc 角色信息修改
      * <AUTHOR>
      */
    SimpleRole  updateRoleInfo(SimpleRole simpleRole);

    /**
      * @desc 根据角色id删除角色信息
      * <AUTHOR>
      */
    boolean  delRoleInfo(String id);

    /**
     * @desc 根据角色id删除角色信息
     * <AUTHOR>
     */
    boolean  delRoleInfos(String[] ids);

    /**
      * @desc 模糊查询出人所在的组织树
      * <AUTHOR>
      */
    Set<UserOrgTree> findDimUserTree(SimpleUser truename);

}
