package com.simbest.boot.exam.examOnline.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.constants.ApplicationConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 用途：考试信息模块--考试汇总信息
 * 作者：gy
 * 时间: 2021-02-01 9:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_summary")
@ApiModel(value = "考试汇总信息")
public class ExamSummary extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ES") //主键前缀，此为可选项注解
    private String id;

    @ApiModelProperty(value = "考试名称")
    @Column(length = 150, nullable = false)
    private String examName;

    @ApiModelProperty(value = "考试编码")
    @Column(length = 100, nullable = false, unique = true)
    private String examCode;

    @ApiModelProperty(value = "移动端展示开启按钮")
    @Column(length = 10, insertable = false, columnDefinition = "int default 0")
    private Boolean appEnabled;

    @ApiModelProperty(value = "移动端考试跳转页面路径")
    @Column(length = 200)
    private String appExamUrl;

    @ApiModelProperty(value = "pc端展示开启按钮")
    @Column(length = 10, insertable = false, columnDefinition = "int default 0")
    private Boolean pcEnabled;

    @ApiModelProperty(value = "pc端考试跳转页面路径")
    @Column(length = 200)
    private String pcExamUrl;

    @ApiModelProperty(value = "移动端端悬浮窗口图片路径")
    @Column(length = 200)
    private String appImageUrl;

    @ApiModelProperty(value = "pc端悬浮窗口图片路径")
    @Column(length = 200)
    private String pcImageUrl;

    @ApiModelProperty(value = "是否不推送待办，全员可答题")
    private Boolean noTodo;

    @ApiModelProperty(value = "是否开启小程序")
    private Boolean smsEnabled;

    @ApiModelProperty(value = "短信小程序跳转地址")
    @Column(length = 200 )
    private String smsExamUrl;

    @ApiModelProperty(value = "应考人数")
    @Column
    private Integer joinExamNum;

    @ApiModelProperty(value = "考试开始时间")
    @JsonFormat(pattern = ApplicationConstants.FORMAT_DATE_TIME, timezone = ApplicationConstants.FORMAT_TIME_ZONE)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime examStartTime;

    @ApiModelProperty(value = "考试结束时间")
    @JsonFormat(pattern = ApplicationConstants.FORMAT_DATE_TIME, timezone = ApplicationConstants.FORMAT_TIME_ZONE)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime examEndTime;

    @ApiModelProperty(value = "统一待办推送状态")
    @Column(length = 10, insertable = false, columnDefinition = "int default 0")
    private Boolean todoFlag;// 默认未推送

    @ApiModelProperty(value = "短信推送状态")
    @Column(length = 10, insertable = false, columnDefinition = "int default 0")
    private Boolean messageFlag;// 默认未推送

    @ApiModelProperty(value = "短信推送次数")
    @Column(length = 50, columnDefinition = "int default 0")
    private Integer numberOfMessage;

    @ApiModelProperty(value = "待办推送状态")
    @Column(length = 10, insertable = false, columnDefinition = "int default 0")
    private Boolean workFlag;// 默认未推送

    @ApiModelProperty(value = "待办推送类型")
    @Column(length = 20)
    private String workType;

    @ApiModelProperty(value = "权重值")
    @Column(unique = true)
    private Integer weightValue;// 根据权重系数，判断多个考试中，应优先采用哪个考试数据

    @ApiModelProperty(value = "是否保存考试明细信息")
    private Boolean saveDetailInfo;

    /**
     * 动态展示字段
     */
    @ApiModelProperty(value = "实考人数")
    @Transient
    private Integer actualExamNum;

    @ApiModelProperty(value = "统一待办人数")
    @Transient
    private Integer todoExamNum;

    @ApiModelProperty(value = "定时器状态")
    @Transient
    private Integer taskStatus;

}
