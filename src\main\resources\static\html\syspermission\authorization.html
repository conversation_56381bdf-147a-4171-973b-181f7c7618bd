﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>权限树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
        th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}"
        rel="stylesheet" />
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui .min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
        type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
        type="text/javascript"></script>
</head>

<body class="body_page_uums">
    <!-- <h5 class="p5">
        <font>选择应用：</font>
        <input id="appId" name="appId" class="easyui-combobox" style="width: 500px; height: 32px;" data-options="
            valueField: 'id',
            ischooseall:true,
            textField: 'appName',
            queryParams:{},
            editable:false,
            contentType:'application/json; charset=utf-8',
            url: '/uums/action/app/app/findAllNoPage',onSelect:loadTree" />
    </h5> -->
    <ul id="permissionIds"></ul>
    <script type="text/javascript">
        var obj = $('#buttons2').dialog('options');
        var urlstr = obj["queryParams"];
        $(function () {
            var gps = getQueryString();
            loadTree();
        });
        function loadTree() {
            var opts = {
                url: "permissionManage/findAllNoPageForKey?key=" + localStorage.getItem('id'),
                treeId: 'id',
                treePid: 'parentId',
                fileds: "id,description|text,parentId,checked",
                contentType: "application/json; charset=utf-8",
                checkbox: true,//是否在每一个借点之前都显示复选框
                lines: true,//是否显示树控件上的虚线
                animate: true,//节点在展开或折叠的时候是否显示动画效果
                firstnode: true//默认展开一级
            };
            //var treeData=toTreeData(data, "id", "parentId", "id,description|text,parentId,checked");
            $("#permissionIds").tree(opts);
        };
        window.gettreechecked = function () {
            return $("#permissionIds").tree('getChecked');
        };
    </script>
</body>

</html>