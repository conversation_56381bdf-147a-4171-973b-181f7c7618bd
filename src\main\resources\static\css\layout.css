﻿/*全局公共样式*/
*{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;}
html{overflow-y:auto;overflow-x:hidden;}
html,body,h1,h2,h3,h4,h5,h6,div,dl,dt,dd,ul,ol,li,p,blockquote,pre,hr,table,th,td,form,fieldset,input,button,textarea,menu,i{margin:0;padding:0;} 
body{font-family:'FontAwesome','Microsoft YaHei';font-size:14px;margin:0;padding:45px 0 0;background:#fbfbfb;}
input,textarea{font-family:inherit;font-size:inherit;line-height:inherit;outline:none;display:block;height:32px;line-height:32px;padding:6px;font-size:14px;vertical-align:middle;background-image:none;border-radius:5px;border:0 none;border:solid 1px #e0e0e0;}
textarea{border:1px solid #e7e7e8;line-height:22px;}
a,a:link,a:visited,a:hover,a:focus,a:active{text-decoration:none;cursor:pointer;} 
img{vertical-align:middle;border:0px none;}
h1,h2,h3,h4,h5,h6{font-weight:500;color:inherit;}
h1,h2,h3,h4,h5{padding:10px 0;}
h1{font-size:36px;}h2{font-size:30px;}h3{font-size:25px;}h4{font-size:18px;}h5{font-size:14px;}h6{font-size:14px;}
p{margin:0px;line-height:25px;}li{list-style:none;}
ul {margin:0;padding:0;list-style:none;}
li ul{margin-bottom:0;padding-left:10px;} 
li a img{border:0;max-width:100%;}
div{margin:0 auto;}
/**百度地图**/
.BMap_cpyCtrl,.anchorBL a{display:none;} 
.BMapLabel{border-radius:5px;}
/*行 盒子的一级内容必须是行*/
.row{}
.row:before,.row:after{display:table;content:" ";}
.row:after{clear:both;}
/*列 行的一级内容必须是列 实际内容必须包含在列中*/
div.c1,div.c2,div.c3,div.c4,div.c5,div.c6,div.c7,div.c8,div.c9,div.c10,div.c11,div.c12{float:left;}
.c12{width:100%;}.c11{width:91.6667%;}.c10{width:83.3333%;}.c9{width:75%;}
.c8{width:66.6667%;}.c7{width:58.3333%;}.c6{width:50%;}.c5{width:41.6667%;}
.c4{width:33.3333%;}.c3{width:25%;}.c2{width:16.6667%;}.c1{width:8.33333%;} 
 
@media only screen and (max-width:479px){ 
.c1,.c2,.c3,.c4,.c5,.c6,.c7,.c8,.c9,.c10,.c11,.c12{width:100%;}
} 
/*基础行高100 h数字 表示倍数 如h6表示600高*/
.hs{height:80px;}.hsm{height:60px;}.hsmm{height:35px;}.h1{height:100px;}.h2{height:200px;}.h3{height:300px;}.h150{height:150px;}.mh80{min-height:80px;}.mh50{min-height:50px;}
.h4{min-height:400px;_height:400px;}.h5{min-height:500px;_height:500px;}.h6{min-height:600px;_height:600px;}.h_5{height:5px;}.h_8{height:8px;}
.h40{height:40px;}.minh40{min-height:40px;}
/*基础宽度100 同上*/
.wl{width:100px;}.w12{width:120px;}.w16{width:160px;}.ws{width:70px;}.wsm{width:60px;}.wsmm{width:35px;}.w1{width:100px;}.w2{width:200px;}.w3{width:300px;}
.w4{width:400px;}.w5{width:500px;}.w6{width:600px;}.w7{width:700px;}.w8{width:800px;}.w9{width:900px;}.w100{width:100%;}.w90{width:90%;}.w40{width:40px;}
.w50{width:50%;}
/*常用padding margin*/
.p0{padding:0;}.p5{padding:5px;}.p10{padding:10px;}.p15{padding:15px;}.p20{padding:20px;}.p30{padding:30px;}.plr15{padding:0px 15px;}.plr10{padding:0px 10px;}.pr10{padding-right:10px;}.ptb5{padding:5px 0px;}.pt50{padding-top:50px;}
.pt10{padding-top:10px;}.pt15{padding-top:15px;}.ptb10{padding:10px 0px;}.p12{padding:12px;}.ptb22{padding:22px 0;}.pt5{padding-top:5px;}.pl10{padding-left:10px;}.pr45{padding-right:45px;}.ptb15rl10{padding:15px 10px;}
.pt0b10{padding:0 0 10px;}.pl80{padding-left:80px;}.pb10{padding-bottom:10px;}.ptb3lr5{padding:3px 5px;}.pt10b5lf5{padding:10px 5px 5px;}.pt32{padding-top:32px;}.pt42{padding-top:42px;}.ptrl20b0{padding:20px 20px 0px;}
.ptb5l10{padding:5px 0 5px 10px;}.pb20{padding-bottom:20px;}.pb40{padding-bottom:40px;}.pt10b8{padding:10px 0 8px;}.pl45{padding-left:45px;}
.m0{margin:0;}.m5{margin:5px;}.m10{margin:10px;}.m15{margin:15px;}.m20{margin:20px;}.m30{margin:30px;}.m50{margin:50px;}.m60{margin:30px 60px;}.mtb20{margin-top:20px;margin-bottom:20px;}
.lh32{line-height:32px;}.lh50{line-height:50px;}.lh20{line-height:20px;}
.mr10{margin-right:10px;}.mr5{margin-right:5px;}.mr20{margin-right:20px;}.mr45{margin-right:45px;}.ml5{margin-left:5px;}.mr5{margin-right:5px;}.mt10{margin-top:10px;}.mt8{margin-top:8px;}.ml15{margin-left:15px;}
.fright,.fr,a.fr{float:right;}.fleft,.fl{float:left;}
.hide,.none{display:none;}
.bgf{background:#fff;}.col_w{color:#fff;}
.disL{display:inline;}
.lh26{line-height:26px;}
/*清除浮动*/
.txtl{text-align:left}.txtc{text-align:center}.txtr{text-align:right}
.lineT{text-decoration:line-through;}
.f10{font-size:10px;color:#999;}.f20{font-size:20px;}.f18{font-size:18px;}.f12{font-size:12px;}.f14{font-size:14px;}.f15{font-size:15px;}.f16{font-size:16px;}
.f18{font-size:18px;}.f20{font-size:20px;}.f22{font-size:22px;}.f24{font-size:24px;}
.line{clear:both;border-top:solid 1px #eee;line-height:40px;min-height:40px;padding:0 10px;background:#FFF;} 
.fontw{font-weight:bold;}
.bgw{background:#fff;}
.line .info{line-height:22px;}
.bgh{background:#fbfbfb;}
.clear10{clear:both;height:10px;width:100%;} 
.clear20{clear:both;height:20px;width:100%;} 
.clear50{clear:both;height:50px;width:100%;} 
.clear60{clear:both;height:60px;width:100%;} 
.clear{clear:both;} 
.cgreen{color:#68bf7a;}
.overflowh,.clearf{overflow:hidden;clear:both;}
.cb{clear:both;zoom:1;overflow:hidden;height:0} .cb:after,.cbli li:after{display:block;clear:both;visibility:hidden;height:0;overflow:hidden;content:".";}
.cb,.cbli li{zoom:1;}
.col_r{color:#00b4f1;}.col_b{color:#00b4f1;}.col_h{color:#aaa;}.col_h3{color:#333;}.col_sr{color:#f00;}.col_o{color:#00b4f1;}.col_g{color:#05c30b;}.col_rs{color:#d81e06;}
.txtdem{text-decoration:line-through;}
/*loading*/
.zjstips{position:fixed;top:28%;padding:10px;text-align:center;z-index:1000;background:#FFF;border:solid 1px #eee;}
/*文本框前提示消息*/
/*.info{margin:6px 0 0 5px;display:inline-block;float:none;font-weight:normal;position:absolute;_width:200px;background:#FFF;z-index:10000;}*/ 
.info{margin:6px 0 0 5px;display:inline-block;float:none;font-weight:normal;position:absolute;_width:200px;background:#FFF;z-index:8;}
/*常用页码样式*/
table.ctable{width:98%;margin:0 auto 26px; border-collapse:collapse;border-spacing:0; empty-cells:show;font-size:12px;}
table.ctable thead tr{font-weight:bold;}
table.ctable thead tr td{border-bottom:2px solid #ddd;border-top:0px none;padding:0px 4px;}
table.ctable thead tr.nomsg td{border:none;text-align:center;font-size:20px;padding:10px;}
table.ctable td input{width:100%;}
table.ctable tbody tr:hover,table.ctable tbody tr:nth-child(even):hover{background:#fafafa}
table.ctable tbody tr:nth-child(even) {background:#fafafa}
table.ctable tr.ordertitle td{background:#fafafa}
table.ctable td{padding:4px;border-top:1px solid #e0e0e0;line-height:20px;}
table.ctable tfoot td{border-top:3px solid #ddd;padding:2px 4px;}
table.ctable tfoot td table tfoot td{border-top:0px none;padding:0px 4px;}
table.ctable .btn,table.ctable .btn_a{padding:4px 4px;}
table.ctable .btn_a{color:#aaaaaa;font-size:20px;margin-right:5px;float:left;}
table.ctable tfoot .pageNumbers .btn {margin:0 3px;padding:4px 0px 3px;min-width:22px;line-height:16px;} 
table.ctable .pagerpostion{border:1px solid #e0e0e0;right:1%;position:absolute;padding:3px;border-radius:50%;}
table.ctable .pagerpostion a{color:#ffffff;line-height:30px;min-width:30px;text-align:center;float:left;font-weight:bold;font-size:14px;-moz-border-radius:50%;-webkit-border-radius:50%;border-radius:50%;} 
table.ctable tfoot .tfootbar{position:fixed;bottom:0px;background:#FFF;right:1%;width:99%;}
table.ctable tfoot .tfootbar table{float:right;}
/*特殊表格 */
table.ctable.nobgcolor tbody tr:hover{background:#fff}
table.ctable.nobgcolor tbody tr:nth-child(even) {background:#fff}
table.ctable.nobgcolor tbody td {border:1px solid #ddd;}
table.ctable.nobgcolor tbody tr.rowindex0 td {border-top:none;}
table.ctable.nobgcolor td.space{padding:0;border:none;}
table.ctable.nobgcolor td.spacerow{padding:10px;border:none;}

.money:after{content:"元/天";}
.delete:after{content:"元/天";}
.money,.moneyo{color:#00b4f1;}
.delete{color:#ccc;text-decoration:line-through;}
.icon-remove{color:red;}
.moneys:after{content:"元";}
.moneys,.moneyred{color:#00b4f1;}
.moneyr{color:#00b4f1;font-weight:bold;}
.moneyr:before{content:"¥";}
.moneyr:after{content:"/天";font-weight:normal;}
.moneyt:after{content:"/天";font-size:12px;}
.moneyh{color:#aaa;}
.moneyh:before,.moneyhei:before,.moneyo:before,.moneyred:before{content:"¥";}
.delline{color:#bbb;text-decoration:line-through;vertical-align: middle}
.beforedot{padding:3px 0px 3px 12px;line-height:20px;position:relative;}
.beforedot:before{content:"•";color:#00b4f1;position:absolute;left:0;}/**●**/
/*按钮样式 主色调按钮btn 副色调按钮btn-org 灰色调按钮btn-hui￥*/
.btn {display:inline-block;padding:4px 8px;margin-bottom:0;font-size:14px;font-weight:normal;text-align:center;line-height:normal;
      white-space:nowrap;vertical-align:middle;cursor:pointer;background-image:none;border:1px solid transparent;          
      border-radius:2px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;-o-user-select:none;user-select:none;background-repeat:repeat-x;}
.btn:hover,.btn:focus {background-position:0 -15px;text-decoration:none;}
.btn:active,.btn.active {background-image:none;outline:0;}
/*中号*/
.btn-l {padding:8px 12px;font-size:16px;border-radius:5px;}
/*大号*/
.btn-lg {padding:10px 12px;font-size:18px;border-radius:6px;}
/*巨号*/
.btn-lgg{padding:10px 32px;}
/*小号*/
.btn-sm,.btn-smm,.btn-smk {padding:5px 3px;font-size:12px;border-radius:3px;} 
.btn-smm {padding:2px 6px;} 
.btn.btn-smk {padding:4px 9px;background:none;color:#333} 
.btn i{margin-right:5px;}

.cselectorImageSelect{}
.cselectorLink{}
.cselectorIcon{}
/*图片上传器*/
.cselectorImageUL{position:relative;}
.cselectorImageUL .btn{position:absolute;z-index:3;border:none;color:#212121;}
.cselectorImageUL .btn i{margin:0;color:#fff;}
.cselectorImageUL input{position:absolute;z-index:5;filter:alpha(opacity=0);opacity:0;width:32px;height:26px;line-height:26px;border:0 none;padding:0;} 
/*图片上传样式*/
.uploadImage{position:absolute;right:32px;top:0px;z-index:10;width:160px;}
.uploadImage img{max-width:80px;max-height:80px;border-radius:50%;}
.uploadImage_del{display:block;position:absolute;right:0;top:-15px;display:none;}
.imageList{}
.imageList li{float:left;width:100px;margin:5px;}
.imageList li img{width:100%;}
.imageList li .delimg{float:right;}
.hideuploadImage .uploadImage,.uploadImage .meitucrop{display:none;}
.standValue{float:left;width:100px;margin:5px;position:relative;}
.standValue .delimg{position:absolute;right:5px;top:0;display:none;}
.standValue:hover .delimg{display:block;}
.ceditor{border:0px;} 
/*下拉列表框*/ 
.cselectorBox{}
.cselectorBox a:hover{text-decoration:none;}
.cselector{}
.cselectorAuto{}
.cselectorInput{display:block;height:30px;line-height:30px;width:100%;padding:0 20px 0 10px;border-radius:3px;
                background:#FFFFFF url(../img/ddl.png) no-repeat right center ;text-align:left;overflow:hidden;border:solid 1px }
.cselectorInput:hover{background:#FFFFFF url(../img/ddl1.png) no-repeat right center ;text-decoration:none;}
.cselectorInput input{width:100%;border:0px;padding:0px;height:28px;}
.cselectorAddress{display:block;height:30px;line-height:30px;width:100%;padding:0px;border-radius:3px;text-align:left;overflow:hidden;color:#8a8a8a;}
.mo{color:#212121;}
/*下拉列表框框*/ 
.cselectorUL{position:absolute;background:#FFFFFF;display:none;border:solid 1px ;border-top-width:0px;
             max-height:369px;_height:369px;z-index:399;margin-top:0px;}
.cselectorUL div{height:25px;line-height:25px;text-align:left;display:block;}
/*.cselectorUL a{height:25px;line-height:25px;text-align:left;display:block;padding:0 20px 0 10px;}*/
.cselectorUL a{height:auto;line-height:20px;text-align:left;display:block;padding:5px 10px 0px 10px;font-size:12px;}
.cselectorUL a.hover{background-color:#255599;color:#FFFFFF;text-decoration:none;zoom:1}
.cselectorUL a:hover{background-color:#255599;color:#FFFFFF;text-decoration:none;zoom:1}
/*单选按钮*/
.cselectorRadio{}
.cselectorRadioUL{min-height:30px;line-height:30px;}
.cselectorRadioLi .cselectorRadioUL{height:auto;}
.cselectorRadioUL a{background:url(../img/r1.png) no-repeat left center;padding:0px 0 0px 15px;margin:0 5px 0 0;}
.cselectorRadioUL a h3{display:inline;}
.cselectorRadioImage+.cselectorRadioUL a{width:100px;padding:20px 0 0 0;text-indent:0px;float:left;margin:0 3px 0 0;}
.cselectorRadioImage+.cselectorRadioUL a img{width:100px;height:100px;}
.cselectorRadioUL a:hover{text-decoration:none;}
.cselectorRadioUL .select{background-image:url(../img/r2.png);} 
/*cselectorRadioTabs选项卡式单选*/
.cselectorRadioTabs .cselectorRadioUL{border-bottom:1px solid #ccc;padding:0 0 3px;}
.cselectorRadioTabs .cselectorRadioUL a{background:none;padding:10px;font-size:18px;}
.cselectorRadioTabs .cselectorRadioUL a.select{color:#FF6600;border-bottom:solid 1px #ff6600}
/*cselectorRadioTabs1选项卡式单选*/
.cselectorRadioTabs1 .cselectorRadioUL{padding:0 0 3px;}
.cselectorRadioTabs1 .cselectorRadioUL a{background:none;padding:6px 12px;font-size:14px;border:1px solid #ccc;}
.cselectorRadioTabs1 .cselectorRadioUL a.select{color:#fff;background:#FF6600;border:solid 1px #ff6600}
/*cselectorRadioTabs1选项卡式单选*/
.cselectorRadioTabs2 .cselectorRadioUL{padding:0 0 3px;}
.cselectorRadioTabs2 .cselectorRadioUL a{background:none;padding:6px;font-size:14px;border-right:solid 1px #e7e7e8;display:block;float:left;width:50%;text-align:center;margin:0px;}
.cselectorRadioTabs2 .cselectorRadioUL a.select{color:#FF6600;}
/*复选按钮*/
.cselectorCheckBox{}
.cselectorCheckBoxUL{min-height:30px;line-height:30px;}
.cselectorCheckBoxUL a{background:url(../img/c1.png) no-repeat left center;padding:0px 0 0px 15px;margin:0 5px 0 0;}
.cselectorCheckBoxUL a:hover{text-decoration:none;}
.cselectorCheckBoxUL .select{background-image:url(../img/c2.png);}
.cselectorImage{}  
/*复选下拉*/
.cselectorCheckList{}
.cselectorCheckListUL a,.panelallbtns .checklistall{background:url(../img/c1.png) no-repeat 8px center;padding:0px 0 0px 25px;margin:0 5px 0 0;}
.cselectorCheckListUL a:hover{text-decoration:none;}
.cselectorCheckListUL .select{background-image:url(../img/c2.png);}
.cselectorCheckListUL a.readonly{background-image:none;padding:0px 0 0px 10px;}
/*始终显示的下拉框*/
.onlyshow .cselectorInput{display:none;}
.onlyshow .cselectorUL{display:block;border:none;}
/*星级评分选择器*/
.cselectorStar{}
.cselectorStarBoxUL{position:relative;width:75px;height:15px;background:url(../img/star1.png) repeat-x 0 -15px;float:left;}
.cselectorStarBoxUL .star{position:absolute;display:block;height:15px;}
.cselectorStarBoxUL .star:hover,.cselectorStarBoxUL .star.hover{background:url(../img/star1.png) repeat-x 0 0px;}
.cselectorStarBoxUL .star1{width:20%;z-index:15;}
.cselectorStarBoxUL .star2{width:40%;z-index:14;}
.cselectorStarBoxUL .star3{width:60%;z-index:13;}
.cselectorStarBoxUL .star4{width:80%;z-index:12;}
.cselectorStarBoxUL .star5{width:100%;z-index:11;}

.starshowbox{}
.starshow{width:65px;height:12px;background:url(../img/star.png) repeat-x 0 -119px;float:left;}
.starshow.star1{background-position:0px -102px;}
.starshow.star2{background-position:0px -85px;}
.starshow.star3{background-position:0px -68px;}
.starshow.star4{background-position:0px -34px;}
.starshow.star5{background-position:0px 0px;}
/*树虚线*/
.cselectorUL li ul{padding:0;margin-left:10px;border-left:dotted 1px #ddd;} 
/*手机端城市选择器*/

.citybox{display:none;}
.changecity .cselectorInput{position:fixed;right:10px;color:#FFF;top:7px;z-index:998;width:60px;border:none;background-color:transparent;} 
.weicitybox{position:absolute;top:0px;left:0;width:100%;min-height:100%;background:#FFF;z-index:998;padding-top:10px;}
.weicitybox .addressChoose{position:absolute;right:10px;top:0px;}
.weicitybox .addressChoose i{font-size:30px;}
.opencitybox{content: "已开通城市";display: block;padding: 10px;background:#000;color:#FFF;}
.weicitybox li{height:50px;line-height:50px;font-size:16px;text-align:center;border-bottom:solid 1px #ddd;color:#333;}
.weicitybox li:hover,
.weicitybox li:active
{background:#ddd;}
/*下拉型城市选择器*/
.cselectorcitybox { width:120px;float:left;margin-right:10px;} 
.cselectorcitybox .cselectorUL { width:120px;overflow:auto;}
.cselectorcitybox .cselectorUL a{overflow:auto;}
/*IOS型城市选择器*/
.cselectorioscitybox {position:fixed;bottom:46px;left:0;right:0; background:#fff;padding-top:5px;}
.cselectorioscitybox .btn { width:100%;margin-top:5px;border-radius:0;}
.cselectorioscity { width:33.3333%;height:120px;overflow:auto;float:left;text-align:center;}
.cselectorioscity a {height:32px;line-height:32px;display:block;overflow:hidden;}
.cselectorioscity a.select { font-weight:bold;}

/*幻灯片样式 Start*/
.cfocus{position:relative;background:#eee;min-height:120px;} 
.cfocus .item{position:absolute}
.cfocus,.cfocus .item,.cfocus .item a,.cfocus .item img{width:100%;height:100%;}

.cfocus .page{position:absolute;z-index:3;bottom:0;right:15px;}
.cfocus .page a{border-radius:50%;display:inline-block;height:12px;margin:3px;width:12px;color:#eee}
.cfocus .page a.hover,.cfocus .page a:hover{color:#2a9af0;} 

/*缩略图式的页码*/
.cfocus .page.thumbnail{position:absolute;z-index:3;bottom:0;left:0px;right:0px;}
.cfocus .page.thumbnail a{border-radius:0;display:inline-block;height:60px;margin:3px;width:60px;color:#eee}
.cfocus .page.thumbnail a img.thumbnail{width:58px;height:58px;float:left;}
.cfocus .page.thumbnail a.hover,.cfocus .page.thumbnail a:hover{border:solid #2a9af0 1px;} 

/*转场效果*/
.inanimation{position:absolute;animation:inanimation 0.2s;
-moz-animation:inanimation 0.2s;	/* Firefox */
-webkit-animation:inanimation 0.2s;	/* Safari 和 Chrome */}
@keyframes inanimation{0%   {right:-100%; } 100% {right:0px; }}
@-moz-keyframes inanimation {0%   {right:-100%; }100% {right:0px; }}/* Firefox */
@-webkit-keyframes inanimation {0%   {right:-100%;}100% {right:0px; }}/* Safari 和 Chrome */

/*退场效果*/
.backanimation{position:absolute;animation:outanimation 0.2s;
-moz-animation:outanimation 0.2s;	/* Firefox */
-webkit-animation:outanimation 0.2s;	/* Safari 和 Chrome */}
@keyframes outanimation{0%   {left:-100%; } 100% {left:0px; }}
@-moz-keyframes outanimation {0%    {left:-100%; } 100% {left:0px; }}/* Firefox */
@-webkit-keyframes outanimation {0%     {left:-100%; } 100% {left:0px; }}/* Safari 和 Chrome */
  
/*大调研*/ 
/*头部*/
header{width:100%;position:fixed;z-index:50;top:0px;background:#00b4f1;color:#fff;text-align:center;line-height:45px;font-size:16px;}
header a{color:#fff;}
.theader{background:#fff;display:inline-block;}
.theader a{text-align:center;padding:2px 6px 0 10px;line-height:16px;overflow:hidden;height:47px;}
.theader a i{font-size:22px;color:#00b4f1;display:block;line-height:22px;margin-bottom:4px;}
.theader a span{font-size:12px;}
/*一级导航 左右上下*/
.topbar,
.stopbar{ height:45px;width:100%;}
.indexbox{margin-left:10px;float:left;}
.indexbox a{width:50px;float:left;text-align:center;padding:1px 5px 0;margin-top:1px;}
.indexbox a span{display:block;font-size:12px;}
.indexbox a i{font-size:22px;} 
header.itemtype .secendbar{display:block;} 
/*主体 需要空出来上面的头和下面的底 顺便加5像素的间隙
.view-content{position:absolute;z-index:10;top:45px;bottom:50px;left:0;width:100%;background:#fff;overflow:hidden;}
.content {position:absolute;z-index:10;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;background:#fff;
-webkit-transform:translateZ(0);-moz-transform:translateZ(0);-ms-transform:translateZ(0);
-o-transform:translateZ(0);transform:translateZ(0);-webkit-touch-callout:none;
-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;
-webkit-text-size-adjust:none;-moz-text-size-adjust:none;-ms-text-size-adjust:none;
-o-text-size-adjust:none;text-size-adjust:none;}*/
/*.view-content{position:absolute;z-index:10;top:47px;padding-bottom:46px;left:0;right:0;background:#f3f3f3;}

超级H5时的页面容器*/
.pageview{position:fixed;width:100%;top:47px;bottom:46px;overflow-y:scroll;overflow-x:hidden;-webkit-overflow-scrolling:touch;background:#f4f4f4;}
.pageview.bgf{background:#fff;}
.pageview:before {  content:"";  width: 1px;  float: left;  height: calc(100% + 1px);  margin-left: -1px;  display: block;  }  
.pageview:after{  content:"";  width: 100%;  clear: both;  display: block;  } 
/***.fromAndroid .pageview{position:absolute;width:100%;top:47px;bottom:auto;padding-bottom:46px;overflow:hidden;} */
.view2{padding-bottom:0px;}
.fromFixed .view2{bottom:0px;}
.view1{padding:0px;}
.view-content{}
/*公共底部*/
nav{width:100%;height:46px;position:fixed;z-index:100;bottom:0px;left:0px;border-top:solid 1px #666;border-bottom:solid 1px #666;background-color:#fff;} 
.index a{width:20%;float:left;text-align:center;padding:0;border-right:1px solid #666;position:relative;}
.index a span{display:block;color:#000;line-height:44px;font-size:12px;}
.index a font{position:absolute;top:0;right:0;background:#f00;color:#fff;border-radius:50%;color:#fff;width:14px;text-align:center;line-height:14px;font-size:10px;}
.index a i.iconfont{font-size:22px;color:#fff;}
.index a.choose span,
.index a.choose i.iconfont{color:#fff;background:#00b4f1;}
.index a.choose font{color:#f00;background:#fff;}
/*地图缩放相关*/
.zoombox{display:none;width:24px;height:135px;position:fixed;top:200px;left:20px;opacity:0.9;z-index:5;}
.zoombox .zoom-mark{position:absolute;width:100%;height:100%;border-radius:10px;box-shadow:0px 2px 8px #999;border:solid 1px #eee;background:#dcdbd7;opacity:0.7;z-index:3;}
.zoombox .zoom-btn{position:absolute;z-index:4;}
.zoombox .zoom-btn .plus,
.zoombox .zoom-btn .minus{color:#ec6c6b;font-size:24px;}
.zoombox .zoom-bar-box{width:6px;height:80px;background:#FFF;position:relative;border-radius:10px;}
.zoombox .zoom-bar{width:6px;background:#ec6c6b;position:absolute;bottom:0px;border-radius:10px;}

/*回到我的位置*/
.gotomylocalbox{width:50px;height:50px;line-height:44px;border:1px solid #e7e7e8;position:fixed;z-index:100;bottom:60px;left:15px;opacity:0.8;background:#FFF;border-radius:50%;text-align:center;}
.gotomylocalbox i{font-size:22px;}
/*系统消息*/ 
.sysmessage{width:50px;height:50px;line-height:50px;border:1px solid #e7e7e8;position:fixed;z-index:100;bottom:60px;right:15px;opacity:0.8;background:#FFF;border-radius:50%;text-align:center;}
.sysmessage span{background:red;border-radius:3px;color:#fff;height:15px;line-height:15px;padding:1px 3px;position:absolute;right:5px;top:3px;}
.sysmessage i{font-size:32px;}

/*色彩定义 字体主背景橙色ea5404 浅橙c1ac91 边框e7e7e8 背景fefefe  粉红色e61f6c*/
.error{color:Red;border-color:Red}
td input,td textarea,.cselectorInput,.cselectorUL{border-color:#e7e7e8}
input,textarea{background-color:#fff}  
input,textarea,.cselectorInput,.cselectorUL{border-color:#e7e7e8}
.cselectorDiv .cselectorTitle,.cselectorDiv,.cselectorDiv .cselectorBody li ul{border-color:#4cae4c}
.btn,.btn:link,.btn:visited{background-color:#00b4f1;border-color:#00b4f1;color:#fff}
.btn-green,.btn-green:link,.btn-green:visited{background-color:#01df7a;border-color:#01df7a;color:#fff}
.btn-org,.btn-org:link,.btn-org:visited{background-color:#5cb85c;border-color:#4cae4c;color:#fff}
.btn-hui,.btn-hui:link,.btn-hui:visited{background-color:#e7e7e8;border-color:#e7e7e8 ;color:#999}
body,a,input,textarea{color:#333} 
input.ui_state_highlight:active,.ui_close:hover,.ui_close:focus,.ui_title{background:#5cb85c}
.cselectorDiv .cselectorTitle,input.ui_state_highlight,.touchslider-btn span.touchslider-nav-item-current{background-color:#4cae4c}
/**无数据提示**/
.noinfo{width:100%;position:absolute;top:80px;text-align:center;display:none;}
.noinfo i{font-size:42px;color:#aaa;line-height:60px;}
.noinfo font{color:#bbb;}
.noviphistory{position:relative;top:0;}
/*无边框表单*/
.nobform{}
.nobform input{border-color:transparent;background:transparent;text-align:right;}
.nobform .info{background:transparent;}

/*一般列表*/
.classlist li{border-bottom:1px solid #e0e0e0;font-size:13px;padding:10px 5px;position:relative;}
.classlist li .state_TR{position:absolute;top:30px;right:15px;}
.tasklist li{padding:10px;}

.listBtn{position:absolute;left:0;right:0;padding:10px;}

/**tab**/
.title{background:#fafafa;width:100%;float:left;line-height:36px;}
.title a{width:50%;float:left;text-align:center;font-size:12px;}
.title a.a_hover{color:#00b4f1;border-bottom:2px solid #00b4f1;}
.dialog{position:fixed;left:0;right:0;top:0;bottom:0;background:rgba(0,0,0,0.3);z-index:100;}
.dia{position:absolute;left:5%;right:5%;top:20%;background:#fff;border:1px solid #aaa;border-radius:6px;padding-bottom:10px;}
.dia h6{border-bottom:1px solid #aaa;text-align:center;padding:10px;font-weight:bold;}

.bom_btn{position:fixed;left:0;right:0;bottom:0;line-height:38px;text-align:center;background:#00b4f1;color:#fff;}