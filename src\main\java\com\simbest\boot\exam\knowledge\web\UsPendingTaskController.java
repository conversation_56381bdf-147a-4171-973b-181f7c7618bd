/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.web;/**
 * Created by KZH on 2019/10/8 16:09.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsInvitations;
import com.simbest.boot.exam.knowledge.model.UsPendingTask;
import com.simbest.boot.exam.knowledge.model.UsUserScore;
import com.simbest.boot.exam.knowledge.service.IUsPendingTaskService;
import com.simbest.boot.exam.knowledge.service.IUsUserScoreService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.DateUtil;
import com.simbest.boot.util.security.LoginUtils;
import io.swagger.annotations.Api;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:09
 * @desc
 **/
@Api(description = "待办", tags = {"待办相关处理控制器"})
@Slf4j
@RestController
@RequestMapping(value = "/action/usPendingTask")
public class UsPendingTaskController extends LogicController<UsPendingTask, String> {

    private IUsPendingTaskService usPendingTaskService;

    @Autowired
    public UsPendingTaskController(IUsPendingTaskService usPendingTaskService) {
        super(usPendingTaskService);
        this.usPendingTaskService = usPendingTaskService;
    }


    @Autowired
    private LoginUtils loginUtils;

    /**
     * 查询被邀请人列表信息
     */
    @PostMapping(value = {"/InvitedTask", "/api/InvitedTask", "/InvitedTask/sso",})
    public JsonResponse InvitedTask(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                    @RequestParam(value = "currentUserCode",required = false) String currentUserCode,
                                    @RequestParam(required = false) String currentDay,
                                    @RequestParam String recUserName) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        if(StringUtil.isBlank(currentDay)){
            currentDay = DateUtil.getCurrentStr();
        }
        List<UsPendingTask> usInvitationsList = usPendingTaskService.InvitedTask(currentDay, recUserName);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
       // return JsonResponse.success(usInvitationsList);
    }



    /**
     * 查询待办信息
     */
    @PostMapping(value = {"/getUsPendingTaskByPmInsId", "/api/getUsPendingTaskByPmInsId", "/getUsPendingTaskByPmInsId/sso",})
    public JsonResponse getUsPendingTaskByPmInsId(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                    @RequestParam(value = "currentUserCode",required = false) String currentUserCode,
                                    @RequestParam String pmInsId) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        UsPendingTask usPendingTask = usPendingTaskService.getUsPendingTaskByPmInsId(pmInsId);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
       // return JsonResponse.success(usPendingTask);
    }
}
