package com.simbest.boot.exam.examOnline.web;

import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamAnswerInfo;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.service.IExamAnswerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Api(description = "答题解析")
@Slf4j
@RestController
@RequestMapping(value = "/action/examAnswerInfo")
public class ExamAnswerInfoController extends LogicController<ExamAnswerInfo, String> {
    private IExamAnswerInfoService iExamAnswerInfoService;

    public ExamAnswerInfoController(IExamAnswerInfoService iExamAnswerInfoService) {
        super(iExamAnswerInfoService);
        this.iExamAnswerInfoService = iExamAnswerInfoService;
    }
    @ApiOperation(value = "查看解析", notes = "查看解析")
    @PostMapping(value = {"/findExamAnswerInfo", "sso/findExamAnswerInfo", "api/findExamAnswerInfo"})
    public JsonResponse findExamAnswerInfo(
//            @RequestParam(required = false) String currentUserCode,
//            @RequestParam(required = false) String source,
            @RequestBody(required = false) Map<String,Object> examMap) {
        return JsonResponse.success(iExamAnswerInfoService.findExamAnswerInfo(examMap));

    }
}
