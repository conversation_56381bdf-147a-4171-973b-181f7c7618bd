/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examStatistics.model;/**
 * Created by KZH on 2020/1/3 10:43.
 */

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020-01-03 10:43
 * @desc
 **/
@Data
public class Option {

    @Excel(name =  "一")
    private String optionA ;

    @Excel(name =  "一")
    private String optionB ;

    @Excel(name =  "一")
    private String optionC ;

    @Excel(name =  "一")
    private String optionD ;

    @Excel(name =  "一")
    private String optionE ;

    @Excel(name =  "一")
    private String optionF ;

    @Excel(name =  "一")
    private String optionG ;

    @Excel(name =  "一")
    private String optionH ;

    @Excel(name =  "一")
    private String optionI ;

    @Excel(name =  "一")
    private String optionJ ;

    @Excel(name =  "一")
    private String optionK ;

    @Excel(name =  "一")
    private String optionL ;

    @Excel(name =  "一")
    private String optionM ;

    @Excel(name =  "一")
    private String optionN ;

    @Excel(name =  "一")
    private String optionO ;

    @Excel(name =  "一")
    private String optionP ;

    @Excel(name =  "一")
    private String optionQ ;

    @Excel(name =  "一")
    private String optionR ;

    @Excel(name =  "一")
    private String optionS ;

    @Excel(name =  "一")
    private String optionT ;

    @Excel(name =  "一")
    private String optionU ;

    @Excel(name =  "一")
    private String optionV ;

    @Excel(name =  "一")
    private String optionW ;

    @Excel(name =  "一")
    private String optionX ;

    @Excel(name =  "一")
    private String optionY ;

    @Excel(name =  "一")
    private String optionZ ;

    @Excel(name = "一")
    private String optionAA ;

    @Excel(name = "一")
    private String optionAB ;

    @Excel(name = "一")
    private String optionAC ;

    @Excel(name = "一")
    private String optionAD ;

    @Excel(name = "一")
    private String optionAE ;

    @Excel(name = "一")
    private String optionAF ;

    @Excel(name = "一")
    private String optionAG ;

    @Excel(name = "一")
    private String optionAH ;

    @Excel(name = "一")
    private String optionAI ;

    @Excel(name = "一")
    private String optionAJ ;

    @Excel(name = "一")
    private String optionAK ;

    @Excel(name = "一")
    private String optionAL ;

    @Excel(name = "一")
    private String optionAM ;

    @Excel(name = "一")
    private String optionAN ;

    @Excel(name = "一")
    private String optionAO ;

    @Excel(name = "一")
    private String optionAP ;

    @Excel(name = "一")
    private String optionAQ ;

    @Excel(name = "一")
    private String optionAR ;

    @Excel(name = "一")
    private String optionAS ;

    @Excel(name = "一")
    private String optionAT ;

    @Excel(name = "一")
    private String optionAU ;

    @Excel(name = "一")
    private String optionAV ;

    @Excel(name = "一")
    private String optionAW ;

    @Excel(name = "一")
    private String optionAX ;

    @Excel(name = "一")
    private String optionAY ;

    @Excel(name = "一")
    private String optionAZ ;

    @Excel(name = "一")
    private String optionBA ;

    @Excel(name = "一")
    private String optionBB ;

    @Excel(name = "一")
    private String optionBC ;

    @Excel(name = "一")
    private String optionBD ;

    @Excel(name = "一")
    private String optionBE ;

    @Excel(name = "一")
    private String optionBF ;

    @Excel(name = "一")
    private String optionBG ;

    @Excel(name = "一")
    private String optionBH ;
    @Excel(name = "一")
    private String optionBI ;
    @Excel(name = "一")
    private String optionBJ ;
    @Excel(name = "一")
    private String optionBK ;
    @Excel(name = "一")
    private String optionBL ;
    @Excel(name = "一")
    private String optionBM ;
    @Excel(name = "一")
    private String optionBN ;
    @Excel(name = "一")
    private String optionBO ;
    @Excel(name = "一")
    private String optionBP ;
    @Excel(name = "一")
    private String optionBQ ;
    @Excel(name = "一")
    private String optionBR ;
    @Excel(name = "一")
    private String optionBS ;
    @Excel(name = "一")
    private String optionBT ;
    @Excel(name = "一")
    private String optionBU ;
    @Excel(name = "一")
    private String optionBV ;
    @Excel(name = "一")
    private String optionBW ;
    @Excel(name = "一")
    private String optionBX ;
    @Excel(name = "一")
    private String optionBY ;
    @Excel(name = "一")
    private String optionBZ ;
    @Excel(name = "一")
    private String optionCA ;
    @Excel(name = "一")
    private String optionCB ;
    @Excel(name = "一")
    private String optionCC ;
    @Excel(name = "一")
    private String optionCD ;
    @Excel(name = "一")
    private String optionCE ;
    @Excel(name = "一")
    private String optionCF ;
    @Excel(name = "一")
    private String optionCG ;
    @Excel(name = "一")
    private String optionCH ;
    @Excel(name = "一")
    private String optionCI ;
    @Excel(name = "一")
    private String optionCJ ;
    @Excel(name = "一")
    private String optionCK ;
    @Excel(name = "一")
    private String optionCL ;
    @Excel(name = "一")
    private String optionCM ;
    @Excel(name = "一")
    private String optionCN ;
    @Excel(name = "一")
    private String optionCO ;
    @Excel(name = "一")
    private String optionCP ;
    @Excel(name = "一")
    private String optionCQ ;
    @Excel(name = "一")
    private String optionCR ;
    @Excel(name = "一")
    private String optionCS ;
    @Excel(name = "一")
    private String optionCT ;
    @Excel(name = "一")
    private String optionCU ;
    @Excel(name = "一")
    private String optionCV ;
    @Excel(name = "一")
    private String optionCW ;
    @Excel(name = "一")
    private String optionCX ;
    @Excel(name = "一")
    private String optionCY ;
    @Excel(name = "一")
    private String optionCZ;
    @Excel(name = "一")
    private String optionDA ;
    @Excel(name = "一")
    private String optionDB ;
    @Excel(name = "一")
    private String optionDC ;
    @Excel(name = "一")
    private String optionDD ;
    @Excel(name = "一")
    private String optionDE ;
    @Excel(name = "一")
    private String optionDF ;
    @Excel(name = "一")
    private String optionDG ;
    @Excel(name = "一")
    private String optionDH ;
    @Excel(name = "一")
    private String optionDI ;
    @Excel(name = "一")
    private String optionDJ ;
    @Excel(name = "一")
    private String optionDK ;
    @Excel(name = "一")
    private String optionDL ;
    @Excel(name = "一")
    private String optionDM ;
    @Excel(name = "一")
    private String optionDN ;
    @Excel(name = "一")
    private String optionDO ;
    @Excel(name = "一")
    private String optionDP ;
    @Excel(name = "一")
    private String optionDQ ;
    @Excel(name = "一")
    private String optionDR ;
    @Excel(name = "一")
    private String optionDS ;
    @Excel(name = "一")
    private String optionDT ;
    @Excel(name = "一")
    private String optionDU ;
    @Excel(name = "一")
    private String optionDV ;
    @Excel(name = "一")
    private String optionDW ;
    @Excel(name = "一")
    private String optionDX ;
    @Excel(name = "一")
    private String optionDY ;
    @Excel(name = "一")
    private String optionDZ ;
    @Excel(name = "一")
    private String optionEA ;
    @Excel(name = "一")
    private String optionEB ;
    @Excel(name = "一")
    private String optionEC ;
    @Excel(name = "一")
    private String optionED ;
    @Excel(name = "一")
    private String optionEF ;
    @Excel(name = "一")
    private String optionEG ;
    @Excel(name = "一")
    private String optionEH ;
    @Excel(name = "一")
    private String optionEI ;
    @Excel(name = "一")
    private String optionEJ ;
    @Excel(name = "一")
    private String optionEK ;
    @Excel(name = "一")
    private String optionEL ;
    @Excel(name = "一")
    private String optionEM ;
    @Excel(name = "一")
    private String optionEN ;
    @Excel(name = "一")
    private String optionEO ;
    @Excel(name = "一")
    private String optionEP ;
    @Excel(name = "一")
    private String optionEQ ;
    @Excel(name = "一")
    private String optionER ;
    @Excel(name = "一")
    private String optionES ;


}
