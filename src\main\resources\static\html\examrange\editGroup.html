<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>新增配置</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
  <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
  <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
  <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
  <link href="../../css/public.css?v=svn.revision" th:href="@{/css/public.css?v=svn.revision}" rel="stylesheet"/>
  
  <style>
    *{margin: 0;border: 0;}
        /* 表单样式控制 */
    .tbc{
      text-align: right;
      margin: auto;
      border: 0;
      width: 455px;
      padding-top: 50px;
    }
  </style>

  <script type="text/javascript">

    $(function (){
      var gps = getQueryString();
      loadForm("groupListTableUpdateForm");
      // idReadonly('appCode');


      //查询试卷下拉框
      $('#paperName').combobox({
          url: web.rootdir + 'action/examAttribute/findAllNoPage',
          valueField: 'examAppCode',
          contentType: 'application/json; charset=utf-8',
          textField: 'examName',
          panelHeight: '300px',
          prompt: '-请选择-',
          editable: false,
          onSelect: function (record) {

          }
      });
      //查询考试下拉框
      $('#examName').combobox({
          url: web.rootdir + 'action/summary/findAllNoPage',
          valueField: 'examCode',
          contentType: 'application/json; charset=utf-8',
          textField: 'examName',
          panelHeight: '400px',
          prompt: '-请选择-',
          editable: false,
          onSelect: function (record) {

          }
      });
      //请求数据并渲染到表单
      ajaxgeneral({
          url:"action/rangeGroup/findById?id="+gps.id,
          success:function(res){
            formval(res.data,"groupListTableUpdateForm");
            $("#paperName").combobox('setValue',res.data.examAppCode);
            $("#examName").combobox('setValue',res.data.examCode);
          }
      });
          //表单校验
      window.fvalidate = function () {
          return $("#groupListTableUpdateForm").form("validate");
      };
      window.getchoosedata = function () {
        $('.easyui-validatebox').validatebox('enableValidation');
        if (fvalidate()) {
          //..
        }else{
          return;
        }
        
        var data = getFormValue("groupListTableUpdateForm");
        //console.log(data,"aaaaa");
        var examAppCode = $('#paperName').combobox('getValue');
        var paperName = $(".paperName > input").val();
        // var examCode =  $('#examName').combobox('getValue');
        // var examName =  $("input",".examName > span").val();
        data.examAppCode = examAppCode;
        data.paperName = paperName;
        // data.examCode = examCode;
        // data.examName = examName;
        //console.log(data);
        return {"data":data,"state":1};
        // return {"data":data,"state":0};
      };

    })

  </script>
</head>
<body>

  <form id="groupListTableUpdateForm">

    <table class="tbc" border="0" cellpadding="0" cellspacing="16" >
      <tr >
        <td width="80" hidden="true"><span class="col_r">*</span>id:</td>
        <td width="160" ><input name="id" class="id"  id="id" hidden="true"  type="text"  /></td>
      </tr>

      <tr >
          <td width="80" ><span class="col_r">*</span>群组名称:</td>
        <td width="160" ><input  name="groupName" class="easyui-validatebox groupName" required="ture" validType="string" id="groupName"  type="text"  /></td>
      </tr>

      <tr >
        <td width="80" hidden><span class="col_r">*</span>群组编码:</td>
        <td width="160" hidden><input name="groupId" class="easyui-validatebox groupId" required="ture" validType="string"  id="groupId"  type="text"  /></td>
      </tr>

      <tr >
        <td width="80" ><span class="col_r">*</span>试卷名称:</td>
        <td width="160" class="paperName">
          <input id="paperName" name="paperName" class=" paperName"  style="width:  300px; height: 32px;"/>
        </td>
      </tr>
      <tr hidden>
        <td >
          <input id="examCode" name="examCode" class=" examCode"  style="width:  300px; height: 32px;"/>
        </td>
        <td>
          <input id="examName" name="examName" class=" examName"  style="width:  300px; height: 32px;"/>
        </td>
      </tr>
    </table>
  </form>

</body>
</html>