<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>生成待办</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">
        $(function () {


            // $('#sql').combobox({
            //     url: web.rootdir + 'action/examQuestion/findDictValue?dictType=sqlType',
            //     valueField: 'value',
            //     contentType: 'application/json; charset=utf-8',
            //     textField: 'value',
            //     panelHeight: 'auto',
            //     prompt: '-请选择-',
            //     editable: false,
            //     onSelect: function (record) {
            //
            //     }
            // });


            // 加载表单
            loadForm("generateTodoTableAddForm");

        });


        //表单校验
        window.fvalidate = function () {
            return $("#generateTodoTableAddForm").form("validate");
        };

        //对表单中的元素进行校验，返回为0代表检验不成功。可以参考appManagementList.html
        window.getchoosedata = function () {
            formsubmit("generateTodoTableAddForm","action/examWork/createToDo");

            return {"data":'',"state":1};
        };

        //创建成功之后
        window.submitcallback = function () {
            top.dialogClose("generateTodo");
            //(top.window.insertQuestionshowDialogTopF || top).window.insertQuestionTableLoad();
        };


    </script>
</head>
<body>
<!--scontentType获取数据时设置传参方式，不设置默认为kv  cmd-select获取数据命令配置，不配置自动从列表中取   cmd-insert新增  cmd-update修改-->
<div class="basic tab_table">
    <form id="generateTodoTableAddForm" method="post" contentType="application/json; charset=utf-8"
          submitcallback="submitcallback()" >
        <input id="id" name="id" type="hidden"/>

        <table border="0" cellpadding="0" cellspacing="10">
            <tr>
                <td width="10%" align="left"><font class="col_r">*</font>公司：</td>
                <td width="20%" length="100">
                    <input id="company" name="company" type="text" value="" class="easyui-validatebox"   style="width:  450px; height: 32px;"/>
                </td>
            </tr>
            <tr>
                <td width="10%" align="left"><font class="col_r">*</font>设置考试标题：</td>
                <td width="20%" length="100">
                    <input id="title" name="title" type="text" value="" class="easyui-validatebox"  required='required'  style="width:  450px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left"><font class="col_r">*</font>设置考试系统编码：</td>
                <td width="20%" length="100">
                    <input id="questionName" name="questionName" type="text" value="" class="easyui-validatebox"  required='required'  style="width:  450px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left"><font class="col_r">*</font>设置待办类型(指向页面不同)：</td>
                <td width="20%" length="100">
                    <input id="questionType" name="questionType" type="text" value="" class="easyui-validatebox"  required='required'  style="width:  450px; height: 32px;"/>
                </td>
            </tr>

        </table>
    </form>
</div>
</body>
</html>
