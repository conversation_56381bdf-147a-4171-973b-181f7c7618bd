/**全局公共样式**/
*{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;}
html,body,h1,h2,h3,h4,h5,h6,div,dl,dt,dd,ul,ol,li,p,blockquote,pre,hr,table,th,td,form,fieldset,input,button,textarea,menu,i{margin:0;padding:0;font-family:"Microsoft Yahei","微软雅黑",arial,"宋体",sans-serif;} 
body{font-size:12px;margin:0;padding:0;background:#FFF;}
input,textarea,select{font-family:inherit;line-height:18px;outline:none;font-size:12px;vertical-align:middle;box-shadow:0 0 0 #fff;}
input { font-family:"Microsoft Yahei","微软雅黑",arial,"宋体",sans-serif; font-size:12px;}
input,textarea,select{padding:7px;line-height:16px;border:1px solid #e5e5e5;-moz-border-radius:2px;-webkit-border-radius:2px;border-radius:2px;background:#fff;}
input[type="radio"],input[type="checkbox"]{padding:0;}
input.input_left{-moz-border-top-left-radius:3px;-webkit-border-top-left-radius:3px;border-top-left-radius:3px;-moz-border-bottom-left-radius:3px;-webkit-border-bottom-left-radius:3px;border-bottom-left-radius:3px;-moz-border-top-right-radius:0px;-webkit-border-top-right-radius:0px;border-top-right-radius:0px;-moz-border-bottom-right-radius:0px;-webkit-border-bottom-right-radius:0px;border-bottom-right-radius:0px;}
a,a:link,a:visited{text-decoration:none;cursor:pointer;color:#39aef5;}
a:hover{text-decoration:underline;}
a:focus{text-decoration:none;}
img{vertical-align:middle;border:0;}
h1,h2,h3,h4,h5,h6{font-weight:500;color:inherit;}
h1,h2,h3,h4,h5{padding:10px 0;}
h1{font-size:36px;}h2{font-size:30px;}h3{font-size:25px;}h4{font-size:18px;}h5{font-size:14px;}h6{font-size:14px;}
p{margin:0px;line-height:25px;}li{list-style:none;}
ul{margin:0;padding:0;list-style:none;}
li ul{padding-left:10px;}
table,th,td{color:#333333;}
th{font-weight:bold;}
div{word-wrap:break-word;word-break:break-all;}
/**button**/
a.btn{width:auto;height:32px;float:left;cursor:pointer;line-height:30px;padding:0 18px;color:#fff;background-color:#39aef5;text-align:center;-moz-border-radius:2px;-webkit-border-radius:2px;border-radius:2px;}
a.a_left{-moz-border-top-left-radius:2px;-webkit-border-top-left-radius:2px;border-top-left-radius:2px;-moz-border-bottom-left-radius:2px;-webkit-border-bottom-left-radius:2px;border-bottom-left-radius:2px;
-moz-border-top-right-radius:0px;-webkit-border-top-right-radius:0px;border-top-right-radius:0px;-moz-border-bottom-right-radius:0px;-webkit-border-bottom-right-radius:0px;border-bottom-right-radius:0px;}
a.a_right{border-left:0px none;-moz-border-top-right-radius:2px;-webkit-border-top-right-radius:2px;border-top-right-radius:2px;-moz-border-bottom-right-radius:2px;-webkit-border-bottom-right-radius:2px;border-bottom-right-radius:2px;
-moz-border-top-left-radius:0px;-webkit-border-top-left-radius:0px;border-top-left-radius:0px;-moz-border-bottom-left-radius:0px;-webkit-border-bottom-left-radius:0px;border-bottom-left-radius:0px;}
a.a_all3{-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;}
a.a_primary{background-color:#337ab7;border-color:#2e6da4;color:#fff;}
a.a_success{background-color:#5cb85c;border-color:#4cae4c;color:#fff;}
a.a_green{background-color:#9CC715;border-color:#95BF10;color:#fff;}
a.a_info{background-color:#5bc0de;border-color:#46b8da;color:#fff;}
a.a_warning{background-color:#f0ad4e;border-color:#eea236;color:#fff;}
a.a_danger{background-color:#d9534f;border-color:#d43f3a;color:#fff;}
a.a_none{background-color:transparent;border:1px solid #bbb;color:#222;}
a.a_blue{background-color:#0086CF;border:1px solid #0086CF;color:#fff;}
a.a_r4{border-radius: 4px;}
a.btn i{width:auto;line-height:30px;float:left;margin:0px 2px 0 0;}
a.btn i.i_add{padding:7px 0px 5px;}
a.btn:hover,a.a_hover{text-decoration:none;background:#38a8ec;}
a.a_primary:hover{background:#35a1e3;}
a.a_success:hover{background:#4cae4c;}
a.a_green:hover{background:#95BF10;}
a.a_warning:hover{background:#eea236;}
a.a_info:hover{background:#46b8da;}
a.a_danger:hover{background:#d43f3a;}
a.a_none:hover{background:transparent;}
a.a_blue:hover{background:#0086CF;}
a.small{height:30px;line-height:28px;padding:0 10px;}
a.small font{font-size:12px;}
a.a_reset{background-color:#fff;border:1px solid #c9c9c9;color:#555;}
a.a_reset:hover{border-color:#39aef5;background:#fff;}
a.a_right:hover{border-left:0px none;}
/**images**/
.images img{margin:5px;height:120px;}
/*基础行高100 h数字 表示倍数 如h6表示600高*/
.hs{height:80px;}.hsm{height:60px;}.hsmm{height:35px;}.h1{height:100px;}.h2{height:200px;}.h3{height:300px;}
.h4{min-height:400px;_height:400px;}.h5{min-height:500px;_height:500px;}.h6{min-height:600px;_height:600px;}
.h32{height:32px;}.lh32{line-height:32px;}.lh65{line-height:65px;}.h16{height:16px;}
/*基础宽度100 同上*/
.ws{width:70px;}.wsm{width:60px;}.wsmm{width:35px;}.w1{width:100px;}.w2{width:200px;}.w3{width:300px;}
.w4{width:400px;}.w5{width:500px;}.w6{width:600px;}.w7{width:700px;}.w8{width:800px;}.w9{width:900px;}
.w100{width:100%;}.w50{width:50%;}.w40{width:40%;}.w60{width:60%;}
/*常用padding margin*/
.p0{padding:0;}.p5{padding:5px;}.p10{padding:10px;}.p15{padding:15px;}.p20{padding:20px;}.p30{padding:30px;}.plr15{padding:0px 15px;}.plr18{padding:0 18px;}.plr10{padding:0 10px;}.pt10lr18{padding:10px 18px 0;}
.m0{margin:0;}.m5{margin:5px;}.m10{margin:10px;}.m15{margin:15px;}.m20{margin:20px;}.m30{margin:30px;}.m50{margin:50px;}.m60{margin:30px 60px;}
.pr12{padding-right:12px;}.pr18{padding-right:18px;}.pb10{padding-bottom:10px;}.plf15{padding:0 15px;}.plf10{padding:0 10px;}.plf5{padding:0 5px;}.pr8{padding-right:8px;}.plr30{padding:0 30px;}.p30t0{padding:0 30px 30px;}
.mr10{margin-right:10px;}.mr15{margin-right:15px;}.ml10{margin-left:10px;}.mr5{margin-right:5px;}.ml15{margin-left:15px;}.mt10{margin-top:10px;}
.fright,.fr,a.fr{float:right;}.fleft,.fl{float:left;}
.hide,.none{display:none;}
.hide.show{display:table-cell;}
.lh16{line-height:16px;}.lh20{line-height:20px;}
.bor_b{border-bottom:1px solid #e6e6e6;}
.text_c{text-align:center;}
.c9a{color:#9a9a9a;}
.fbold{font-weight:bold;}
.of_hidden{overflow:hidden;}
/*清除浮动*/
.txtl{text-align:left}.txtc{text-align:center}.txtr{text-align:right}
.f10{font-size:10px;color:#999;}.f20{font-size:20px;}.f18{font-size:18px;}.f50{font-size:50px;}.f12{font-size:12px;}.f14{font-size:14px;}.f16{font-size:16px;}
.line{clear:both;height:1px;margin:10px 0;width:100%;overflow:hidden;background:#eee} 
.clearB{clear:both;}
.clearfix:after {visibility: hidden;display: block;font-size: 0;content: " ";clear: both;height: 0;}
.clearfix {*zoom:1;}
/*字体颜色*/
.col_r{color:#d9534f;}.col_b{color:#00b4f1;}.col_h{color:#9a9a9a;}

.rightData {position: absolute;top:0px;left:200px;bottom:0; right:0; overflow:auto; border-left:solid 1px #F0F0F0;}
.process_org{left:360px;}
.i_style { color: #00b4f1; cursor: pointer; font-size: 18px; }
/*图片上传器*/
.cselectorImageUL{position:relative;}
.cselectorImageUL .btn{position:absolute;z-index:3;}
.cselectorImageUL input{position:absolute;z-index:5;filter:alpha(opacity=0);opacity:0;cursor:pointer;width:55px;padding: 3px 7px;margin-left:10px;}
/*图片上传样式*/
.uploadImage{position:absolute;left:60px;top:3px;z-index:10;*left:70px;right:0;}
.uploadImageI{padding-top:38px;}
.meitucrop{position:relative;width:100px;height:100px;float:left;text-align:center;overflow:hidden;margin-right:6px;}
.meitucrop img{height:100px;max-width:100px;}
.uploadImage_del{display:block;position:absolute;right:0;top:0;color:#f00;}
.uploadImage p{clear:both;}
.ke-dialog-body .uploadImageI{width:auto;padding-top:32px;}
/*ctable*/
table.ctable{border-collapse:collapse;border-spacing:0; empty-cells:show;max-width:100%;}
table.ctable thead tr{font-weight:bold;background:#e2f4ff;}
table.ctable td{padding:8px;line-height:21px;border-width:1px 0px 0px;border-style:solid;border-color:#e6e6e6;white-space: nowrap;}
table.ctable.wrap td{white-space: pre-wrap;}
table.ctable thead tr td{border:0px none;}
table.ctable tbody{border-width:0px 1px;border-style:solid;border-color:#e6e6e6;}
table.ctable tbody tr:last-child td{border-width:1px 0px 1px;border-style:solid;border-color:#e6e6e6;}
table.ctable tbody tr:nth-child(even){background:#fafafa;}
table.ctable tbody tr:hover{background:#e2f4ff;}
table.ctable tfoot td{border:none;padding:5px 0;background:#f5f6fa;}

table.ctable2 thead tr{background:#fff;}
table.ctable2 td{padding:5px 8px;font-size:12px;}
table.ctable2 thead tr td{border-bottom:2px solid #39aef5;}
table.ctable2 tbody{border:0 none;}
table.ctableTdfixed td{white-space:pre-wrap;}
.over_point,div.tdfixed{overflow:hidden;word-wrap:normal;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis;}

.imgtobig{position:absolute;text-align:center;z-index:11000;width:60%;height:60%;padding:28px;left:20%;top:20%;overflow-x:hidden;overflow-y:auto;border:1px solid #e0e0e0;background:#fff;}
.imgtobig i{position:absolute;z-index:11001;right:18px;top:15px;color:#00b4f1;font-size:25px;cursor:pointer;}
.choose a{background:#f0f0f0;color:#aaa;}
.choose a.a_hover{background:#f88f69;}

.pagination{background:#f5f6fa;padding:5px 0 6px;}

.tab,.tab4 { width: 100%; float: left; background-color: #00b4f1; line-height: 32px; color: #fff; margin-bottom:5px;}
.tab li,.tab4 li { float: left; width: 200px; height: 32px; text-align: center; cursor: pointer; }
.tab2 li{width:20%;}
.tab_click { background-color: #f3f3f3; color: #00b4f1; }
.tab3{line-height:32px;background:#fff;height:34px;}
.tab3 li{float:left;text-align:center;font-size:14px;width:10%;background:#fcfcfc;border-top:1px solid #eee;position:relative;}
.tab3 li span{display:block;border-left:1px solid #eee;border-right:1px solid #eee;border-top:1px solid #fcfcfc;cursor:pointer;}
.tab3 .tab_click{border-color:#00b4f1;font-weight:bold;background:#fff;bottom:0px;height:35px;}
.tab3 .tab_click span{border-color:#00b4f1;width:100%;}
.tab3D{border:1px solid #00b4f1;min-height:100px;background:#fff;}
.tab3.PerSpareTab li span.twoL{line-height:16px;height:33px;}
.tab3.PerSpareTab li span{font-size:12px;}
.tab3.PerSpareTab .tab_click span{font-size:14px;}

.tab4{background:#fcfcfc;color:#333;font-size:12px;margin:0;}
.tab4 li {border-style:solid;border-width:0 1px 0 0;border-color:#eee;}
.tab4 li.tab_click{background-color: #fcfcfc; color: #00b4f1;border-bottom:2px solid #00b4f1;font-weight:bold;}

.tasktabD{width:100%;background: #f9f9f9;height: 40px;margin-bottom: 10px;}
.tasktabDd{width: 206px;margin: 0 auto;}
.tasktabD .tab{ width: 100%; float: left; background-color: #f9f9f9; line-height: 32px; color: #000000; margin-bottom:5px;text-align:center;}
.tasktabD .tab li{float:left; width:auto; height: 32px; text-align: center; cursor: pointer; height: 40px;line-height: 40px;min-width: 90px;margin-left: 13px;}
.tasktabD .tab_click {background-color: #f9f9f9;color: #39aef5;font-weight: bold;border-bottom: 3px solid;}

/**focus**/
.cfocus{position:relative;overflow:hidden;}
.cfocus .item{position:absolute;background-size:100% auto;background-repeat: no-repeat;top:0;bottom:0;background-position:center center;}
.cfocus .item a{color:#333;}
.cfocus .item p{position:absolute;left:0;right:0;bottom:0;background:#e0e0e0;}
.cfocus,.cfocus .item,.cfocus .item a,.cfocus .item img{width:100%;}
.cfocus .page{position:absolute;z-index:3;bottom:0;right:15px;}
.cfocus .page a{border-radius: 50%;display: inline-block;height: 12px;margin: 3px;width: 12px;color:#eee}
.cfocus .page a.hover,.cfocus .page a:hover{color:#2a9af0;}

.textAndInput_readonly,.textAndInput_readonly .validatebox-readonly{background:#eee;}
.infoD{position:relative;}
.infoD .spanInfo{position:absolute;top:0;left:0;padding:6px 8px;color:#888;}

/*下拉列表框*/
.cselector { }
.cselectorInput { display: block; height: 34px; line-height: 32px; width: 100%; padding: 0 20px 0 10px; border-radius: 4px; background: #FFFFFF url(../images/ddl.png) no-repeat right center; text-align: left; overflow: hidden; border: solid 1px; }
.cselectorInput:hover { background: #FFFFFF url(../images/ddl1.png) no-repeat right center; text-decoration: none; }
.cselectorInput input { width: 100%; border: 0px; padding: 0px; height: 38px; }
.showotherbox .cselectorInput { width: 49%; }
.showotherbox input { width: 49%; float: right; }
/*下拉列表框框*/
.cselectorUL { position: absolute; background: #FFFFFF; display: none; border: solid 1px; max-height: 369px; _height: 369px; z-index: 399; margin-top: -1px; }
.cselectorUL div { height: 25px; line-height: 25px; text-align: left; display: block; }
.cselectorUL a { height: 25px; line-height: 25px; text-align: left; display: block; padding: 0 20px 0 10px; overflow: hidden; }
.cselectorUL a.hover,
.cselectorUL a:hover { background-color: #255599; color: #FFFFFF; text-decoration: none; zoom: 1; }
/*树型下拉*/
.cselectorTreeMoreUL { }
.cselectorTreeMoreUL li ul { display: none; }
.cselectorTreeMoreUL a.readonly { position: absolute; right: 0; display: inline; padding: 0 5px; }
.cselectorTreeOpenUL { }
.cselectorTreeOpenUL a.readonly { background: #FFFFFF url(../images/shang.gif) no-repeat right center; }
.cselectorTreeOpenUL a.readonly[isopen] { background-image: url(../images/xia.gif); }
/*HTML容器下拉 收货地址 银行卡之类的*/
.cselectorHTMLUL .cselectorInput { border: none; height: auto; padding: 10px 20px 10px 10px; }
.cselectorHTMLUL .cselectorUL { border: none; }
.cselectorHTMLUL .cselectorUL a { height: auto; border-bottom: solid 10px #f8f8f8; }
.cselectorHTMLUL .cselectorUL a:last-child { border: none; }
/*下拉型城市选择器*/
.cselectorcitybox { width: 120px; float: left; margin-right: 10px; }
.cselectorcitybox .cselectorUL { width: 120px; overflow: auto; }
.cselectorcitybox .cselectorUL a { overflow: auto; }
/*下拉型组织选择器*/
.cselectororgbox { width: 33.3333%; float: left; padding-left: 10px; }
.cselectororgbox.cselectorproorgbox { padding-left: 0; }
.cselectororgbox .cselectorUL { width: 200px; overflow: auto; }
.cselectororgbox .cselectorUL a { overflow: auto; }
/*IOS型城市选择器*/
.cselectorioscitybg { position: fixed; top: 0; bottom: 0; left: 0; right: 0; background: rgba(0,0,0,0.7); z-index: 110; }
.cselectorioscitybox { position: fixed; bottom: 0; left: 0; right: 0; background: #fff; padding-top: 5px; z-index: 111; border-top: solid 5px #f8f8f8; }
.cselectorioscitybox .btn { width: 100%; margin-top: 5px; border-radius: 0; }
.cselectorioscity { width: 33.3333%; height: 320px; overflow: auto; float: left; text-align: center; }
.cselectorioscity a { height: 36px; line-height: 36px; display: block; overflow: hidden; }
.cselectorioscity a.select { background: #eee; }
/*下拉列表 abcd选择器模式*/
.cselectorPanel { min-width: 500px; border-width: 1px; }
.cselectorPanel .panelbtns { border-bottom: dotted 1px #cdcdcd; _display: none; }
.cselectorPanel .panelbtns b { margin: 0 10px; cursor: pointer; }
.cselectorPanel a { width: 96px; float: left; overflow: hidden; }
/*单选按钮*/
.cselectorRadio { }
.cselectorRadioUL { line-height: 34px; padding: 0; overflow: hidden; }
.cselectorRadioUL a { display: inline-block;vertical-align: middle;background: url(../images/r1.png) no-repeat left center; line-height: 19px; padding: 0px 0 0px 18px; margin: 0 5px 0 0;color:#333; }
.cselectorRadioUL a:hover { text-decoration: none; }
.cselectorRadioUL .select { background-image: url(../images/r2.png); }
.cselectorRadioUL.cselectorReadonly a{background: url(../images/r1R.png) no-repeat left center;}
.cselectorRadioUL.cselectorReadonly .select{background: url(../images/r2R.png) no-repeat left center;}
/*选项卡式单选*/
.cselectorRadioTabs { width: 98%; margin: 0 auto; }
.cselectorRadioTabs .cselectorRadioUL { border: 1px solid #efefef; padding: 0; border-width: 0 0 1px 0; overflow: visible; }
.cselectorRadioTabs .cselectorRadioUL a { display: inline-block; margin: 0; background: none; padding: 0 20px; text-align: center; }
.cselectorRadioTabs .cselectorRadioUL a span { display: inline-block; line-height: 36px; padding: 11px 0; }

.smcselectorRadioTabs { margin-bottom: 5px; }
.smcselectorRadioTabs .cselectorRadioUL a { background: none; padding: 0 3px; }
.smcselectorRadioTabs .cselectorRadioUL a span { padding: 10px 0; }
/*搜索条件式单选*/
.cselectorRadioss { }
.cselectorRadioss .cselectorRadioUL { border: none; padding: 0px; overflow: visible; }
.cselectorRadioss .cselectorRadioUL a { background: none; padding: 1px 5px; margin: 0 10px 0 0; }
/*卡片式*/
.cselectorRadiokp { width: 98%; }
.cselectorRadiokp .cselectorRadioUL { border: none; padding: 0px; overflow: visible; }
.cselectorRadiokp .cselectorRadioUL a { background: none; padding: 0px; margin: 0px; }
.cselectorRadiokp .cselectorRadioUL a span { display: inline-block; padding: 12px 15px; }
/*搜索式城市选择器*/
.cselectorsscitybox { }
.cselectorsscitybox .cselectorRadioUL { border: none; padding: 0px; overflow: visible; }
.cselectorsscitybox .cselectorRadioUL a { background: none; padding: 1px 5px; margin: 0 10px 0 0; }
.cselectorsscitybox .cselectorRadioUL b { margin: 0 5px 0 10px; }
/*复选按钮*/
.cselectorCheckBox { }
.cselectorCheckBoxUL { line-height: 34px; padding: 0; border-radius: 4px; overflow: hidden; }
.cselectorCheckBoxUL a {display: inline-block;vertical-align: middle; background: url(../images/c1.png) no-repeat left center; line-height: 19px; padding: 0px 0 0px 18px; margin: 0 5px 0 0; color:#333;}
.cselectorCheckBoxUL a:hover { text-decoration: none; }
.cselectorCheckBoxUL .select { background-image: url(../images/c2.png); }
.cselectorCheckBoxUL.cselectorReadonly a{background: url(../images/c1R.png) no-repeat left center;}
.cselectorCheckBoxUL.cselectorReadonly .select{background: url(../images/c2R.png) no-repeat left center;}
.cselectorImage { }
/*复选下拉*/
.cselectorCheckList { }
.cselectorCheckListUL a, .panelallbtns .checklistall { background: url(../images/c1.png) no-repeat 3px center;  padding: 0px 0 0px 25px; margin: 0 5px 0 0; }
.cselectorCheckListUL a:hover { text-decoration: none; }
.cselectorCheckListUL .select,.panelallbtns .checklistall.select { background-image: url(../images/c2.png); }
.cselectorCheckListUL a.readonly { background-image: none; padding: 0px 0 0px 10px; }
/*始终显示的下拉框*/
.onlyshow .cselectorInput { display: none; }
.onlyshow .cselectorUL { display: block; border: none; max-height: 1000px; }
.showother { }
.showother input { width: auto; float: right; }
.showother .cselectorInput { width: auto; }
/*星级评分选择器*/
.cselectorStar { }
.cselectorStarBox { overflow: hidden; border: 1px solid #ccc; border-radius: 4px; }
.cselectorStarBoxUL { position: relative; width: 105px; height: 21px; margin: 8px; background: url(../images/star.png) repeat-x; background-size: contain; float: left; }
.cselectorStarBoxUL .star { position: absolute; display: block; height: 21px; }
.cselectorStarBoxUL .star:hover, .cselectorStarBoxUL .star.hover { background: url(../images/stars.png) repeat-x; background-size: contain; }
.cselectorStarBoxUL .star1 { width: 20%; z-index: 15; }
.cselectorStarBoxUL .star2 { width: 40%; z-index: 14; }
.cselectorStarBoxUL .star3 { width: 60%; z-index: 13; }
.cselectorStarBoxUL .star4 { width: 80%; z-index: 12; }
.cselectorStarBoxUL .star5 { width: 100%; z-index: 11; }
.starshow { height: 21px; background: url(../images/stars.png) repeat-x; background-size: contain; width: 0px; display: inline-block; }
.starshow.star1 { width: 21px; }
.starshow.star2 { width: 42px; }
.starshow.star3 { width: 63px; }
.starshow.star4 { width: 84px; }
.starshow.star5 { width: 105px; }
.starshows { height: 15px; background: url(../images/stars.png) repeat-x; background-size: contain; width: 0px; float: right; }
.starshows.star1 { width: 15px; }
.starshows.star2 { width: 30px; }
.starshows.star3 { width: 45px; }
.starshows.star4 { width: 60px; }
.starshows.star5 { width: 75px; }
/*弹出框 确定按钮置灰样式*/
.dialog-button .l-btn-disabled{background: #eee;}
.dialog-button .l-btn-disabled .l-btn-text{color: #666;}
.more_close{color: #a1a1a1;font-size: 36px;font-weight: normal;cursor: pointer;}
.window .more_tool .panel-tool {height: 40px;margin-top: -30px;}
.nomalTool{display: none;}
.processBody .dialog-button{text-align: center;border: none;padding-top: 0;padding-bottom: 15px;}
.processBody .dialog-button a{margin-left: 36px;border-radius: 4px;width: 120px;height: 34px;padding-top: 4px;}
.processBody .dialog-button a:first-child{margin-left: 0;}