﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>组织人员树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <link href="../../js/jquery-ztree/css/zTreeStyle/zTreeStyle.css" rel="stylesheet" type="text/css" />
    <script src="../../js/jquery-ztree/js/jquery.ztree.core.js" type="text/javascript"></script>
    <script src="../../js/jquery-ztree/js/jquery.ztree.exhide.js" type="text/javascript"></script>
    <style type="text/css">
        .ztree li ul.line{height:auto;}
    </style>
</head>
<body class="page_body">
<input id="key" type="text"/>
<ul id="userIds" class="ztree"></ul>
<ul id="orgTree"></ul>
<div class="role orgC"></div>
<script type="text/javascript">
    var gps=getQueryString();
    function fuzzySearch(zTreeId, searchField, isHighLight, isExpand){
        var zTreeObj = $.fn.zTree.getZTreeObj(zTreeId);//get the ztree object by ztree id
        if(!zTreeObj){
            alert("fail to get ztree object");
        }
        var nameKey = zTreeObj.setting.data.key.name; //get the key of the node name
        isHighLight = isHighLight===false?false:true;//default true, only use false to disable highlight
        isExpand = isExpand?true:false; // not to expand in default
        zTreeObj.setting.view.nameIsHTML = isHighLight; //allow use html in node name for highlight use

        var metaChar = '[\\[\\]\\\\\^\\$\\.\\|\\?\\*\\+\\(\\)]'; //js meta characters
        var rexMeta = new RegExp(metaChar, 'gi');//regular expression to match meta characters

        // keywords filter function
        function ztreeFilter(zTreeObj,_keywords,callBackFunc) {
            if(!_keywords){
                _keywords =''; //default blank for _keywords
            }

            // function to find the matching node
            function filterFunc(node) {
                if(node && node.oldname && node.oldname.length>0){
                    node[nameKey] = node.oldname; //recover oldname of the node if exist
                }
                zTreeObj.updateNode(node); //update node to for modifications take effect
                if (_keywords.length == 0) {
                    //return true to show all nodes if the keyword is blank
                    zTreeObj.showNode(node);
                    zTreeObj.expandNode(node,isExpand);
                    return true;
                }
                //transform node name and keywords to lowercase
                if (node[nameKey] && node[nameKey].toLowerCase().indexOf(_keywords.toLowerCase())!=-1) {
                    if(isHighLight){ //highlight process
                        //a new variable 'newKeywords' created to store the keywords information
                        //keep the parameter '_keywords' as initial and it will be used in next node
                        //process the meta characters in _keywords thus the RegExp can be correctly used in str.replace
                        var newKeywords = _keywords.replace(rexMeta,function(matchStr){
                            //add escape character before meta characters
                            return '\\' + matchStr;
                        });
                        node.oldname = node[nameKey]; //store the old name
                        var rexGlobal = new RegExp(newKeywords, 'gi');//'g' for global,'i' for ignore case
                        //use replace(RegExp,replacement) since replace(/substr/g,replacement) cannot be used here
                        node[nameKey] = node.oldname.replace(rexGlobal, function(originalText){
                            //highlight the matching words in node name
                            var highLightText =
                                '<span style="color: whitesmoke;background-color: darkred;">'
                                + originalText
                                +'</span>';
                            return 	highLightText;
                        });
                        zTreeObj.updateNode(node); //update node for modifications take effect
                    }
                    zTreeObj.showNode(node);//show node with matching keywords
                    return true; //return true and show this node
                }

                zTreeObj.hideNode(node); // hide node that not matched
                return false; //return false for node not matched
            }

            var nodesShow = zTreeObj.getNodesByFilter(filterFunc); //get all nodes that would be shown
            processShowNodes(nodesShow, _keywords);//nodes should be reprocessed to show correctly
        }

        /**
         * reprocess of nodes before showing
         */
        function processShowNodes(nodesShow,_keywords){
            if(nodesShow && nodesShow.length>0){
                //process the ancient nodes if _keywords is not blank
                if(_keywords.length>0){
                    $.each(nodesShow, function(n,obj){
                        var pathOfOne = obj.getPath();//get all the ancient nodes including current node
                        if(pathOfOne && pathOfOne.length>0){
                            //i < pathOfOne.length-1 process every node in path except self
                            for(var i=0;i<pathOfOne.length-1;i++){
                                zTreeObj.showNode(pathOfOne[i]); //show node
                                zTreeObj.expandNode(pathOfOne[i],true); //expand node
                            }
                        }
                    });
                }else{ //show all nodes when _keywords is blank and expand the root nodes
                    var rootNodes = zTreeObj.getNodesByParam('level','0');//get all root nodes
                    $.each(rootNodes,function(n,obj){
                        zTreeObj.expandNode(obj,true); //expand all root nodes
                    });
                }
            }
        }

        //listen to change in input element
        $(searchField).bind('input propertychange', function() {
            var _keywords = $(this).val();
            searchNodeLazy(_keywords); //call lazy load
        });

        var timeoutId = null;
        var lastKeyword = '';
        // excute lazy load once after input change, the last pending task will be cancled
        function searchNodeLazy(_keywords) {
            if (timeoutId) {
                //clear pending task
                clearTimeout(timeoutId);
            }
            timeoutId = setTimeout(function() {
                if (lastKeyword === _keywords) {
                    return;
                }
                ztreeFilter(zTreeObj,_keywords); //lazy load ztreeFilter function
                // $(searchField).focus();//focus input field again after filtering
                lastKeyword = _keywords;
            }, 500);
        }
    }
    var setting = {
        view: {
            dblClickExpand: false,
            showLine: true
        },
        check: {
            enable: true
        },
        data:{
            simpleData: {
                enable: true,
                idKey: "id",
                pIdKey: "parentId",
                rootPId: null
            }
        },
        callback: {
            onClick: function(){

            }  //回调函数为单击操作
        }
    };
    $(function(){
        var ajaxopts = {
            url:'action/user/user/findAllOrgUser?appcode='+web.appCode,
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                userIdzNodes = data.data;
                //console.log(userIdzNodes);
                var a=0;
                for(var i in userIdzNodes){
                    userIdzNodes[i].open="false";
                    if(userIdzNodes[i].parentId=="OW19") a++;
                    // if(userIdzNodes[i].id=="00000000000000000000") //console.log(userIdzNodes[i]);
                    // if(userIdzNodes[i].id=="00000000000000000000") userIdzNodes[i].parentId="0";
                }
                //console.log(a);
                userIdzTree = $.fn.zTree.init($("#userIds"), setting, userIdzNodes);
                fuzzySearch('userIds','#key',null,true); //初始化模糊搜索方法
            }
        };
        ajaxgeneral(ajaxopts);
        // treeLoadSuccess();
        // $(".role").empty();
        // $("#orgTree").tree({
        //     url:"/uums/action/user/user/findOneStep?appcode="+web.appCode,
        //     // url:'action/user/user/findAllOrgUser?appcode='+web.appCode,
        //     //checkbox:true,//是否在每一个借点之前都显示复选框
        //     lines:true,//是否显示树控件上的虚线
        //     treePid:'parentId',
        //     queryParams:{"appCode":web.appCode},
        //     contentType: "application/json; charset=utf-8",
        //     //cascadeCheck:false,
        //     //onlyLeafCheck:true,
        //     onlyone:gps.multi==0?true:false,//不要乱配
        //     fileds:'id,parentId,name|text,treeType',
        //     animate:true,//节点在展开或折叠的时候是否显示动画效果
        //     onClick:function(node){
        //         if(node.treeType=="org"){
        //             if(node.children){
        //                 if(node.children.length==0) top.mesAlert("提示信息","该组织无下级组织！", 'info');
        //             }else{
        //                 ajaxgeneral({
        //                     url:"action/user/user/findOneStep?appcode="+web.appCode+"&orgCode="+node.id,
        //                     data:{"appCode":web.appCode,"orgCode":node.id},
        //                     contentType: "application/json; charset=utf-8",
        //                     success:function(data){
        //                         if(data.data.length==0){
        //                             top.mesShow("温馨提示","该组织无下级数据！", 2000);
        //                         }else{
        //                             for(var i in data.data){
        //                                 data.data[i].text=data.data[i].name;
        //                             }
        //                             $("#orgTree").tree("append", {
        //                                 parent : node.target,
        //                                 data : data.data
        //                             });
        //                         }
        //                     }
        //                 });
        //             }
        //         }
        //     },
        //     onBeforeSelect:function(node){
        //         if(node.treeType=="org") return false;
        //         if(gps.multi==0){
        //             var nodes=$("#orgTree").tree("getChecked");
        //             for(var i in nodes){
        //                 //var nodei=$("#orgTree").tree("find",nodes[i].id);
        //                 $("#orgTree").tree("uncheck",nodes[i].target);
        //             }
        //             $(".role").html("");
        //         }
        //         if(!(getObjects("id,name",node.id+","+node.name))) $(".role").append("<a name='"+node.name+"' id='"+node.id+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
        //     }
        // });
        // $(document).on("click",".role a i",function(){
        //     $(this).parent("a").remove();
        // });
    });
    //是否有该条数据
    function getObjects(idas,keyas){
        var a=false;
        var ids=idas.split(",");
        var idkeys=keyas.split(",");
        var b=0;
        $(".role a").each(function(i,v){
            for(var i in ids){
                if ($(v).attr(ids[i]) == idkeys[i]) {
                    b++;
                }
            }
            if(b==ids.length) {
                a=true;
                return false;
            }
        });
        return a;
    };
    //数据加载成功
    function treeLoadSuccess(){
        var chooseRow=top.chooseWeb[gps.name]?top.chooseWeb[gps.name].data:[];
        for(var i in chooseRow){
            if(!(getObjects("id,name",chooseRow[i].id+","+chooseRow[i].name))) $(".role").append("<a name='"+chooseRow[i].name+"' id='"+chooseRow[i].id+"'><font>"+chooseRow[i].name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
        }
    };
    window.getchoosedata=function(){
        var datas=[];
        $(".role a").each(function(i,v){
            var data={};
            data.id=$(v).attr("id");
            data.name=$(v).children("font").html();
            datas.push(data);
        });
        return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    };
</script>
</body>
</html>
