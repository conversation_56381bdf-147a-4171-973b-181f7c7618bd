/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;

import java.util.List;

/**
 * <strong>Title : IExamQuestionUserService</strong><br>
 * <strong>Description : 人员试卷题目Service </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR> k<PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IExamQuestionUserService extends ILogicService<ExamQuestionUser,String> {

    /**
     * 获取人员试卷题目
     * @param bankCode
     * @return
     */
    List<ExamQuestionUser> getExamQuestionUserByBankCode(String bankCode,String username);

    List<ExamQuestionUser> getExamQuestionUserByBankCode(String[] questionCode,String bankCode,String username);
}
