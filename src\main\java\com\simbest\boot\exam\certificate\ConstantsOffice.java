/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate;

/**
 * <strong>Title : ConstantsOffice</strong><br>
 * <strong>Description :  </strong><br>
 * <strong>Create on : 2020/11/11</strong><br>
 * <strong>Modify on : 2020/11/11</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public class ConstantsOffice {


    /**
     * 私有构造方法，不允许new操作
     */
    private ConstantsOffice(){

    }

    /**
     * 转换PDF文件服务依赖地址
     */
    public static final String LIBRE_OFFICE_PATH_WINDOWS = "D:\\Program Files\\LibreOffice";
    public static final String LIBRE_OFFICE_PATH_LINUX = "/opt/libreoffice6.0";
    public static final String LIBRE_OFFICE_PATH_MAC = "/Application/openOfficeSoft";

    public static final String IMAGE_SUFFIX = "jpg";

    public static final String IMAGE_SCALE_STR = "2";

    /**
     * 模板目录
     */
    //public static final String TEMPLATE_FILE_PATH = "/home/<USER>/simbestboot/uploadFiles/exam/template/template.docx";
    public static final String TEMPLATE_FILE_PATH = "/bpsAttachment/bpsAttachment/exam/template/template.docx";

    /**
     * 生成文件临时目录
     */
    //public static final String TEMP_FILE_PATH = "/home/<USER>/simbestboot/uploadFiles/exam/tmp";
    public static final String TEMP_FILE_PATH = "/bpsAttachment/bpsAttachment/exam/tmp";
}
