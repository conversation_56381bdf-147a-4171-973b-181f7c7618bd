<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>组织人员树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .clear{position: fixed;top: 10px;right: 10px; display: inline-block;width: 65px;height: 26px;line-height: 26px;text-align: center;background-color: #e34d4d;color:#fff;border-radius: 2px;z-index:9}
        .orgC{padding-top: 35px;}
    </style>
</head>
<script type="text/javascript">
    var gps=getQueryString();
    var hasChoosedUsers = '';
    var hasChoosedGroup = '';
    // getCurrent(getTree);
    $(function(){
        $('#noData').hide();
        treeLoadSuccess();
        getTree();

        //删除收信人时在已选择的收信人标记中删除
        $(document).on("click",".role a i",function(){
            var str = $(this).parent("a").attr('groupId');
            var str1 = $(this).parent("a").attr('id');
            hasChoosedGroup = hasChoosedGroup.replace(str, "");
            hasChoosedUsers = hasChoosedUsers.replace(str1, "");
            $(this).parent("a").remove();
        });
        $(".role").html("");
        $(document).on("click",".clear",function(){
            hasChoosedGroup = '';
            hasChoosedUsers = '';
            $(".role").html("");
        });
    });
    function getTree(){
        $("#orgTree").tree({
            // url: "base/group/findGroupUserTree",//checkbox:true,//是否在每一个借点之前都显示复选框
            url:"network/group/findAllGroupNoPage",
            lines:true,//是否显示树控件上的虚线
            treePid:'parentId',
            queryParams:{"useFlag":true},
            contentType: "application/json; charset=utf-8",
            //cascadeCheck:false,
            //onlyLeafCheck:true,
            onlyone:gps.multi==0?true:false,//不要乱配
            fileds:'id,parentId,name|text,treeType,orgDisplayName,preferredMobile,iconCls,parentName,childList,belongDepartmentCode',
            animate:true,//节点在展开或折叠的时候是否显示动画效果
            onLoadSuccess:function(node, data){
                console.log("onLoadSuccess");

                if(data.length==0){
                    $('#orgTree').hide();
                    $('#noData').show();
                }
            },
            onClick:function(node){
                console.log(node);
                console.log("onClick");

            },
            beforeSelect:function (node) {
                console.log(node);
              console.log("onBeforeSelect");  
            },
            onSelect:function(node){
                if(node.treeType=="group"){
                    if(node.children){
                        if(node.children.length==0) top.mesAlert("提示信息","该组织无下级组织！", 'info');
                    }else{
                        console.log("onSelect");

                        // ajaxgeneral({
                        //     url:"network/group/findGroupById?id="+node.id,
                        //     data:{"appCode":web.appCode,"orgCode":node.id},
                        //     contentType: "application/json; charset=utf-8",
                        //     success:function(data){
                        //         if(data.data.userInfoSet.length==0){
                        //             top.mesShow("温馨提示","该组织无下级数据！", 2000);
                        //         }else{
                        //             for(var i in data.data.userInfoSet){
                        //                 data.data.userInfoSet[i].text=data.data.userInfoSet[i].truename;
                        //                 data.data.userInfoSet[i].iconCls=data.data.userInfoSet[i].treeType?"tree-org":"tree-user";
                        //                 // data.data.userInfoSet[i].iconCls="tree-org";
                        //             }
                        //             $("#orgTree").tree("append", {
                        //                 parent : node.target,
                        //                 data : data.data.userInfoSet
                        //             });
                        //         }
                        //     }
                        // });
                    }
                }
            },


            //双击操作
            onClick:function(node){
                console.log("onDblClick");
                console.log(node)
                // ajaxgeneral({
                //             url:"network/group/findGroupById?id="+node.id,
                //             data:{"appCode":web.appCode,"orgCode":node.id},
                //             contentType: "application/json; charset=utf-8",
                //             success:function(data){
                //                 //如果双击分组则把分组添加入收信方
                //                 if(node.treeType=="group"&&isNotChoosedGroup(node)){
                                    hasChoosedGroup = hasChoosedGroup +','+ node.id;
                                    $(".role a").each(function (i,v) {
                                        if ($(v).attr('groupId') == node.id) {
                                            $(v).remove();
                                        }
                                        
                                    })
                                    $(".role").append("<a groupName='"+node.name+"' dataType='group'  groupId='"+node.id+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                                    var seleted = $(".role a");

                                // }
                            // }
                        // })
            },
            onBeforeSelect:function(node){
                if(gps.multi==0){
                    var nodes=$("#orgTree").tree("getChecked");
                    for(var i in nodes){
                        $("#orgTree").tree("uncheck",nodes[i].target);
                    }
                    $(".role").html("");
                }
                if(node.treeType=='group'){
                    return;//单击不能选择分组
                }else{
                    if (isNotChoosedGroup(node)) {
                        //分组未被选择
                        if(getObjects(node.username)){
                            $(".role").append("<a name='"+node.truename+"' dataType='user' receivePhoneNum='"+node.receivePhoneNum+"' orgDisplayName='"+node.orgDisplayName+"' groupId='"+node.groupId+"'  belongDepartmentCode='"+node.belongDepartmentCode+"'  preferredMobile='"+node.preferredMobile+"' id='"+node.username+"'><font>"+node.truename+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                        } else{
                                top.mesAlert("提示", "成员【"+node.truename+"】已存在，无需再次添加！", 'warning');                            
                        }
                    }else{
                        top.mesAlert("提示", "成员【"+node.truename+"】的分组已存在，无需再次添加！", 'warning');

                    }

                }
            }
        });
    }
    //是否有该条数据
    function getObjects(id){
        var a = true;
        $(".role a").each(function(i,v){
            if($(v).attr('id')==id){
                a = false;
                return;
            }
        });
        return a;
    };
    //判断是否已选择
    // function isNotChoosed(node){
    //     if(gps.hasChoosed.indexOf(node.username)!=-1){
    //             hasChoosedUsers = hasChoosedUsers + "，" +node.username;
    //         return false;
    //     }else{
    //         return true;
    //     }
    // }
    
    //判断分组是否被选择
    function isNotChoosedGroup(node){
        //如果有node.id则说明选择的是分组
        if(node.id) {
            //判断分组是否被选中;
            if(hasChoosedGroup.indexOf(node.id)!=-1){
                return false;
            }else{
                return true;
            }
        }else { 
            // 判断人员所在的分组是否被选中
            if(hasChoosedGroup.indexOf(node.groupId)!=-1){
                return false;
            }else{
                return true;
            }
        }
    }



    //数据加载成功
    function treeLoadSuccess(){
        var chooseRow=top.chooseWeb[gps.name]?top.chooseWeb[gps.name].data:[];
        for(var i in chooseRow){
            if(getObjects(chooseRow[i].username)) $(".role").append("<a name='"+chooseRow[i].truename+" id='"+chooseRow[i].username+"' preferredMobile='"+chooseRow[i].preferredMobile+"' orgDisplayName='"+chooseRow[i].orgDisplayName+"' parentName='"+chooseRow[i].parentName+"' belongDepartmentCode='"+chooseRow[i].belongDepartmentCode+"'><font>"+chooseRow[i].truename+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
        }
    };
    window.getchoosedata=function(){
        var datas=[];
        $(".role a").each(function(i,v){
            var data={};

            data.username=$(v).attr("id");

            if (data.username) {
                data.truename=$(v).children("font").html();
                data.preferredMobile=$(v).attr("preferredMobile");
                data.orgDisplayName=$(v).attr("orgDisplayName");
                // data.receivePhoneNum=$(v).attr("receivePhoneNum");        
                data.belongDepartmentCode=$(v).attr("belongDepartmentCode");
            }else{
                data.groupName = $(v).attr("groupName");
                data.groupId = $(v).attr("groupId");
            }

            data.dataType = $(v).attr("dataType");
            datas.push(data);
        });

        return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    };
</script>
<body class="page_body">
<ul id="orgTree"></ul>
<div id="noData" style="height:300px;line-height: 300px;width: 350px;text-align: center;color:#bbb;">暂无分组</div>
<div class="clear">全部清空</div>
<div class="role orgC"></div>
</body>
</html>
