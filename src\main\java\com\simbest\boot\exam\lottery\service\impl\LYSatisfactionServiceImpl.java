package com.simbest.boot.exam.lottery.service.impl;

import com.google.common.collect.Lists;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.lottery.model.LYSatisfaction;
import com.simbest.boot.exam.lottery.model.LyJackpot;
import com.simbest.boot.exam.lottery.repository.LYSatisfactionRepository;
import com.simbest.boot.exam.lottery.service.ILYSatisfactionService;
import com.simbest.boot.exam.lottery.service.ILyJackpotService;
import com.simbest.boot.exam.util.ProbabilityUtils;
import com.simbest.boot.security.IPosition;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
@Slf4j
public class LYSatisfactionServiceImpl extends LogicService<LYSatisfaction,String> implements ILYSatisfactionService {
  @Autowired
  private LYSatisfactionRepository repository;
    @Autowired
    public LYSatisfactionServiceImpl(LYSatisfactionRepository repository){
        super(repository);
        this.repository=repository;

    }
    @Autowired
    private ILyJackpotService iLyJackpotService;

    //中奖概率
    @Value("${app.exam.lottery.probability}")
    private int PROBABILITY;


    @Override
    public JsonResponse isLySatisfaction(String type,String examCode) {
        //根据工具类获取是否中奖 中奖概率为固定25%

        IUser user = SecurityUtils.getCurrentUser();
        //洛阳移动工会满意度问卷调查抽奖表
        LYSatisfaction lySatisfaction=new LYSatisfaction();
        lySatisfaction.setExamCode(examCode);
        lySatisfaction.setUsername(user.getUsername());
        lySatisfaction.setTruename(user.getTruename());
        lySatisfaction.setPhone(user.getPreferredMobile());
        lySatisfaction.setEmail(user.getEmail());

        Set<? extends IPosition> authPositions = user.getAuthPositions();
        List<String> positionName = Lists.newArrayList();
        for (IPosition authPosition : authPositions) {
            positionName.add(authPosition.getPositionName());
        }
        lySatisfaction.setDuty(StringUtils.join(positionName, ","));

        String belongCompanyTypeDictValue = user.getBelongCompanyTypeDictValue();
        if ("02".equals(belongCompanyTypeDictValue)||"01".equals(belongCompanyTypeDictValue)){
            lySatisfaction.setBelongCompanyName(user.getBelongCompanyName());
            lySatisfaction.setBelongCompanyCode(user.getBelongCompanyCode());
            lySatisfaction.setBelongDepartmentName(user.getBelongDepartmentName());
            lySatisfaction.setBelongDepartmentCode(user.getBelongDepartmentCode());
        }
        if ("03".equals(belongCompanyTypeDictValue)){
            lySatisfaction.setBelongCompanyName(user.getBelongCompanyNameParent());
            lySatisfaction.setBelongCompanyCode(user.getBelongCompanyCodeParent());
            lySatisfaction.setBelongDepartmentName(user.getBelongCompanyName());
            lySatisfaction.setBelongDepartmentCode(user.getBelongCompanyCode());
        }
        lySatisfaction.setBelongCompanyTypeDictValue(user.getBelongCompanyTypeDictValue());
        lySatisfaction.setBelongOrgCode(user.getBelongOrgCode());
        lySatisfaction.setBelongOrgName(user.getBelongOrgName());
        //默认不中奖
        String isWinner="0";
        log.info("当前中奖概率为：{}", PROBABILITY);
        boolean isLottery = ProbabilityUtils.IsHit(PROBABILITY);
        if(isLottery){
            //中奖后判断奖池是否有剩余奖项
            LyJackpot lyJackpot = iLyJackpotService.findLyJackpotIsResidue(type);
            int remain = lyJackpot.getRemain();
            if(remain>0){
                remain--;
                lyJackpot.setRemain(remain);
                iLyJackpotService.update(lyJackpot);
                isWinner="1";
                lySatisfaction.setIsWinner(isWinner);
                this.insert(lySatisfaction);
                return JsonResponse.success(true);
            }

        }
        lySatisfaction.setIsWinner(isWinner);
        this.insert(lySatisfaction);
        return JsonResponse.success(false);
    }


}
