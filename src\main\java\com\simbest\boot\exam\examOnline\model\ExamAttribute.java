/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;/**
 * Created by KZH on 2019/10/8 10:23.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 10:23
 * @desc 试卷属性表
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_attribute")
@ApiModel(value = "试卷属性")
public class ExamAttribute extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EA") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "试卷名称",name = "examName",example = "2019年河南公司业务支撑中心反腐倡廉教育考试",required = true)
    private String examName;

    @Column(length = 2000)
    @ApiModelProperty(value = "试卷备注",name = "examRemark",example = "考试时间60分钟，共四道大题，其中单选题14道每道2分，多选题10道题每题4分，判断题7道每题2分，简答题2道每题9分，共计100分。",required = true)
    private String examRemark;

    @Column(length = 2000)
    @ApiModelProperty(value = "试卷备注-底部说明",name = "examRemarkFooter",example = "感谢您的支持和参与！我们将认真对待您的反馈。",required = false)
    private String examRemarkFooter;

    @Column(length = 40)
    @ApiModelProperty(value = "系统试卷",name = "examAppCode",example = "hnjjwz",required = true)
    private String examAppCode;// 当前字段应是试卷编码、与考试编码无关

    @Column(length = 40)
    @ApiModelProperty(value = "总题目数",name = "topicSum",example = "30",required = true)
    private String topicSum;

    @Column(length = 40)
    @ApiModelProperty(value = "题库编码",name = "questionBankCode",example = "A-001",required = true)
    private String questionBankCode;

    @Column(length = 40)
    @ApiModelProperty(value = "单选题比例",name = "single",example = "20")
    private String single;

    @Column(length = 40)
    @ApiModelProperty(value = "多选题比例",name = "more",example = "20")
    private String more;

    @Column(length = 40)
    @ApiModelProperty(value = "判断题比例",name = "judge",example = "20")
    private String judge;

    @Column(length = 40)
    @ApiModelProperty(value = "填空题比例",name = "filling",example = "20")
    private String filling;

    @Column(length = 40)
    @ApiModelProperty(value = "简答题比例",name = "shortAnswer",example = "20")
    private String shortAnswer;

    @Column(length = 40)
    @ApiModelProperty(value = "试卷规定时间限制",name = "setTime",example = "15",required = true)
    private String setTime;

    @Column(length = 20)
    @ApiModelProperty(value = "出题方式",name = "topicStyle",example = "random",required = true)
    private String topicStyle;

    @Column(length = 20)
    @ApiModelProperty(value = "随机题目数量",name = "topicNumber",example = "10",required = true)
    private String topicNumber;

    @ApiModelProperty(value = "提交检验类型 ,空/0：无校验 ，  1:多选答案数量校验  ")
    private Integer checkType;

    @ApiModelProperty(value = "特殊规则配置")
    @Column(length = 2000)
    private String specialRuleConfig;

    @Transient
    private List<ExamQuestion> singleQuestionList;

    @Transient
    private List<ExamQuestion> moreQuestionList;

    @Transient
    private List<ExamQuestion> judgeQuestionList;

    @Transient
    private List<ExamQuestion> fillingQuestionList;

    @Transient
    private List<ExamQuestion> shortAnswerQuestionList;

    @Transient
    private List<ExamQuestion> indefiniteQuestionList;

}
