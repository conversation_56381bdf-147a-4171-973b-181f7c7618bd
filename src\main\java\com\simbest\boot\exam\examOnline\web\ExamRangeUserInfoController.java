package com.simbest.boot.exam.examOnline.web;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.controller.GenericController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfo;
import com.simbest.boot.exam.examOnline.service.IExamRangeUserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-04-23
 * @desc 考试范围群组 人员信息控制器
 **/

@Api(description = "ExamRangeUserInfoController", tags = {"考试信息模块-考试人员范围群组--人员--信息控制器"})
@Slf4j
@RestController
@RequestMapping("/action/rangeUserInfo")
public class ExamRangeUserInfoController extends GenericController<ExamRangeUserInfo, String> {
    private IExamRangeUserInfoService service;

    @Autowired
    public ExamRangeUserInfoController(IExamRangeUserInfoService service) {
        super(service);
        this.service = service;
    }

    @ApiOperation(value = "保存群组下的人员信息", notes = "保存群组下的人员信息")
    @PostMapping(value = {"/saveExamRangeUserInfo", "/sso/saveExamRangeUserinfo", "/api/saveExamRangeUserinfo"})
    public JsonResponse saveExamRangeUserInfo(@RequestParam String groupId,
                                              @RequestBody List<ExamRangeUserInfo> examRangeUserInfos) {
        try {
            return service.saveExamRangeUserInfo(groupId, examRangeUserInfos);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return JsonResponse.success("操作失败!");
    }

    @ApiOperation(value = "根据群组编号查询该群组下的人员信息", notes = "根据群组编号查询该群组下的人员信息")
    @PostMapping(value = {"/findAllUserInfoByGroupId", "/sso/findAllUserInfoByGroupId", "/api/findAllUserInfoByGroup"})
    public JsonResponse findAllUserInfoByGroup(@RequestParam(required = false) Integer page,
                                               @RequestParam(required = false) Integer size,
                                               @RequestParam String groupId,
                                               @RequestBody(required = false) Map<String, String> map) {
        return service.findByGroupId(page, size, groupId, map.get("username"), map.get("truename"), map.get("displayName"));
    }

    @ApiOperation(value = "根据群组编号查询该群组下的人员信息不分页", notes = "根据群组编号查询该群组下的人员信息不分页")
    @PostMapping(value = {"/findByGroupIdNoPage", "/sso/findByGroupIdNoPage", "/api/findByGroupIdNoPage"})
    public JsonResponse findAllUserInfoByGroupNoPage(@RequestParam String groupId) {
        return JsonResponse.success(service.findByGroupIdNoPage(groupId));
    }

    @ApiOperation(value = "根据id删除群组人员信息", notes = "根据id删除群组人员信息")
    @PostMapping(value = {"/delById", "/sso/delById", "/api/delById"})
    public JsonResponse delById(@RequestParam String id) {
        return service.delById(id);
    }

    @ApiOperation(value = "根据id进行批量删除群组人员信息", notes = "根据id进行批量删除群组人员信息")
    @PostMapping(value = {"/delByIds", "/sso/delByIds", "/api/delByIds"})
    public JsonResponse delByIds(@RequestBody(required = false) String[] ids) {
        return service.delByIds(ids);
    }

    @ApiOperation(value = "根据uums的OA账号、群组编码模糊查询群组下的人员信息", notes = "根据uums的OA账号、群组编码模糊查询群组下的人员信息")
    @PostMapping(value = {"/findBySidAndUserName", "/sso/findBySidAndUserName", "/api/findBySidAndUserName"})
    public JsonResponse findBySidAndUserName(@RequestParam(required = false, defaultValue = "1") int page,
                                             @RequestParam(required = false, defaultValue = "10") int size,
                                             @RequestParam String groupId,
                                             @RequestBody Map<String, Object> map) {//map传的key为username
        return service.findBySidAndUserName(page, size, groupId, map);
    }
    /**
     * 快速复制考试
     */
    @ApiOperation(value = "导入考试人员信息（接收待办，统一待办）", notes = "导入考试人员信息（接收待办，统一待办）")
    @PostMapping(value = {"/importRangeUserInfo", "/sso/importRangeUserInfo", "/api/importRangeUserInfo", "importRangeUserInfo/anonymous"})
    public JsonResponse importRangeUserInfo(HttpServletRequest request,
                                            HttpServletResponse response,@RequestParam String groupId) {
        return service.importRangeUserInfo(request,response,groupId);
    }

}
