package com.simbest.boot.exam.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_information")
@ApiModel(value = "答题记录表")
public class ExamInformation extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EIM") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "答题人",name = "publishUsername",example = "hadmin",required = true)
    private String publishUsername;

    @Column(length = 250)
    @ApiModelProperty(value = "答题人性别",name = "sex",example = "男",required = true)
    private String sex;

    @ExcelVOAttribute(name = "答题人姓名", column = "A")
    @ApiModelProperty(value = "答题人",name = "publishTruename",example = "管理员",required = true)
    private String publishTruename;

    @Column(length = 200)
    @ApiModelProperty(value = "答题人所在公司编码",name = "departmentCode",example = "2700526267653981965")
    private String companyCode;

    @Column(length = 200)
    @ExcelVOAttribute(name = "所在部门", column = "B")
    @ApiModelProperty(value = "答题人所在公司名称",name = "departmentName",example = "业务支撑中心")
    private String companyName;

    @Column(length = 200)
    @ApiModelProperty(value = "答题人职务信息",name = "positionName",example = "资深经理")
    private String positionName;

    @Column(length = 40)
    @ApiModelProperty(value = "剩余时间",name = "residueTime",example = "20",required = true)
    private String residueTime;

    @Column(length = 40)
    @ExcelVOAttribute(name = "最终成绩", column = "C")
    @ApiModelProperty(value = "得分",name = "score",example = "100",required = true)
    private Integer score;

    @Column(length = 40)
    @ExcelVOAttribute(name = "答题次数", column = "D")
    @ApiModelProperty(value = "答题次数",name = "examNumber",example = "1")
    private Integer examNumber;

    @Column(length = 40)
    @ApiModelProperty(value = "考试编码",name = "examCode")
    private String examCode;

    @Column(length = 40)
    @ApiModelProperty(value = "试卷编码",name = "examAppCode",example = "hnjjwz",required = true)
    private String examAppCode;

    @Column(length = 80)
    @ApiModelProperty(value = "题目编码",name = "questionCode",example = "A-001-1")
    private String questionCode;

    @Column(length = 80)
    @ApiModelProperty(value = "题目号码",name = "questionIndex",example = "1")
    private Integer questionIndex;

    @Column(length = 2000)
    @ApiModelProperty(value = "补充信息",name = "massage",example = "补充信息")
    private String massage;

    @Column(length = 2000)
    @ApiModelProperty(value = "补充信息",name = "message",example = "补充信息")
    private String message;

    @Column(length = 40)
    @ApiModelProperty(value = "答案记录",name = "examAnswer",example = "A/B/C/B,C/A,B,C/")
    private String examAnswer;

    @ApiModelProperty(value = "是否完成试卷",name = "isFinishExam",example = "1")
    private Boolean isFinishExam;

    @ApiModelProperty(value = "题目大类",name = "questionCategory",example = "员工有感知")
    private String questionCategory;

    @ApiModelProperty(value = "是否完成阅卷",name = "isMarkingExam",example = "1")
    private Boolean isMarkingExam;

    @Transient
    private List<ExamInformation> examInformationList;

}
