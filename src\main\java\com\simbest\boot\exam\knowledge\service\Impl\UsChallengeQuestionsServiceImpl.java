/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service.Impl;/**
 * Created by KZH on 2019/10/8 16:07.
 */


import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.component.distributed.lock.DistributedRedisLock;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionService;
import com.simbest.boot.exam.knowledge.model.UsAnswerRecord;
import com.simbest.boot.exam.knowledge.model.UsUserAnswers;
import com.simbest.boot.exam.knowledge.service.IUsAnswerRecordService;
import com.simbest.boot.exam.knowledge.service.IUsChallengeQuestionsService;
import com.simbest.boot.exam.knowledge.service.IUsUserAnswersService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class UsChallengeQuestionsServiceImpl implements IUsChallengeQuestionsService {
    @Autowired
    LoginUtils loginUtils;

    @Autowired
    IUsUserAnswersService userAnswersService;

    @Autowired
    IExamQuestionAnswerService iExamQuestionAnswerService;

    @Autowired
    SysDictValueService sysDictValueService;

    @Autowired
    IExamQuestionService iExamQuestionService;

    @Autowired
    IUsAnswerRecordService usAnswerRecordService;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Override
    public JsonResponse getAnswersList(String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        Integer challengeQuestionCount =Integer.valueOf( sysDictValueService.findByDictType("challengeQuestionCount").get(0).getValue());//日常答题数量
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
//        List<ExamQuestion> randomQuestionsByCategory = iExamQuestionService.getRandomQuestionsByCategory(knowledgeQuestionBankCode, challengeQuestionCount);
        List<ExamQuestion> randomQuestionsByCategory = iExamQuestionService.getRandomQuestionsByCategoryWithOutAnswer(knowledgeQuestionBankCode, challengeQuestionCount);
        return JsonResponse.success(randomQuestionsByCategory);
    }

    @Override
    public JsonResponse answersRecordCheck(String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        Integer challengeAnswerCount =Integer.valueOf( sysDictValueService.findByDictType("challengeAnswerCount").get(0).getValue());
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
        //先判断今日答题数量书否已用完
        IUser currentUser = SecurityUtils.getCurrentUser();
        List<UsAnswerRecord> usAnswerRecordList = usAnswerRecordService.findTodyAnswerRecordByWorkType(knowledgeQuestionBankCode, Constants.ANSWER_RECORD_CHALLENGE,currentUser.getUsername());
        Map<String, Object> resultMap = new HashMap<>();

        resultMap.put("toAnswer",challengeAnswerCount-usAnswerRecordList.size());
        resultMap.put("answerd",usAnswerRecordList.size());
        return JsonResponse.success(resultMap);
    }

    @Override
    public JsonResponse saveRecord(String workType, String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        String key = currentUser.getUsername() + "-" + workType;
        try {
            DistributedRedisLock.lock(key , 20);
            //校验答题次数是否用完
            Integer challengeAnswerCount =Integer.valueOf( sysDictValueService.findByDictType("challengeAnswerCount").get(0).getValue());
            String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
            List<UsAnswerRecord> usAnswerRecordList = usAnswerRecordService.findTodyAnswerRecordByWorkType(knowledgeQuestionBankCode, Constants.ANSWER_RECORD_CHALLENGE,currentUser.getUsername());
            if (challengeAnswerCount==usAnswerRecordList.size()){
                return JsonResponse.fail("您今天已完成挑战答题，请明天再继续挑战！");
            }

            UsAnswerRecord usAnswerRecord = new UsAnswerRecord();
            usAnswerRecord.setExamCode(knowledgeQuestionBankCode);
            usAnswerRecord.setAnsewersUserName(currentUser.getUsername());
            usAnswerRecord.setAnsewersTrueName(currentUser.getTruename());
            usAnswerRecord.setWorkType(workType);
            usAnswerRecord.setBelongCompanyCode(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyCodeParent() : currentUser.getBelongCompanyCode() );
            usAnswerRecord.setBelongCompanyName(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyNameParent() : currentUser.getBelongCompanyName());
            usAnswerRecord.setBelongDepartmentCode(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyCode() : currentUser.getBelongDepartmentCode());
            usAnswerRecord.setBelongDepartmentName(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyName() : currentUser.getBelongDepartmentName());
            usAnswerRecord.setBelongOrgCode(currentUser.getBelongOrgCode());
            usAnswerRecord.setBelongOrgName(currentUser.getAuthOrgs().iterator().next().getDisplayName());
            usAnswerRecord.setBelongCompanyTypeDictValue(currentUser.getBelongCompanyTypeDictValue());
            usAnswerRecordService.insert(usAnswerRecord);
            return JsonResponse.success(usAnswerRecord);
        } catch (Exception e) {
            log.error("--->>>saveRecord接口异常，{}",e.toString());
            return JsonResponse.fail("答题开始失败，请联系管理员");
        }finally {
            DistributedRedisLock.unlock(key);
        }
    }

    @Override
    public JsonResponse saveAnswer(Map<String, Object> requestParam, String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
        Integer dailyAnswerCount =Integer.valueOf( sysDictValueService.findByDictType("dailyAnswerCount").get(0).getValue());//日常答题次数

        String answerRecordId = MapUtil.getStr(requestParam, "answerRecordId");//用户答题记录表主键id
        String questionId = MapUtil.getStr(requestParam, "questionId");//问题ID
        String questionCode = MapUtil.getStr(requestParam, "questionCode");//问题Code
        String chosenAnswer = MapUtil.getStr(requestParam, "chosenAnswer");//用户回答的答案
        Assert.notNull(answerRecordId, "answerRecordId不能为空，检查参数！");
        Assert.notNull(questionId, "questionId不能为空，检查参数！");
        Assert.notNull(questionCode, "questionCode不能为空，检查参数！");

        List<UsUserAnswers> userAnswersList= userAnswersService.findUsUserAnswersByQuestionIdAndAnswerRecordId(questionId,answerRecordId);
        if (userAnswersList.size()>0){
            return JsonResponse.fail(null,null,204);
        }

        if (StrUtil.isEmpty(chosenAnswer)){
            return JsonResponse.success("0",null);

        }
        ExamQuestion examQuestion = iExamQuestionService.findAllByQuestionCode(questionCode);
        if (examQuestion==null){
            return  JsonResponse.fail("未查询到对应试题");
        }

        //判断答题是否正确
        List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
        List<ExamQuestionAnswer> examQuestionAnswers = examQuestionAnswerList.stream().filter(examQuestionAnswer -> examQuestionAnswer.getIsCorrect()!=null&&true == examQuestionAnswer.getIsCorrect()).collect(Collectors.toList());
        List<String> collect = examQuestionAnswers.stream().map(examQuestionAnswer -> examQuestionAnswer.getAnswerCode()).collect(Collectors.toList());

        List<String> chosenAnswerList = Arrays.asList(chosenAnswer.split(","));
        Collections.sort(chosenAnswerList);
        Collections.sort(collect);
        String isCorrect="0";
        if (examQuestionAnswers.size()>0&&chosenAnswerList.equals(collect)){//正确
            isCorrect="1";
        }
        UsUserAnswers usUserAnswers = new UsUserAnswers();
        usUserAnswers.setAnswerRecordId(answerRecordId);
        usUserAnswers.setAnsewersUserName(currentUser.getUsername());
        usUserAnswers.setAnsewersTrueName(currentUser.getTruename());
        usUserAnswers.setQuestionBankCode(knowledgeQuestionBankCode);
        usUserAnswers.setQuestionId(questionId);
        usUserAnswers.setQuestionCode(questionCode);
        usUserAnswers.setChosenAnswer(chosenAnswer);
        usUserAnswers.setTrueAnswer(String.join(",",collect));
        usUserAnswers.setIsCorrect(isCorrect);
        usUserAnswers.setScore(examQuestion.getQuestionScore());
        usUserAnswers.setAnswerTime(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
        usUserAnswers.setWorkType(Constants.ANSWER_RECORD_CHALLENGE);
        usUserAnswers.setBelongCompanyCode(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyCodeParent() : currentUser.getBelongCompanyCode() );
        usUserAnswers.setBelongCompanyName(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyNameParent() : currentUser.getBelongCompanyName());
        usUserAnswers.setBelongDepartmentCode(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyCode() : currentUser.getBelongDepartmentCode());
        usUserAnswers.setBelongDepartmentName(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyName() : currentUser.getBelongDepartmentName());
        usUserAnswers.setBelongOrgCode(currentUser.getBelongOrgCode());
        usUserAnswers.setBelongOrgName(currentUser.getAuthOrgs().iterator().next().getDisplayName());
        usUserAnswers.setBelongCompanyTypeDictValue(currentUser.getBelongCompanyTypeDictValue());
        userAnswersService.insert(usUserAnswers);
        List <UsUserAnswers> usUserAnswersList= userAnswersService.getUsUserAnswersByRecordId(answerRecordId);
        if ("0".equals(isCorrect)||usUserAnswersList.size()>=dailyAnswerCount){
            UsAnswerRecord usAnswerRecord = usAnswerRecordService.findById(answerRecordId);
            usAnswerRecord.setEndTime(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
            Map<String,Object>   map= userAnswersService.findListByAnswerRecordId(answerRecordId);
            DateTime minTime = DateUtil.getJodaDateTime(map.get("MINTIME") + "", "yyyy-MM-dd HH:mm:ss");
            // 将毫秒转换为分钟和秒
            Date date = new Date();
            DateTime dateTime = new DateTime(date);
            Duration duration =new Duration(minTime, dateTime);
            usAnswerRecord.setDuration(duration.getMillis());
            usAnswerRecord.setEndTime(DateUtil.getDate(date,"yyyy-MM-dd HH:mm:ss"));
            usAnswerRecordService.update(usAnswerRecord);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("isCorrect",isCorrect);
        resultMap.put("realAnswer",examQuestionAnswers);
        return JsonResponse.success(resultMap);
    }
}
