package com.simbest.boot.exam.uums.web;

import com.fasterxml.jackson.core.type.TypeReference;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.AuthoritiesConstants;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.uums.service.ExtendUumsSysPermissionService;
import com.simbest.boot.security.SimplePermission;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.permission.UumsSysPermissionApi;
import com.simbest.boot.uums.api.user.UumsSysUserRoleApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc:
 * @date 2021/7/2  17:44
 */
@Api(description = "考试管理系统菜单权限相关接口",tags = "考试管理系统菜单权限控制器")
@Slf4j
@RestController
@RequestMapping(value = "/permissionManage")
public class ExtendUumsSysPermissionController {

    @Autowired
    private ExtendUumsSysPermissionService extendUumsSysPermissionService;




    @ApiOperation(value = "根据应用查询其下的所有权限不分页",notes = "根据应用查询其下的所有权限不分页")
    @PostMapping(value = {"/findAllPermissionNoPage", "/api/findAllPermissionNoPage", "/sso/findAllPermissionNoPage", "/anonymous/findAllPermissionNoPage"})
    public JsonResponse findAllPermissionNoPage() {
      return   JsonResponse.success(extendUumsSysPermissionService.findAllPermissionNoPage());
    }


    @ApiOperation(value = "用于主数据管理后台获取角色修改应用权限时的列表", notes = "用于主数据管理后台获取角色修改应用权限的列表",tags={"权限 特殊查询"})
    @PostMapping(value = {"/findAllNoPageForKey"})
    public JsonResponse findAllNoPageForKey(@RequestParam String key) {
      return  extendUumsSysPermissionService.findAllNoPageForKey(key);
    }

    @ApiOperation(value = "修改角色权限信息，先删除角色所有权限，再重新添加", notes = "修改角色权限信息，先删除角色所有权限，再重新添加")
    @PostMapping(value = {"/updateListByRoleId","/api/updateListByRoleId", "/sso/updateListByRoleId"})
    public JsonResponse updateListByRoleId(@RequestParam String permissionIds,@RequestParam String roleId) {
        String username = SecurityUtils.getCurrentUserName();
        /** 非hadmin管理员不能对考试维护菜单权限进行操作
         * **/
        if (!username.equals("hadmin") && permissionIds.contains(Constants.EXAM_PERMISSION)){
            return JsonResponse.fail("您不能对考试维护菜单进行操作");
        }
        return JsonResponse.success(extendUumsSysPermissionService.updateListByRoleId(permissionIds,roleId));
    }

}
