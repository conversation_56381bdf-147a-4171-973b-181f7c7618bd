package com.simbest.boot.exam.study.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.time.LocalDateTime;

/**
 * 日常训练记录实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "daily_training_record")
@ApiModel(value = "DailyTrainingRecord", description = "日常训练记录表")
public class DailyTrainingRecord extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "DTR")
    @ApiModelProperty(value = "主键ID", example = "1")
    private String id;

    @Column(name = "username", length = 50)
    @ApiModelProperty(value = "用户名", example = "hadmin")
    private String username;

    @Column(name = "training_date")
    @ApiModelProperty(value = "训练日期", example = "2025-07-24T11:08:58")
    private LocalDateTime trainingDate;

    @Column(name = "total_questions")
    @ApiModelProperty(value = "答题总数", example = "10")
    private int totalQuestions;

    @Column(name = "correct_questions")
    @ApiModelProperty(value = "正确题目数量", example = "8")
    private int correctQuestions;

    @Column(name = "incorrect_questions")
    @ApiModelProperty(value = "错误题目数量", example = "2")
    private int incorrectQuestions;

    @Column(name = "now_seq")
    @ApiModelProperty(value = "当前题目数量", example = "2")
    private int nowSeq;

    @Column(length = 10)
    @ApiModelProperty(value = "是否完成", example = "Y")
    private String isFinish;

    @Column(name = "remarks", length = 255)
    @ApiModelProperty(value = "备注信息", example = "备注信息")
    private String remarks;
}