package com.simbest.boot.exam.examOnline.service.impl;

import cn.hutool.json.JSONArray;
import com.google.common.collect.Lists;
import com.simbest.boot.exam.examOnline.dto.ExamAvgRankExcel;
import com.simbest.boot.exam.examOnline.dto.ExamDimensionAvgExcel;
import com.simbest.boot.exam.examOnline.dto.ExamDimensionAvgJgExcel;
import com.simbest.boot.exam.examOnline.dto.ExamStatisticsExcel;
import com.simbest.boot.exam.examOnline.model.ExamAnnualQuarterInfo;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.repository.ExamAnswerNotesRepository;
import com.simbest.boot.exam.examOnline.repository.ExamQuestionRepository;
import com.simbest.boot.exam.examOnline.repository.ExamQuestionUserRepository;
import com.simbest.boot.exam.examOnline.service.IExamAnnualQuarterInfoService;
import com.simbest.boot.exam.examOnline.service.IExamAnswerNotesService;
import com.simbest.boot.exam.examOnline.service.IExamStatisticsExcelService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.FileTool;
import com.simbest.boot.util.office.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.simbest.boot.exam.util.Constants.*;

/**
 * <AUTHOR>
 * @title: ExamStatisticsExamServiceImpl
 * @projectName exam
 * @description:
 * @date 2021/6/10  14:26
 */
@Slf4j
@Service
public class ExamStatisticsExcelServiceImpl implements IExamStatisticsExcelService {

    @Autowired
    private ExamAnswerNotesRepository examAnswerNotesRepository;


    @Autowired
    private ExamQuestionUserRepository examQuestionUserRepository;

    @Autowired
    private ExamQuestionRepository examQuestionRepository;

    @Autowired
    private IExamAnnualQuarterInfoService iExamAnnualQuarterInfoService;

    @Autowired
    private IExamAnswerNotesService iExamAnswerNotesService;


    /**
     * 统计机关各部门对各部门的满意度评价结果导出
     * 1-6为财务部的结果
     * 7-12为党群工会
     * 13-18为人力资源部
     * 19-24为市场经营部
     * 25-30为客户服务中心
     * 31-36为网络部
     * 37-42为政企客户部
     * 43-48为综合部
     *
     * @return
     */
    @Override
    public void statisticsDepartments(String annualQuarterCode, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String questionBankCode = Constants.LYJG_QUESTION_BANK_CODE;
        List<ExamQuestion> allNoPage = examQuestionRepository.findAllByQuestionBankCode(questionBankCode);
        List<Map<String, Object>> endResult = Lists.newArrayList();
        List<String> codeList = Lists.newArrayList();
        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数格式

        Map<String, Object> mapCwb = new HashMap<>();
        Map<String, Object> mapDqgh = new HashMap<>();
        Map<String, Object> mapRlzy = new HashMap<>();
        Map<String, Object> mapScjy = new HashMap<>();
        Map<String, Object> mapKhfwzx = new HashMap<>();
        Map<String, Object> mapInternet = new HashMap<>();
        Map<String, Object> mapZqkehb = new HashMap<>();
        Map<String, Object> mapZhb = new HashMap<>();
        Map<String, Object> mapAvg = new HashMap<>();//各维度的部门平均分

        List<Double> questionScoreList = new ArrayList<>();

        for (ExamQuestion examQuestion : allNoPage) {
            String questionCode = examQuestion.getQuestionCode();
            codeList.add(questionCode);
        }

        for (int i = 0; i < codeList.size(); i++) {
            Double questionScore = examQuestionUserRepository.findQuestionScore(codeList.get(i), annualQuarterCode);
            questionScoreList.add(questionScore);
            // Double.parseDouble()
        }



    /*        Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("questionCode", code);
            String dataSql = "select round(avg (equ.answer_score),2) as \"average\" from us_exam_question_user equ " +
                    "where equ.question_code =:questionCode " +
                    "and equ.annual_quarter_code=:annualQuarterCode "+
                    "and equ.answer_score is not null";
            Set<String> resultList = dynamicWhere.queryNamedParameterForList(dataSql, paramMap);*/
        /**
         * 财务部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Cwb = questionScoreList.subList(0, 6);//财务部的各维度平均值
        mapCwb.put("department", "财务部");
        Double CPL = Cwb.get(0) == null ? 0.0 : Cwb.get(0);
        Double CPS = Cwb.get(1) == null ? 0.0 : Cwb.get(1);
        Double CTF = Cwb.get(2) == null ? 0.0 : Cwb.get(2);
        Double CWE = Cwb.get(3) == null ? 0.0 : Cwb.get(3);
        Double CCA = Cwb.get(4) == null ? 0.0 : Cwb.get(4);
        Double CSC = Cwb.get(5) == null ? 0.0 : Cwb.get(5);
        mapCwb.put(DIMENSION_PL, CPL);//专业水平
        mapCwb.put(DIMENSION_PS, CPS);//问题解决
        mapCwb.put(DIMENSION_TF, CTF);//及时反馈
        mapCwb.put(DIMENSION_WE, CWE);//工作效率
        mapCwb.put(DIMENSION_CA, CCA);//咨询解答
        mapCwb.put(DIMENSION_SC, CSC);//服务意识
        //部门成绩等于各维度之和平均值
        Double CWBScore;
        CWBScore = (CPL + CPS + CTF + CWE + CCA + CSC) / Cwb.size();
        mapCwb.put(DEPARTMENT_SCORE, df.format(CWBScore));//部门成绩
        /**
         * 党群工会的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Dqgh = questionScoreList.subList(6, 12);//党群工会的各维度平均值
        mapDqgh.put("department", "党群工会");
        Double DPL = Dqgh.get(0) == null ? 0.0 : Dqgh.get(0);
        Double DPS = Dqgh.get(1) == null ? 0.0 : Dqgh.get(1);
        Double DTF = Dqgh.get(2) == null ? 0.0 : Dqgh.get(2);
        Double DWE = Dqgh.get(3) == null ? 0.0 : Dqgh.get(3);
        Double DCA = Dqgh.get(4) == null ? 0.0 : Dqgh.get(4);
        Double DSC = Dqgh.get(5) == null ? 0.0 : Dqgh.get(5);
        mapDqgh.put(DIMENSION_PL, DPL);//专业水平
        mapDqgh.put(DIMENSION_PS, DPS);//问题解决
        mapDqgh.put(DIMENSION_TF, DTF);//及时反馈
        mapDqgh.put(DIMENSION_WE, DWE);//工作效率
        mapDqgh.put(DIMENSION_CA, DCA);//咨询解答
        mapDqgh.put(DIMENSION_SC, DSC);//服务意识
        //部门成绩等于各维度之和平均值
        Double DQGHScore;
        DQGHScore = (DPL + DPS + DTF + DWE + DCA + DSC) / Dqgh.size();
        mapDqgh.put(DEPARTMENT_SCORE, df.format(DQGHScore));//部门成绩

        /**
         * 人力资源部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Rlzy = questionScoreList.subList(12, 18);//人力资源部的各维度平均值
        mapRlzy.put("department", "人力资源部");
        Double RPL = Rlzy.get(0) == null ? 0.0 : Rlzy.get(0);
        Double RPS = Rlzy.get(1) == null ? 0.0 : Rlzy.get(1);
        Double RTF = Rlzy.get(2) == null ? 0.0 : Rlzy.get(2);
        Double RWE = Rlzy.get(3) == null ? 0.0 : Rlzy.get(3);
        Double RCA = Rlzy.get(4) == null ? 0.0 : Rlzy.get(4);
        Double RSC = Rlzy.get(5) == null ? 0.0 : Rlzy.get(5);
        mapRlzy.put(DIMENSION_PL, RPL);//专业水平
        mapRlzy.put(DIMENSION_PS, RPS);//问题解决
        mapRlzy.put(DIMENSION_TF, RTF);//及时反馈
        mapRlzy.put(DIMENSION_WE, RWE);//工作效率
        mapRlzy.put(DIMENSION_CA, RCA);//咨询解答
        mapRlzy.put(DIMENSION_SC, RSC);//服务意识
        //部门成绩等于各维度之和平均值
        Double HRScore;
        HRScore = (RPL + RPS + RTF + RWE + RCA + RSC) / Rlzy.size();
        mapRlzy.put(DEPARTMENT_SCORE, df.format(HRScore));//部门成绩

        /**
         * 市场经营部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Scjy = questionScoreList.subList(18, 24);//市场经营部的各维度平均值
        mapScjy.put("department", "市场经营部");
        Double SPL = Scjy.get(0) == null ? 0.0 : Scjy.get(0);
        Double SPS = Scjy.get(1) == null ? 0.0 : Scjy.get(1);
        Double STF = Scjy.get(2) == null ? 0.0 : Scjy.get(2);
        Double SWE = Scjy.get(3) == null ? 0.0 : Scjy.get(3);
        Double SCA = Scjy.get(4) == null ? 0.0 : Scjy.get(4);
        Double SSC = Scjy.get(5) == null ? 0.0 : Scjy.get(5);
        mapScjy.put(DIMENSION_PL, SPL);//专业水平
        mapScjy.put(DIMENSION_PS, SPS);//问题解决
        mapScjy.put(DIMENSION_TF, STF);//及时反馈
        mapScjy.put(DIMENSION_WE, SWE);//工作效率
        mapScjy.put(DIMENSION_CA, SCA);//咨询解答
        mapScjy.put(DIMENSION_SC, SSC);//服务意识
        //部门成绩等于各维度之和平均值
        Double MKScore;
        MKScore = (SPL + SPS + STF + SWE + SCA + SSC) / Scjy.size();
        mapScjy.put(DEPARTMENT_SCORE, df.format(MKScore));//部门成绩

        /**
         * 客户服务中心的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Khfwzx = questionScoreList.subList(24, 30);//客户服务中心的各维度平均值
        mapKhfwzx.put("department", "客户服务中心");
        Double KPL = Khfwzx.get(0) == null ? 0.0 : Khfwzx.get(0);
        Double KPS = Khfwzx.get(1) == null ? 0.0 : Khfwzx.get(1);
        Double KTF = Khfwzx.get(2) == null ? 0.0 : Khfwzx.get(2);
        Double KWE = Khfwzx.get(3) == null ? 0.0 : Khfwzx.get(3);
        Double KCA = Khfwzx.get(4) == null ? 0.0 : Khfwzx.get(4);
        Double KSC = Khfwzx.get(5) == null ? 0.0 : Khfwzx.get(5);
        mapKhfwzx.put(DIMENSION_PL, KPL);//专业水平
        mapKhfwzx.put(DIMENSION_PS, KPS);//问题解决
        mapKhfwzx.put(DIMENSION_TF, KTF);//及时反馈
        mapKhfwzx.put(DIMENSION_WE, KWE);//工作效率
        mapKhfwzx.put(DIMENSION_CA, KCA);//咨询解答
        mapKhfwzx.put(DIMENSION_SC, KSC);//服务意识
        //部门成绩等于各维度之和平均值
        Double KHScore;
        KHScore = (KPL + KPS + KTF + KWE + KCA + KSC) / Khfwzx.size();
        mapKhfwzx.put(DEPARTMENT_SCORE, df.format(KHScore));//部门成绩


        /**
         * 网络部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Internet = questionScoreList.subList(30, 36);//网络部的各维度平均值
        mapInternet.put("department", "网络部");
        Double IPL = Internet.get(0) == null ? 0.0 : Internet.get(0);
        Double IPS = Internet.get(1) == null ? 0.0 : Internet.get(1);
        Double ITF = Internet.get(2) == null ? 0.0 : Internet.get(2);
        Double IWE = Internet.get(3) == null ? 0.0 : Internet.get(3);
        Double ICA = Internet.get(4) == null ? 0.0 : Internet.get(4);
        Double ISC = Internet.get(5) == null ? 0.0 : Internet.get(5);
        mapInternet.put(DIMENSION_PL, IPL);//专业水平
        mapInternet.put(DIMENSION_PS, IPS);//问题解决
        mapInternet.put(DIMENSION_TF, ITF);//及时反馈
        mapInternet.put(DIMENSION_WE, IWE);//工作效率
        mapInternet.put(DIMENSION_CA, ICA);//咨询解答
        mapInternet.put(DIMENSION_SC, ISC);//服务意识
        //部门成绩等于各维度之和平均值
        Double ITScore;
        ITScore = (IPL + IPS + ITF + IWE + ICA + ISC) / Internet.size();
        mapInternet.put(DEPARTMENT_SCORE, df.format(ITScore));//部门成绩

        /**
         * 政企客户部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Zqkehb = questionScoreList.subList(36, 42);//政企客户部的各维度平均值
        mapZqkehb.put("department", "政企客户部");
        Double ZPL = Zqkehb.get(0) == null ? 0.0 : Zqkehb.get(0);
        Double ZPS = Zqkehb.get(1) == null ? 0.0 : Zqkehb.get(1);
        Double ZTF = Zqkehb.get(2) == null ? 0.0 : Zqkehb.get(2);
        Double ZWE = Zqkehb.get(3) == null ? 0.0 : Zqkehb.get(3);
        Double ZCA = Zqkehb.get(4) == null ? 0.0 : Zqkehb.get(4);
        Double ZSC = Zqkehb.get(5) == null ? 0.0 : Zqkehb.get(5);
        mapZqkehb.put(DIMENSION_PL, ZPL);//专业水平
        mapZqkehb.put(DIMENSION_PS, ZPS);//问题解决
        mapZqkehb.put(DIMENSION_TF, ZTF);//及时反馈
        mapZqkehb.put(DIMENSION_WE, ZWE);//工作效率
        mapZqkehb.put(DIMENSION_CA, ZCA);//咨询解答
        mapZqkehb.put(DIMENSION_SC, ZSC);//服务意识
        //部门成绩等于各维度之和平均值
        Double ZQScore;
        ZQScore = (ZPL + ZPS + ZTF + ZWE + ZCA + ZSC) / Zqkehb.size();
        mapZqkehb.put(DEPARTMENT_SCORE, df.format(ZQScore));//部门成绩

        /**
         * 综合部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Zhb = questionScoreList.subList(42, 48);//综合部的各维度平均值
        mapZhb.put("department", "综合部");
        Double BPL = Zhb.get(0) == null ? 0.0 : Zhb.get(0);
        Double BPS = Zhb.get(1) == null ? 0.0 : Zhb.get(1);
        Double BTF = Zhb.get(2) == null ? 0.0 : Zhb.get(2);
        Double BWE = Zhb.get(3) == null ? 0.0 : Zhb.get(3);
        Double BCA = Zhb.get(4) == null ? 0.0 : Zhb.get(4);
        Double BSC = Zhb.get(5) == null ? 0.0 : Zhb.get(5);
        mapZhb.put(DIMENSION_PL, BPL);//专业水平
        mapZhb.put(DIMENSION_PS, BPS);//问题解决
        mapZhb.put(DIMENSION_TF, BTF);//及时反馈
        mapZhb.put(DIMENSION_WE, BWE);//工作效率
        mapZhb.put(DIMENSION_CA, BCA);//咨询解答
        mapZhb.put(DIMENSION_SC, BSC);//服务意识
        //部门成绩等于各维度之和平均值
        Double ZHScore;
        ZHScore = (BPL + BPS + BTF + BWE + BCA + BSC) / Zhb.size();
        mapZhb.put(DEPARTMENT_SCORE, df.format(ZHScore));//部门成绩
        /**
         * 各维度的部门平均分
         * <AUTHOR>
         */

        mapAvg.put("department", "平均分");
        mapAvg.put(DIMENSION_PL, df.format((CPL + DPL + RPL + SPL + KPL + IPL + ZPL + BPL) / 8));//专业水平的部门平均分
        mapAvg.put(DIMENSION_PS, df.format((CPS + CPS + RPS + SPS + KPS + IPS + ZPS + BPS) / 8));//问题解决的部门平均分
        mapAvg.put(DIMENSION_TF, df.format((CTF + DTF + RTF + STF + KTF + ITF + ZTF + BTF) / 8));//及时反馈的部门平均分
        mapAvg.put(DIMENSION_WE, df.format((CWE + DWE + RWE + SWE + KWE + IWE + ZWE + BWE) / 8));//工作效率的部门平均分
        mapAvg.put(DIMENSION_CA, df.format((CCA + DCA + RCA + SCA + KCA + ICA + ZCA + BCA) / 8));//咨询解答的部门平均分
        mapAvg.put(DIMENSION_SC, df.format((CSC + DCA + RCA + SCA + KCA + ICA + ZCA + BCA) / 8));//服务意识的部门平均分
        Double allAvg;
        allAvg = (CWBScore + DQGHScore + HRScore + MKScore + HRScore + ZHScore + ZQScore + ITScore) / 8;
        mapAvg.put(DEPARTMENT_SCORE, df.format(allAvg));//部门成绩平均分
        endResult.add(mapCwb);
        endResult.add(mapDqgh);
        endResult.add(mapRlzy);
        endResult.add(mapScjy);
        endResult.add(mapKhfwzx);
        endResult.add(mapInternet);
        endResult.add(mapZqkehb);
        endResult.add(mapZhb);
        endResult.add(mapAvg);

        JSONArray jsonArray = new JSONArray();
        jsonArray.addAll(endResult);
        List<ExamDimensionAvgJgExcel> result = jsonArray.toList(ExamDimensionAvgJgExcel.class);
        //设置导出Excel的名称
        String fileName = "洛阳分公司各部门协同评价平均得分情况表.xls";
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径

        String targetFileName = path + "\\" + fileName;
        File targetFile = new File(targetFileName);
        //覆盖文件
        FileUtils.touch(targetFile);
        ExcelUtil<ExamDimensionAvgJgExcel> exportUtil = new ExcelUtil<>(ExamDimensionAvgJgExcel.class);
        exportUtil.exportExcel(result, Constants.SHEET_NAME, new FileOutputStream(targetFile), null);
        FileTool.download(targetFile.getPath(), request, response);
    }

    /**
     * 功能描述 将各部门百分制分值转成BigDecimal
     *
     * <AUTHOR>
     * @date 2021/6/10
     */
    public static BigDecimal comparingByKpScore(Map map) {
        return new BigDecimal(map.get("Centennial").toString());
    }


    /**
     * 机关各部门对机关各部门的满意度评价总平均分以及百分制分值统计结果导出
     *
     * @return
     */
    @Override
    public void statisticsDepartmentsAvg(String annualQuarterCode, HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> avgAndPercentageValue = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数格式

        BigDecimal zero = new BigDecimal(0);
        //统计各部门的平均值以及其百分值
        Map<String, Object> finance = examAnswerNotesRepository.financeDepartment(annualQuarterCode);//财务部
        HashMap<String, Object> financeDepartment = new HashMap<>();
        financeDepartment.put("Avgs", finance.get("Avgs") == null ? zero : finance.get("Avgs"));
        financeDepartment.put("Centennial", finance.get("Centennial") == null ? zero : finance.get("Centennial"));
        financeDepartment.put("department", "财务部");
        //获取百分制的分数
        BigDecimal financeCentennial = (BigDecimal) financeDepartment.get("Centennial");

        Map<String, Object> party = examAnswerNotesRepository.PartyTradeUnion(annualQuarterCode);
        Map<String, Object> partyTradeUnion = new HashMap<>();
        partyTradeUnion.put("Avgs", party.get("Avgs") == null ? zero : party.get("Avgs"));
        partyTradeUnion.put("Centennial", party.get("Centennial") == null ? zero : party.get("Centennial"));
        partyTradeUnion.put("department", "党群工会");
        //获取百分制的分数
        BigDecimal partyCentennial = (BigDecimal) partyTradeUnion.get("Centennial");

        Map<String, Object> humanResources = examAnswerNotesRepository.humanResourcesDepartment(annualQuarterCode);
        Map<String, Object> humanResourcesDepartment = new HashMap<>();
        humanResourcesDepartment.put("Avgs", humanResources.get("Avgs") == null ? zero : humanResources.get("Avgs"));
        humanResourcesDepartment.put("Centennial", humanResources.get("Centennial") == null ? zero : humanResources.get("Centennial"));
        humanResourcesDepartment.put("department", "人力资源部");
        //获取百分制的分数
        BigDecimal humanResourcesCentennial = (BigDecimal) humanResourcesDepartment.get("Centennial");

        Map<String, Object> marketing = examAnswerNotesRepository.marketingDepartment(annualQuarterCode, "政企客户部", "网络部", "客户服务部");
        Map<String, Object> marketingDepartment = new HashMap<>();
        marketingDepartment.put("Avgs", marketing.get("Avgs") == null ? zero : marketing.get("Avgs"));
        marketingDepartment.put("Centennial", marketing.get("Centennial") == null ? zero : marketing.get("Centennial"));
        marketingDepartment.put("department", "市场经营部");
        //获取百分制的分数
        BigDecimal marketingCentennial = (BigDecimal) marketingDepartment.get("Centennial");
//        Map<String, Object> customer = examAnswerNotesRepository.customerServiceCenter(annualQuarterCode);
//        Map<String, Object> customerServiceCenter = new HashMap<>();
//        customerServiceCenter.put("Avgs",customer.get("Avgs")==null ? zero : customer.get("Avgs"));
//        customerServiceCenter.put("Centennial",customer.get("Centennial")==null ? zero : customer.get("Centennial"));
//        customerServiceCenter.put("department","客户服务中心");
        //获取百分制的分数
        //BigDecimal customerCentennial = (BigDecimal) customerServiceCenter.get("Centennial");
        Map<String, Object> network = examAnswerNotesRepository.networkDepartment2(annualQuarterCode, "政企客户部", "市场经营部", "客户服务部");
        Map<String, Object> networkDepartment = new HashMap<>();
        networkDepartment.put("Avgs", network.get("Avgs") == null ? zero : network.get("Avgs"));
        networkDepartment.put("Centennial", network.get("Centennial") == null ? zero : network.get("Centennial"));
        networkDepartment.put("department", "网络部");
        //获取百分制的分数
        BigDecimal networkCentennial = (BigDecimal) networkDepartment.get("Centennial");

        Map<String, Object> government = examAnswerNotesRepository.governmentEnterpriseCustomerDepartment2(annualQuarterCode, "市场经营部", "网络部", "客户服务部");
        Map<String, Object> governmentEnterpriseCustomerDepartment = new HashMap<>();
        governmentEnterpriseCustomerDepartment.put("Avgs", government.get("Avgs") == null ? zero : government.get("Avgs"));
        governmentEnterpriseCustomerDepartment.put("Centennial", government.get("Centennial") == null ? zero : government.get("Centennial"));
        governmentEnterpriseCustomerDepartment.put("department", "政企客户部");
        //获取百分制的分数
        BigDecimal governmentCentennial = (BigDecimal) governmentEnterpriseCustomerDepartment.get("Centennial");

        Map<String, Object> general = examAnswerNotesRepository.generalDepartment(annualQuarterCode);
        Map<String, Object> generalDepartment = new HashMap<>();
        generalDepartment.put("Avgs", general.get("Avgs") == null ? zero : general.get("Avgs"));
        generalDepartment.put("Centennial", general.get("Centennial") == null ? zero : general.get("Centennial"));
        generalDepartment.put("department", "综合部");
        //获取百分制的分数
        BigDecimal generalCentennial = (BigDecimal) generalDepartment.get("Centennial");

        Map<String, Object> laborUnion = examAnswerNotesRepository.laborUnionDepartment(annualQuarterCode);
        Map<String, Object> laborUnionDepartment = new HashMap<>();
        laborUnionDepartment.put("Avgs", laborUnion.get("Avgs") == null ? zero : laborUnion.get("Avgs"));
        laborUnionDepartment.put("Centennial", laborUnion.get("Centennial") == null ? zero : laborUnion.get("Centennial"));
        laborUnionDepartment.put("department", "工会");
        //获取百分制的分数
        BigDecimal laborUnionCentennial = (BigDecimal) laborUnionDepartment.get("Centennial");

        Map<String, Object> clientele = examAnswerNotesRepository.clienteleDepartment(annualQuarterCode);
        Map<String, Object> clienteleDepartment = new HashMap<>();
        clienteleDepartment.put("Avgs", clientele.get("Avgs") == null ? zero : clientele.get("Avgs"));
        clienteleDepartment.put("Centennial", clientele.get("Centennial") == null ? zero : clientele.get("Centennial"));
        clienteleDepartment.put("department", "客户服务部");
        //获取百分制的分数
        BigDecimal clienteleCentennial = (BigDecimal) clienteleDepartment.get("Centennial");

        HashMap<String, Object> allAvg = new HashMap<>();//用于存储各部门平均分的平均分
        allAvg.put("department", "平均成绩");
        //获取部门平均分
        BigDecimal financeAvgs = (BigDecimal) financeDepartment.get("Avgs");
        BigDecimal partyAvgs = (BigDecimal) partyTradeUnion.get("Avgs");
        BigDecimal humanAvgs = (BigDecimal) humanResourcesDepartment.get("Avgs");
        BigDecimal marketingAvgs = (BigDecimal) marketingDepartment.get("Avgs");
        // BigDecimal customerAvgs = (BigDecimal) customerServiceCenter.get("Avgs");
        BigDecimal networkAvgs = (BigDecimal) networkDepartment.get("Avgs");
        BigDecimal governmentAvgs = (BigDecimal) governmentEnterpriseCustomerDepartment.get("Avgs");
        BigDecimal generalAvgs = (BigDecimal) generalDepartment.get("Avgs");
        BigDecimal laborUnionAvgs = (BigDecimal) laborUnionDepartment.get("Avgs");
        BigDecimal clienteleAvgs = (BigDecimal) clienteleDepartment.get("Avgs");
        BigDecimal bigDecimalCentennial = new BigDecimal(BigDecimal.ROUND_UP);

        BigDecimal AllScore = bigDecimalCentennial.add(financeCentennial)
                .add(partyCentennial)
                .add(humanResourcesCentennial)
                .add(marketingCentennial)
                //.add(customerCentennial)
                .add(networkCentennial)
                .add(governmentCentennial)
                .add(generalCentennial)
                .add(laborUnionCentennial)
                .add(clienteleCentennial);
        int i = 9;
        BigDecimal departmentCount = new BigDecimal(i);
        BigDecimal CentennialAvg = AllScore.divide(departmentCount); //百分值的部门成绩的平均分
        String Centennial = df.format(CentennialAvg);

        BigDecimal bigDecimalAvgs = new BigDecimal(BigDecimal.ROUND_UP);
        BigDecimal allAvgs = bigDecimalAvgs.add(financeAvgs)
                .add(partyAvgs)
                .add(humanAvgs)
                .add(marketingAvgs)
                //.add(customerAvgs)
                .add(networkAvgs)
                .add(governmentAvgs)
                .add(generalAvgs)
                .add(laborUnionAvgs)
                .add(clienteleAvgs);

        BigDecimal Avgs = allAvgs.divide(departmentCount);
        String Avg2 = df.format(Avgs);
        BigDecimal Avg = new BigDecimal(Avg2);
        //    avgs=(financeAvgs+partyAvgs+humanAvgs+marketingAvgs+customerAvgs+networkAvgs+generalAvgs+governmentAvgs)/8;//各部门平均分的平均分
        allAvg.put("Avgs", Avg);

        //各部门百分值的平均值
        //  Centennial=(financeCentennial+partyCentennial+humanResourcesCentennial+marketingCentennial+customerCentennial+networkCentennial+governmentCentennial+generalCentennial)/8;
        allAvg.put("Centennial", Centennial);

        avgAndPercentageValue.add(financeDepartment);
        avgAndPercentageValue.add(partyTradeUnion);
        //avgAndPercentageValue.add(customerServiceCenter);
        avgAndPercentageValue.add(humanResourcesDepartment);
        avgAndPercentageValue.add(marketingDepartment);
        avgAndPercentageValue.add(networkDepartment);
        avgAndPercentageValue.add(governmentEnterpriseCustomerDepartment);
        avgAndPercentageValue.add(generalDepartment);
        avgAndPercentageValue.add(laborUnionDepartment);
        avgAndPercentageValue.add(clienteleDepartment);


        /**
         * 根据百分值分数进行排名
         * <AUTHOR>
         * @date 2021/6/10
         */
        List<Map<String, Object>> collect = avgAndPercentageValue.stream().sorted(Comparator.comparing(ExamAnswerNotesServiceImpl::comparingByKpScore).reversed()).collect(Collectors.toList());

        int j = 1;
        for (Map<String, Object> map : collect) {
            if (j > collect.size()) {
                break;
            } else {
                map.put("rank", j);
                j++;
            }
        }
        avgAndPercentageValue.add(allAvg);

        JSONArray jsonArray = new JSONArray();
        jsonArray.addAll(avgAndPercentageValue);
        List<ExamAvgRankExcel> result = jsonArray.toList(ExamAvgRankExcel.class);
        //设置导出Excel的名称
        String fileName = "洛阳分公司各部门协同评价平均分及排名情况表.xls";
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径

        String targetFileName = path + "\\" + fileName;
        File targetFile = new File(targetFileName);
        //覆盖文件
        FileUtils.touch(targetFile);
        ExcelUtil<ExamAvgRankExcel> exportUtil = new ExcelUtil<>(ExamAvgRankExcel.class);
        exportUtil.exportExcel(result, Constants.SHEET_NAME, new FileOutputStream(targetFile), null);
        FileTool.download(targetFile.getPath(), request, response);


    }

    /**
     * 统计县分公司对各部门的满意度评价结果
     * 1-9为财务部的结果
     * 10-18为党群工会
     * 19-27为人力资源部
     * 28-36为市场经营部
     * 37-45为客户服务中心
     * 46-54为网络部
     * 55-63为政企客户部
     * 64-72为综合部
     *
     * @return
     */
    @Override
    public void countyBranchStatistics(String annualQuarterCode, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String questionBankCode = Constants.LYXF_QUESTION_BANK_CODE;
        List<ExamQuestion> allNoPage = examQuestionRepository.findAllByQuestionBankCode(questionBankCode);
        List<Map<String, Object>> endResult = Lists.newArrayList();
        List<String> codeList = Lists.newArrayList();

        Map<String, Object> mapCwb = new HashMap<>();
        Map<String, Object> mapDqgh = new HashMap<>();
        Map<String, Object> mapRlzy = new HashMap<>();
        Map<String, Object> mapScjy = new HashMap<>();
        Map<String, Object> mapKhfwzx = new HashMap<>();
        Map<String, Object> mapInternet = new HashMap<>();
        Map<String, Object> mapZqkehb = new HashMap<>();
        Map<String, Object> mapZhb = new HashMap<>();
        Map<String, Object> mapAvg = new HashMap<>();//各维度的部门平均分

        List<Double> questionScoreList = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数格式

        for (ExamQuestion examQuestion : allNoPage) {
            String questionCode = examQuestion.getQuestionCode();
            codeList.add(questionCode);
        }

        for (int i = 0; i < codeList.size(); i++) {
            Double questionScore = examQuestionUserRepository.findQuestionScore(codeList.get(i), annualQuarterCode);
            questionScoreList.add(questionScore);
            // Double.parseDouble()
        }
    /*        Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("questionCode", code);
            String dataSql = "select round(avg (equ.answer_score),2) as \"average\" from us_exam_question_user equ " +
                    "where equ.question_code =:questionCode " +
                    "and equ.annual_quarter_code=:annualQuarterCode "+
                    "and equ.answer_score is not null";
            Set<String> resultList = dynamicWhere.queryNamedParameterForList(dataSql, paramMap);*/
        /**
         * 财务部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Cwb = questionScoreList.subList(0, 9);//财务部的各维度平均值
        mapCwb.put("department", "财务部");
        Double CPL = Cwb.get(0) == null ? 0.0 : Cwb.get(0);
        Double CPS = Cwb.get(1) == null ? 0.0 : Cwb.get(1);
        Double CTF = Cwb.get(2) == null ? 0.0 : Cwb.get(2);
        Double CWE = Cwb.get(3) == null ? 0.0 : Cwb.get(3);
        Double CCA = Cwb.get(4) == null ? 0.0 : Cwb.get(4);
        Double CSC = Cwb.get(5) == null ? 0.0 : Cwb.get(5);
        Double CPG = Cwb.get(6) == null ? 0.0 : Cwb.get(6);
        Double CPN = Cwb.get(7) == null ? 0.0 : Cwb.get(7);
        Double COE = Cwb.get(8) == null ? 0.0 : Cwb.get(8);
        mapCwb.put(DIMENSION_PL, CPL);//专业水平
        mapCwb.put(DIMENSION_PS, CPS);//问题解决
        mapCwb.put(DIMENSION_TF, CTF);//及时反馈
        mapCwb.put(DIMENSION_WE, CWE);//工作效率
        mapCwb.put(DIMENSION_CA, CCA);//咨询解答
        mapCwb.put(DIMENSION_SC, CSC);//服务意识
        mapCwb.put(DIMENSION_PG, CPG);//政策指导
        mapCwb.put(DIMENSION_PN, CPN);//计划性
        mapCwb.put(DIMENSION_OE, COE);//总体评价
        //部门成绩等于各维度之和平均值
        Double CWBScore;
        CWBScore = (CPL + CPS + CTF + CWE + CCA + CSC + CPG + CPN + COE) / Cwb.size();
        mapCwb.put(DEPARTMENT_SCORE, df.format(CWBScore));//部门成绩

        /**
         * 党群工会的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Dqgh = questionScoreList.subList(9, 18);//党群工会的各维度平均值
        mapDqgh.put("department", "党群工会");
        Double DPL = Dqgh.get(0) == null ? 0.0 : Dqgh.get(0);
        Double DPS = Dqgh.get(1) == null ? 0.0 : Dqgh.get(1);
        Double DTF = Dqgh.get(2) == null ? 0.0 : Dqgh.get(2);
        Double DWE = Dqgh.get(3) == null ? 0.0 : Dqgh.get(3);
        Double DCA = Dqgh.get(4) == null ? 0.0 : Dqgh.get(4);
        Double DSC = Dqgh.get(5) == null ? 0.0 : Dqgh.get(5);
        Double DPG = Dqgh.get(6) == null ? 0.0 : Dqgh.get(6);
        Double DPN = Dqgh.get(7) == null ? 0.0 : Dqgh.get(7);
        Double DOE = Dqgh.get(8) == null ? 0.0 : Dqgh.get(8);
        mapDqgh.put(DIMENSION_PL, DPL);//专业水平
        mapDqgh.put(DIMENSION_PS, DPS);//问题解决
        mapDqgh.put(DIMENSION_TF, DTF);//及时反馈
        mapDqgh.put(DIMENSION_WE, DWE);//工作效率
        mapDqgh.put(DIMENSION_CA, DCA);//咨询解答
        mapDqgh.put(DIMENSION_SC, DSC);//服务意识
        mapDqgh.put(DIMENSION_PG, DPG);//政策指导
        mapDqgh.put(DIMENSION_PN, DPN);//计划性
        mapDqgh.put(DIMENSION_OE, DOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double DQGHScore;
        DQGHScore = (DPL + DPS + DTF + DWE + DCA + DSC + DPG + DPN + DOE) / Dqgh.size();
        mapDqgh.put(DEPARTMENT_SCORE, df.format(DQGHScore));//部门成绩

        /**
         * 人力资源部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Rlzy = questionScoreList.subList(18, 27);//人力资源部的各维度平均值
        mapRlzy.put("department", "人力资源部");
        Double RPL = Rlzy.get(0) == null ? 0.0 : Rlzy.get(0);
        Double RPS = Rlzy.get(1) == null ? 0.0 : Rlzy.get(1);
        Double RTF = Rlzy.get(2) == null ? 0.0 : Rlzy.get(2);
        Double RWE = Rlzy.get(3) == null ? 0.0 : Rlzy.get(3);
        Double RCA = Rlzy.get(4) == null ? 0.0 : Rlzy.get(4);
        Double RSC = Rlzy.get(5) == null ? 0.0 : Rlzy.get(5);
        Double RPG = Rlzy.get(6) == null ? 0.0 : Rlzy.get(6);
        Double RPN = Rlzy.get(7) == null ? 0.0 : Rlzy.get(7);
        Double ROE = Rlzy.get(8) == null ? 0.0 : Rlzy.get(8);
        mapRlzy.put(DIMENSION_PL, RPL);//专业水平
        mapRlzy.put(DIMENSION_PS, RPS);//问题解决
        mapRlzy.put(DIMENSION_TF, RTF);//及时反馈
        mapRlzy.put(DIMENSION_WE, RWE);//工作效率
        mapRlzy.put(DIMENSION_CA, RCA);//咨询解答
        mapRlzy.put(DIMENSION_SC, RSC);//服务意识
        mapRlzy.put(DIMENSION_PG, RPG);//政策指导
        mapRlzy.put(DIMENSION_PN, RPN);//计划性
        mapRlzy.put(DIMENSION_OE, ROE);//总体评价
        //部门成绩等于各维度之和平均值
        Double HRScore;
        HRScore = (RPL + RPS + RTF + RWE + RCA + RSC + RPG + RPN + ROE) / Rlzy.size();
        mapRlzy.put(DEPARTMENT_SCORE, df.format(HRScore));//部门成绩

        /**
         * 市场经营部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Scjy = questionScoreList.subList(27, 36);//市场经营部的各维度平均值
        mapScjy.put("department", "市场经营部");
        Double SPL = Scjy.get(0) == null ? 0.0 : Scjy.get(0);
        Double SPS = Scjy.get(1) == null ? 0.0 : Scjy.get(1);
        Double STF = Scjy.get(2) == null ? 0.0 : Scjy.get(2);
        Double SWE = Scjy.get(3) == null ? 0.0 : Scjy.get(3);
        Double SCA = Scjy.get(4) == null ? 0.0 : Scjy.get(4);
        Double SSC = Scjy.get(5) == null ? 0.0 : Scjy.get(5);
        Double SPG = Scjy.get(6) == null ? 0.0 : Scjy.get(6);
        Double SPN = Scjy.get(7) == null ? 0.0 : Scjy.get(7);
        Double SOE = Scjy.get(8) == null ? 0.0 : Scjy.get(8);
        mapScjy.put(DIMENSION_PL, SPL);//专业水平
        mapScjy.put(DIMENSION_PS, SPS);//问题解决
        mapScjy.put(DIMENSION_TF, STF);//及时反馈
        mapScjy.put(DIMENSION_WE, SWE);//工作效率
        mapScjy.put(DIMENSION_CA, SCA);//咨询解答
        mapScjy.put(DIMENSION_SC, SSC);//服务意识
        mapScjy.put(DIMENSION_PG, SPG);//政策指导
        mapScjy.put(DIMENSION_PN, SPN);//计划性
        mapScjy.put(DIMENSION_OE, SOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double MKScore;
        MKScore = (SPL + SPS + STF + SWE + SCA + SSC + SPG + SPN + SOE) / Scjy.size();
        mapScjy.put(DEPARTMENT_SCORE, df.format(MKScore));//部门成绩

        /**
         * 客户服务中心的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Khfwzx = questionScoreList.subList(36, 45);//客户服务中心的各维度平均值
        mapKhfwzx.put("department", "客户服务中心");
        Double KPL = Khfwzx.get(0) == null ? 0.0 : Khfwzx.get(0);
        Double KPS = Khfwzx.get(1) == null ? 0.0 : Khfwzx.get(1);
        Double KTF = Khfwzx.get(2) == null ? 0.0 : Khfwzx.get(2);
        Double KWE = Khfwzx.get(3) == null ? 0.0 : Khfwzx.get(3);
        Double KCA = Khfwzx.get(4) == null ? 0.0 : Khfwzx.get(4);
        Double KSC = Khfwzx.get(5) == null ? 0.0 : Khfwzx.get(5);
        Double KPG = Khfwzx.get(6) == null ? 0.0 : Khfwzx.get(6);
        Double KPN = Khfwzx.get(7) == null ? 0.0 : Khfwzx.get(7);
        Double KOE = Khfwzx.get(8) == null ? 0.0 : Khfwzx.get(8);
        mapKhfwzx.put(DIMENSION_PL, KPL);//专业水平
        mapKhfwzx.put(DIMENSION_PS, KPS);//问题解决
        mapKhfwzx.put(DIMENSION_TF, KTF);//及时反馈
        mapKhfwzx.put(DIMENSION_WE, KWE);//工作效率
        mapKhfwzx.put(DIMENSION_CA, KCA);//咨询解答
        mapKhfwzx.put(DIMENSION_SC, KSC);//服务意识
        mapKhfwzx.put(DIMENSION_PG, KPG);//政策指导
        mapKhfwzx.put(DIMENSION_PN, KPN);//计划性
        mapKhfwzx.put(DIMENSION_OE, KOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double KHScore;
        KHScore = (KPL + KPS + KTF + KWE + KCA + KSC + KPG + KPN + KOE) / Khfwzx.size();
        mapKhfwzx.put(DEPARTMENT_SCORE, df.format(KHScore));//部门成绩


        /**
         * 网络部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Internet = questionScoreList.subList(45, 54);//网络部的各维度平均值
        mapInternet.put("department", "网络部");
        Double IPL = Internet.get(0) == null ? 0.0 : Internet.get(0);
        Double IPS = Internet.get(1) == null ? 0.0 : Internet.get(1);
        Double ITF = Internet.get(2) == null ? 0.0 : Internet.get(2);
        Double IWE = Internet.get(3) == null ? 0.0 : Internet.get(3);
        Double ICA = Internet.get(4) == null ? 0.0 : Internet.get(4);
        Double ISC = Internet.get(5) == null ? 0.0 : Internet.get(5);
        Double IPG = Internet.get(6) == null ? 0.0 : Internet.get(6);
        Double IPN = Internet.get(7) == null ? 0.0 : Internet.get(7);
        Double IOE = Internet.get(8) == null ? 0.0 : Internet.get(8);
        mapInternet.put(DIMENSION_PL, IPL);//专业水平
        mapInternet.put(DIMENSION_PS, IPS);//问题解决
        mapInternet.put(DIMENSION_TF, ITF);//及时反馈
        mapInternet.put(DIMENSION_WE, IWE);//工作效率
        mapInternet.put(DIMENSION_CA, ICA);//咨询解答
        mapInternet.put(DIMENSION_SC, ISC);//服务意识
        mapInternet.put(DIMENSION_PG, IPG);//政策指导
        mapInternet.put(DIMENSION_PN, IPN);//计划性
        mapInternet.put(DIMENSION_OE, IOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double ITScore;
        ITScore = (IPL + IPS + ITF + IWE + ICA + ISC + IPG + IPN + IOE) / Internet.size();
        mapInternet.put(DEPARTMENT_SCORE, df.format(ITScore));//部门成绩

        /**
         * 政企客户部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Zqkehb = questionScoreList.subList(54, 63);//政企客户部的各维度平均值
        mapZqkehb.put("department", "政企客户部");
        Double ZPL = Zqkehb.get(0) == null ? 0.0 : Zqkehb.get(0);
        Double ZPS = Zqkehb.get(1) == null ? 0.0 : Zqkehb.get(1);
        Double ZTF = Zqkehb.get(2) == null ? 0.0 : Zqkehb.get(2);
        Double ZWE = Zqkehb.get(3) == null ? 0.0 : Zqkehb.get(3);
        Double ZCA = Zqkehb.get(4) == null ? 0.0 : Zqkehb.get(4);
        Double ZSC = Zqkehb.get(5) == null ? 0.0 : Zqkehb.get(5);
        Double ZPG = Zqkehb.get(6) == null ? 0.0 : Zqkehb.get(6);
        Double ZPN = Zqkehb.get(7) == null ? 0.0 : Zqkehb.get(7);
        Double ZOE = Zqkehb.get(8) == null ? 0.0 : Zqkehb.get(8);
        mapZqkehb.put(DIMENSION_PL, ZPL);//专业水平
        mapZqkehb.put(DIMENSION_PS, ZPS);//问题解决
        mapZqkehb.put(DIMENSION_TF, ZTF);//及时反馈
        mapZqkehb.put(DIMENSION_WE, ZWE);//工作效率
        mapZqkehb.put(DIMENSION_CA, ZCA);//咨询解答
        mapZqkehb.put(DIMENSION_SC, ZSC);//服务意识
        mapZqkehb.put(DIMENSION_PG, ZPG);//政策指导
        mapZqkehb.put(DIMENSION_PN, ZPN);//计划性
        mapZqkehb.put(DIMENSION_OE, ZOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double ZQScore;
        ZQScore = (ZPL + ZPS + ZTF + ZWE + ZCA + ZSC + ZPG + ZPN + ZOE) / Zqkehb.size();
        mapZqkehb.put(DEPARTMENT_SCORE, df.format(ZQScore));//部门成绩

        /**
         * 综合部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Zhb = questionScoreList.subList(63, 72);//综合部的各维度平均值
        mapZhb.put("department", "综合部");
        Double BPL = Zhb.get(0) == null ? 0.0 : Zhb.get(0);
        Double BPS = Zhb.get(1) == null ? 0.0 : Zhb.get(1);
        Double BTF = Zhb.get(2) == null ? 0.0 : Zhb.get(2);
        Double BWE = Zhb.get(3) == null ? 0.0 : Zhb.get(3);
        Double BCA = Zhb.get(4) == null ? 0.0 : Zhb.get(4);
        Double BSC = Zhb.get(5) == null ? 0.0 : Zhb.get(5);
        Double BPG = Zhb.get(6) == null ? 0.0 : Zhb.get(6);
        Double BPN = Zhb.get(7) == null ? 0.0 : Zhb.get(7);
        Double BOE = Zhb.get(8) == null ? 0.0 : Zhb.get(8);
        mapZhb.put(DIMENSION_PL, BPL);//专业水平
        mapZhb.put(DIMENSION_PS, BPS);//问题解决
        mapZhb.put(DIMENSION_TF, BTF);//及时反馈
        mapZhb.put(DIMENSION_WE, BWE);//工作效率
        mapZhb.put(DIMENSION_CA, BCA);//咨询解答
        mapZhb.put(DIMENSION_SC, BSC);//服务意识
        mapZhb.put(DIMENSION_PG, BPG);//政策指导
        mapZhb.put(DIMENSION_PN, BPN);//计划性
        mapZhb.put(DIMENSION_OE, BOE);//总体评价


        //部门成绩等于各维度之和平均值
        Double ZHScore;
        ZHScore = (BPL + BPS + BTF + BWE + BCA + BSC + BPG + BPN + BOE) / Zhb.size();
        mapZhb.put(DEPARTMENT_SCORE, df.format(ZHScore));//部门成绩
        /**
         * 各维度的部门平均分
         * <AUTHOR>
         */
        mapAvg.put("department", "平均分");
        mapAvg.put(DIMENSION_PL, df.format((CPL + DPL + RPL + SPL + KPL + IPL + ZPL + BPL) / 8));//专业水平的部门平均分
        mapAvg.put(DIMENSION_PS, df.format((CPS + DPS + RPS + SPS + KPS + IPS + ZPS + BPS) / 8));//问题解决的部门平均分
        mapAvg.put(DIMENSION_TF, df.format((CTF + DTF + RTF + STF + KTF + ITF + ZTF + BTF) / 8));//及时反馈的部门平均分
        mapAvg.put(DIMENSION_WE, df.format((CWE + DWE + RWE + SWE + KWE + IWE + ZWE + BWE) / 8));//工作效率的部门平均分
        mapAvg.put(DIMENSION_CA, df.format((CCA + DCA + RCA + SCA + KCA + ICA + ZCA + BCA) / 8));//咨询解答的部门平均分
        mapAvg.put(DIMENSION_SC, df.format((CSC + DSC + RSC + SSC + KSC + ISC + ZSC + BSC) / 8));//服务意识的部门平均分
        mapAvg.put(DIMENSION_PG, df.format((CPG + DPG + RPG + SPG + KPG + IPG + ZPG + BPG) / 8));//政策指导的部门平均分
        mapAvg.put(DIMENSION_PN, df.format((CPN + DPN + RPN + SPN + KPN + IPN + ZPN + BPN) / 8));//计划性的部门平均分
        mapAvg.put(DIMENSION_OE, df.format((COE + DOE + ROE + SOE + KOE + IOE + ZOE + BOE) / 8));//总体评价的部门平均分
        //所有部门成绩的平均分
        Double allAvg;
        allAvg = (CWBScore + DQGHScore + HRScore + MKScore + KHScore + ZHScore + ZQScore + ITScore) / 8;
        mapAvg.put(DEPARTMENT_SCORE, df.format(allAvg));//部门成绩的平均分

        endResult.add(mapCwb);
        endResult.add(mapDqgh);
        endResult.add(mapRlzy);
        endResult.add(mapScjy);
        endResult.add(mapKhfwzx);
        endResult.add(mapInternet);
        endResult.add(mapZqkehb);
        endResult.add(mapZhb);
        endResult.add(mapAvg);

        //导出统计表格
        JSONArray jsonArray = new JSONArray();
        jsonArray.addAll(endResult);
        List<ExamDimensionAvgExcel> result = jsonArray.toList(ExamDimensionAvgExcel.class);
        //设置导出Excel的名称
        String fileName = "洛阳分公司各部门服务支撑力度评价平均分及排名情况表.xls";
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径

        String targetFileName = path + "\\" + fileName;
        File targetFile = new File(targetFileName);
        //覆盖文件
        FileUtils.touch(targetFile);
        ExcelUtil<ExamDimensionAvgExcel> exportUtil = new ExcelUtil<>(ExamDimensionAvgExcel.class);
        exportUtil.exportExcel(result, Constants.SHEET_NAME, new FileOutputStream(targetFile), null);
        FileTool.download(targetFile.getPath(), request, response);

    }

    /**
     * 功能描述  县分公司的部门平均分以及其百分值统计
     *
     * @date 2021/6/9
     */
    @Override
    public void countyBranchStatisticsDepartmentsAvg(String annualQuarterCode, HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> avgAndPercentageValue = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数格式
        BigDecimal zero = new BigDecimal(0);
        //统计各部门的平均值以及其百分值
        Map<String, Object> finance = examAnswerNotesRepository.countyBranchFinanceDepartment(annualQuarterCode);
        Map<String, Object> financeDepartment = new HashMap<>();
        financeDepartment.put("Avgs", finance.get("Avgs") == null ? zero : finance.get("Avgs"));
        financeDepartment.put("Centennial", finance.get("Centennial") == null ? zero : finance.get("Centennial"));
        financeDepartment.put("department", "财务部");
        //获取百分制的分数
        BigDecimal financeCentennial = (BigDecimal) financeDepartment.get("Centennial");
        Map<String, Object> party = examAnswerNotesRepository.countyBranchPartyTradeUnion(annualQuarterCode);
        Map<String, Object> partyTradeUnion = new HashMap<>();
        partyTradeUnion.put("Avgs", party.get("Avgs") == null ? zero : party.get("Avgs"));
        partyTradeUnion.put("Centennial", party.get("Centennial") == null ? zero : party.get("Centennial"));
        partyTradeUnion.put("department", "党群工会");
        //获取百分制的分数
        BigDecimal partyCentennial = (BigDecimal) partyTradeUnion.get("Centennial");
        Map<String, Object> humanResources = examAnswerNotesRepository.countyBranchHumanResourcesDepartment(annualQuarterCode);
        Map<String, Object> humanResourcesDepartment = new HashMap<>();
        humanResourcesDepartment.put("Avgs", humanResources.get("Avgs") == null ? zero : humanResources.get("Avgs"));
        humanResourcesDepartment.put("Centennial", humanResources.get("Centennial") == null ? zero : humanResources.get("Centennial"));
        humanResourcesDepartment.put("department", "人力资源部");
        //获取百分制的分数
        BigDecimal humanResourcesCentennial = (BigDecimal) humanResourcesDepartment.get("Centennial");
        Map<String, Object> marketing = examAnswerNotesRepository.countyBranchMarketingDepartment(annualQuarterCode, "政企客户部", "网络部", "客户服务部");
        Map<String, Object> marketingDepartment = new HashMap<>();
        marketingDepartment.put("Avgs", marketing.get("Avgs") == null ? zero : marketing.get("Avgs"));
        marketingDepartment.put("Centennial", marketing.get("Centennial") == null ? zero : marketing.get("Centennial"));
        marketingDepartment.put("department", "市场经营部");
        //获取百分制的分数
        BigDecimal marketingCentennial = (BigDecimal) marketingDepartment.get("Centennial");
//        Map<String, Object> customer = examAnswerNotesRepository.countyBranchCustomerServiceCenter(annualQuarterCode);
//        Map<String, Object> customerServiceCenter = new HashMap<>();
//        customerServiceCenter.put("Avgs",customer.get("Avgs")==null ? zero : customer.get("Avgs"));
//        customerServiceCenter.put("Centennial",customer.get("Centennial")==null ? zero : customer.get("Centennial"));
//        customerServiceCenter.put("department","客户服务中心");
        //获取百分制的分数
//        BigDecimal customerCentennial = (BigDecimal) customerServiceCenter.get("Centennial");
        Map<String, Object> network = examAnswerNotesRepository.countyBranchNetworkDepartment2(annualQuarterCode, "政企客户部", "市场经营部", "客户服务部");
        Map<String, Object> networkDepartment = new HashMap<>();
        networkDepartment.put("Avgs", network.get("Avgs") == null ? zero : network.get("Avgs"));
        networkDepartment.put("Centennial", network.get("Centennial") == null ? zero : network.get("Centennial"));
        networkDepartment.put("department", "网络部");
        //获取百分制的分数
        BigDecimal networkCentennial = (BigDecimal) networkDepartment.get("Centennial");
        Map<String, Object> government = examAnswerNotesRepository.countyBranchGovernmentEnterpriseCustomerDepartment2(annualQuarterCode, "市场经营部", "网络部", "客户服务部");
        Map<String, Object> governmentEnterpriseCustomerDepartment = new HashMap<>();
        governmentEnterpriseCustomerDepartment.put("Avgs", government.get("Avgs") == null ? zero : government.get("Avgs"));
        governmentEnterpriseCustomerDepartment.put("Centennial", government.get("Centennial") == null ? zero : government.get("Centennial"));
        governmentEnterpriseCustomerDepartment.put("department", "政企客户部");
        //获取百分制的分数
        BigDecimal governmentCentennial = (BigDecimal) governmentEnterpriseCustomerDepartment.get("Centennial");
        Map<String, Object> general = examAnswerNotesRepository.countyBranchGeneralDepartment(annualQuarterCode);
        Map<String, Object> generalDepartment = new HashMap<>();
        generalDepartment.put("Avgs", general.get("Avgs") == null ? zero : general.get("Avgs"));
        generalDepartment.put("Centennial", general.get("Centennial") == null ? zero : general.get("Centennial"));
        generalDepartment.put("department", "综合部");
        //获取百分制的分数
        BigDecimal generalCentennial = (BigDecimal) generalDepartment.get("Centennial");

        Map<String, Object> laborUnion = examAnswerNotesRepository.laborUnionDepartment(annualQuarterCode);
        Map<String, Object> laborUnionDepartment = new HashMap<>();
        laborUnionDepartment.put("Avgs", laborUnion.get("Avgs") == null ? zero : laborUnion.get("Avgs"));
        laborUnionDepartment.put("Centennial", laborUnion.get("Centennial") == null ? zero : laborUnion.get("Centennial"));
        laborUnionDepartment.put("department", "工会");
        //获取百分制的分数
        BigDecimal laborUnionCentennial = (BigDecimal) laborUnionDepartment.get("Centennial");

        Map<String, Object> clientele = examAnswerNotesRepository.clienteleDepartment(annualQuarterCode);
        Map<String, Object> clienteleDepartment = new HashMap<>();
        clienteleDepartment.put("Avgs", clientele.get("Avgs") == null ? zero : clientele.get("Avgs"));
        clienteleDepartment.put("Centennial", clientele.get("Centennial") == null ? zero : clientele.get("Centennial"));
        clienteleDepartment.put("department", "客户服务部");
        //获取百分制的分数
        BigDecimal clienteleCentennial = (BigDecimal) clienteleDepartment.get("Centennial");

        HashMap<String, Object> allAvg = new HashMap<>();//用于存储各部门平均分的平均分
        allAvg.put("department", "平均成绩");
        BigDecimal avgs;
        //获取各部门的平均分
        BigDecimal financeAvgs = (BigDecimal) financeDepartment.get("Avgs");
        BigDecimal partyAvgs = (BigDecimal) partyTradeUnion.get("Avgs");
        BigDecimal humanAvgs = (BigDecimal) humanResourcesDepartment.get("Avgs");
        BigDecimal marketingAvgs = (BigDecimal) marketingDepartment.get("Avgs");
//        BigDecimal customerAvgs = (BigDecimal) customerServiceCenter.get("Avgs");
        BigDecimal networkAvgs = (BigDecimal) networkDepartment.get("Avgs");
        BigDecimal governmentAvgs = (BigDecimal) governmentEnterpriseCustomerDepartment.get("Avgs");
        BigDecimal generalAvgs = (BigDecimal) generalDepartment.get("Avgs");
        BigDecimal laborUnionAvgs = (BigDecimal) laborUnionDepartment.get("Avgs");
        BigDecimal clienteleAvgs = (BigDecimal) clienteleDepartment.get("Avgs");
        BigDecimal bigDecimalCentennial = new BigDecimal(BigDecimal.ROUND_UP);

        BigDecimal AllScore = bigDecimalCentennial.add(financeCentennial)
                .add(partyCentennial)
                .add(humanResourcesCentennial)
                .add(marketingCentennial)
//                .add(customerCentennial)
                .add(networkCentennial)
                .add(governmentCentennial)
                .add(generalCentennial)
                .add(laborUnionCentennial)
                .add(clienteleCentennial);
        int i = 9;
        BigDecimal departmentCount = new BigDecimal(i);
        BigDecimal CentennialAvg = AllScore.divide(departmentCount); //百分值的部门成绩的平均分
        String Centennial = df.format(CentennialAvg);

        BigDecimal bigDecimalAvgs = new BigDecimal(BigDecimal.ROUND_UP);
        BigDecimal allAvgs = bigDecimalAvgs.add(financeAvgs)
                .add(partyAvgs)
                .add(humanAvgs)
                .add(marketingAvgs)
//                .add(customerAvgs)
                .add(networkAvgs)
                .add(governmentAvgs)
                .add(generalAvgs)
                .add(laborUnionAvgs)
                .add(clienteleAvgs);

        BigDecimal Avgs = allAvgs.divide(departmentCount);
        String Avg1 = df.format(Avgs);
        BigDecimal Avg = new BigDecimal(Avg1);


        //    avgs=(financeAvgs+partyAvgs+humanAvgs+marketingAvgs+customerAvgs+networkAvgs+generalAvgs+governmentAvgs)/8;//各部门平均分的平均分
        allAvg.put("Avgs", Avg);
        //各部门百分值的平均值
        //  Centennial=(financeCentennial+partyCentennial+humanResourcesCentennial+marketingCentennial+customerCentennial+networkCentennial+governmentCentennial+generalCentennial)/8;
        allAvg.put("Centennial", Centennial);

        avgAndPercentageValue.add(financeDepartment);
        avgAndPercentageValue.add(partyTradeUnion);
//        avgAndPercentageValue.add(customerServiceCenter);
        avgAndPercentageValue.add(humanResourcesDepartment);
        avgAndPercentageValue.add(marketingDepartment);
        avgAndPercentageValue.add(networkDepartment);
        avgAndPercentageValue.add(governmentEnterpriseCustomerDepartment);
        avgAndPercentageValue.add(generalDepartment);
        avgAndPercentageValue.add(laborUnionDepartment);
        avgAndPercentageValue.add(clienteleDepartment);
        /**
         * 根据百分值分数进行排名
         * <AUTHOR>
         * @date 2021/6/10
         */
        List<Map<String, Object>> collect = avgAndPercentageValue.stream().sorted(Comparator.comparing(ExamAnswerNotesServiceImpl::comparingByKpScore).reversed()).collect(Collectors.toList());

        int j = 1;
        for (Map<String, Object> map : collect) {
            if (j > collect.size()) {
                break;
            } else {
                map.put("rank", j);
                j++;
            }
        }
        avgAndPercentageValue.add(allAvg);
        //导出统计表格
        JSONArray jsonArray = new JSONArray();
        jsonArray.addAll(avgAndPercentageValue);
        List<ExamAvgRankExcel> result = jsonArray.toList(ExamAvgRankExcel.class);
        //设置导出Excel的名称
        String fileName = "洛阳分公司各部门服务支撑力度评价平均分及排名表.xls";
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径

        String targetFileName = path + "\\" + fileName;
        File targetFile = new File(targetFileName);
        //覆盖文件
        FileUtils.touch(targetFile);
        ExcelUtil<ExamAvgRankExcel> exportUtil = new ExcelUtil<>(ExamAvgRankExcel.class);
        exportUtil.exportExcel(result, Constants.SHEET_NAME, new FileOutputStream(targetFile), null);
        FileTool.download(targetFile.getPath(), request, response);

    }

    @Override
    public void statisticalSummary(String annualQuarterCode, HttpServletRequest request, HttpServletResponse response) throws IOException {
        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数格式
        BigDecimal two = new BigDecimal(2);
        BigDecimal twenty = new BigDecimal(20);
        //根据洛阳下拉框选择的机关考试年度季度编码来获取县分公司考试的年度季度编码
        String subAnnualQuarterCode = annualQuarterCode.substring(4, 14);
        ExamAnnualQuarterInfo lYJGAnnualQuarterInfo = iExamAnnualQuarterInfoService.findAnnualQuarterInfo3("%" + annualQuarterCode + "%");
        //获取年度季度信息字段
        String annualQuarterInfo = lYJGAnnualQuarterInfo.getAnnualQuarterInfo();
        String LYXFAnnualQuarterCode = "lyxf" + subAnnualQuarterCode;
        ExamAnnualQuarterInfo LYXFAnnualQuarterInfo = iExamAnnualQuarterInfoService.findAnnualQuarterInfo3("%" + LYXFAnnualQuarterCode + "%");
        String LYXFAnnualQuarterCode2 = LYXFAnnualQuarterInfo.getAnnualQuarterCode();
        String lYXFAnnualQuarterInfo = LYXFAnnualQuarterInfo.getAnnualQuarterInfo();
        //获取机关与县分的统计结果
        List<Map<String, Object>> LYJGList = iExamAnswerNotesService.statisticsDepartmentsAvg(annualQuarterCode);
        List<Map<String, Object>> LYXFList = iExamAnswerNotesService.countyBranchStatisticsDepartmentsAvg(LYXFAnnualQuarterCode2);

        List<Map<String, Object>> mapArrayList = new ArrayList<>();
        //   List<Map<String, Object>> titleArrayList = new ArrayList<>();
        Map<String, Object> hashMap = new HashMap<>();
        String substring = annualQuarterInfo.substring(0, 9);

    /*    String annualQuarterInfoLYJG=substring+"服务支撑满意度得分(县区评价)";
        String annualQuarterInfoLYXF = substring + "协同满意度得分(机关互评)";
        String annualQuarter = substring + "协作满意度总得分";
        hashMap.put("department","部门");
        hashMap.put("lyxfAvgs",annualQuarterInfoLYJG);
        hashMap.put("lyjgAvgs",annualQuarterInfoLYXF);
        hashMap.put("Quarter",annualQuarter);
        hashMap.put("Centennial","百分制成绩");
        hashMap.put("rank","排名");
        titleArrayList.add(hashMap);*/

        List<Map<String, Object>> list = new ArrayList<>();
        for (Map<String, Object> map : LYXFList) {
            list.add(map);
        }

        for (Map<String, Object> map : LYJGList) {
            list.add(map);
        }

        Map<String, Object> CWBMap = new HashMap<>();//存储财务部的相关数据
        Map<String, Object> lyxfCWBmap = list.get(0);//县分财务部数据
        Map<String, Object> lyjgCWBmap = list.get(9);//机关财务部数据
        BigDecimal lyxfCWBavgs = (BigDecimal) lyxfCWBmap.get("Avgs");
        BigDecimal lyjgCWBavgs = (BigDecimal) lyjgCWBmap.get("Avgs");
        CWBMap.put("department", lyxfCWBmap.get("department"));
        CWBMap.put("lyxfAvgs", lyxfCWBavgs);
        CWBMap.put("lyjgAvgs", lyjgCWBavgs);
        BigDecimal decimalCWB = lyxfCWBavgs.add(lyjgCWBavgs);
        BigDecimal bigDecimalCWB = decimalCWB.divide(two);//县分财务部得分与机关财务部得分的平均分
        String bigDecimalCWB2 = df.format(bigDecimalCWB);
        BigDecimal CentennialCWB = bigDecimalCWB.multiply(twenty);//县分财务部得分与机关财务部得分的平均分的百分制得分
        CWBMap.put("Quarter", bigDecimalCWB2);
        String CentennialCWB2 = df.format(CentennialCWB);
        CWBMap.put("Centennial", CentennialCWB2);

        Map<String, Object> DQGHMap = new HashMap<>();//存储党群工会的相关数据
        Map<String, Object> lyxfDQGHmap = list.get(1);//县分党群工会数据
        Map<String, Object> lyjgDQGHmap = list.get(10);//机关党群工会数据
        BigDecimal lyxfDQGHavgs = (BigDecimal) lyxfDQGHmap.get("Avgs");
        BigDecimal lyjgDQGHavgs = (BigDecimal) lyjgDQGHmap.get("Avgs");
        DQGHMap.put("department", lyxfDQGHmap.get("department"));
        DQGHMap.put("lyxfAvgs", lyxfDQGHavgs);
        DQGHMap.put("lyjgAvgs", lyjgDQGHavgs);
        BigDecimal decimalDQGH = lyxfDQGHavgs.add(lyjgDQGHavgs);
        BigDecimal bigDecimalDQGH = decimalDQGH.divide(two);//县分党群工会得分与党群工会得分的平均分
        String bigDecimalDQGH2 = df.format(bigDecimalDQGH);
        BigDecimal CentennialDQGH = bigDecimalDQGH.multiply(twenty);
        DQGHMap.put("Quarter", bigDecimalDQGH2);
        String CentennialDQGH2 = df.format(CentennialDQGH);
        DQGHMap.put("Centennial", CentennialDQGH2);

        Map<String, Object> KHFUMap = new HashMap<>();//存储客户服务中心的相关数据
        Map<String, Object> lyxfKHFUmap = list.get(2);//县分客户服务中心数据
        Map<String, Object> lyjgKHFUmap = list.get(11);//机关客户服务中心数据
        BigDecimal lyxfKHFUavgs = (BigDecimal) lyxfKHFUmap.get("Avgs");
        BigDecimal lyjgKHFUavgs = (BigDecimal) lyjgKHFUmap.get("Avgs");
        KHFUMap.put("department", lyxfKHFUmap.get("department"));
        KHFUMap.put("lyxfAvgs", lyxfKHFUavgs);
        KHFUMap.put("lyjgAvgs", lyjgKHFUavgs);
        BigDecimal decimalKHFU = lyxfKHFUavgs.add(lyjgKHFUavgs);
        BigDecimal bigDecimalKHFU = decimalKHFU.divide(two);
        String bigDecimalKHFU2 = df.format(bigDecimalKHFU);
        BigDecimal CentennialKHFU = bigDecimalKHFU.multiply(twenty);
        KHFUMap.put("Quarter", bigDecimalKHFU2);
        String CentennialKHFU2 = df.format(CentennialKHFU);
        KHFUMap.put("Centennial", CentennialKHFU2);

        Map<String, Object> HRMap = new HashMap<>();//存储人力资源部的相关数据
        Map<String, Object> lyxfHRmap = list.get(3);//县分人力资源部数据
        Map<String, Object> lyjgHRmap = list.get(12);//机关人力资源部数据
        BigDecimal lyxfHRavgs = (BigDecimal) lyxfHRmap.get("Avgs");
        BigDecimal lyjgHRavgs = (BigDecimal) lyjgHRmap.get("Avgs");
        HRMap.put("department", lyxfHRmap.get("department"));
        HRMap.put("lyxfAvgs", lyxfHRavgs);
        HRMap.put("lyjgAvgs", lyjgHRavgs);
        BigDecimal decimalHR = lyxfHRavgs.add(lyjgHRavgs);
        BigDecimal QuarterHR = decimalHR.divide(two);
        String QuarterHR2 = df.format(QuarterHR);
        BigDecimal CentennialHR = QuarterHR.multiply(twenty);
        HRMap.put("Quarter", QuarterHR2);
        String CentennialHR2 = df.format(CentennialHR);
        HRMap.put("Centennial", CentennialHR2);

        Map<String, Object> MKMap = new HashMap<>();//存储市场部的相关数据
        Map<String, Object> lyxfMKmap = list.get(4);//县分市场部数据
        Map<String, Object> lyjgMKmap = list.get(13);//机关市场部数据
        BigDecimal lyxfMKavgs = (BigDecimal) lyxfMKmap.get("Avgs");
        BigDecimal lyjgMKavgs = (BigDecimal) lyjgMKmap.get("Avgs");
        MKMap.put("department", lyxfMKmap.get("department"));
        MKMap.put("lyxfAvgs", lyxfMKavgs);
        MKMap.put("lyjgAvgs", lyjgMKavgs);
        BigDecimal decimalMK = lyxfMKavgs.add(lyjgMKavgs);
        BigDecimal QuarterMK = decimalMK.divide(two);
        String QuarterMK2 = df.format(QuarterMK);
        BigDecimal CentennialMK = QuarterMK.multiply(twenty);
        MKMap.put("Quarter", QuarterMK2);
        String CentennialMK2 = df.format(CentennialMK);
        MKMap.put("Centennial", CentennialMK2);

        Map<String, Object> ITMap = new HashMap<>();//存储网络部的相关数据
        Map<String, Object> lyxfITmap = list.get(5);//县分网络部数据
        Map<String, Object> lyjgITmap = list.get(14);//机关网络部数据
        BigDecimal lyxfITavgs = (BigDecimal) lyxfITmap.get("Avgs");
        BigDecimal lyjgITavgs = (BigDecimal) lyjgITmap.get("Avgs");
        ITMap.put("department", lyxfITmap.get("department"));
        ITMap.put("lyxfAvgs", lyxfITavgs);
        ITMap.put("lyjgAvgs", lyjgITavgs);
        BigDecimal decimalIT = lyxfITavgs.add(lyjgITavgs);
        BigDecimal QuarterIT = decimalIT.divide(two);
        String QuarterIT2 = df.format(QuarterIT);
        BigDecimal CentennialIT = QuarterIT.multiply(twenty);
        ITMap.put("Quarter", QuarterIT2);
        String CentennialIT2 = df.format(CentennialIT);
        ITMap.put("Centennial", CentennialIT2);

        Map<String, Object> ZQMap = new HashMap<>();//存储政企部的相关数据
        Map<String, Object> lyxfZQmap = list.get(6);//县分政企部数据
        Map<String, Object> lyjgZQmap = list.get(15);//机关政企部数据
        BigDecimal lyxfZQavgs = (BigDecimal) lyxfZQmap.get("Avgs");
        BigDecimal lyjgZQavgs = (BigDecimal) lyjgZQmap.get("Avgs");
        ZQMap.put("department", lyxfZQmap.get("department"));
        ZQMap.put("lyxfAvgs", lyxfZQavgs);
        ZQMap.put("lyjgAvgs", lyjgZQavgs);
        BigDecimal decimalZQ = lyxfZQavgs.add(lyjgZQavgs);
        BigDecimal QuarterZQ = decimalZQ.divide(two);
        String QuarterZQ2 = df.format(QuarterZQ);
        BigDecimal CentennialZQ = QuarterZQ.multiply(twenty);
        ZQMap.put("Quarter", QuarterZQ2);
        String CentennialZQ2 = df.format(CentennialZQ);
        ZQMap.put("Centennial", CentennialZQ2);

        Map<String, Object> ZHMap = new HashMap<>();//存储综合部的相关数据
        Map<String, Object> lyxfZHmap = list.get(7);//县分综合部数据
        Map<String, Object> lyjgZHmap = list.get(16);//机关综合部数据
        BigDecimal lyxfZHavgs = (BigDecimal) lyxfZHmap.get("Avgs");
        BigDecimal lyjgZHavgs = (BigDecimal) lyjgZHmap.get("Avgs");
        ZHMap.put("department", lyxfZHmap.get("department"));
        ZHMap.put("lyxfAvgs", lyxfZHavgs);
        ZHMap.put("lyjgAvgs", lyjgZHavgs);
        BigDecimal decimalZH = lyxfZHavgs.add(lyjgZHavgs);
        BigDecimal QuarterZH = decimalZH.divide(two);
        String QuarterZH2 = df.format(QuarterZH);
        BigDecimal CentennialZH = QuarterZH.multiply(twenty);
        ZHMap.put("Quarter", QuarterZH2);
        String CentennialZH2 = df.format(CentennialZH);
        ZHMap.put("Centennial", CentennialZH2);

        Map<String, Object> AVGMap = new HashMap<>();//存储人力资源部的相关数据
        Map<String, Object> lyxfAVGmap = list.get(8);//县分人力资源部数据
        Map<String, Object> lyjgAVGmap = list.get(17);//机关人力资源部数据
        BigDecimal lyxfAVGavgs = (BigDecimal) lyxfAVGmap.get("Avgs");
        BigDecimal lyjgAVGavgs = (BigDecimal) lyjgAVGmap.get("Avgs");
        AVGMap.put("department", lyxfAVGmap.get("department"));
        AVGMap.put("lyxfAvgs", lyxfAVGavgs);
        AVGMap.put("lyjgAvgs", lyjgAVGavgs);
        BigDecimal decimalAVG = lyxfAVGavgs.add(lyjgAVGavgs);
        BigDecimal QuarterAVG = decimalAVG.divide(two);
        String QuarterAVG2 = df.format(QuarterAVG);
        BigDecimal CentennialAVG = QuarterAVG.multiply(twenty);
        AVGMap.put("Quarter", QuarterAVG2);
        String CentennialAVG2 = df.format(CentennialAVG);
        AVGMap.put("Centennial", CentennialAVG2);


        mapArrayList.add(CWBMap);
        mapArrayList.add(DQGHMap);
        mapArrayList.add(KHFUMap);
        mapArrayList.add(HRMap);
        mapArrayList.add(MKMap);
        mapArrayList.add(ITMap);
        mapArrayList.add(ZQMap);
        mapArrayList.add(ZHMap);

        List<Map<String, Object>> collect = mapArrayList.stream().sorted(Comparator.comparing(ExamAnswerNotesServiceImpl::comparingByKpScore).reversed()).collect(Collectors.toList());

        int j = 1;
        for (Map<String, Object> map : collect) {
            if (j > collect.size()) {
                break;
            } else {
                map.put("rank", j);
                j++;
            }
        }
        mapArrayList.add(AVGMap);

        //导出统计表格
        JSONArray jsonArray = new JSONArray();
        jsonArray.addAll(mapArrayList);
        List<ExamStatisticsExcel> result = jsonArray.toList(ExamStatisticsExcel.class);
        //设置导出Excel的名称
        String fileName = "协作满意度汇总.xls";
        String fileName2 = substring + fileName;
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径

        String targetFileName = path + "\\" + fileName2;
        File targetFile = new File(targetFileName);
        //覆盖文件
        FileUtils.touch(targetFile);
        ExcelUtil<ExamStatisticsExcel> exportUtil = new ExcelUtil<>(ExamStatisticsExcel.class);
        exportUtil.exportExcel(result, Constants.SHEET_NAME, new FileOutputStream(targetFile), null);
        FileTool.download(targetFile.getPath(), request, response);

    }
}
