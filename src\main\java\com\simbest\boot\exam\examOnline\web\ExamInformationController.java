package com.simbest.boot.exam.examOnline.web;

import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.model.ExamInformation;
import com.simbest.boot.exam.examOnline.service.IExamInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;




/**
 * <AUTHOR>
 */
@Api(description = "答题记录")
@Slf4j
@RestController
@RequestMapping(value = "/action/examInformation")
public class ExamInformationController extends LogicController<ExamInformation, String> {

    private IExamInformationService iExamInformationService;

    @Autowired
    public ExamInformationController(IExamInformationService service) {
        super(service);
        this.iExamInformationService=service;
    }



    @ApiOperation(value = "提交试卷", notes = "提交试卷")
    @PostMapping(value = {"/submitExam", "sso/submitExam", "api/submitExam"})
    public JsonResponse submitExam(@RequestParam(required = false) String currentUserCode,
                                   @RequestParam(required = false) String source,
                                   @RequestBody(required = false) ExamInformation examInformation) {
        return JsonResponse.success(iExamInformationService.submitExam(currentUserCode, source,examInformation));

    }

}
