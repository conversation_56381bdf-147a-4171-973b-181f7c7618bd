package com.simbest.boot.exam.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.SystemModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @desc: 试卷题目信息实体类
 * @date 2021/7/4  11:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_attribute_question")
@ApiModel(value = "考试范围群组信息")
public class ExamAttributeQuestion extends SystemModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EAQ") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "系统试卷",name = "examAppCode",example = "hnjjwz",required = true)
    private String examAppCode;// 当前字段应是试卷编码、与考试编码无关

    @Column(length = 40)
    @ApiModelProperty(value = "题库编码",name = "questionBankCode",example = "A-001",required = true)
    private String questionBankCode;


    @Column(length = 40)
    @ApiModelProperty(value = "题目编码",name = "questionCode",example = "A-001-1",required = true)
    private String questionCode;


}
