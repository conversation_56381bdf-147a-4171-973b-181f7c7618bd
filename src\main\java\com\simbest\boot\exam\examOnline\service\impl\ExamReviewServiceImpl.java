/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service.impl;/**
 * Created by KZH on 2019/10/17 16:47.
 */

import com.google.common.collect.Lists;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamReview;
import com.simbest.boot.exam.examOnline.repository.ExamReviewRepository;
import com.simbest.boot.exam.examOnline.service.IExamInfoService;
import com.simbest.boot.exam.examOnline.service.IExamReviewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2019-10-17 16:47
 * @desc 评测记录
 **/
@Slf4j
@Service
public class ExamReviewServiceImpl extends LogicService<ExamReview,String> implements IExamReviewService {

    private ExamReviewRepository examReviewRepository;

    @Autowired
    private IExamInfoService iExamInfoService;

    @Autowired
    public ExamReviewServiceImpl(ExamReviewRepository repository){
        super(repository);
        this.examReviewRepository=repository;

    }

    /**
     * 保存评测记录
     * @param sumMap
     * @return
     */
    @Override
    public JsonResponse saveExamReview(Map<String,Object> sumMap) {
        List<Map<String,String>>  judgeAnswerList=  (List<Map<String,String>>) sumMap.get("judgeAnswerList");
        String usernmae=String.valueOf(sumMap.get("username"));
        String truename=String.valueOf(sumMap.get("truename"));
        String reviewUsername=String.valueOf(sumMap.get("reviewUsername"));
        String reviewTruename=String.valueOf(sumMap.get("reviewTruename"));

        List<ExamReview> examReviewList= Lists.newArrayList();

        for(Map<String,String> review:judgeAnswerList){
            Set<Map.Entry<String, String>> entries = review.entrySet();
            Iterator<Map.Entry<String, String>> iteratorMap = entries.iterator();
            while (iteratorMap.hasNext()){
                ExamReview examReview=new ExamReview();
                Map.Entry<String, String> next = iteratorMap.next();
                String key = next.getKey();
                String value = next.getValue();
                examReview.setUsername(usernmae);
                examReview.setTruename(truename);
                examReview.setReviewUsername(reviewUsername);
                examReview.setReviewTruename(reviewTruename);
                examReview.setQuestionCode(key);
                examReview.setQuestionScore(value);
                examReviewList.add(examReview);
            }
        }
        this.saveAll(examReviewList);

        boolean accomplishMarking = iExamInfoService.accomplishMarking(usernmae);
        if(accomplishMarking){
            return JsonResponse.defaultSuccessResponse();

        }

        return JsonResponse.defaultErrorResponse();
    }
}
