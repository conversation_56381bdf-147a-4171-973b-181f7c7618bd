package com.simbest.boot.exam.examOnline.web;

import com.google.common.collect.Lists;
import com.simbest.boot.base.web.controller.GenericController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.dto.ExamRangeDto;
import com.simbest.boot.exam.examOnline.model.ExamRange;
import com.simbest.boot.exam.examOnline.service.IExamRangeService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.SimpleOrg;
import com.simbest.boot.security.UserOrgTree;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 用途：考试信息模块--考试范围controller
 * 作者：gy
 * 时间: 2021-02-01 10:56
 */
@Api(description = "ExamRangeController", tags = {"考试信息模块-考试范围信息控制器"})
@Slf4j
@RestController
@RequestMapping("/action/range")
public class ExamRangeController extends GenericController<ExamRange, String> {
    private IExamRangeService service;

    @Autowired
    public ExamRangeController(IExamRangeService service) {
        super(service);
        this.service = service;
    }

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;


    @ApiOperation(value = "保存考试范围信息", notes = "保存考试范围信息")
    @PostMapping(value = {"/saveExamRange", "/sso/saveExamRange", "/api/saveExamRange"})
    public JsonResponse saveExamRange(@RequestBody ExamRangeDto o) {
        service.saveExamRange(o.getSummaryId(), o.getRangeList());
        return JsonResponse.success(null, "操作成功!") ;
    }

    @ApiOperation(value = "选择考试范围采用的试卷", notes = "选择考试范围采用的试卷")
    @PostMapping(value = {"/saveRangePaper", "/sso/saveRangePaper", "/api/saveRangePaper"})
    public JsonResponse saveRangePaper(@RequestParam String id,
                                       @RequestParam String examPaperCode) {
        return JsonResponse.success(service.saveRangePaper(id, examPaperCode), "操作成功!") ;
    }

    @ApiOperation(value = "查询考试范围信息", notes = "查询考试范围信息")
    @PostMapping(value = {"/findRangeList", "/sso/findRangeList", "/api/findRangeList"})
    public JsonResponse findRangeList(@RequestBody ExamRange o) {
        return JsonResponse.success(service.findRangeList(o)) ;
    }


    @ApiOperation(value = "查询用户当前所属公司及下级公司", notes = "查询用户当前所属公司及下属公司")
    @PostMapping(value = {"/findCompanyAndChildCompany", "/api/findCompanyAndChildCompany"})
    public JsonResponse findCompanyAndChildCompany() {
        //如果当前人为省公司的人，选出省公司及市公司
        List<SimpleOrg> orgList = uumsSysOrgApi.findPOrgAnd18CityOrg(Constants.APP_CODE);
        //过滤根组织-河南移动
        for (SimpleOrg org : orgList) {
            if (org.getOrgName().equals("河南移动")) {
                orgList.remove(org);
                break;
            }
        }
        return JsonResponse.success(orgList);
    }

    @ApiOperation(value = "一层层去查询全部组织和人", notes = "一层层去查询全部组织和人", tags = {"人员api 层级查"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appcode", value = "当前应用appcode", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgCode", value = "组织编码", dataType = "String", paramType = "query")
    })
    @PostMapping(value = {"/findOneStep", "/api/findOneStep"})
    public JsonResponse findOneStep(@RequestParam String appcode,
                                    @RequestParam(required = false) String orgCode,
                                    @RequestParam(required = false) String source,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestBody(required = false) Map<String, Object> extraValueMap) {
        List<UserOrgTree> list = uumsSysUserinfoApi.findOneStep(appcode, orgCode, extraValueMap, currentUserCode);
        if (StringUtils.isEmpty(orgCode)) {
            // 如果未传入orgCode，则需要过滤特殊公司
            List<UserOrgTree> resultList = Lists.newArrayList();
            for (UserOrgTree tree : list) {
                if (Constants.ORG_CODE_SHENGFEIDA.equals(tree.getId()) || Constants.ORG_CODE_ZHONGDUAN.equals(tree.getId())) {
                    continue;
                }
                resultList.add(tree);
            }
            list.clear();
            list.addAll(resultList);
        }
        list.sort((p1, p2) -> {
            // 人员在前，组织在后
            Integer treeType1 = p1.getTreeType().equals("org") ? 0 : 1;
            Integer treeType2 = p2.getTreeType().equals("org") ? 0 : 1;
            return treeType2.compareTo(treeType1);
        });
        return JsonResponse.success(list);
    }




}
