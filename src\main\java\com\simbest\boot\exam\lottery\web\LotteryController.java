/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.lottery.web;/**
 * Created by KZH on 2019/12/9 9:23.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.lottery.model.Lottery;
import com.simbest.boot.exam.lottery.service.ILotteryService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.util.security.LoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2019-12-09 9:23
 * @desc 考试抽奖
 **/
@Api(description = "考试抽奖")
@Slf4j
@RestController
@RequestMapping(value = "/action/lottery")
public class LotteryController extends LogicController<Lottery, String> {

    private ILotteryService iLotteryService;
    private LoginUtils loginUtils;

    @Autowired
    public LotteryController(ILotteryService service, LoginUtils loginUtils) {
        super(service);
        this.iLotteryService = service;
        this.loginUtils = loginUtils;
    }

    @ApiOperation(value = "根据概率是否中奖", notes = "根据概率是否中奖")
    @PostMapping({"/isLottery", "/sso/isLottery", "/api/isLottery"})
    public JsonResponse isLottery(@RequestParam(required = false, defaultValue = "PC") String source,
                                  @RequestParam(required = false) String currentUserCode,
                                  @RequestParam String examCode) {
        if (StringUtils.isNotEmpty(currentUserCode) && "MOBILE".equals(source)) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        return iLotteryService.isLottery(examCode);
    }

    @ApiOperation(value = "根据概率是否中奖", notes = "根据概率是否中奖")
    @PostMapping(value = {"/isLotteryExamPowerBuilding", "/isLotteryExamPowerBuilding/api", "/isLotteryExamPowerBuilding/sso"})
    public JsonResponse isLotteryExamPowerBuilding(@RequestParam String examCode,
                                                   @RequestParam String source,
                                                   @RequestParam String currentUserCode) {

        return iLotteryService.isLotteryExamPowerBuilding(examCode, source, currentUserCode);
    }

    @ApiOperation(value = "对中奖人的信息进行处理", notes = "对中奖人的信息进行处理")
    @PostMapping({"/createToJackpotReduce", "/sso/createToJackpotReduce", "/api/createToJackpotReduce"})
    public JsonResponse createToJackpotReduce(@RequestParam(required = false, defaultValue = "PC") String source,
                                              @RequestParam(required = false) String currentUserCode,
                                              @RequestBody Lottery lottery) {
        if (StringUtils.isNotEmpty(currentUserCode) && "MOBILE".equals(source)) {
            loginUtils.manualLogin(currentUserCode, Constants.APP_CODE);
        }
        return iLotteryService.createToJackpotReduce(lottery);
    }
}
