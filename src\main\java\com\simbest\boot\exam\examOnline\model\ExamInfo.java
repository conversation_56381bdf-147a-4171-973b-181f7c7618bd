/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;/**
 * Created by KZH on 2019/10/8 10:24.
 */

import com.fasterxml.jackson.annotation.JsonFormat;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 10:24
 * @desc 答题记录表
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_info")
@ApiModel(value = "答题记录表")
public class ExamInfo extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EI") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "答题人",name = "publishUsername",example = "hadmin",required = true)
    private String publishUsername;

    @Column(length = 250)
    @ApiModelProperty(value = "答题人性别",name = "sex",example = "男",required = true)
    private String sex;

    @Column(length = 250)
    @ExcelVOAttribute(name = "测评人员", column = "A")
    @ApiModelProperty(value = "答题人",name = "publishTruename",example = "管理员",required = true)
    private String publishTruename;

    @Column(length = 200)
    @ApiModelProperty(value = "办理人所在部门编码",name = "departmentCode",example = "2700526267653981965")
    private String departmentCode;

    @Column(length = 200)
    @ExcelVOAttribute(name = "所在部门", column = "B")
    @ApiModelProperty(value = "办理人所在部门名称",name = "departmentName",example = "业务支撑中心")
    private String departmentName;

    @Column(length = 200)
    @ApiModelProperty(value = "办理人职务信息",name = "positionName",example = "资深经理")
    private String positionName;

    @Column(length = 40)
    @ApiModelProperty(value = "剩余时间",name = "residueTime",example = "20",required = true)
    private String residueTime;

    @Column(length = 40)
    @ExcelVOAttribute(name = "最终成绩", column = "C")
    @ApiModelProperty(value = "得分",name = "score",example = "100",required = true)
    private String score;

    @Column(length = 40)
    @ExcelVOAttribute(name = "答题次数", column = "D")
    @ApiModelProperty(value = "答题次数",name = "examNumber",example = "1")
    private Integer examNumber;

    @Column(length = 40)
    @ApiModelProperty(value = "考试编码",name = "examCode")
    private String examCode;

    @Column(length = 40)
    @ApiModelProperty(value = "试卷编码",name = "examAppCode",example = "hnjjwz",required = true)
    private String examAppCode;

    @Column(length = 2000)
    @ApiModelProperty(value = "答题记录",name = "examRecord",example = "A-001-1,A-001-2,A-001-3,A-001-4")
    private String examRecord;

    @Column(length = 3000)
    @ApiModelProperty(value = "答案记录",name = "examAnswer",example = "A/B/C/B,C/A,B,C/")
    private String examAnswer;

    @ApiModelProperty(value = "是否完成试卷",name = "isFinishExam",example = "1")
    private Boolean isFinishExam;

    @ApiModelProperty(value = "是否完成阅卷",name = "isMarkingExam",example = "1")
    private Boolean isMarkingExam;

    @Column(length = 40)
    @ApiModelProperty(value = "附件id",name = "fileId",example = "F00000000000000001",required = true)
    private String fileId;

    @Column(length = 40)
    @ApiModelProperty(value = "证书id",name = "certificateID",example = "F00000000000000001",required = true)
    private String certificateID;

    @Column(length = 3000)
    @ApiModelProperty(value = "扩展字段")
    private String extendFields;


    /**
     * 20240408 中央巡视整改工作调查问卷专用  所在部门   政治面貌
     */
    @Column(length = 200)
    @ApiModelProperty(value = "所在部门")
    private String belongDept;

    @Column(length = 100)
    @ApiModelProperty(value = "政治面貌")
    private String politics ;

    @Column
    @ApiModelProperty(value = "答题记录 逻辑删除时间，便于恢复")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime delDate;

    @Column
    @ApiModelProperty(value = "时间戳")
    private Long timestamp;

    @Column
    @ApiModelProperty(value = "洛阳季度标识")
    private String quarter;


    @Transient
    @ApiModelProperty(value = "时间戳加密文")
    private String salt;

    @Transient
    @ExcelVOAttribute(name = "测评结果", column = "E")
    private String pass;

    @Transient
    private SysFile sysFile;

    @Transient
    @ApiModelProperty(value = "是否在答题时间之内")
    private Boolean hasValid;

    /*@Transient
    private List<ExamInfo> examInfoList;*/


    @Transient
    @ApiModelProperty(value = "是否弃权")
    private boolean hasDrop;

    @Transient
    @ApiModelProperty(value = "单选数据信息")
    private List<Map<String , Object>> singleData;

    @Transient
    @ApiModelProperty(value = "多选数据信息")
    private List<Map<String , Object>> multipleData;

    @Transient
    @ApiModelProperty(value = "判断数据信息")
    private List<Map<String , Object>> judgeData;

    @Transient
    @ApiModelProperty(value = "简答数据信息")
    private List<Map<String , Object>> shortData;

    @Transient
    @ApiModelProperty(value = "填空数据信息")
    private List<Map<String , Object>> fillingData;


}
