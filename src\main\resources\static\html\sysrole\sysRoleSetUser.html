<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
	<title>角色配置</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
	<!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
	<link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
		rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
	<link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
	<script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
	<script type="text/javascript">
		var arr = []
		$(function () {
			//showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
			var param = {
				"listtable": {
					"listname": "#roleUserTable",//table列表的id名称，需加#
					"querycmd": "action/userRoleManage/findUserByRoleId?roleId=" + gps.roleId,//table列表的查询命令
					"checkboxall": true,
					"styleClass": "noScroll",
					"fitColumns": true,
					// "idField": "USERNAME",
					//"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
					"nowrap": true,//把数据显示在一行里,默认true
					"frozenColumns": [[
						{ field: "ck", checkbox: true }
					]],//固定在左侧的列
					"columns": [[//列
						{ title: "OA账号", field: "USERNAME", width: 80 },
						{ title: "用户姓名", field: "TRUENAME", width: 80 },
						{ title: "所在组织的全路径", field: "DISPLAY_NAME", width: 200 },
						{
							field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
							formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
								arr = row
								var g = "<a  href='#' class='del operateRed' username=" + row.USERNAME + " '>【删除】</a>";
								// var g = "<a href='#' contentType='application/x-www-form-urlencoded; charset=utf-8' class='del' '>【删除】</a>";
								return g;
							}
							// delete='action/userRoleManage/deleteById?id=" + gps.roleId+ "' deleteid='" + usernames + "
						}
					]],
					"pagerbar": [{
						id: "deleteall",
						iconCls: 'icon-remove',
						text: "批量删除&nbsp;"
					}],
					"deleteall": {//批量删除deleteall.id要与pagerbar.id相同
						"id": "deleteall",
						"contentType": "application/json; charset=utf-8",
						"url": "action/userRoleManage/deleteById?id=" + gps.roleId,
					}
				}
			};
			//加载list表格
			loadGrid(param);
			//查询已有数据
			$(document).on('click', '.del', function (e) {
				//console.log(e.target.attributes.username.value);
				let usernames = []
				usernames.push(e.target.attributes.username.value)
				$.messager.confirm("删除", "确定删除吗?", function (r) {
					if (r) {
						ajaxgeneral({
							url: "action/userRoleManage/deleteById?id=" + gps.roleId,
							contentType: "application/json; charset=utf-8",
							data: usernames,
							success: function (datas) {
								$('#searchtable').click()
								loadGrid(param);
							}
						});
					} else {

					}
				})
			})


			//选择用户
			$(".choosePersons,.chooseGroups,.chooseOrgs").on("click", function () {
				//第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
				//第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
				var cn = "choosePersons";
				var dn = "user_choose";
				var tn = "选择用户";
				if ($(this).hasClass("chooseOrgs")) { cn = "chooseOrgs"; dn = "org_choose"; tn = "选择组织"; }
				if ($(this).hasClass("chooseGroups")) { cn = "chooseGroups"; dn = "group_choose"; tn = "选择角色"; }
				var href = { "multi": "1", "name": cn + "Val" };
				if ($("." + dn + " a").length > 0) {
					var datas = [];
					$("." + dn + " a").each(function (i, v) {
						var datai = {};
						datai.id = $(v).attr("id");
						datai.name = $(v).find("font").text();
						datas.push(datai);
					});
					top.chooseWeb[cn + "Val"] = { "data": datas };
				} else {//表示新增
					top.chooseWeb[cn + "Val"] = { "data": [] };
				}
				var url = tourl($(this).attr("path"), href);
				//第二个值看当前页面的iframe的name值，去f12里面看页面属性
				top.dialogP(url, window.name, tn, cn, false, '800');
			});
			//删除
			$(document).on("click", ".user_choose a i,.org_choose a i,.group_choose a i", function () {
				$(this).parent().remove();
			});
			//点击增加按钮把用户加入角色
			$(".addAboveUser").on("click", function () {
				var roleId = gps.roleId;
				var links = $(".user_choose a");
				var len = links.length;
				var usernames = "";
				//获取每个div标签下的a标签中的id值，值为oa账号
				for (var i = 0; i < len; i++) {
					usernames = usernames + $(links[i]).attr('id');
					if (i != len - 1) {
						usernames = usernames + ",";
					}
				}
				ajaxgeneral({
					url: "action/userRoleManage/createRoleUsers?roleId=" + roleId + "&userName=" + usernames,
					contentType: "application/json; charset=utf-8",
					success: function (data) {
						getparent().mesShow("温馨提示", data.message || "操作成功", 1000);
						$('#searchtable').click()
						loadGrid(param);
					}
				});
			});
			//清空用户
			$(".clearAboveUser").on("click", function () {
				$(".user_choose a").each(function () {
					$(this).remove();
				});
			});
		});

		//用于回显
		window.choosePersons = function (data) {
			dTem("user_choose", data.data);
		};
		window.chooseOrgs = function (data) {
			for (var i in data.data) {
				data.data[i].id = data.data[i].orgCode;
				data.data[i].name = data.data[i].displayName;
				if ($(".org_choose a[id=" + data.data[i].id + "]").length == 0) $(".org_choose").append("<a id='" + data.data[i].id + "'><font>" + data.data[i].name + "</font><i class='fr iconfont'>&#xe6ef;</i></a>");
			}
		};
		window.chooseGroups = function (data) {
			dTem("group_choose", data.data);
		};
		function dTem(dn, data) {
			for (var i in data) {
				if ($("." + dn + " a[id=" + data[i].id + "]").length == 0) $("." + dn).append("<a id='" + data[i].id + "'><font>" + data[i].name + "</font><i class='fr iconfont'>&#xe6ef;</i></a>");
			}
		};
		//刷新列表以及右侧的树形选择框
		function formLoad() {
			$("#groupuserTable").datagrid("reload");
			//移除掉右侧的列表中代表用户的a标签
			$(".user_choose a").each(function () {
				$(this).remove();
			});
		};
		//form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
		function beforerender(data, isupdate) {
			if (isupdate) {
				$('.update-readonly').hide();
			} else {
				$('.update-readonly').show();
			}
		};

	</script>
</head>

<body style="padding-right:240px;">
	<!--<h6 class="pageTit"><font class="col_b fwb">群组设置</font><i class="iconfont">&#xe66e;</i><small>为群组新增或删除人、组织、子群组</small></h6>-->
	<!--查询框-->
	<form id="roleUserTableQueryForm">
		<table border="0" cellpadding="0" cellspacing="6" width="100%">
			<tr>
				<td width="90" align="right">OA账号：</td>
				<td width="150"><input name="username" type="text" value="" /></td>
				<td>
					<div class="w100">
						<a class="btn a_primary fl searchtable"><span>查询</span></a>
					</div>
				</td>
			</tr>
		</table>
	</form>
	<div class="roleUserTable">
		<table id="roleUserTable"></table>
	</div>
	<table border="0" cellpadding="0" cellspacing="8" style="position:fixed;top:0;right:0;bottom:0;width:240px;">
		<tr>
			<td colspan="2" class="lh32"><b>选择新增用户:</b><a class="fr btn a_success choosePersons"
					path="html/sysuser/chooseUsers.html"><i class="iconfont">&#xe634;</i></a></td>
		</tr>
		<tr>
			<td colspan="2">
				<div class="role user_choose"></div>
			</td>
		</tr>
		<tr>
			<td width="100"><a class="fl btn a_success clearAboveUser">清空用户</a></td>
			<td class="lh32"><a class="fr btn a_success addAboveUser">增加用户</a></td>
		</tr>
	</table>
</body>

</html>