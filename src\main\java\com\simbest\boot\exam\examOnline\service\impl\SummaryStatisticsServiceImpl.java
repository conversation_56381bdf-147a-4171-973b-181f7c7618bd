package com.simbest.boot.exam.examOnline.service.impl;

import com.simbest.boot.base.service.impl.SystemService;
import com.simbest.boot.exam.examOnline.model.SummaryStatistics;
import com.simbest.boot.exam.examOnline.repository.SummaryStatisticsRepository;
import com.simbest.boot.exam.examOnline.service.ISummaryStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;



/**
 * <AUTHOR>
 * @title: SummaryStatisticsServiceImpl
 * @projectName exam
 * @description:
 * @date 2021/6/29  20:54
 */
@Service
@Slf4j
public class SummaryStatisticsServiceImpl extends SystemService<SummaryStatistics,String> implements ISummaryStatisticsService {
    private SummaryStatisticsRepository repository;

    @Autowired
    public SummaryStatisticsServiceImpl(SummaryStatisticsRepository repository) {
        super(repository);
        this.repository = repository;
    }

    /**
     *获取所有季度信息
     * <AUTHOR>
     * @date 2021/6/29
     * @param
     */
    @Override
    public Page<SummaryStatistics> ListSummary() {
        Sort createdTime = Sort.by("createdTime");
        return findAll(createdTime);
    }
}
