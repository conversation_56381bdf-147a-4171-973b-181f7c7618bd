<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>资源管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/public.css?v=svn.revision" th:href="@{../../css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        html,body{
            overflow-y:auto
        }
    </style>
    <script type="text/javascript">
		$(function(){
			//showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称

            var pageparam={
				"listtable":{
					"listname":"#permissionTable",//table列表的id名称，需加#
					"querycmd":"action/permission/permission/findAll",//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
					"nowrap": true,//把数据显示在一行里,默认true
					"checkboxall":true,
					"remoteSort":false,
					"frozenColumns":[[
						{ field: "ck",checkbox:true}
					]],//固定在左侧的列
					"columns":[[//列   
						{ title: "所属应用编码", field: "appCode", width: 40,
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
						        return row.appId.appCode;
                            }
                        },
                        { title: "所属应用名称", field: "appName", width: 90,
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                return row.appId.appName;
                            }
                        },
						{ title: "资源路径", field: "url", width: 100,tooltip:true},
						{ title: "资源图标", field: "icon", width: 40},
						{ title: "资源描述", field: "description", width: 80 },
						{ title: "菜单级别", field: "menuLevel", width: 40 },//排序sortable: true
						{ title: "上级菜单", field: "parentId", width: 40 },//排序sortable: true
						{
							field: "opt", title: "操作", width: 80, rowspan: 1,//align：对齐此列的数据，可以用left、right、center【查看】
							formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
								var g = "<a href='#' class='showDialog' showDialogindex='" + index + "'>【修改】</a>"
								+"<a href='#' contentType='application/x-www-form-urlencoded; charset=utf-8' delete='action/permission/permission/deleteById' deleteid='"+row.id+"'>【删除】</a>";
								return g;
							}
						}
					] ],
					"pagerbar": [{
						id:"deleteall",
						iconCls: 'icon-remove',
						text:"批量删除&nbsp;"
					}],
					"deleteall":{//批量删除deleteall.id要与pagerbar.id相同
						"id":"deleteall",
						"url":"action/permission/permission/deleteAllByIds",
						"contentType":"application/json; charset=utf-8"
					}
				},
				"dialogform":{
					"dialogid":"#buttons",//对话框的id
					"formname":"#permissionTableAddForm",//新增或修改对话框的formid需加#
					"getcmd":"action/permission/permission/findById",//查询单条命令
					"insertcmd":"action/permission/permission/create",//新增命令
					"updatacmd":"action/permission/permission/update",//修改命令
					"onSubmit":function(data){
						if($("#permissionCode").attr("codeError") && $("#permissionTableAddForm #id").val()==""){
							top.mesAlert("提示信息","资源编码已存在,请重新输入！", 'error');
							return false;
						}
						return true;
					}
				}
			};
			loadGrid(pageparam);
		});
		function beforerender( data ) {
		    //console.log(data);
		    if(data.appId!=null){
                data.appCode=data.appId.appCode;
            }
        }
    </script>
</head>
<body class="body_page_uums">
<h6 class="pageTit"><font class="col_b fwb">权限管理</font><i class="iconfont">&#xe66e;</i><small>权限的增删改查</small></h6>
<!--searchform-->
<form id="permissionTableQueryForm"> 
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">资源描述：</td><td width="150"><input  name="description" type="text" value="" /></td>
            <td width="90" align="right">资源路径：</td><td width="150"><input  name="url" type="text" value="" /></td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fl searchtable"><span>查询</span></a>   
					<a class="btn a_success showDialog fr"><span>新增</span></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="permissionTable"><table id="permissionTable"></table></div>
<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:900px;height:500px;">
<!--scontentType获取数据时设置传参方式，不设置默认为kv  cmd-select获取数据命令配置，不配置自动从列表中取   cmd-insert新增  cmd-update修改-->
<form id="permissionTableAddForm" method="post" contentType="application/json; charset=utf-8" beforerender="beforerender()">
	<input id="id" name="id" type="hidden" />
	<table border="0" cellpadding="0" cellspacing="10" width="100%">
        <tr>
            <td width="120" align="right">上级菜单：</td>
            <td colspan="3">
                <input id="parentId" name="parentId" class="easyui-combotree" style="width:100%;height:32px;" data-options="
				ischooseall:true,
				panelHeight:'250px',
				treeprompt:'--请选择--',
				treeId:'id',
				treePid:'parentId',
                fileds:'id,description|text,parentId',
				url: '/uums/action/permission/permission/findAllPermissions'"/>
            </td>
        </tr>
		<tr>		
            <!--<td width="100" align="right">权限标识：</td><td><input id="permissionCode" name="permissionCode" type="text" class="easyui-validatebox" required='required' cmd="action/permission/permission/isHaveCode" onblur="isHaveCode(this)" /></td>-->
            <td width="120" align="right">权限标识：</td>
            <td width="160">
                <input id="permissionCode" name="permissionCode" type="text" class="easyui-validatebox" required='required'/>
            </td>
            <td width="100" align="right">资源图标：</td>
            <td width="180">
                <input id="icon" name="icon" type="text"/>
            </td>
		</tr>
        <tr>
            <td width="120" align="right">资源路径：</td>
            <td  >
                <input id="url" name="url" type="text" />
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td width="120" align="right">所属应用：</td>
            <td width="160" >
                <input id="appCode" name="appCode" class="easyui-combobox" required='required' style="width: 100%; height: 32px;" data-options="
                        valueField: 'appCode',
                        ischooseall:true,
                        textField: 'appName',
                        queryParams:{
                            'spare1':'oa'
                        },
                        contentType:'application/json; charset=utf-8',
                        url: '/uums/action/app/app/findAllNoPage'" />
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td width="120" align="right">权限类型：</td>
            <td width="160"  >
                <input id="permissionType" name="permissionType" class="easyui-combobox" required='required' style="width: 100%; height: 32px;" data-options="
					valueField: 'value',
					ischooseall:true,
					textField: 'name',
					panelHeight:'auto',
					queryParams:{'dictType':'permissionType'},
					contentType:'application/json; charset=utf-8',
					url: '/uums/sys/dictValue/findDictValue'" />
            </td>
            <td></td>
            <td></td>
        </tr>
		<tr>		
            <td width="120" align="right">菜单级别：</td>
            <td width="160">
				<select class="easyui-combobox" id="menuLevel" name="menuLevel" style="width: 100%; height: 32px;" data-options="panelHeight:'auto'">
					<option value="" selected>--请选择--</option>
					<option value="1">一级</option>
					<option value="2">二级</option>
                    <option value="3">三级</option>
                    <option value="4">四级</option>
				</select>
			</td>
			<td width="100" align="right">显示顺序：</td>
            <td width="180">
                <input id="displayOrder" name="displayOrder" class="easyui-validatebox" validType="zinteger" type="text" />
            </td>
		</tr>

        <tr>
            <td width="120" align="right" valign="top">资源描述：</td>
            <td colspan="3">
                <textarea placeholder="最多输入200字" class="easyui-validatebox" validType="maxLength[200]" id="description" name="description" style="width:100%;height:120px;resize:none;" ></textarea>
            </td>
        </tr>
    </table>
</form>
</div>
</body>
</html>
