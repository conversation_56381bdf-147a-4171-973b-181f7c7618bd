<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>试卷评测</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <style>
        .scoreBox {
            font-size: 18px;
            margin-left: 13px;
        }
        #score {
            font-size: 22px;
            color: red;
        }
        .panel.combo-p {
            height: 100px;
            overflow-y: auto;
            border: 1px solid #e6e6e6;
        }
        .combo-panel.panel-body.panel-body-noheader {
            border: none;
        }
    </style>
    <script type="text/javascript">
        $(function () {
            $(".searchScore").click(function () {
                if (!getFormValue("examCodeForm").examCode) {
                    getparent().mesShow("温馨提示","请选择试卷", 1000,'red');
                } else {
                    ajaxgeneral({
                        url:"action/examInfo/findExamInfoByExamCode?examCode="+getFormValue("examCodeForm").examCode,
                        data:{examCode: getFormValue("examCodeForm").examCode},
                        contentType:"application/json; charset=utf-8",
                        success:function(datas){
                            if (datas.data.score) {
                                $(".scoreBox").show()
                                $("#score").html(datas.data.score)
                            }
                        },sError: function (data) {
                            $(".scoreBox").hide()
                        }, error: function (data) {
                            $(".scoreBox").hide()
                        }
                    });
                }
            })
        })
    </script>
</head>
<body class="body_page">
<form id="examCodeForm">
    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td width="150" align="right">请选择要查询的考试：</td>
            <td width="280">
                <input  name="examCode" type="text" class="easyui-combobox" editable="false" style="width:100%;height: 32px"
                        data-options="valueField: 'examCode',
                             panelHeight:'auto',
                             textField: 'examName',
                             contentType:'application/json; charset=utf-8',
                             url: web.rootdir+'action/summary/findAllNoPage',
                             prompt:'--请选择--'"/>
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl searchScore "><font>查询</font></a>
                </div>
            </td>
            <td></td>
            <td></td>
        </tr>
    </table>
</form>
<div class="scoreBox" style="display: none">
    您的成绩为<span id="score"></span>分
</div>
</body>
</html>
