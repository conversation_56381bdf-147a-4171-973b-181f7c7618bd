package com.simbest.boot.exam.background.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.sys.model.SysFile;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.beans.Transient;
import java.util.List;

@Repository
public interface FileExtendRepository extends LogicRepository<SysFile, String> {

    @Transient
    @Modifying
    @Query(value = "update SYS_FILE t set t.pm_ins_id =:pmInsId where t.id = :id", nativeQuery = true)
    int updatePmInsId(@Param("pmInsId") String pmInsId, @Param("id") String id);

    @Transient
    @Query(value = "select * from SYS_FILE t where t.pm_ins_id =:pmInsId and t.removed_time is null and t.pm_ins_type_part = :typePart", nativeQuery = true)
    List<SysFile> getPartFile(@Param("pmInsId") String pmInsId, @Param("typePart") String typePart);

    @Query(nativeQuery = true, value = "select * from SYS_FILE t where t.pm_ins_id =:pmInsId and t.removed_time is null order by t.created_time asc")
    List<SysFile> findSysFileByPmInsId(@Param("pmInsId") String pmInsId);

    @Modifying
    @Query(nativeQuery = true, value = "delete from SYS_FILE t where t.id = :id")
    void deleteSysFile(@Param("id") String id);
}
