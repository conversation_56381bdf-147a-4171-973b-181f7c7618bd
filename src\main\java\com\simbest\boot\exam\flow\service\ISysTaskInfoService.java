package com.simbest.boot.exam.flow.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.flow.model.SysTaskInfo;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: ISysTaskInfoService
 * @description:
 * @author: ZHAOBO
 * @create: 2024-07-09 17:31
 */
public interface ISysTaskInfoService extends ILogicService<SysTaskInfo, String> {

    /**
     * 查询待办
     * @param page  页码
     * @param rows  数量
     * @param source    来源
     * @param title     标题
     * @param pmInsType     单据类型
     * @param currentUserCode   当前人
     * @return
     */
    JsonResponse myTaskToDo(Integer page, Integer rows, String source, String title, String pmInsType, String applyNumber , String currentUserCode,String pmInsId);

    /**
     * 查询已办
     * @param page  页码
     * @param rows  数量
     * @param source    来源
     * @param title     标题
     * @param pmInsType     单据类型
     * @param currentUserCode   当前人
     * @return
     */
    JsonResponse myJoin(Integer page, Integer rows, String source, String title, String pmInsType, String applyNumber , String currentUserCode);


    /**
     * 流程启动
     * @param map
     * @return
     */
    Map<String , Object> startProcess(Map<String  , Object> map);

    int updateTaskStatus (String taskId);
    /**
     * 根据待办编码获取待办信息
     * @param taskId        待办编码
     * @return
     */
    SysTaskInfo findByTaskId (String taskId);

    /**
     * 完成当前任务
     * @param taskId        任务实例id
     * @param decisionInfo  决策项
     */
    int finishTaskInfo(String taskId,  String nextActivityDefId , String nextActivityDefName);



    List<SysTaskInfo> queryTodoTaskInfoByParam(String applyNumber,String belongCompanyName,String belongDepartmentName);

    /**
     * 已阅
     *
     * @param taskId
     * @return
     */
    JsonResponse done( String taskId);

    /**
     *
     * @param pmInsId
     * @param type (taskToDo 待办 ,myDone 已办,null 全查 )
     * @return
     */
    List<SysTaskInfo>  findByPmInsId(String pmInsId, String type);
    Page<SysTaskInfo> findByPmInsIdPage(String pmInsId, String type,Integer page, Integer rows);

    List<SysTaskInfo> findTaskByUserName(String currentUserName);
}
