package com.simbest.boot.exam.examOnline.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.SystemModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Map;

/**
 * 用途：考试管理--考试范围模块
 * 当前实体控制控制参与考试人员得群组
 * 作者：sws
 * 时间: 2021-04-22
 */
@Data

public class ExamRangeUserInfoExcel {
    @ExcelVOAttribute(name = "人员id", column = "A")
    private String userName;
}
