package com.simbest.boot.exam.publicLottery.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.component.repeat.lock.RequestLock;
import com.simbest.boot.exam.publicLottery.model.PublicLottery;
import com.simbest.boot.exam.publicLottery.service.IPublicLotteryService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.security.LoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/action/publicLottery")
public class PublicLotteryController extends LogicController<PublicLottery, String> {
    private final LoginUtils loginUtils;
    private final RsaEncryptor rsaEncryptor;
    private final IPublicLotteryService service;

    public PublicLotteryController(IPublicLotteryService service, LoginUtils loginUtils, RsaEncryptor rsaEncryptor) {
        super(service);
        this.service = service;
        this.loginUtils = loginUtils;
        this.rsaEncryptor = rsaEncryptor;
    }

    /**
     * 查询个人信息
     */
    @PostMapping(value = {"/getLottery", "/getLottery/sso", "/api/getLottery"})
    public JsonResponse getLottery(@RequestParam(required = false) String source,
                                   @RequestParam(required = false) String currentUserCode) {
        return JsonResponse.success(service.getLottery());
    }

    /**
     * 摇号结果公示
     */
    @PostMapping(value = {"/getLotteryList", "/getLotteryList/sso", "/api/getLotteryList", "/getLotteryList/anonymous"})
    public JsonResponse getLotteryList(@RequestParam(required = false) String source,
                                       @RequestParam(required = false) String currentUserCode) {
        return JsonResponse.success(service.getLotteryList());
    }

    /**
     * 公开摇号请求
     */
    @RequestLock(expire = 10L)
    @PostMapping(value = {"/drawLottery", "/drawLottery/sso", "/api/drawLottery"})
    public JsonResponse drawLottery(@RequestParam(required = false) String source,
                                    @RequestParam(required = false) String currentUserCode) {
        return JsonResponse.success(service.drawLottery());
    }

    /**
     * 测试公开摇号
     */
    @PostMapping(value = {"/drawLotteryTest", "/drawLotteryTest/sso", "/api/drawLotteryTest", "/drawLotteryTest/anonymous"})
    public JsonResponse drawLotteryTest(@RequestParam(required = false) String source,
                                        @RequestParam(required = false) String currentUserCode) {
        loginUtils.manualLogin(rsaEncryptor.encrypt(currentUserCode), Constants.APP_CODE);
        return JsonResponse.success(service.drawLottery());
    }

    /**
     * 重置摇号池
     */
    @PostMapping(value = {"/resetLottery", "/resetLottery/sso", "/api/resetLottery", "/resetLottery/anonymous"})
    public JsonResponse resetLottery(@RequestParam(required = false) String source,
                                     @RequestParam(required = false) String currentUserCode) {
        loginUtils.adminLogin();
        service.resetLottery();
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 添加摇号人员
     */
    @PostMapping(value = {"/resetLotteryPerSon", "/resetLotteryPerSon/sso", "/api/resetLotteryPerSon", "/resetLotteryPerSon/anonymous"})
    public JsonResponse resetLotteryPerSon(String username,
                                           boolean allDelete,
                                           @RequestParam(required = false) String source,
                                           @RequestParam(required = false) String currentUserCode) {
        loginUtils.adminLogin();
        service.resetLotteryPerSon(username, allDelete);
        return JsonResponse.defaultSuccessResponse();
    }
}
