/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service.Impl;/**
 * Created by KZH on 2019/10/8 16:07.
 */


import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.component.distributed.lock.DistributedRedisLock;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionService;
import com.simbest.boot.exam.knowledge.model.UsAnswerRecord;
import com.simbest.boot.exam.knowledge.model.UsUserAnswers;
import com.simbest.boot.exam.knowledge.service.IUsAnswerRecordService;
import com.simbest.boot.exam.knowledge.service.IUsDailyQuestionsService;
import com.simbest.boot.exam.knowledge.service.IUsUserAnswersService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class UsDailyQuestionsServiceImpl implements IUsDailyQuestionsService {

    @Autowired
    SysDictValueService sysDictValueService;

    @Autowired
    IExamQuestionService iExamQuestionService;

    @Autowired
    IUsAnswerRecordService usAnswerRecordService;

    @Autowired
     LoginUtils loginUtils;

    @Autowired
    IUsUserAnswersService userAnswersService;

    @Autowired
    IExamQuestionAnswerService iExamQuestionAnswerService;

    @Autowired
    private RsaEncryptor rsaEncryptor;
    /**
     * 获取答题题目列表接口
     *
     * @param source
     * @param currentUserCode
     * @return
     */
    @Override
    public JsonResponse getAnswersList( String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();

        Integer dailyQuestionCount =Integer.valueOf( sysDictValueService.findByDictType("dailyQuestionCount").get(0).getValue());//日常答题题目数量
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
        //先判断今日答题数量书否已用完
        List<UsAnswerRecord> usAnswerRecordList = usAnswerRecordService.findTodyAnswerRecordByWorkType(knowledgeQuestionBankCode, Constants.ANSWER_RECORD_DAILY,currentUser.getUsername());
        Iterator<UsAnswerRecord> iterator = usAnswerRecordList.iterator();
      String questionId ="";
        List<String> questionIds=new ArrayList<>();
        while (iterator.hasNext()){
            UsAnswerRecord usAnswerRecord = iterator.next();
            questionId=usAnswerRecord.getQuestionId();
            Specification<UsUserAnswers> specification= Specifications.<UsUserAnswers>and().eq("answerRecordId",usAnswerRecord.getId()).build();
            List<UsUserAnswers> usUserAnswers = userAnswersService.findAllNoPage(specification);
            //处理题目未答完退出的问题。
            if (usUserAnswers.size()>0&&usUserAnswers.size()<dailyQuestionCount){
                questionIds = usUserAnswers.stream().map(usUserAnswers1 -> usUserAnswers1.getQuestionId()).collect(Collectors.toList());
                dailyQuestionCount = dailyQuestionCount - questionIds.size();
            }
        }
        List<String> list = new ArrayList<>(Arrays.asList(questionId.split(",")));
        //过滤掉已答题
        if (questionIds.size()>0){
            Iterator<String> iterator1 = list.iterator();
            while (iterator1.hasNext()){
                String next = iterator1.next();
                if (questionIds.contains(next)){
                    iterator1.remove();
                }
            }
        }

        List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByIDs(list);
        Collections.sort(examQuestionList);
        for (ExamQuestion examQuestion : examQuestionList) {
            List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
            examQuestion.setAnswerList(examQuestionAnswerList);
        }
        return JsonResponse.success(examQuestionList);
    }

    @Override
    public JsonResponse answersRecordCheck(String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        Integer dailyQuestionCount =Integer.valueOf( sysDictValueService.findByDictType("dailyQuestionCount").get(0).getValue());//日常答题题目数量
        Integer dailyAnswerCount =Integer.valueOf( sysDictValueService.findByDictType("dailyAnswerCount").get(0).getValue());//日常答题次数
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
        //先判断今日答题数量书否已用完
        IUser currentUser = SecurityUtils.getCurrentUser();
        List<UsAnswerRecord> usAnswerRecordList = usAnswerRecordService.findTodyAnswerRecordByWorkType(knowledgeQuestionBankCode, Constants.ANSWER_RECORD_DAILY,currentUser.getUsername());

        //处理题目未答完退出的问题。
        Boolean ifCountinue=false;
        Iterator<UsAnswerRecord> iterator = usAnswerRecordList.iterator();
        Map<String, Object> resultMap = new HashMap<>();
        String answerRecordId="";
        while (iterator.hasNext()){
            UsAnswerRecord usAnswerRecord = iterator.next();
            Specification<UsUserAnswers> specification= Specifications.<UsUserAnswers>and().eq("answerRecordId",usAnswerRecord.getId()).build();
            List<UsUserAnswers> usUserAnswers = userAnswersService.findAllNoPage(specification);
            if (usUserAnswers.size()<dailyQuestionCount){
                answerRecordId = usAnswerRecord.getId();
                iterator.remove();
                ifCountinue=true;
            }
        }

        resultMap.put("answerRecordId",answerRecordId);
        resultMap.put("toAnswer",dailyAnswerCount-usAnswerRecordList.size());
        resultMap.put("answerd",usAnswerRecordList.size());
        resultMap.put("ifCountinue",ifCountinue);
        return JsonResponse.success(resultMap);
    }

    @Override
    public JsonResponse saveRecord(String workType, String source, String currentUserCode) {
            //手机端模拟登陆
            if (Constants.SOURCE_M.equals(source)){
                loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
            }
            IUser currentUser = SecurityUtils.getCurrentUser();
            String key = currentUser.getUsername() + "-" + workType;
        try {
            DistributedRedisLock.lock(key , 20);
            //校验答题次数是否用完
            Integer dailyQuestionCount =Integer.valueOf( sysDictValueService.findByDictType("dailyQuestionCount").get(0).getValue());//日常答题数量
            Integer dailyAnswerCount =Integer.valueOf( sysDictValueService.findByDictType("dailyAnswerCount").get(0).getValue());//日常答题次数
            String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
            List<UsAnswerRecord> usAnswerRecordList = usAnswerRecordService.findTodyAnswerRecordByWorkType(knowledgeQuestionBankCode, Constants.ANSWER_RECORD_DAILY,currentUser.getUsername());
            if (dailyAnswerCount<=usAnswerRecordList.size()){
                return JsonResponse.fail("今日已答题，请明日再答！");
            }
            List<ExamQuestion> questionList = iExamQuestionService.getRandomQuestionsByCategory(knowledgeQuestionBankCode, dailyQuestionCount);
            List<String> ids = questionList.stream().map(examQuestion -> examQuestion.getId()).collect(Collectors.toList());
            List<String> codeList = questionList.stream().map(examQuestion -> examQuestion.getQuestionCode()).collect(Collectors.toList());
            String questionId = String.join(",", ids);
            String questionCode = String.join(",", codeList);
            UsAnswerRecord usAnswerRecord = new UsAnswerRecord();
            usAnswerRecord.setExamCode(knowledgeQuestionBankCode);
            usAnswerRecord.setAnsewersUserName(currentUser.getUsername());
            usAnswerRecord.setAnsewersTrueName(currentUser.getTruename());
            usAnswerRecord.setWorkType(workType);
            usAnswerRecord.setBelongCompanyCode(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyCodeParent() : currentUser.getBelongCompanyCode() );
            usAnswerRecord.setBelongCompanyName(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyNameParent() : currentUser.getBelongCompanyName());
            usAnswerRecord.setBelongDepartmentCode(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyCode() : currentUser.getBelongDepartmentCode());
            usAnswerRecord.setBelongDepartmentName(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyName() : currentUser.getBelongDepartmentName());
            usAnswerRecord.setBelongOrgCode(currentUser.getBelongOrgCode());
            usAnswerRecord.setBelongOrgName(currentUser.getAuthOrgs().iterator().next().getDisplayName());
            usAnswerRecord.setBelongCompanyTypeDictValue(currentUser.getBelongCompanyTypeDictValue());
            usAnswerRecord.setQuestionId(questionId);
            usAnswerRecord.setQuestionCode(questionCode);
            usAnswerRecordService.insert(usAnswerRecord);
            return JsonResponse.success(usAnswerRecord);
        } catch (Exception e) {
           log.error("--->>>saveRecord接口异常，{}",e.toString());
           return JsonResponse.fail("答题开始失败，请联系管理员");
        } finally {
            DistributedRedisLock.unlock(key);
        }
    }

    @Override
    public JsonResponse saveAnswer(Map<String, Object> requestParam, String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }
        IUser currentUser = SecurityUtils.getCurrentUser();
        String knowledgeQuestionBankCode = sysDictValueService.findByDictType("knowledgeQuestionBankCode").get(0).getValue();
        Integer dailyAnswerCount =Integer.valueOf( sysDictValueService.findByDictType("dailyAnswerCount").get(0).getValue());//日常答题次数

        String answerRecordId = MapUtil.getStr(requestParam, "answerRecordId");//用户答题记录表主键id
        String questionId = MapUtil.getStr(requestParam, "questionId");//问题ID
        String questionCode = MapUtil.getStr(requestParam, "questionCode");//问题Code
        String chosenAnswer = MapUtil.getStr(requestParam, "chosenAnswer");//用户回答的答案
        Assert.notNull(answerRecordId, "answerRecordId不能为空，检查参数！");
        Assert.notNull(questionId, "questionId不能为空，检查参数！");
        Assert.notNull(questionCode, "questionCode不能为空，检查参数！");
        Assert.notNull(chosenAnswer, "chosenAnswer不能为空，检查参数！");
        ExamQuestion examQuestion = iExamQuestionService.findAllByQuestionCode(questionCode);
        if (examQuestion==null){
            return  JsonResponse.fail("未查询到对应试题");
        }

        List<UsUserAnswers> userAnswersList= userAnswersService.findUsUserAnswersByQuestionIdAndAnswerRecordId(questionId,answerRecordId);
        if (userAnswersList.size()>0){
            return JsonResponse.fail(null,null,204);
        }
        //判断答题是否正确
        List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(examQuestion.getQuestionCode());
        List<ExamQuestionAnswer> examQuestionAnswers = examQuestionAnswerList.stream().filter(examQuestionAnswer ->  examQuestionAnswer.getIsCorrect()!=null&&true ==examQuestionAnswer.getIsCorrect()).collect(Collectors.toList());
        List<String> collect = examQuestionAnswers.stream().map(examQuestionAnswer -> examQuestionAnswer.getAnswerCode()).collect(Collectors.toList());
        List<String> chosenAnswerList = Arrays.asList(chosenAnswer.split(","));
        Collections.sort(chosenAnswerList);
        Collections.sort(collect);
        String isCorrect="0";
        if (examQuestionAnswers.size()>0&&chosenAnswerList.equals(collect)){//正确
                isCorrect="1";
        }

        UsUserAnswers usUserAnswers = new UsUserAnswers();
        usUserAnswers.setAnswerRecordId(answerRecordId);
        usUserAnswers.setAnsewersUserName(currentUser.getUsername());
        usUserAnswers.setAnsewersTrueName(currentUser.getTruename());
        usUserAnswers.setQuestionBankCode(knowledgeQuestionBankCode);
        usUserAnswers.setQuestionId(questionId);
        usUserAnswers.setQuestionCode(questionCode);
        usUserAnswers.setChosenAnswer(chosenAnswer);
        usUserAnswers.setTrueAnswer(String.join(",",collect));
        usUserAnswers.setIsCorrect(isCorrect);
        usUserAnswers.setScore(examQuestion.getQuestionScore());
        usUserAnswers.setAnswerTime(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
        usUserAnswers.setWorkType(Constants.ANSWER_RECORD_DAILY);
        usUserAnswers.setBelongCompanyCode(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyCodeParent() : currentUser.getBelongCompanyCode() );
        usUserAnswers.setBelongCompanyName(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyNameParent() : currentUser.getBelongCompanyName());
        usUserAnswers.setBelongDepartmentCode(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyCode() : currentUser.getBelongDepartmentCode());
        usUserAnswers.setBelongDepartmentName(StrUtil.equals(currentUser.getBelongCompanyTypeDictValue() , Constants.COUNTY_CODE) ? currentUser.getBelongCompanyName() : currentUser.getBelongDepartmentName());
        usUserAnswers.setBelongOrgCode(currentUser.getBelongOrgCode());
        usUserAnswers.setBelongOrgName(currentUser.getAuthOrgs().iterator().next().getDisplayName());
        usUserAnswers.setBelongCompanyTypeDictValue(currentUser.getBelongCompanyTypeDictValue());
        userAnswersService.insert(usUserAnswers);
      List <UsUserAnswers> usUserAnswersList= userAnswersService.getUsUserAnswersByRecordId(answerRecordId);
      if (usUserAnswersList.size()>=dailyAnswerCount){
          UsAnswerRecord usAnswerRecord = usAnswerRecordService.findById(answerRecordId);
          usAnswerRecord.setEndTime(DateUtil.getDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
          Map<String,Object>   map= userAnswersService.findListByAnswerRecordId(answerRecordId);
          DateTime minTime = DateUtil.getJodaDateTime(map.get("MINTIME") + "", "yyyy-MM-dd HH:mm:ss");
          // 将毫秒转换为分钟和秒
          Date date = new Date();
          DateTime dateTime = new DateTime(date);
          Duration duration =new Duration(minTime, dateTime);
          usAnswerRecord.setDuration(duration.getMillis());
          usAnswerRecord.setEndTime(DateUtil.getDate(date,"yyyy-MM-dd HH:mm:ss"));
          usAnswerRecordService.update(usAnswerRecord);
      }
        return JsonResponse.defaultSuccessResponse();
    }
}
