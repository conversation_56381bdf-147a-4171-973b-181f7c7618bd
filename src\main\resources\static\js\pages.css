html,body{height:100%;margin:0;padding:0;overflow:hidden;}
body{overflow-x:hidden;overflow-y:auto;}
.body_page{font-size:14px;padding:15px 15px 0;height:100%;margin:0;overflow-x:hidden;overflow-y:auto;}
.page_body{font-size:12px;padding:0;height:100%;margin:0;overflow-x:hidden;overflow-y:auto;}
.process_page{font-size:14px;overflow-x:hidden;overflow-y:auto;padding:15px 15px 0;}
.body_page_uums{min-width:1024px;overflow-x:auto;padding:0 15px;}
.b_e{background:#eee;}.b_l{background:#99d7fc;}
.b_yel1{background:#e5af2e;}.b_lan{background:#02b7e3;}.b_zi{background:#4e3495;}.b_red{background:#c70206;}.b_yel2{background:#ebb316;}.b_cui{background:#54cdc3;}
.b_fen{background:#ff6abd;}.b_molv{background:#387268;}.b_lv{background:#48b73c;}.b_qing{background:#02a9c1;}.b_org{background:#f77400;}.b_qianlv{background:#9fb56a;}

input,textarea{width:100%;}
textarea{resize:none;}
input.wauto{width:auto;}

span.audit{cursor:pointer;}

.role{width:230px;height:320px;overflow-y:auto;border:1px solid #e0e0e0;border-top:2px solid #00b4f1;border-radius:3px;padding:5px;}
.role_choose{border-top-color:#5cb85c;}
.user_choose{border-top-color:#5cb85c;}

.chooseuser{border-top:1px dashed #f0f0f0;position:absolute;left:0;right:0;bottom:0;background:#fafafa;overflow-x:auto;}
.choose{width:50px;height:50px;background:#e2f4ff;border-radius:50%;float:left;text-align:center;position:relative;margin:8px;}
.choose font{line-height:50px;font-size:14px;width:50px;height:50px;overflow:hidden;float:left;}
.role a,.role_btn a,.chooseUser a,.chooseCopyUser a{display:block;padding:0px 10px;line-height:30px;color:#333;}
.role a:hover,.role_btn a:hover,.role a.a_hover,.role_btn a.a_hover,.chooseUser a:hover{background:#e2f4ff;color:#00b4f1;text-decoration:none;font-weight:bold;}
.role_choose a:hover,.role_choose a.a_hover{background:#e2ffe2;color:#5cb85c;}
.choose i{position:absolute;right:0;top:-5px;color:#ff9a97;font-size:20px;cursor:pointer;}
.choose i:hover{color:#d9534f;}

.orgC{width:230px;position:fixed;background:#fff;height:auto;top:5px;bottom:5px;right:5px;}
.title{border-width:1px medium medium;border-style:solid none none;border-color:#e2e2e2;}
.title legend{font-size:15px;margin-left:20px;padding:0 10px;line-height:20px;font-weight:bold;}
.titleA{border:1px solid #e2e2e2;}
.titleA legend{margin-left:0px;}
.pageInfo{padding:15px 15px 10px;position:fixed;z-index:11;top:0px;left:0px;right:0px;background:#fff;}
.pageInfoD{background-color:#f2f2f2;border-left:5px solid #39aef5;border-radius:0 2px 2px 0;line-height:22px;overflow:hidden;padding:15px;}
.pageInfo2{background:#F5F5F5;overflow:hidden;padding:15px 30px;position:fixed;z-index:11;top:0px;left:0px;right:0px;}
.proNxtTit{background:#eee;text-align:center;border-top:2px solid #39aef5;font-weight:bold;padding:5px 0 7px;overflow:hidden;width:100%;}

.pageTit{border-bottom:1px solid #dee0f0;line-height:34px;padding:0px 8px;}
.pageTit small{padding-left:5px;color:#999cb9;}
.pageTit i{font-size:12px;padding-left:8px;color:#999cb9;}
.titleType{color:#222222;font-size:14px;line-height: 24px;margin: 10px 0;}    
.titleType .before{display: inline-block;width:3px;height: 14px;background: #0086CF;margin-right: 10px;position: relative;top:2px;}      
.table_bor{background: #FAFAFA;border: 1px solid #F2F2F2;}      
.ctableT{color:#9a9a9a;text-align:center;border-bottom:1px solid #e6e6e6;padding:10px;margin-top:10px;overflow:hidden;}

table.tabForm{border-collapse:collapse;border-spacing:0; empty-cells:show;border-color:#cdcdcd;font-size:12px;margin-top:10px;table-layout:fixed;}
table.tabForm td{border-color:#cdcdcd;}
table.tabForm td.tit{background:#ddf1fe;text-align:left;padding:0 8px;}
table.tabForm td.text{padding:0 8px;}
table.tabForm td input,table.tabForm td textarea,table.tabForm td select,table.tabForm td .textbox{border-color:#fff;}
table.tabPrint{margin:0;line-height:22px;font-size:14px;border-color:#333;}
table.tabPrint td{padding:8px;min-height:50px;text-align:center;border-color:#333;}
table.tabPrint td.titInfo{padding:0 8px;}

.borderFormTit{width:100%;padding:0 10px;font-size:15px;color:#ed6a5b;line-height:32px;font-weight:bold;}
.borderFormTit font{margin-right:4px;}
table.borderForm{width:100%;max-width:1000px;margin:0 auto;font-size:12px;margin:0 auto;margin-top:10px;border-collapse:collapse;border-spacing:0;empty-cells:show;table-layout:fixed;}
table.borderForm td{border:1px solid #afafaf;}
table.borderForm td.label{background:#deeeff;text-align:left;padding:0 8px;}
table.borderForm td.text{padding:0 8px;}
table.borderForm td .tip{padding:0 8px;}
table.borderForm td input,table.borderForm td textarea,table.borderForm td select,table.borderForm td .textbox{border-color:#fff;}
table.borderForm font.col_r{font-size:16px}
table.borderForm .textAndInput_readonly,table.borderForm .textAndInput_readonly .validatebox-readonly{background:#f7f7f7;}
table.borderForm .ctable thead tr{background:#e1edf9;}
table.borderForm .ctable td{border:0;padding:6px 4px;}
table.borderForm .ctable td input,table.borderForm .ctable td textarea,table.borderForm .ctable td select,table.borderForm .ctable td .textbox{border-color:#e5e5e5;}

.bgFormTit{width:100%;max-width:1000px;margin:0 auto;margin-top:10px;margin-bottom:10px;font-size:15px;color:#ed6a5b;line-height:24px;font-weight:bold;}
.bgFormTit .line{position:relative;top:3px;display:inline-block;width:3px;height:16px;background:#ed6a5b;margin:0 8px 0 0;}
table.bgForm{width:100%;max-width:1000px;margin:0 auto;background:#fafafa;border:1px solid #eaeaea;empty-cells:show;table-layout:fixed;}
table.bgForm font.col_r{font-size:16px}
.ctableT{color:#9a9a9a;text-align:center;border-bottom:1px solid #e6e6e6;padding:10px;margin-top:10px;overflow:hidden;}
/*table.bgForm .ctable thead tr{background:#e1edf9;}
table.bgForm .ctable td{border:0;padding:6px 4px;}
table.bgForm .ctable td input,table.bgForm .ctable td textarea,table.bgForm .ctable td select,table.bgForm .ctable td .textbox{border-color:#e5e5e5;}*/

/*dict三列表单*/
.coverFormTit{width: 98%;margin: 0 auto;margin-top: 15px;margin-bottom: 5px;font-size: 15px;color: #ed6a5b;line-height: 24px;font-weight: bold;*zoom: 1;}
.coverFormTit:after{visibility: hidden;display: block;font-size: 0;content: " ";clear: both;height: 0;}
.coverFormTit .line{position: relative;top: 3px;display: inline-block;width: 3px;height: 16px;background: #ed6a5b;margin: 0 8px 0 0;}
.coverFormTit a.btn{font-weight: normal;}
.coverForm{width: 98%;background: #fafafa;border: 1px solid #eaeaea;margin: 0 auto;empty-cells: show;table-layout: fixed;/*box-shadow: 0 2px 5px 1px #e7e7e7;*/}

.orgPosR{position:fixed;left:25%;bottom:0;top:0;right:0;}
.orgPosL{position:fixed;top:0;left:0;bottom:0;right:75%;overflow:auto;}

#processNext .layout-split-north{border-bottom-width:5px;}
#processNext .panel-body-noheader{border-top-width:0px;}
#processNext .panel-header,#processNext .panel-body{border-width:0;}
.proNetTreeF,.copyPhF{position:relative;padding:15px 10px 5px;}
.proNetTreeFT,.copyPhT{overflow:hidden;position:absolute;top:-10px;}
.proNetTreeFT a,.copyPhT a{float:left;margin-right:8px;background:#fff;padding:0 10px;font-size:14px;color:#333;}
.proNetTreeFT a:hover,.copyPhT a:hover{font-weight:bold;text-decoration:none;}
.proNetTreeFT a.hover,.copyPhT a.hover{font-weight:bold;}
.proNetPeoF{width:35%;position:absolute;top:-16px;right:-11px;border:1px solid #e2e2e2;bottom:0;overflow-y:auto;background:#fff;}
.treeListD{position:relative;}
.treeListDT{width:65%;float:left;overflow-y:auto;}
.decList{overflow-x:hidden;overflow-y:auto;}

.PerSpare{width:65px;background:url(/simbestui/js/themes/default/images/combo_arrow.png) no-repeat right center #fff; padding:3px 20px 3px 5px;border:1px solid #e5e5e5;display:inline-block;line-height:18px;position:relative;}
.PerSpare i{width:18px;height:26px;position:absolute;right:0;top:0;}
.PerSpare input{border:0 none;padding:0px;}
.perSpareTxt{width:111px;font-size:11px;line-height:14px;padding:2px;height:48px;overflow-y:auto;overflow-x:hidden;}
.PerSpare_C{border-color:#6d9cde;}
.perSpare_E{border:1px solid #ffa8a8;background-color:#fff3f3;}
.perSpare_E input{background-color:#fff3f3;}
.datagrid-cell.perSpare_E{border:0 none;}
.PerSpareD{position:absolute;z-index:9000; background: #fff;font-size:12px; border:1px solid #e6e6e6;}
.PerSpareD a{width:50%;float:left;text-align:center;line-height:25px;color:#333;}
.PerSpareD a.a_first{width:100%;}
.PerSpareD a:hover{background:#eaf2ff;text-decoration:none;}
.PerSpareD a.a_hover{background:#ffe48d;color:#000;text-decoration:none;}
.PerSpareGroupD .datagrid-header-row{height:24px;}
.PerSpareGroupD .datagrid-cell-group{height:24px;line-height:24px;}
.PerSpareGroupD .datagrid-view2 .datagrid-header .datagrid-cell{word-wrap: break-word;white-space:normal;}
/**dprevent**/
.table_searchD{width:100%;border:1px solid #F2F2F2;padding:8px 0;}
/*流程跟踪*/
.trackContainer{width:920px;height:490px;margin:10px auto;}
.initLeft,.initRight{width:444px;height:100%;border:2px solid rgb(130,164,210);float:left;}
.initLeft{overflow:hidden;}
.initRight{overflow-y:auto;}
.initLeft iframe{width:100%;height:100%;}
.initLeft span{position:absolute;left:30px;top:15px;z-index:6;font-size:14px;cursor:pointer;}
.fullWidth{width:904px;}
.initMiddle{position:relative;height:100%;background-color:#82a4d2;float:left;}
.initMiddle .initArrow{width:15px;height:100%;float:left;}
.initMiddle .leftArrow{width:16px;border-right:1px solid #fff;}
.initMiddle .noBorder{width:15px;border:0;}
.initArrow button{position:absolute;top:50%;width:14px;height:28px;background-color:#fff;outline:0;border:1px solid #DCDFE6;border-radius:3px;margin-top:-14px;cursor:pointer;}
.initArrow button:active{border:1px solid #3a8ee6;outline: 0;}
.initArrow button:active .iconfont{color: #3a8ee6;}
.initArrow .iconfont{font-size:12px;font-style:normal;color:#909090;line-height:28px;}
.initArrow button:hover{border-color: #c6e2ff;background-color:#ecf5ff;}
.initArrow button:hover .iconfont{color:#409EFF;}