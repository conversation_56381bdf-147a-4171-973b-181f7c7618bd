package com.simbest.boot.exam.briefDistribution.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.security.SimpleUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 转阅人员实体类
 *
 * @Auther: ztz
 * @Date: 2021/3/31 16:24
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity(name = "us_user_info")
@ApiModel(value = "转阅人员实体类")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInfo extends WfFormModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UUI") //主键前缀，此为可选项注解
    private String id;

    @ApiModelProperty(value = "工单id")
    @Column(length = 200)
    private String pmInsId;

    @ApiModelProperty(value = "主数据id")
    @Column(length = 200)
    private String businessId;

    @ApiModelProperty(value = "用户名")
    @Column(length = 200)
    private String username;

    @ApiModelProperty(value = "用户真实姓名")
    @Column(length = 200)
    private String truename;

    @ApiModelProperty(value = "手机号码")
    @Column(length = 200)
    private String preferredMobile; //即主数据规范首选移动电话

    @ApiModelProperty(value = "主机号码")
    @Column(length = 200)
    private String telephoneNumber; //即主数据规范用户办公电话
}
