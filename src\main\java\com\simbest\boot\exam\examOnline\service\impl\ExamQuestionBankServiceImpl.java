/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service.impl;/**
 * Created by KZH on 2019/10/8 15:14.
 */

import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.model.ExamQuestionBank;
import com.simbest.boot.exam.examOnline.repository.ExamQuestionBankRepository;
import com.simbest.boot.exam.examOnline.service.IExamQuestionBankService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionService;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @create 2019-10-08 15:14
 * @desc 题库
 **/
@Slf4j
@Service
public class ExamQuestionBankServiceImpl extends LogicService<ExamQuestionBank,String> implements IExamQuestionBankService {

    private ExamQuestionBankRepository examQuestionBankRepository;

    @Autowired
    public ExamQuestionBankServiceImpl(ExamQuestionBankRepository repository){
        super(repository);
        this.examQuestionBankRepository=repository;

    }

    @Autowired
    public IExamQuestionService examQuestionService;

    /**
     * 根据题库编码获取题库
     * @param questionBankCode
     * @return
     */
    @Override
    public ExamQuestionBank findAllByQuestionBankCode(String questionBankCode) {
        return super.findOne(Specifications.<ExamQuestionBank>and().eq("questionBankCode", questionBankCode).build());
    }

    /**
     * 根据登录人查询题库列表
     * <AUTHOR>
     * @date 2021/6/28
     * @param
     * @return
     */
    @Override
    public Page<ExamQuestionBank> listByUserName(Pageable pageable, ExamQuestionBank examQuestionBank) {
        String currentUserName = SecurityUtils.getCurrentUserName();
        //如果是hadmin则可对所有题进行操作
        if (currentUserName.equals("hadmin")){
            Specification<ExamQuestionBank> build = Specifications.<ExamQuestionBank>and()
                    .like(StringUtils.isNotEmpty(examQuestionBank.getQuestionBankCode()),"questionBankCode", "%"+examQuestionBank.getQuestionBankCode()+"%")
                    .like(StringUtils.isNotEmpty(examQuestionBank.getQuestionBankName()),"questionBankName", "%"+examQuestionBank.getQuestionBankName()+"%")
                    .build();
            return findAll(build, pageable);
        }

        //否则只能操作属于自己建的题库
        Specification<ExamQuestionBank> build = Specifications.<ExamQuestionBank>and()
                .eq("creator", currentUserName)
                .like(StringUtils.isNotEmpty(examQuestionBank.getQuestionBankCode()),"questionBankCode", "%"+examQuestionBank.getQuestionBankCode()+"%")
                .like(StringUtils.isNotEmpty(examQuestionBank.getQuestionBankName()),"questionBankName", "%"+examQuestionBank.getQuestionBankName()+"%")
                .build();
        return findAll(build,pageable);
    }


    /**
     * 根据登录人查询题库列表 不分页
     * <AUTHOR>
     * @date 2021/6/28
     * @param
     * @return
     */
    @Override
    public List<ExamQuestionBank> listByUserNameNoPage() {
        String currentUserName = SecurityUtils.getCurrentUserName();

        if (currentUserName.equals("hadmin")){
            return findAllNoPage();
        }

        Specification<ExamQuestionBank> creator = Specifications.<ExamQuestionBank>and()
                .eq("creator", currentUserName)
                .build();
        return findAllNoPage(creator);
    }

    /**
      * @desc 创建题库
      * <AUTHOR>
      */
    @Override
    public String creatExamQuestionBank(ExamQuestionBank examQuestionBank) {
        String msg = this.checkUnique(examQuestionBank);
        if (Objects.nonNull(msg)) return msg;

        examQuestionBank.setTruename(SecurityUtils.getCurrentUser().getTruename());
        super.insert(examQuestionBank);
        return "创建成功";
    }

    @Override
    public String updateExamQuestionBank(ExamQuestionBank examQuestionBank) {
        String msg = this.checkUnique(examQuestionBank);
        if (Objects.nonNull(msg)) return msg;

        super.update(examQuestionBank);
        return "修改成功";
    }

    /**
     * 检查试题库是否唯一
     *
     * @param examQuestionBank 试题库对象
     * @return 如果试题库已存在则返回对应的提示信息，否则返回null
     */
    private String checkUnique(ExamQuestionBank examQuestionBank) {
        List<ExamQuestionBank> allNoPage = this.findAllNoPage(Specifications.<ExamQuestionBank>or()
                .eq(StringUtils.isNotBlank(examQuestionBank.getQuestionBankCode()), "questionBankCode", examQuestionBank.getQuestionBankCode())
                .eq(StringUtils.isNotBlank(examQuestionBank.getQuestionBankName()), "questionBankName", examQuestionBank.getQuestionBankName())
                .build());
        if (CollectionUtils.isNotEmpty(allNoPage)) {
            boolean match = allNoPage.stream().anyMatch(v -> Objects.equals(v.getQuestionBankCode(), examQuestionBank.getQuestionBankCode()));
            boolean match1 = allNoPage.stream().anyMatch(v -> Objects.equals(v.getQuestionBankName(), examQuestionBank.getQuestionBankName()));
            if (match && match1) return "此编码和题库名称已被使用";
            if (match) return "此编码已被使用";
            if (match1) return "此题库名称已被使用";
        }
        return null;
    }

    /**
      * @desc 删除题库，并将其下的题目删除
      * <AUTHOR>
      */
    @Override
    public String delExamQuestionBank(String id) {
//        String id = MapUtil.getStr(paramMap, "id");
        ExamQuestionBank examQuestionBank = this.findById(id);
        String questionBankCode = examQuestionBank.getQuestionBankCode();
        this.deleteById(id);
        if (questionBankCode!=null){
            Specification<ExamQuestion> spec = Specifications.<ExamQuestion>and()
                    .eq("questionBankCode", questionBankCode)
                    .build();
            List<ExamQuestion> allNoPage = examQuestionService.findAllNoPage(spec);
            examQuestionService.deleteAll(allNoPage);
            return "删除成功";
        }
        return "删除成功";
    }
}
