<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>试题页面</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/static/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/static/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/exam.js?v=svn.revision" th:src="@{/static/js/exam.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .panel.combo-p {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #e6e6e6;
        }
        .combo-panel.panel-body.panel-body-noheader {
            border: none;
        }
    </style>
    <script type="text/javascript">

        $(function () {

            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#insertQuestionTable",//table列表的id名称，需加#
                    "querycmd": "action/examQuestion/findAllExamQuestion",//table列表的查询命令
                    //"queryParams":{"direction":"desc","properties":"questionCode"},
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "sortOrder": 'desc',
                    "columns": [[//列
                        {title: "题目编号", field: "questionCode", width: 80,sortable: true, tooltip: true,align:"center"},
                        {title: "题目名称", field: "questionName", width: 400,sortable: true, tooltip: true,align:"center"},
                        {title: "所属题库编码", field: "questionBankCode", width: 100,sortable: true, tooltip: true,align:"center"},
                        {title: "题目类型", field: "questionType", width: 100,sortable: true, tooltip: true,align:"center",
                            formatter:function (value,row,index) {
                                if(value == "more"){
                                    value = "多选题"
                                }else if(value == "single"){
                                    value = "单选题"
                                }else if(value == "judge"){
                                    value = "判断题"
                                }else if(value == "shortAnswer"){
                                    value = "简答题"
                                }else{
                                    value = "填空题"
                                }
                                return value
                            }
                        },
                        {title: "题目分数", field: "questionScore", width: 100,sortable: true, tooltip: true,align:"center"},
                        {
                            field: "opt", title: "操作", width: 250,sortable: true, tooltip: true,align:"center", rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g=[];
                                g.push("<a href='#' class='readDialog' readDialogindex='" + index + "'>【查看】</a>");
                                g.push("<a class='stick' ptitle='答案详情' path='html/exam/examAnswer.html?questionCode=" + row.questionCode + "'>【答案详情】</a>");
                                g.push("<a class='transactionUpdate' ptitle='修改'  path='html/examquestion/updateQuestion.html?id=" + row.id + " '>【修改】</a>");
                                g.push("<a href='#' delete='action/examQuestion/deleteById' deleteid='" + row.id + "'>【删除】</a>");
                                return g.join("");
                            }
                        }

                    ]]
                },
                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "ctable": "maUserOrg",
                    "formname": "#insertQuestionTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/examQuestion/importQuestion",//新增命令
                    "updatacmd": "action/examQuestion/update",//修改命令
                    "closeNoFresh":false,
                    "onSubmit": function (param) {//在请求加载数据之前触发。返回false可以停止该动作

                        var questionSize = $("#questionSize").val();
                        var questionBank = $("#questionBank").combobox('getValue');
                        var map= {};
                        for(var i=1;i<=questionSize;i++){
                            var answerList=[];
                            var judgeAnswerList=[];
                            var shortAnswerList=[];

                            var submenuName=$("#submenuName"+i).val();//获取单选多选题目名称
                            var judgeName=$("#judgeName"+i).val();//获取判断题目名称
                            var shortAnswerName=$("#shortAnswerName"+i).val();//获取简答题目名称
                            var fillAnswerName=$("#fillAnswerName"+i).val();//获取简答题目名称

                            //单选多选 获取答案
                            if(answerList.length==0){
                                answerList.push("single");
                                for(var j=1;j<=12;j++){
                                    var answer=$("#answer"+i+j ).val();
                                    if(answer!=""){
                                        answerList.push(answer);
                                    }
                                }
                            }

                            //判断题
                            if(judgeAnswerList.length==0){
                                judgeAnswerList.push("judge");
                                $("input[id='judge1"+i+"']:checked").each(function () {
                                    if($(this).prop('checked')==true){
                                        var judge1=($(this).val());
                                        judgeAnswerList.push(judge1);
                                    }
                                });

                                $("input[id='judge2"+i+"']:checked").each(function () {
                                    if($(this).prop('checked')==true){
                                        var judge2=($(this).val());
                                        judgeAnswerList.push(judge2);
                                    }
                                });
                            }

                            //简答题
                            var shortAnswer=$("#shortAnswer"+i ).val();
                            if(shortAnswer!=undefined){
                                if(shortAnswerList.length==0){
                                    shortAnswerList.push("shortAnswer");
                                }
                                shortAnswerList.push(shortAnswer);
                            }

                            //获取到正确答案
                            $("input[id='correct"+i+"']:checked").each(function () {
                                if($(this).prop('checked')==true){
                                    var correct=($(this).val());
                                    answerList.push(correct);
                                }
                            });

                            if(submenuName!=undefined){
                                map[submenuName]=answerList;
                            }
                            if(judgeName!=undefined){
                                map[judgeName]=judgeAnswerList;
                            }
                            if(shortAnswer!=undefined){
                                map[shortAnswerName]=shortAnswerList;
                            }
                            map[fillAnswerName]=[ "filling", "0" ];
                        }

                        param.examQuestionList=map;
                        param.questionBank=questionBank;

                        console.log(param,'param');
                        return true;
                    }

                },
                "readDialog": {//查看
                    "dialogid": "#readDag",
                    "dialogedit": false,//查看对话框底部要不要编辑按钮
                    "formname": "#insertQuestionTableReadForm"
                }
            };
            loadGrid(pageparam);

            $('#questionType').combobox({
                    url: web.rootdir + 'action/examQuestion/findDictValue?dictType=questionType',
                    valueField: 'value',
                    contentType: 'application/json; charset=utf-8',
                    textField: 'name',
                    panelHeight: 'auto',
                    prompt: '-请选择-',
                    editable: false,
                    onSelect: function (record) {
                        var questionSize = $("#questionSize").val();
                        //console.log(questionSize);
                        if(questionSize==""){
                            getparent().mesShow("温馨提示","请选择题目数量", 2000,'red');
                        }else {
                            console.log(record);
                            switch (record.name) {
                                case "单选题":
                                    addDRadio(questionSize);
                                    break;
                                case "多选题":
                                    addTrRadio(questionSize);
                                    break;
                                case "判断题":
                                    addTrJudge(questionSize);
                                    break;
                                case "填空题":
                                    addTrGap(questionSize);
                                    break;
                                case "简答题":
                                    addTrAnswer(questionSize);
                                    break;
                            }
                        }

                    }
                });

            $('#questionBank').combobox({
                    url: web.rootdir + 'action/examQuestionBank/findAllNoPage',
                    valueField: 'questionBankCode',
                    contentType: 'application/json; charset=utf-8',
                    textField: 'questionBankName',
                    panelHeight: 'auto',
                    prompt: '-请选择-',
                    editable: false,
                    onSelect: function (record) {}
            });

            // 新增页面关闭时 清空数据
            $("#buttons").dialog({"onClose":function(){
               var newTable=document.getElementById("newTable");
               newTable.innerHTML="";
                    $('#questionSize').val('')
                    $('#questionBank').combobox('clear')
                    $('#questionType').combobox('clear')
                }}).dialog("close");

            //导入 执行事件
            $(".import").on("click",function(){
                top.dialogP ( "html/examquestion/examImport.html", "insertQuestion", '试题导入','examImport',false, '1400', '800' );
            });

        });
        function examImport(data){
            ajaxgeneral({
                url:"action/examQuestion/saveExamQuestionList",
                loading:true,
                async:false,
                data:{"answerListImport":data.data.answerListImport},
                contentType:"application/json; charset=utf-8",
                success:function(datas){
                }
            });
            $("#insertQuestionTable").datagrid("reload");
        };

        //表单校验
        window.fvalidate = function () {
            return $("#insertQuestionTableAddForm").form("validate");
        };

        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data, isupdate) {
            if (isupdate) {
                $('.update-readonly').hide();
            } else {
                $('.update-readonly').show();
            }
        }

        function initsystem() {
            //初始化表单操作代码

        }
        $(document).on("click", ".transactionUpdate", function () {
            var $t = $(this);
            //从按钮处获取地址以及参数
            var url = $t.attr("path");
            top.dialogP(url, 'insertQuestion', '修改题目', 'updateQuestion', false, '650', '600',close);

        });

        $(document).on("click", ".stick", function () {
            var gps=getQueryString();
            var $t=$(this);
            //从应用配置按钮处获取地址以及参数
            var url=$t.attr("path");
            top.dialogP ( url, "insertQuestion", '答案详情', 'examAnswer', true, '1200', '500' );

        });
        //弹出框关闭时，去刷新列表
        function close() {
            $("#insertQuestionTable").datagrid("reload");
        }
        // 刷新考核指标列表
        window.insertQuestionTableLoad = function () {
            $("#insertQuestionTable").datagrid("reload");
        };
        function updateQuestion(data) {}
    </script>
</head>
<body class="body_page">
<form id="insertQuestionTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td colspan="5" width="300">
            </td>
        </tr>
        <tr>
            <td width="90" align="right">题目名称：</td>
            <td width="150">
                <input name="questionName" type="text" value=""/>
            </td>

            <td width="90" align="right">题库编码：</td>
            <td width="280">
                <input  name="questionBankCode" type="text" class="easyui-combobox"  editable="false" style="width:100%;height: 32px"
                        data-options="valueField: 'questionBankCode',
                             panelHeight:'auto',
                             textField: 'questionBankName',
                             contentType:'application/json; charset=utf-8',
                             url: web.rootdir+'action/examQuestionBank/findAllNoPage',
                             prompt:'--请选择--'"/>
            </td>

            <td width="90" align="right">题目类型：</td>
            <td width="150">
                <input  name="questionType" type="text" class="easyui-combobox"  editable="false" style="width:100%;height: 32px"
                       data-options="valueField: 'value',
                             panelHeight:'auto',
                             textField: 'name',
                             contentType:'application/json; charset=utf-8',
                             url: web.rootdir+'action/examQuestion/findDictValue?dictType=questionType',
                             prompt:'--请选择--'"/>
            </td>

            <td>
                <div class="w100">
                    <a class="btn fl searchtable "><font>查询</font></a>
                    <a class="btn fl a_success import"><span>试题导入</span></a>
                    <a class="btn a_success showDialog fr"><span>新增</span></a>
                </div>
            </td>
            <td></td>
            <td></td>
        </tr>
    </table>
</form>
<!--searchform-->

<!--table-->
<div class="insertQuestionTable">
    <table id="insertQuestionTable"></table>
</div>

<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:950px;height:800px;">
    <table border="0" cellpadding="0" cellspacing="10" width="100%">
        <p><font class="col_r">提示：输入题目数量，选择题目类型，生成题目, 每次新增只能新增同一种题目类型</font></p>
        <tr>
            <td width="10%" align="left">题目数量：</td>
            <td width="30%" length="100">
                <input id="questionSize"   style="width: 150px; height: 32px;" class="easyui-validatebox"/>
            </td>

            <td width="10%" align="left">归属题库：</td>
            <td width="30%" length="100">
                <input id="questionBank" name="questionBank"  style="width: 280px; height: 32px;" />
            </td>

            <td width="10%" align="left">题目类型：</td>
            <td width="30%" length="100">
                <input id="questionType" name="questionType"  style="width: 150px; height: 32px;" />
            </td>
        </tr>
        <tr>
            <td colspan="7">
                <h6 class="ctableT"><strong class="col_b">题目信息</strong></h6>
            </td>
        </tr>
    </table>
    <form id="insertQuestionTableAddForm" method="post" contentType="application/json; charset=utf-8" initsystem="initsystem()">
        <input id="id" name="id" type="hidden"/>
        <table border="0" cellpadding="0" cellspacing="10" width="100%" id="newTable"> </table>
    </form>
</div>

<div id="readDag" title="应用详情" class="easyui-dialog" style="width:650px;height:350px;">
    <form id="insertQuestionTableReadForm" method="post" contentType="application/json; charset=utf-8">
        <table border="0" cellpadding="0" cellspacing="10" width="100%">
            <tr>
                <td width="15%" align="left">题目编号：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="questionCode"  readonly="readonly"/>
                </td>
            </tr>
            <tr>
                <td width="15%" align="left">题目名称：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="questionName" readonly="readonly"/>
                </td>
            </tr>
            <tr>
                <td width="15%" align="left">题目类型：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="questionType"  readonly="readonly"/>
                </td>
            </tr>
            <tr>
                <td width="15%" align="left">题目分数：</td>
                <td width="20%" length="100">
                    <input  type="text"  class="questionScore"  readonly="readonly"/>
                </td>
            </tr>
        </table>
    </form>
</div>
</body>
</html>
