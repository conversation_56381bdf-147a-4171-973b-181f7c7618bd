/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service;/**
 * Created by KZH on 2019/10/8 15:11.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.dto.ExamAttributeDto;
import com.simbest.boot.exam.examOnline.model.ExamAttribute;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:11
 * @desc 试卷属性
 **/
public interface IExamAttributeService extends ILogicService<ExamAttribute,String> {

    /**
     * 获取试卷模板
     * @param condition
     * @return
     */
    JsonResponse constructExamLayout(String currentUserCode, String source,Map<String,Object> condition);

    JsonResponse constructWindowsExamLayout(Map<String,Object> condition);

    /**
     *获取试卷模板和答案
     * @param currentUserCode
     * @param source
     * @param condition
     * @return
     */
    JsonResponse constructExamLayoutAndAnswer(String currentUserCode, String source,Map<String,Object> condition);


    JsonResponse constructPowerBuildingLayout(Map<String,Object> condition);

    /**
     * 根据题库code和试卷数量随机生成该用户的试卷
     * @param condition
     * @return
     */
    JsonResponse constructSpecialExamLayout(Map<String,Object> condition);

    /**
     *  examAppCode 获取试卷属性
     * @param examAppCode
     * @return
     */
    ExamAttribute getExamAttributeByExamAppCode(String examAppCode);

    /**
     * 根据examCode考试编码获取试卷信息
     */
    ExamAttribute findExamPaper(String currentUserCode, String source, Map<String, Object> condition);

    /**
      * @desc 根据登录人查询对应试卷信息不分页
      * <AUTHOR>
      */
    List<ExamAttribute> findExamPaper(ExamAttribute examAttribute);

    /**
     * @desc 根据登录人查询对应试卷信息分页
     * <AUTHOR>
     */
    Page<ExamAttribute> findExamPaperInfo(ExamAttribute examAttribute, Pageable pageable);

    /**
      * @desc 新增试卷消息
      * <AUTHOR>
      */
    String createExamPaper(ExamAttributeDto examAttributeDto);

    /**
     * @desc 更改试卷消息
     * <AUTHOR>
     */
    boolean updateExamPaper(ExamAttributeDto examAttributeDto);


    /**
     * 获取试卷模板
     * @param condition
     * @return
     */
    JsonResponse constructExamLayoutTWO(String currentUserCode, String source, Map<String, Object> condition);

    ExamInfo computeScore(String userName, String examCode, String examAppCode);

    JsonResponse pageJump();

    /**
     * 定制化考试出题（IT运维知识测评）
     * @param currentUserCode
     * @param source
     * @param condition
     * @return
     */
    JsonResponse specialExamLayout(String currentUserCode, String source, Map<String, Object> condition);

    /**
     * 定制化考试出题（公共加专业）
     * @param currentUserCode
     * @param source
     * @param condition
     * @return
     */
    JsonResponse specialExamPublic(String currentUserCode, String source, Map<String, Object> condition);


    JsonResponse specialExamLDGBZWJDCP2(String currentUserCode, String source, Map<String, Object> condition);
}
