package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.SystemRepository;
import com.simbest.boot.exam.examOnline.model.ExamRangeGroup;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-04-23 11:09
 * @desc
 **/
public interface ExamRangeGroupRepository extends SystemRepository<ExamRangeGroup, String> {

    /**
     * 根据群组id查询群组信息
     *
     * @param groupId
     * @return
     */
    @Query(value = "select * from us_exam_range_user_info u where u.groupId=:groupId",
            nativeQuery = true)
    ExamRangeGroup findExamGroupByGroupId(@Param("groupId") String groupId);


    /**
     * 根据考试编码查询参加此考试群组中的人与考试名称
     *
     * @param examCode
     * @return
     */
    @Query(value = "select ui.user_Name as USERNAME ,ui.USER_TRUE_NAME as TRUENAME,g.exam_name as title  " +
            "from US_EXAM_RANGE_GROUP g join US_EXAM_RANGE_USER_INFO ui " +
            "on g.GROUP_ID=ui.GROUP_ID " +
            "WHERE g.EXAM_CODE=:examCode",
            nativeQuery = true)
    List<Map<String, Object>> findUserByExamAppCode(@Param("examCode") String examCode);


    /**
     * 跟据examCode查询群组名称
     *
     * @return
     */
    @Query(value = "select * from US_EXAM_RANGE_GROUP rg where rg.EXAM_CODE=:examCode",
            nativeQuery = true)
    List<Map<String, Object>> findExamDetailAndGroup(@Param("examCode") String examCode);

    /**
     * 根据当前登陆人获取试卷编号
     *
     * @return
     */
    @Query(value = "select rg.EXAM_APP_CODE as paperCode  from US_EXAM_RANGE_USER_INFO ui " +
            "JOIN US_EXAM_RANGE_GROUP rg " +
            "on ui.GROUP_ID=rg.GROUP_ID " +
            "where ui.user_name=:userName",
            nativeQuery = true)
    String findExamAppCodeByCurrentUser(@Param("userName") String userName);

}
