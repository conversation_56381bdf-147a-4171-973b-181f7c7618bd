package com.simbest.boot.exam.appraise.model.vo;

import com.simbest.boot.exam.appraise.model.AppraiseTemplate;
import com.simbest.boot.exam.appraise.model.AppraiseWork;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * AppraiseWorkVO 用户评价相关信息汇总
 *
 * <AUTHOR>
 * @since 2024/1/25 9:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AppraiseWorkLayoutVO extends AppraiseWork implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 20240129
     * 按照前端要求的数据结构开发
     */
    private Map<String, List<AppraiseTemplate>> templates;

}
