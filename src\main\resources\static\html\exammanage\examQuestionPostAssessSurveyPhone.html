<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>巡察整改工作满意度评估调查问卷</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
    <meta name="format-detaction" content="telephone=no"/>
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"  rel="stylesheet"/>
    <link href="/simbestui/js/themes/default/easyui.css?v=svn.revision"  rel="stylesheet"/>
    <link href="/simbestui/js/themes/icon.css?v=svn.revision"  rel="stylesheet"/>
    <link href="/simbestui/css/public.css?v=svn.revision"  rel="stylesheet"/>
    <link href="/simbestui/css/pages.css?v=svn.revision"  rel="stylesheet"/>
    <script src="/simbestui/js/jquery.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/jquery.easyui.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/jquery.zjsfile.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/jquery.aduq.js?v=svn.revision"  type="text/javascript"></script>
    <script src="/simbestui/js/jquery.process.js?v=svn.revision"  type="text/javascript"></script>
    <script type="text/javascript">
        var currentUser={}; //存储移动端url携带的参数
        var companyName='';
        var gps = getQueryString();
        // gps.access_token='0';
        if(!gps.access_token){
            window.location.href='errorShow.html?linkType=phoneUser';
        }
        else{
            //console.log(window.location.href)
            // currentUser=parseURL(window.location.href);
            //这里跳转到考试页面时要把相关考试信息字段传递过去，用于查询考题和提交等操作 access_token currentUserCode  appcode examCode
            ajaxgeneral({
                url: "getCurrentUser/api?access_token="+gps.access_token,
                async: false,
                success: function (res) {
                    getUser();
                },error:function(error){
                    window.location.href='errorShow.html?linkType=phoneUser';
                }
            });
        }
        function getUser(){
            ajaxgeneral({
                url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.currentUserCode,
                async: false,
                success: function (ress) {
                    // alert(ress.data)
                    // username = ress.data.username;
                    if(ress.data.belongCompanyTypeDictValue=='01'){
                        companyName='省公司';
                    }
                    if(ress.data.belongCompanyTypeDictValue=='02'){
                        companyName=ress.data.belongCompanyName;
                    }
                    if(ress.data.belongCompanyTypeDictValue=='03'){
                        companyName=ress.data.belongCompanyNameParent;
                    }
                }
            });
        }
        $(function(){
            var titName='分公司';
            //获取分辨率
            var fbl = window.screen.height;
            if(fbl>1000){
                $(".main_wrap").css({"bottom": "27%"});
                // $(".main_wrap p").css({"line-height": "2.6rem"});
            }else if(fbl>740 && fbl<1000){
                $(".main_wrap").css({"bottom": "28%"});
                $(".main_wrap p").css({"line-height": "1.6rem"});
                $(".startTest").css({"bottom":"17%","padding": "1rem 0"});
            }else if(fbl>670 && fbl<740){
                $(".main_wrap").css({"bottom": "29%"});
                $(".main_wrap p").css({"line-height": "1.4rem"});
                $(".startTest").css({"bottom":"17%","padding": "1rem 0"});
            } else if(fbl>570 && fbl<670){
                $(".main_wrap").css({"bottom": "27%"});
                $(".main_wrap p").css({"line-height": "1.3rem","font-size":"0.56rem"});
                $(".startTest").css({"bottom":"17%","padding": "1rem 0"});
            }else{
                $(".main_wrap").css({"bottom": "28%"});
                $(".main_wrap p").css({"line-height": "1.1rem","font-size":"0.56rem"});
                $(".startTest").css({"bottom":"17%","padding": "1rem 0"});
            }
            var gps = getQueryString();
            // gps.access_token='0';
            if(!gps.access_token){
                window.location.href='errorShow.html';
            }else {
                //console.log('sure')
                // getCurrent();
                // ajaxgeneral({
                //     url: "getCurrentUser?appcode=exam&access_token=" + gps.access_token,
                //     async: false,
                //     success: function (res) {
                //
                //     }
                // });
                // var arr=['商丘','鹤壁','安阳','开封'];
                // for(var i=0;i<arr.length;i++){
                //     if(web.currentUser && web.currentUser.belongCompanyName.indexOf(arr[i])>-1){
                //         titName=arr[i];
                //     }
                // }
                $('.titName').text(companyName);
            }
            $(document).on('click','#submit',function(){
                // var url='html/exammanage/examQuestionPostAssessPhone.html?from=phoneUser&titName='+encodeURI(titName)+'&currentUserCode='+gps.currentUserCode+'&appcode='+gps.appcode+'&examCode='+gps.examCode+'&access_token='+gps.access_token;
                // var url='html/exammanage/examQuestionPostAssessPhone.html?from=phoneUser&titName='+encodeURI(titName);
                var url='examQuestionPostAssessPhone.html?from=phoneUser&titName='+encodeURI(titName)+'&currentUserCode='+gps.currentUserCode+'&appcode='+gps.appcode+'&examCode='+gps.examCode+'&access_token='+gps.access_token;
                // top.dialogP(url, 'examProblemSurvey','答题区域','audit',true,'maximized','maximized');
                // winOpen(url, 'examQuestionPostAssessPhone', '数据导入', 'examQuestionPostAssessPhone');
                window.location.href=url;
            })
        })
        // window.examQuestionPostAssessPhone = function (data){
        //
        // }
        function parseURL(url){
            if(!url) return;
            url = decodeURI(url);
            var url = url.split("?")[1];
            var para = url.split("&");
            var len = para.length;
            var res = {};
            var arr = [];
            for(var i=0;i<len;i++){
                arr = para[i].split("=");
                res[arr[0]] = arr[1];
            }
            return res;
        }
    </script>
    <style>
        body {
            /*background-image: url("survey.jpg");*/
            /*background-position: center center;*/
            /*background-repeat: no-repeat;*/
            /*background-size: cover;*/
            /*opacity: 0.8;*/
            /*margin: 0px;*/
            /*padding: 0px;*/
        }
        .wrapper{widdth:100%;}
        .main{
            /*width:80%;height:100%;margin:0 auto;margin-top:1%;*/
            /*background-image: url("search.jpg");*/
            /*background-position: center center;*/
            /*background-repeat: no-repeat;*/
            /*background-size: 100% 100%;*/
            /*filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='search.jpg',sizingMethod='scale');*/
            /*opacity: 0.8;*/
            /*position: relative;*/
            min-height:1924px;margin:0 auto;background-color:#fff;color:#000;background: url("./examBig.png") no-repeat top;
        }
        .explain{color:#FFFFFF;font-size:34px;font-weight:700;text-align: center}
        .mainVal{width:100%;border:1px solid #B2C5E5;color:#FDF8FC;margin-top:4%;background:rgba(245,245,245,0.3);padding: 20px 20px;font-size:16px}
        .mainVal p{margin-top: 1%}
        .mainVal p span{color:#FFFFFF}
        /*.startTest{position:absolute;bottom:6%;border:none;cursor:pointer;font-size:16px;width:190px;height:40px;text-align: center;line-height:40px;border-radius:20px;background:linear-gradient(to right, #016FE2 ,#00A9F5);color:#FFFFFF;margin-left: 39%;margin-top: 2%;}*/
        /*.startTest{position:absolute;bottom:6%;font-weight:600;border:none;cursor:pointer;font-size:16px;width:20%;height:40px;text-align: center;line-height:40px;border-radius:20px;background:#F7B70f;color:#051400;margin-left: 39%;margin-top: 2%;}*/
        .hover{
            width:100%;height:100%;margin:0 auto;
            background-image: url("examBig.png");
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='examBig.png',sizingMethod='scale');
            opacity: 0.8;
            position: relative;
        }
        /*.main_wrap{position: absolute;width:39%;bottom:32%;left:31%;}*/
        .main_wrap{position: absolute;bottom:29%;padding:0 1rem;}
        .main_wrap p{font-size:0.8rem;color: #2A2A2A;line-height: 1.4rem;padding: 0 1.2rem;}
        .main_wrap .titClass{text-indent:1.6rem;}
        .startTest{position:absolute;bottom:17%;border:none;cursor:pointer;font-size:1.6rem;width:72%;text-align: center;border-radius:6rem;background:#D90000;color:#FFFFFF;margin-left: 14%;padding:0.6rem 0;}
    </style>
</head>
<body style="height: 100%;">
<!--<div class="wrapper">-->
<div class="hover">
    <!--    <img class="bigPic" style="display: block;" usemap="#Map">-->
    <div class="main_wrap">
        <p>各位同仁：</p>
        <p class="titClass">您好！欢迎参加公司2020年巡察整改工作满意度评估调查问卷。</p>
        <p class="titClass"> 公司党委于2020年6月对<span class="titName"></span>进行了内部巡察，分公司于12月进行了巡察整改公开通报。希望通过您的真实回答，使我们真正了解巡察整改开展以来取得的成效，以便为今后更好地开展工作提供决策依据。本问卷采用匿名填答方式，您的信息我们会为您保密，请如实回答，谢谢！</p>
    </div>
    <button class="startTest" id="submit">开始问卷调查</button>
</div>
<!--</div>-->
<!--<div class="main">-->
<!--        <p class="explain">河南公司 "机关化"丶"衙门化"问题调查问卷</p>-->
<!--        <div class="mainVal">-->
<!--            <p style=" font-size: 14px;text-indent:39px">为进一步改进和加强本部各职能部门作风建设，按照“以下看上找问题”，切实了解基层广大员工对各职能部门工作作风的真实想法，现就“机关化”、“衙门化”问题整治以调查问卷的形式听取您的意见、建议。</p>-->
<!--            <p style=" font-size: 14px;text-indent:38px"><span style="font-weight:700">为确保本次调查取得实效，在此郑重承诺：</span>本次问卷调查将采用无记名方式开展，对填写内容严格保密，绝不会采用任何技术手段获取任何个人的填写内容，请您放心填写。您的意见和建议将为各职能部门扎实推进作风转变、提升工作效率、工作质量提供基础，感谢您的合作。</p>-->
<!--            <p style=" font-size: 14px;text-indent:38px">问卷调查共计21项，其中，前20项为勾选项，您可以结合实际勾选5-10项您认为表现最突出的问题，也可以就具体事项进行补充说明，以便更有针对性的开展整改。除此之外，您还可以结合工作实际，写出您认为本部各职能部门还存在哪些其他方面需要改进的突出问题。</p>-->
<!--            <p style="text-indent:38px">-->
<!--                <span style="display: block;font-size: 14px;font-weight:700">此次问卷参与对象：</span>-->
<!--                <span style="font-size: 14px;display:block">地市分公司本部人员（评价对象为省公司本部各职能部门）</span>-->
<!--                <span style=" font-size: 14px;display:block">县分公司人员（评价对象为地市分公司本部各职能部门）</span>-->
<!--            </p>-->
<!--        </div>-->
<!--    <button class="startTest" id="submit">开始作答</button>-->
<!--</div>-->
<!--</div>-->
<map name="Map" id="Map" style="cursor: pointer;">
    <area shape="rect" coords="63,600,382,662" target="_blank">
</map>
</body>
</html>