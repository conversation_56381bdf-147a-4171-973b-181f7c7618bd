package com.simbest.boot.exam.appraise.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.appraise.model.AppraiseItem;
import com.simbest.boot.exam.appraise.model.AppraiseTemplate;
import com.simbest.boot.exam.appraise.model.AppraiseWork;
import com.simbest.boot.exam.appraise.model.ExternalInterfaceLog;
import com.simbest.boot.exam.appraise.model.dto.AppraiseInfoDTO;
import com.simbest.boot.exam.appraise.model.dto.AppraiseTemplateInfoDTO;
import com.simbest.boot.exam.appraise.model.vo.AppraiseWorkLayoutVO;
import com.simbest.boot.exam.appraise.model.vo.AppraiseWorkVO;
import com.simbest.boot.exam.appraise.repository.AppraiseWorkRepository;
import com.simbest.boot.exam.appraise.service.IAppraiseItemService;
import com.simbest.boot.exam.appraise.service.IAppraiseTemplateService;
import com.simbest.boot.exam.appraise.service.IAppraiseWorkService;
import com.simbest.boot.exam.appraise.service.IExternalInterfaceLogService;
import com.simbest.boot.exam.examOnline.util.ExamOnlineTool;
import com.simbest.boot.exam.message.service.IMessageService;
import com.simbest.boot.exam.sms.sevice.ILoginService;
import com.simbest.boot.exam.test.util.MyUtils;
import com.simbest.boot.exam.util.BelongInfoTool;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.OperateLogTool;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.util.encrypt.AesEncryptor;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.redis.RedisUtil;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.simbest.boot.config.MultiThreadConfiguration.MULTI_THREAD_BEAN;

@Slf4j
@Service
public class AppraiseWorkServiceImpl extends LogicService<AppraiseWork, String> implements IAppraiseWorkService {

    private final AppraiseWorkRepository repository;

    public AppraiseWorkServiceImpl(AppraiseWorkRepository repository, IExternalInterfaceLogService externalInterfaceLogService, IAppraiseTemplateService appraiseTemplateService, IAppraiseItemService appraiseItemService, ILoginService loginService, UumsSysAppApi uumsSysAppApi, UumsSysUserinfoApi uumsSysUserinfoApi, IMessageService messageService, ExamOnlineTool examOnlineTool, OperateLogTool operateLogTool, AesEncryptor aesEncryptor, ISysDictValueService dictValueService, RsaEncryptor rsaEncryptor) {
        super(repository);
        this.repository = repository;
        this.externalInterfaceLogService = externalInterfaceLogService;
        this.appraiseTemplateService = appraiseTemplateService;
        this.appraiseItemService = appraiseItemService;
        this.loginService = loginService;
        this.uumsSysAppApi = uumsSysAppApi;
        this.uumsSysUserinfoApi = uumsSysUserinfoApi;
        this.messageService = messageService;
        this.examOnlineTool = examOnlineTool;
        this.operateLogTool = operateLogTool;
        this.aesEncryptor = aesEncryptor;
        this.dictValueService = dictValueService;
        this.rsaEncryptor = rsaEncryptor;
    }

    private final IExternalInterfaceLogService externalInterfaceLogService;
    private final IAppraiseTemplateService appraiseTemplateService;
    private final IAppraiseItemService appraiseItemService;
    private final ILoginService loginService;
    private final UumsSysAppApi uumsSysAppApi;
    private final UumsSysUserinfoApi uumsSysUserinfoApi;
    private final IMessageService messageService;
    private final ExamOnlineTool examOnlineTool;
    private final OperateLogTool operateLogTool;
    private final AesEncryptor aesEncryptor;
    private final ISysDictValueService dictValueService;
    private final RsaEncryptor rsaEncryptor;

    @Value("${app.host.port}")
    public String hostPost;
    /**
     * 评价模板html地址
     */
    @Value("${app.exam.appraise.htmlUrl}")
    private String htmlUrl;
    private static final String param1 = "/action/appraise/work";

    /**
     * 根据评价信息生成工单，并推送短消息
     * 允许重复推送短信，(同一工单只会创建一次,短信可以多次推送)
     *
     * @param dto 外部传入的评价信息
     * @return 返回评价工单信息
     */
    @Override
    public JsonResponse pushWork(AppraiseInfoDTO dto) {
        // 记录调用日志
        ExternalInterfaceLog externalInterfacelog = ExternalInterfaceLog.builder()
                .urlInfo("/action/appraise/work/remote/pushWork")
                .paramInfo(JacksonUtils.obj2json(dto))
                .type(Constants.ONE)
                .status("500")
                .build();
        // 检查参数
        this.checkParam(dto);
        String msg = "操作成功";
        try {
            // 检查是否已有工单
            Optional<AppraiseWork> owork = super.findAllNoPage(Specifications.<AppraiseWork>and().eq("username", dto.getUsername()).eq("appraiseWorkId", dto.getAppraiseWorkId()).build(), Sort.by(Sort.Direction.DESC, "createdTime")).stream().findFirst();
            AppraiseWork work = owork.orElse(new AppraiseWork());
            if (!owork.isPresent()) {
                // 构造工单
                work = this.getWork(dto);
            }
            // 未评价
            if (Objects.equals(work.getStatus(), Constants.ZERO)) {
                // 推送短消息
                this.sendSmsMessage(work);
                msg = "操作成功，已推送短信";
            } else if (Objects.equals(work.getStatus(), Constants.ONE)) {
                // 已评价 未推送dict
                if (Objects.equals(work.getDictPushStatus(), Constants.ZERO)) {
                    msg = "操作成功，工单已评价，不再推送短信,已重推dict";
                    try {
                        // 异步推送dict
                        this.writeInfoByDict(work.getUsername(), work.getAppraiseWorkId());
                    } catch (Exception e) {
                        Exceptions.printException(e);
                    }
                } else {
                    msg = "操作成功，工单已评价，不再推送短信";
                }
            }
            externalInterfacelog.setResultInfo(JacksonUtils.obj2json(work));
            externalInterfacelog.setStatus("200");
            return JsonResponse.success(work, msg);
        } catch (Exception e) {
            Exceptions.printException(e);
            throw new IllegalStateException("接口调用失败！");
        } finally {
            externalInterfaceLogService.insert(externalInterfacelog);
        }
    }

    /**
     * 生成工单信息
     *
     * @param dto 外部传入的评价信息
     * @return AppraiseWork对象
     */
    private AppraiseWork getWork(AppraiseInfoDTO dto) {
        SimpleUser user = uumsSysUserinfoApi.findByUsernameFromCurrent(dto.getUsername(), Constants.APP_CODE);
        AppraiseWork work = AppraiseWork.builder()
                .truename(user.getTruename())
                .phone(user.getPreferredMobile())
                .code(MyUtils.getUniqueCode("Work"))
                .status(Constants.ZERO)
                .smsPushStatus(Constants.ZERO)
                .dictPushStatus(Constants.ZERO)
                .build();
        BeanUtils.copyProperties(dto, work);
        BelongInfoTool.setBaseField(work, user);
        log.info("生成工单信息：dto: {},work: {}", dto, work);
        return super.insert(work);
    }

    /**
     * 检查参数是否合法
     *
     * @param dto AppraiseInfoDTO对象
     */
    private void checkParam(AppraiseInfoDTO dto) {
        Assert.notNull(dto, "未接收到相关评价信息，检查参数！");
        Assert.state(StringUtils.isNotBlank(dto.getUsername()), "未接收到[评价人oa账户]信息，检查参数！");
//        Assert.state(StringUtils.isNotBlank(dto.getTruename()), "未接收到[评价人姓名]信息，检查参数！");
        Assert.state(StringUtils.isNotBlank(dto.getAppraiseUsername()), "未接收到[被评价人oa账户]信息，检查参数！");
        Assert.state(StringUtils.isNotBlank(dto.getAppraiseTruename()), "未接收到[被评价人姓名]信息，检查参数！");
        Assert.state(StringUtils.isNotBlank(dto.getAppraiseWorkId()), "未接收到[被评价工单id]信息，检查参数！");
        Assert.state(StringUtils.isNotBlank(dto.getAppraiseWorkTitle()), "未接收到[被评价商机名称]信息，检查参数！");
        Assert.state(StringUtils.isNotBlank(dto.getTemplateCode()), "未接收到[评价模板code]信息，检查参数！");
        log.info("检查参数完成！ dto: {}", dto);
    }

    /**
     * 根据工单发送短信 并更新工单状态
     *
     * @return true 发送成功 false 发送失败
     */
    private boolean sendSmsMessage(AppraiseWork work) {
        SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, Constants.HADMIN);
        Assert.notNull(simpleApp, "主数据未找到此应用信息，检查主数据应用配置信息！");
        Assert.isTrue(simpleApp.getIsSendMsg(), "此应用未打开推送短信开关！");
        boolean flag = false;

        try {
            // 获取定制的短信内容
            String msg = messageService.findMessageContentByExamCode(work.getTemplateCode());
            msg = StringUtils.defaultIfBlank(msg, "您好！您有一个待处理的评价，请及时处理！");
            // 获取5g小程序短链接
            String url = htmlUrl.concat("?workId=").concat(work.getId());
            String shortUrl = loginService.ConvertShortUrl(url);
            // 是测试服 通过链接直接转换成短链接
            if (hostPost.contains("************:8088")) {
                url = hostPost.replaceFirst("/$", "").concat(url);
                shortUrl = loginService.getShortUrl(url);
            }
            shortUrl = String.format("点击下方链接直达：%s", shortUrl);
            String content = msg + shortUrl;
            String username = work.getUsername();
            // 发送短信
            log.info("发送短信信息：username: {}, url: {}, content: {}", username, url, content);
            flag = examOnlineTool.sendShortMessageQuestInfo(username, content);

            // 设置短信推送状态
            if (Objects.equals(work.getSmsPushStatus(), Constants.ZERO)) {
                String status = flag ? Constants.ONE : Constants.TWO;
                work.setSmsPushStatus(status);
            } else {
                String status = flag ? Constants.THREE : Constants.TWO;
                work.setSmsPushStatus(status);
            }
            log.info("短信推送状态：{}，work: {}", flag, work);
            work = super.update(work);
            repository.flush();
        } catch (Exception e) {
            Exceptions.printException(e);
        }

        return flag;
    }

    /**
     * 根据评价人和评价工单id获取评价信息
     *
     * @param username       评价人
     * @param appraiseWorkId 评价工单id
     * @return 评价信息列表
     */
    @Override
    public List<AppraiseWorkVO> getWorkInfo(String username, String appraiseWorkId) {
        // 记录调用日志
        ExternalInterfaceLog externalInterfacelog = ExternalInterfaceLog.builder()
                .urlInfo("/action/appraise/work/remote/getWorkInfo")
                .paramInfo(JacksonUtils.obj2json(String.format("username: %s, appraiseWorkId: %s", username, appraiseWorkId)))
                .type(Constants.ONE)
                .status("500")
                .build();

        try {
            List<AppraiseWork> works = super.findAllNoPage(Specifications.<AppraiseWork>and().eq("username", username).eq("appraiseWorkId", appraiseWorkId).build(), Sort.by(Sort.Direction.DESC, "createdTime"));
            Assert.state(CollectionUtils.isNotEmpty(works), "未找到相关评价工单，检查参数！");

            // 获取评价模板信息
            List<AppraiseWorkVO> collect = works.stream().map(work -> {
                AppraiseWorkVO vo = new AppraiseWorkVO();
                vo.setTemplates(appraiseTemplateService.findAllNoPage(Specifications.<AppraiseTemplate>and().eq("code", work.getTemplateCode()).build(), Sort.by(Sort.Direction.ASC, "displayOrder")));
                BeanUtils.copyProperties(work, vo);
                // 获取用户填写信息
                List<AppraiseItem> items = appraiseItemService.findAllNoPage(Specifications.<AppraiseItem>and().eq("workId", work.getId()).build());
                vo.getTemplates().forEach(v -> items.stream().filter(i -> Objects.equals(v.getId(), i.getTemplateId()))
                        .findFirst().ifPresent(k -> v.setUserContent(k.getContent())));
                return vo;
            }).collect(Collectors.toList());
            externalInterfacelog.setResultInfo(JacksonUtils.obj2json(works));
            externalInterfacelog.setStatus("200");
            return collect;
        } catch (Exception e) {
            Exceptions.printException(e);
            throw new IllegalStateException("接口调用失败！");
        } finally {
            externalInterfaceLogService.insert(externalInterfacelog);
        }
    }

    /**
     * 对已有工单重推短信
     *
     * @param appraiseWorkId 被评价的工单id
     * @return true 发送成功 false 发送失败
     */
    @Deprecated
    @Override
    public boolean retrySendSmsMessage(String username, String appraiseWorkId) {
        // 记录调用日志
        ExternalInterfaceLog externalInterfacelog = ExternalInterfaceLog.builder()
                .urlInfo("/action/appraise/work/remote/retrySendSmsMessage")
                .paramInfo(JacksonUtils.obj2json(String.format("username: %s, appraiseWorkId: %s", username, appraiseWorkId)))
                .type(Constants.ONE)
                .status("500")
                .build();

        try {
            Optional<AppraiseWork> owork = super.findAllNoPage(Specifications.<AppraiseWork>and().eq("username", username).eq("appraiseWorkId", appraiseWorkId).build(), Sort.by(Sort.Direction.DESC, "createdTime")).stream().findFirst();
            Assert.isTrue(owork.isPresent(), "未找到相关评价工单，检查参数！");
            AppraiseWork work = owork.get();
            log.info("开始重推短信息：work: {}", work);
            // 发送短信
            boolean sendSmsMessage = this.sendSmsMessage(work);
            externalInterfacelog.setResultInfo(JacksonUtils.obj2json(sendSmsMessage));
            externalInterfacelog.setStatus("200");
            return sendSmsMessage;
        } catch (Exception e) {
            Exceptions.printException(e);
            throw new IllegalStateException("接口调用失败！");
        } finally {
            externalInterfaceLogService.insert(externalInterfacelog);
        }
    }

    /**
     * 获取评价布局信息
     *
     * @param username 当前登录人
     * @param workId   评价工单id
     * @return 评价封装信息
     */
    @Override
    public AppraiseWorkLayoutVO getInfo(String username, String workId) {
        return this.getInfo(username, workId, false);
    }

    /**
     * 获取评价布局信息
     *
     * @param username    当前登录人
     * @param workId      评价工单id
     * @param isGetDetail 是否需要获取用户填写信息
     * @return 评价封装信息
     */
    private AppraiseWorkLayoutVO getInfo(String username, String workId, boolean isGetDetail) {
        AppraiseWork work = super.findById(workId);
        Assert.notNull(work, "未找到相关评价工单，检查参数！");

        // 检查权限
        this.checkPermissionByUsername(username, work);

        // 获取评价模板信息
        AppraiseWorkLayoutVO vo = new AppraiseWorkLayoutVO();
        List<AppraiseTemplate> templates = appraiseTemplateService.findAllNoPage(Specifications.<AppraiseTemplate>and().eq("code", work.getTemplateCode()).build(), Sort.by(Sort.Direction.ASC, "displayOrder"));
        Map<String, List<AppraiseTemplate>> templateMap = templates.stream().collect(Collectors.groupingBy((v) -> {
            if (Objects.equals(v.getType(), "0")) return "score";
            if (Objects.equals(v.getType(), "1")) return "write";
            return "unknown";
        }, Collectors.toList()));
        vo.setTemplates(templateMap);
        BeanUtils.copyProperties(work, vo);

        // 构造前端所需的数据结构 20240129
        vo.getTemplates().get("score").forEach(v -> {
            int score = Integer.parseInt(v.getScore());
            for (int i = 1; i <= score; i++) {
                Map<String, Object> map = Maps.newHashMap();
                map.put("code", i);
                map.put("content", i + "分");
                v.getTemplateItems().add(map);
            }
        });
        // 需要获取用户填写信息
//        if (isGetDetail) {
//            List<AppraiseItem> items = appraiseItemService.findAllNoPage(Specifications.<AppraiseItem>and().eq("workId", workId).build());
//            vo.getTemplates().forEach(v -> items.stream().filter(i -> Objects.equals(v.getId(), i.getTemplateId()))
//                    .findFirst().ifPresent(k -> v.setUserContent(k.getContent())));
//        }
        return vo;
    }

    /**
     * 检查用户权限
     *
     * @param username 登录人
     * @param work     当前工单
     */
    private void checkPermissionByUsername(String username, AppraiseWork work) {
        // 非当前评价人查看
        if (!Objects.equals(username, work.getUsername())) {
            log.info("当前用户不是评价人，无法查看评价信息！");
            throw new IllegalStateException("您当前不是评价人，无法查看评价信息！");
        }
        // 已完成评价 不允许查看
        if (Objects.equals(work.getStatus(), Constants.ONE)) {
            log.info("当前评价已完成，无法查看评价信息！");
            throw new IllegalStateException("当前评价已完成，无法查看评价信息！");
        }

    }

    /**
     * 提交用户评价信息
     *
     * @param source          当前操作来源
     * @param currentUserCode 当前操作人编码
     * @param str             用户评价信息（加密）
     * @return 用户评价信息保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AppraiseItem> submitInfoSalt(String source, String currentUserCode, String str) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("submitInfoSalt");
        try {
            // 解密操作
            String decrypt = aesEncryptor.decrypt(str);

            // 按照前端要求的数据结构开发 20240129
            Map<String, String> map = JacksonUtils.json2Type(decrypt, new TypeReference<Map<String, String>>() {
            });
            String workId = map.get("workId");
            String templateIds = map.get("templateIds");
            String contents = map.get("contents");
            Assert.state(StringUtils.isNotBlank(workId), "未找到[workId]，检查参数！");
            Assert.state(StringUtils.isNotBlank(templateIds), "未找到[templateIds]，检查参数！");
            Assert.state(StringUtils.isNotBlank(contents), "未找到[contents]，检查参数！");
            String[] template = templateIds.split(",");
            String[] content = contents.split(",");
            if (template.length != content.length)
                throw new IllegalStateException("模板id与用户填写内容数据无法一一对应，检查参数！");

            // 构造保存数据
            List<AppraiseItem> list = new ArrayList<>();
            for (int i = 0; i < content.length; i++) {
                AppraiseItem build = AppraiseItem.builder()
                        .workId(workId)
                        .templateId(template[i])
                        .content(content[i])
                        .build();
                list.add(build);
            }
            if (CollectionUtils.isEmpty(list)) throw new IllegalStateException();
            stopWatch.stop();
            log.info("提交用户评价信息salt耗时：{} ms", stopWatch.getLastTaskTimeMillis());
            return this.submitInfo(source, currentUserCode, list);
        } catch (IllegalStateException e) {
            Exceptions.printException(e);
            throw e;
        } catch (Exception e) {
            Exceptions.printException(e);
            throw new IllegalStateException("保存出错,数据不合法，联系管理员处理！");
        }
    }

    /**
     * 提交用户评价信息 提交时应使用加密方法
     *
     * @param items 用户评价信息
     * @return 用户评价信息保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AppraiseItem> submitInfo(String source, String currentUserCode, List<AppraiseItem> items) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("submitInfo");

        // ------------------------------------防重复提交 start-----------------------
        String key = "submitInfo-" + items.get(0).getWorkId();
        String value = "no-submit";
        if (Objects.equals(RedisUtil.get(key), value))
            throw new IllegalStateException("提交请求正在处理，请稍后重试！");
        if (RedisUtil.setIfAbsent(key, value))
            RedisUtil.expire(key, 2, TimeUnit.SECONDS);
        // ------------------------------------防重复提交 end  -----------------------

        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/submitInfo";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode + "items=" + items;
        operateLog.setInterfaceParam(params);
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
        if (returnObj != null) {
            log.error("保存日志失败！");
        }
        List<AppraiseItem> list = null;
        AppraiseWork work = null;

        try {
            work = super.findById(items.get(0).getWorkId());
            if (Objects.equals(work.getStatus(), Constants.ONE)) {
                log.info("用户已评价！");
                return items;
            }
            // 保存评价信息
            list = appraiseItemService.saveAll(items);

            // 更新评价工单
            work.setAppraiseDate(LocalDateTime.now());
            work.setStatus(Constants.ONE);
            // 计算用户得分
            Integer score = this.computeScore(items);
            work.setScore(String.valueOf(score));
            work = super.update(work);
            log.info("用户提交评价完成，用户得分：" + work.getScore());
        } catch (Exception e) {
            log.error("保存用户信息失败！ source: {}, currentUserCode: {}, items: {}", source, currentUserCode, items);
            Exceptions.printException(e);
            throw new IllegalStateException("保存用户信息失败！");
        }

        // 异步推送dict
        this.writeInfoByDict(work.getUsername(), work.getAppraiseWorkId());

        stopWatch.stop();
        log.info("提交用户评价信息耗时：{} ms", stopWatch.getLastTaskTimeMillis());
        return list;
    }

    /**
     * 根据用户评价信息计算得分
     *
     * @param items 用户评价信息
     * @return 用户得分
     */
    private Integer computeScore(List<AppraiseItem> items) {
        Assert.state(CollectionUtils.isNotEmpty(items), "评价信息不能为空！");

        List<AppraiseTemplate> templates = appraiseTemplateService.findAllNoPage(Specifications.<AppraiseTemplate>and().in("id", items.stream().map(AppraiseItem::getTemplateId).collect(Collectors.toList())).build());
        // 计算总分 1.过滤所有打分类型 2.设置用户得分 3.计算得分
        return templates.stream().filter(v -> Objects.equals(v.getType(), Constants.ZERO))
                .peek(v -> items.stream().filter(j -> Objects.equals(v.getId(), j.getTemplateId())).findFirst().ifPresent(k -> v.setUserContent(k.getContent())))
                .map(v -> Integer.parseInt(v.getUserContent()))
                .reduce(0, Integer::sum);
    }

    /**
     * 获取评价模板信息
     *
     * @return 评价模板信息
     */
    @Override
    public List<AppraiseTemplateInfoDTO> getTemplateInfo() {
        List<AppraiseTemplate> templates = appraiseTemplateService.findAllNoPage();
        return templates.stream().map(v -> {
            AppraiseTemplateInfoDTO dto = new AppraiseTemplateInfoDTO();
            BeanUtils.copyProperties(v, dto);
            return dto;
        }).distinct().collect(Collectors.toList());
    }

    /**
     * 调用应用外部接口  为避免接口耗时较长 这里采用异步调用的方式
     *
     * @param username       用户名
     * @param appraiseWorkId 评价工单id
     */
    @Async(MULTI_THREAD_BEAN)
    public void writeInfoByDict(String username, String appraiseWorkId) {
        List<AppraiseWorkVO> works = this.getWorkInfo(username, appraiseWorkId);
        try {
            // 调用dict接口写入数据
            JsonResponse jsonResponse = this.writeInfo("dict", works.get(0));
            if (jsonResponse != null && Objects.equals(jsonResponse.getStatus(), JsonResponse.SUCCESS_STATUS)) {
                // 设置dict推送状态
                works.forEach(work -> {
                    work.setDictPushStatus(Constants.ONE);
                    super.update(work);
                });
            }
        } catch (Exception e) {
            log.error("调用dict接口写入数据 失败！ work: {}", works);
            Exceptions.printException(e);
        }
    }

    /**
     * 调用应用外部接口
     *
     * @param appcode 应用编码
     * @param params  参数
     * @return 接口返回结果
     */
    private JsonResponse writeInfo(String appcode, Object params) {
        // 记录调用日志
        ExternalInterfaceLog externalInterfacelog = ExternalInterfaceLog.builder()
                .urlInfo("/action/appraise/work/remote/writeInfo")
                .paramInfo(JacksonUtils.obj2json(String.format("appcode: %s, params: %s", appcode, params)))
                .type(Constants.ZERO)
                .status("500")
                .build();
        try {
            SysDictValue dictValue = this.getAppcodeInterfaceInfo(appcode);
            Assert.notNull(dictValue, "未找到对应应用的接口地址！");

            String url = dictValue.getValue();
            url = String.format("%s?appcode=%s&loginuser=%s", url, appcode, rsaEncryptor.encrypt(SecurityUtils.getCurrentUserName()));
            JsonResponse response = HttpClient.textBody(url).json(JacksonUtils.obj2json(params)).asBean(JsonResponse.class);
            if (response == null || response.getErrcode() != 0) {
                log.error("远程请求【{}】应用地址失败，url: {}, params: {}, response: {}", appcode, url, params, response);
            } else {
                log.info("远程请求【{}】应用地址成功，url: {}, params: {}, response: {}", appcode, url, params, response);
            }
            externalInterfacelog.setResultInfo(JacksonUtils.obj2json(response));
            externalInterfacelog.setStatus("200");
            return response;
        } catch (Exception e) {
            Exceptions.printException(e);
            throw new IllegalStateException("接口调用失败！");
        } finally {
            externalInterfaceLogService.insert(externalInterfacelog);
        }
    }

    /**
     * 更新外部接口调用路径
     *
     * @param appcode 应用编码
     * @param url     接口调用路径
     * @return 返回更新后的接口调用路径
     */
    @Override
    public String updateInterfaceUrl(String appcode, String url) {
        // 记录调用日志
        ExternalInterfaceLog externalInterfacelog = ExternalInterfaceLog.builder()
                .urlInfo("/action/appraise/work/remote/updateInterfaceUrl")
                .paramInfo(JacksonUtils.obj2json(String.format("appcode: %s, url: %s", appcode, url)))
                .type(Constants.ONE)
                .status("500")
                .build();
        try {
            SysDictValue dictValue = this.getAppcodeInterfaceInfo(appcode);
            Assert.notNull(dictValue, "未找到对应应用的接口地址！");
            dictValue.setValue(url);
            SysDictValue update = dictValueService.update(dictValue);
            externalInterfacelog.setResultInfo(JacksonUtils.obj2json(update.getValue()));
            externalInterfacelog.setStatus("200");
            log.info("更新接口调用路径成功，appcode: {}, url: {}", appcode, url);
            return update.getValue();
        } catch (Exception e) {
            Exceptions.printException(e);
            throw new IllegalStateException("接口调用失败！");
        } finally {
            externalInterfaceLogService.insert(externalInterfacelog);
        }
    }

    /**
     * 获取应用接口路径信息
     *
     * @param appcode 应用编码
     * @return dictValue
     */
    private SysDictValue getAppcodeInterfaceInfo(String appcode) {
        return dictValueService.findByDictTypeAndName("EXAM_REMOTE_INTERFACE", appcode);
    }

}
