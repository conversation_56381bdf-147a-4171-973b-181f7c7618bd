package com.simbest.boot.exam.util;

import com.simbest.boot.util.distribution.id.RedisIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 用途：编码生成工具类
 * 作者：gy
 * 时间: 2021-04-27 11:06
 * 注意: 如果编号存在问题，需要清除redis缓存内容，同步调整redis_id_key（非）
 */
@Slf4j
@Component
public class CodeGenerateUtil {

    @Autowired
    private RedisIdGenerator idGenerator;

    private static final String CODE_PREFIX_MSXC = "MSXC"; //



    public String generateMSXC() {
        Long dateId = idGenerator.getDateId(CODE_PREFIX_MSXC, 4);
        String code = CODE_PREFIX_MSXC.concat(String.valueOf(dateId));
        StringBuffer sb = new StringBuffer(code);
        sb.insert(4, "-20").insert(13, "-");
        return sb.toString();
    }


}
