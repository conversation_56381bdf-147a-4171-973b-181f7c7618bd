/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;/**
 * Created by KZH on 2019/10/8 10:22.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 10:22
 * @desc 题目表
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_question")
@ApiModel(value = "题目表")
public class ExamQuestion extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EQ") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "题目编码",name = "questionCode",example = "A-001-1",required = true)
    private String questionCode;

    @Column(length = 40)
    @ApiModelProperty(value = "题库编码",name = "questionBankCode",example = "A-001",required = true)
    private String questionBankCode;

    @Column(length = 10)
    @ApiModelProperty(value = "顺序",name = "order",example = "1")
    private Integer questionOrder;

    @ApiModelProperty(value = "题目分组")
    @Column(length = 200)
    private String questionGroupName;

    @Column(length = 2000)
    @ApiModelProperty(value = "题目名称",name = "questionName",required = true)
    private String questionName;

    @Column(length = 250)
    @ApiModelProperty(value = "题目类型",required = true)
    private String questionType;

    @Column(length = 250)
    @ApiModelProperty(value = "题目分数",name = "questionScore",example = "2-2-2-2-10",required = true)
    private String questionScore;
    @Column(length = 250)
    @ApiModelProperty(value = "题目类",name = "questionClass",example = "2-2-2-2-10",required = true)
    private String questionClass;

    @ApiModelProperty(value = "多选题最多可选选项")
    private Integer maxChooseNum;

    @Transient
    private List<ExamQuestionAnswer>  answerList;

    @Transient
    private String  doneAnswer;


    @Transient
    private String  answerRecordId;


    @Transient
    private String  sendTime;


    @Transient
    private String  reciveTime;

    @Transient
    private String  isStart;//1是邀请人  0是被邀请人
    /**
     * 为开发洛阳市县分公司对机关各部门进行满意度评价
     * 以及部门间相互满意度评价的项目添加的属性值
     *   titleDescription  题目描述
     */
    @Column(length = 250)
    @ApiModelProperty(value = "题目描述",name = "titleDescription",example = "满意度评价可以及时反馈问题",required = false)
    private String titleDescription;

    @Transient
    private List<ExamQuestionImport>  answerListImport;

    @Transient
    private String answer;

    @Transient
    private String message;

    @Transient
    @ApiModelProperty(value = "题目类型展示")
    private String type;

    @Transient
    @ApiModelProperty(value = "题目类型展示-html")
    private String typeHtml;


}
