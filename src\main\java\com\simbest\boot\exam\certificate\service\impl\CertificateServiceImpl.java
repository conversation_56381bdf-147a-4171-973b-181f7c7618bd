/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate.service.impl;

import cn.hutool.core.io.FileUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.exam.certificate.ConstantsOffice;
import com.simbest.boot.exam.certificate.doc.model.CertificateData;
import com.simbest.boot.exam.certificate.doc.model.CertificateField;
import com.simbest.boot.exam.certificate.doc.util.DocOperator;
import com.simbest.boot.exam.certificate.doc.util.DocxConverter;
import com.simbest.boot.exam.certificate.path.model.CertificateTempPaths;
import com.simbest.boot.exam.certificate.pdf.PdfConverter;
import com.simbest.boot.exam.certificate.service.ICertificateService;
import com.simbest.boot.exam.examOnline.model.CertificateDetail;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.FileUtils;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.http.client.PostRequest;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.xmlbeans.XmlException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.Map;

import static com.simbest.boot.exam.certificate.doc.util.DocOperator.addBlankSpace;

/**
 * <strong>Title : CertificateServiceImpl</strong><br>
 * <strong>Description : 证书Service </strong><br>
 * <strong>Create on : 2020/11/16</strong><br>
 * <strong>Modify on : 2020/11/16</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Service
@Slf4j
@DependsOn({"httpClient"})
public class CertificateServiceImpl implements ICertificateService {

    @Autowired
    private RsaEncryptor rsaEncryptor;


    @Override
    public File callOfficeConvert(File docx) {
        File file = null;
        try {
            IUser user = SecurityUtils.getCurrentUser();
            String currUserName = user.getUsername();

            Map<String, Object> mapList = Maps.newConcurrentMap();
            mapList.put(ApplicationConstants.REST_TEMPLATE_PARM_FILE, docx);

            file = HttpClient.textBody("http://************:8088/office/action/fileConverter/toPdfUsingLibreOffice/sso?loginuser=" + rsaEncryptor.encrypt(currUserName) + "&appcode=" + Constants.APP_CODE)
                    //file = HttpClient.textBody( "http://10.87.41.197:8084/office/action/fileConverter/toPdfUsingLibreOffice/sso?loginuser="+rsaEncryptor.encrypt( currUserName)+"&appcode="+Constants.APP_CODE )
                    .json(JacksonUtils.obj2json(mapList))
                    .asBean(File.class);

        } catch (Exception e) {
            e.printStackTrace();
            Exceptions.printException(new Exception("调用服务转换失败！", e.getCause()));
        }
        return file;
    }

    @Override
    public CertificateTempPaths generate(CertificateDetail certificateDetail) throws Exception {

        CertificateData data = new CertificateData();
        data.put(new CertificateField("持证人", addBlankSpace(certificateDetail.getTruename()), 18));
        data.put(new CertificateField("证书中文信息", certificateDetail.getTitle(), 18));
        data.put(new CertificateField("证书编号", certificateDetail.getNumber(), 18));
        data.put(new CertificateField("证书日期", certificateDetail.getDate(), 15));


        //String officePath = ConstantsOffice.LIBRE_OFFICE_PATH_WINDOWS;
        String imageSuffix = ConstantsOffice.IMAGE_SUFFIX;
        String imageScaleStr = ConstantsOffice.IMAGE_SCALE_STR;

        Float imageScale = null;

        imageScale = Float.valueOf(imageScaleStr);

        CertificateTempPaths tempPaths=null;
        // 获取证书模板
        try {
            //File docxTemplate = new ClassPathResource(ConstantsOffice.TEMPLATE_FILE_PATH).getFile();
            File docxTemplate = FileUtil.file(ConstantsOffice.TEMPLATE_FILE_PATH);

            // 生成临时文件路径
            tempPaths = CertificateTempPaths.newInstance("docx", "pdf", imageSuffix);

            // 复制证书模板，将字段替换为自定义数据
            String tempDocxPath = tempPaths.getTempDocPathName();
            DocOperator.toCumstomDoc(docxTemplate, tempDocxPath, data);


            // 将doc模板转成pdf
            //String tempPdfPath = tempPaths.getTempPdfPathName();
            //DocxConverter.toPdfUsingLibreOffice(tempDocxPath, tempPdfPath, officePath);
            File officeFile = this.callOfficeConvert(FileUtil.file(tempDocxPath));
            tempPaths.setTempFile(officeFile);
            // 将pdf转成证书图片
            PdfConverter.toImageUsingPdfbox(officeFile, tempPaths.getTempImagePathName(), imageSuffix, imageScale);


        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return tempPaths;

    }
}
