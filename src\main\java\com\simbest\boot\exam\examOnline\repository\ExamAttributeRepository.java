/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.repository;/**
 * Created by KZH on 2019/10/8 15:06.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamAttribute;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:06
 * @desc 试卷属性
 **/
public interface ExamAttributeRepository extends LogicRepository<ExamAttribute,String> {


    /**
     * 根据 examAppCode 获取试卷属性
     * @param examAppCode
     * @return
     */
    @Query(value =  "select t.*  from US_EXAM_ATTRIBUTE t  where t.exam_app_code=:examAppCode   and t.enabled=1 ",
            nativeQuery = true)
    ExamAttribute findAllByExamAppCode(@Param("examAppCode")String examAppCode);

    /**
     * 根据 questionBankCode 题库code 获取试卷
     * @param questionBankCode
     * @return
     */
    @Query(value =  "select t.*  from US_EXAM_ATTRIBUTE t  where t.question_bank_code=:questionBankCode   and t.enabled=1 ",
            nativeQuery = true)
    List<ExamAttribute> findAllByQuestionBankCode(@Param("questionBankCode")String questionBankCode);
}
