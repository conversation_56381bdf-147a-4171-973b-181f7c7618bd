<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>试卷评测</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">

        $(function () {

            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#examReviewTable",//table列表的id名称，需加#
                    "querycmd": "action/examInfo/findAllByIsMarkingExam",//table列表的查询命令
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "sortOrder": 'desc',
                    "columns": [[//列
                        {title: "答题人", field: "publishTruename", width: 150,sortable: true, tooltip: true,align:"center"},
                        {title: "部门名称", field: "departmentName", width: 300,sortable: true, tooltip: true,align:"center"},
                        {title: "系统试卷", field: "examAppCode", width: 150,sortable: true, tooltip: true,align:"center"},
                        {
                            field: "opt", title: "操作", width: 250, rowspan: 1,sortable: true, tooltip: true,align:"center",//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g=[];
                                //g.push("<a href='#' class='readDialog' readDialogindex='" + index + "'>【查看】</a>");
                                //g.push("<a href='#' class='showDialog' showDialogindex='" + index + "'>【修改】</a>");
                                //g.push("<a href='#' delete='action/examInfo/deleteById' deleteid='" + row.id + "'>【删除】</a>");
                                g.push("<a class='stick' ptitle='试卷评测' path='html/exam/examReviewDetails.html?publishUsername=" + row.publishUsername + "&publishTruename="+row.publishTruename+"'>【试卷评测】</a>");


                                return g.join("");
                            }
                        }

                    ]]
                }
            };
            loadGrid(pageparam);

        });

        $(document).on("click", ".stick", function () {
            var $t=$(this);
            //从应用配置按钮处获取地址以及参数
            var url=$t.attr("path");
            top.dialogP ( url, "examReview", '试卷评测详情', 'examReviewDetails', true, '1200', '800',close );

        });

        //弹出框关闭时，去刷新列表
        function close() {
            $("#insertQuestionTable").datagrid("reload");
        }

        function examReviewDetails(data) {

        }

        // 刷新列表
        window.examReviewTableLoad = function () {
            $("#examReviewTable").datagrid("reload");
        };

    </script>
</head>
<body class="body_page">
<form id="examReviewTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td colspan="5" width="300">
            </td>
        </tr>
        <tr>

            <td width="90" align="right">答题人：</td>
            <td width="150">
                <input name="publishUsername" type="text" value=""/>
            </td>

            <td width="90" align="right">部门名称：</td>
            <td width="150">
                <input name="departmentName" type="text" value=""/></td>


            <td width="90" align="right">系统试卷：</td>
            <td width="150">
                <input name="examAppCode" type="text" value=""/></td>
            <td>
                <div class="w100">
                    <a class="btn fl searchtable"><font>查询</font></a>
                </div>
            </td>
            <td></td>
            <td></td>
        </tr>
    </table>
</form>
<!--searchform-->

<!--table-->
<div class="examReviewTable">
    <table id="examReviewTable"></table>
</div>

</body>
</html>
