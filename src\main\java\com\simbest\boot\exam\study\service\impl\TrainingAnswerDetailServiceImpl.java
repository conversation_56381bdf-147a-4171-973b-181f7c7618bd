package com.simbest.boot.exam.study.service.impl;

import lombok.extern.slf4j.Slf4j;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.study.model.TrainingAnswerDetail;
import com.simbest.boot.exam.study.repository.TrainingAnswerDetailRepository;
import com.simbest.boot.exam.study.service.ITrainingAnswerDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用途：TrainingAnswerDetail领域对象名称服务层实现
 */
@Slf4j
@Service
public class TrainingAnswerDetailServiceImpl extends LogicService<TrainingAnswerDetail, String> implements ITrainingAnswerDetailService {

    private TrainingAnswerDetailRepository repository;

    @Autowired
    public TrainingAnswerDetailServiceImpl(TrainingAnswerDetailRepository repository) {
        super(repository);
        this.repository = repository;
    }
}