//package com.simbest.boot.exam.flow.web;
//
//import com.simbest.boot.base.web.response.JsonResponse;
//import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
//import com.simbest.boot.cmcc.nmsg.model.Content;
//import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
//import com.simbest.boot.exam.util.Constants;
//import com.simbest.boot.security.SimpleApp;
//import com.simbest.boot.util.security.LoginUtils;
//import com.simbest.boot.uums.api.app.UumsSysAppApi;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.Assert;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.HashSet;
//import java.util.Set;
//
///**
// * @ClassName: SysTaskInfoController
// * @description:
// * @author: ZHAOBO
// * @create: 2024-07-09 17:40
// */
//@RestController
//@RequestMapping(value = "/action/test")
//@Slf4j
//public class TestController  {
//
//    @Autowired
//    private LoginUtils loginUtils;
//
//    @Autowired
//    private UumsSysAppApi uumsSysAppApi;
//
//    @Autowired
//    private MsgPostOperatorService msgPostOperatorService;
//
//    @PostMapping(value = "/sendMsgTest/anonymous")
//    public JsonResponse sendMsgTest() {
//        loginUtils.adminLogin();
//        SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, "hadmin");
//        Assert.notNull(simpleApp , "查询应用配置信息失败");
//
//        //推送短信
//        if (simpleApp.getIsSendMsg()) {
//            String msg = "您好，您收到一条豫起奋发任务派发待办，请及时办理。";
//            ShrotMsg shrotMsg = readyParams("oazhaobo", msg);
//            Boolean isSuccess = msgPostOperatorService.postMsg(shrotMsg);
//            if (isSuccess) {
//                return  JsonResponse.defaultSuccessResponse();
//            } else {
//                return JsonResponse.defaultErrorResponse();
//            }
//        }
//        return JsonResponse.fail(-1 , "短信开关未开启");
//
//    }
//
//    /**
//     * 准备短信对象
//     *
//     * @param sendUser 发送人
//     * @param msg      短信内容
//     * @return
//     */
//    public static ShrotMsg readyParams(String sendUser, String msg) {
//        ShrotMsg shrotMsg = new ShrotMsg();
//        Content content = new Content();
//        Set<Content> contentSet = new HashSet<Content>();
//        shrotMsg.setAppCode(Constants.APP_CODE);
//        content.setUsername(sendUser);
//        content.setMsgContent(msg);
//        content.setImmediately(true);
//        content.setSmsPriority(1);
//        contentSet.add(content);
//        shrotMsg.setContents(contentSet);
//        return shrotMsg;
//    }
//
//}
