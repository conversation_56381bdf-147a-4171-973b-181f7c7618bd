/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examStatistics.model;/**
 * Created by KZH on 2019/11/26 9:40.
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @create 2019-11-26 9:40
 * @desc 临时数据暂存
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Temporary {

    @Setter
    @Getter
    @ApiModelProperty(value = "姓名")
    private String truename;

    @Setter
    @Getter
    @ApiModelProperty(value = "职位")
    private String positionName;

    @Setter
    @Getter
    @ApiModelProperty(value = "OA账号")
    private String username;

    @Setter
    @Getter
    @ApiModelProperty(value = "手机号码")
    private String phone;

    @Setter
    @Getter
    @ApiModelProperty(value = "公司")
    private String companyName;

    @Setter
    @Getter
    @ApiModelProperty(value = "部门")
    private String departmentName;

    @Setter
    @Getter
    @ApiModelProperty(value = "路径")
    private String displayName;

    @Setter
    @Getter
    @ApiModelProperty(value = "是否答题")
    private Object isFinish;

    @Setter
    @Getter
    @ApiModelProperty(value = "题目")
    private String examRecord;

    @Setter
    @Getter
    @ApiModelProperty(value = "答案")
    private String examAnswer;

    @Setter
    @Getter
    @ApiModelProperty(value = "考试工单标题")
    private String title;

    @Setter
    @Getter
    @ApiModelProperty(value = "剩余时间")
    private String residueTime;

    @Setter
    @Getter
    @ApiModelProperty(value = "得分")
    private String score;

}
