package com.simbest.boot.exam.uums.service.impl;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.AuthoritiesConstants;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.PageTool;
import com.simbest.boot.exam.uums.service.ExtendUumsSysUserRoleService;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.user.UumsSysUserRoleApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @desc:
 * @date 2021/7/3  14:56
 */
@Service
@Slf4j
public class ExtendUumsSysUserRoleServiceImpl implements ExtendUumsSysUserRoleService {


    private static final String USER_MAPPING = "/action/user/user/";
    private static final String USER_MAPPING2 = "/action/user/role/";
    private static final String SSO = "/sso";
    @Autowired
    private AppConfig config;
    @Autowired
    private RsaEncryptor encryptor;

    /**
      * @desc 查询角色下的人员信息
      * <AUTHOR>
      */
    @Override
    public JsonResponse findUserByRole(int page,int size,String roleId,String username) {

       /* List<SimpleUser> userByRoleNoPage = uumsSysUserinfoApi.findUserByRoleNoPage(roleId, Constants.APP_CODE);
        //进行分页处理
        List<SimpleUser> pagination = PageTool.pagination(userByRoleNoPage, page, size);
        return JsonResponse.success(pagination);*/

            String username1 = SecurityUtils.getCurrentUserName();
            log.debug("Http remote request user by username: {}", username1);
            JsonResponse response =  HttpClient.post(config.getUumsAddress() + USER_MAPPING + "findUserByRole"+SSO)
                    .param(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username1))
                    .param(AuthoritiesConstants.SSO_API_APP_CODE,Constants.APP_CODE )
                    .param( "roleId",roleId)
                    .param( "appcode",Constants.APP_CODE)
                    .param( "page",page)
                    .param( "rows",size)
                    .param( "size",size)
                    .param( "username",username)
                    .asBean(JsonResponse.class);
            return response;

    }

    /**
      * @desc 用户关联角色
      * <AUTHOR>
      */
    @Override
    public JsonResponse createRoleUsers(String roleId, String usernames) {
        String username = SecurityUtils.getCurrentUserName();
        log.debug("Http remote request user by username: {}", username);
        JsonResponse response =  HttpClient.post(config.getUumsAddress() + USER_MAPPING2 + "createRoleUsers"+SSO)
                .param(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username))
                .param(AuthoritiesConstants.SSO_API_APP_CODE, Constants.APP_CODE )
                .param("roleId",roleId)
                .param("usernames",usernames)
                .asBean(JsonResponse.class);
        if(response==null){
            log.error("--response对象为空!--");
            return null;
        }
        return response;
    }

    /**
      * @desc 根据id删除用户角色信息
      * <AUTHOR>
      */
    @Override
    public JsonResponse deleteById(String roleId,String[] usernames) {

        //拼接字符串
        String userNameUums="";
        for (String username : usernames) {
            userNameUums+=username+",";
        }
        String username = SecurityUtils.getCurrentUserName();
        log.debug("Http remote request user by username: {}", username);
        JsonResponse response =  HttpClient.post(config.getUumsAddress() + USER_MAPPING2 + "deleteUsers"+SSO)
                .param(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username))
                .param(AuthoritiesConstants.SSO_API_APP_CODE, Constants.APP_CODE )
                .param("roleId",roleId)
                .param("usernames",userNameUums)
                .asBean(JsonResponse.class);
        if(response==null){
            log.error("--response对象为空!--");
            return null;
        }
        return response;

        /*String username = SecurityUtils.getCurrentUserName();
        log.debug("Http remote request user by username: {}", username);
        JsonResponse response =  HttpClient.post(config.getUumsAddress() + USER_MAPPING2 + "deleteById"+SSO)
                .param(AuthoritiesConstants.SSO_API_USERNAME, encryptor.encrypt(username))
                .param(AuthoritiesConstants.SSO_API_APP_CODE, Constants.APP_CODE )
                .param("id",id)
                .asBean(JsonResponse.class);
        if(response==null){
            log.error("--response对象为空!--");
            return null;
        }
        return response;*/
    }
}
