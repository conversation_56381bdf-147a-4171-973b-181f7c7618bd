/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service.Impl;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.model.UsTerminalLoginInfo;
import com.simbest.boot.exam.knowledge.repository.UsAnswerRecordRepository;
import com.simbest.boot.exam.knowledge.repository.UsTerminalLoginInfoRepository;
import com.simbest.boot.exam.knowledge.repository.UsUserAnswersRepository;
import com.simbest.boot.exam.knowledge.service.IUsTerminalLoginInfoService;

import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class UsTerminalLoginInfoServiceImpl extends LogicService<UsTerminalLoginInfo, String> implements IUsTerminalLoginInfoService {

  private   UsTerminalLoginInfoRepository terminalLoginInfoRepository;
    @Autowired
    private LoginUtils loginUtils;
    @Autowired
    public UsTerminalLoginInfoServiceImpl(UsTerminalLoginInfoRepository terminalLoginInfoRepository) {
        super(terminalLoginInfoRepository);
        this.terminalLoginInfoRepository = terminalLoginInfoRepository;
    }

    @Override
    public JsonResponse permissionCheck(Long timestamp, String source, String currentUserCode) {
        //手机端模拟登陆
        if (Constants.SOURCE_M.equals(source)){
            loginUtils.manualLogin(currentUserCode,Constants.APP_CODE);
        }

        IUser currentUser = SecurityUtils.getCurrentUser();
        List<UsTerminalLoginInfo> list =  terminalLoginInfoRepository.findByUserNameAndTimetamp(currentUser.getUsername(),timestamp);
        if (list.size()>0){
            return JsonResponse.success(0);
        }else {
            UsTerminalLoginInfo usTerminalLoginInfo = new UsTerminalLoginInfo();
            usTerminalLoginInfo.setTimestamp(timestamp);
            usTerminalLoginInfo.setUsername(currentUser.getUsername());
            usTerminalLoginInfo.setUserTruename(currentUser.getTruename());
            usTerminalLoginInfo.setSource(source);
            this.insert(usTerminalLoginInfo);
//            //清除掉20分钟前的登录日志
            long deleteTime =timestamp - (1000 * 60 * 20);
            terminalLoginInfoRepository.deleteByCreateTime(deleteTime);
            return JsonResponse.success(1);
        }

    }


}
