package com.simbest.boot.exam.study.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 错误反馈纠正，用于记录用户的错题信息
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "modify_report")
@ApiModel(value = "modify_report", description = "错题集表")
public class ModifyReport extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "WQC") //主键前缀，此为可选项注解
    @ApiModelProperty(value = "主键ID", example = "1")
    private String id;

    @Column(name = "username", length = 50)
    @ApiModelProperty(value = "用户名", example = "hadmin")
    private String username;

    @ApiModelProperty(value = "用户真实姓名", example = "hadmin")
    @Column(name = "truename", length = 200)
    private String truename;

    @ApiModelProperty(value = "显示组织名称")
    @Column(name = "display_org_name", length = 200)
    private String displayOrgName;

    @Column(name = "question_id", length = 40)
    @ApiModelProperty(value = "题目ID", example = "EQ20250724110858")
    private String questionId;

    @Column(name = "question_code", length = 250)
    @ApiModelProperty(value = "题目编码", example = "A-001-1")
    private String questionCode;

    @Column(name = "question_type", length = 50)
    @ApiModelProperty(value = "题目类型", example = "single")
    private String questionType;

    @Column(name = "question_group_name", length = 200)
    @ApiModelProperty(value = "题目分组名称", example = "计算机基础")
    private String questionGroupName;

    @Column(name = "question_content", length = 2000)
    @ApiModelProperty(value = "题目内容", example = "下列选项中，哪个是Java的关键字？")
    private String questionContent;

    @Column(name = "user_answer", length = 2000)
    @ApiModelProperty(value = "用户答案", example = "B")
    private String userAnswer;

    @ApiModelProperty(value = "修改原因")
    @Column(name = "modify_content", length = 2000)
    private String modifyContent;

    @ApiModelProperty(value = "状态 , 0:上报 ， 1：采纳 ， 2：拒绝采纳")
    @Column(name = "status", length = 10)
    private String status;

    @Transient
    private ExamQuestion question;

}