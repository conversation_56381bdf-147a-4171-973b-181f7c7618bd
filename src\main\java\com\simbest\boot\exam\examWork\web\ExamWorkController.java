/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examWork.web;/**
 * Created by KZH on 2019/10/8 16:09.
 */

import com.google.common.collect.Lists;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.service.IExamInfoService;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.todo.model.UsTodoModel;
import com.simbest.boot.exam.todo.service.IUsTodoModelService;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:09
 * @desc
 **/
@Api(description = "待办", tags = {"待办相关处理控制器"})
@Slf4j
@RestController
@RequestMapping(value = "/action/examWork")
public class ExamWorkController extends LogicController<ExamWork, String> {
    private IExamWorkService iExamWorkService;

    @Autowired
    private IExamInfoService iExamInfoService;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    public ExamWorkController(IExamWorkService service) {
        super(service);
        this.iExamWorkService = service;
    }

    @Autowired
    private IUsTodoModelService iUsTodoModelService;

    /**
     * 查询待办
     *
     * @param page            页码
     * @param size            来源
     * @param source          来源
     * @param currentUserCode 当前人OA账号
     * @param title
     * @return
     */
    @ApiOperation(value = "查询我的待办", notes = "查询我的待办")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query",
                    example = "1"),
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query",
                    example = "10"),
            @ApiImplicitParam(name = "source", value = "来源", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "createYear", value = "年度", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/queryMyTask", "/api/queryMyTask", "/queryMyTask/sso",})
    public JsonResponse queryMyTask(@RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int size,
                                    @RequestParam String source,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestParam(required = false) String title) {
        return iExamWorkService.queryMyTask(page, size, source, currentUserCode, title);
    }

    /**
     * 查询已办
     *
     * @param page            页码
     * @param size            来源
     * @param source          来源
     * @param currentUserCode 当前人OA账号
     * @param title
     * @return
     */
    @ApiOperation(value = "查询已办", notes = "查询已办")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query",
                    example = "1"),
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query",
                    example = "10"),
            @ApiImplicitParam(name = "source", value = "来源", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "title", value = "代办名称", required = false, dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/queryMyJoin", "/api/queryMyJoin", "/queryMyJoin/sso",})
    public JsonResponse queryMyJoin(@RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int size,
                                    @RequestParam String source,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestParam(required = false) String title) {
        //title="2019年业务支撑中心反腐倡廉教育考试";
        return iExamWorkService.queryMyJoin(page, size, source, currentUserCode, title);
    }

    /**
     * 查询待办
     *
     * @param page            页码
     * @param size            来源
     * @param source          来源
     * @param currentUserCode 当前人OA账号
     * @param title
     * @return
     */
    @ApiOperation(value = "查询我的待办", notes = "查询我的待办")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query",
                    example = "1"),
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query",
                    example = "10"),
            @ApiImplicitParam(name = "source", value = "来源", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "createYear", value = "年度", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/queryMyTaskMJSF", "/api/queryMyTaskMJSF", "/queryMyTaskMJSF/sso",})
    public JsonResponse queryMyTaskMJSF(@RequestParam(required = false, defaultValue = "1") int page,
                                        @RequestParam(required = false, defaultValue = "10") int size,
                                        @RequestParam String source,
                                        @RequestParam(required = false) String currentUserCode,
                                        @RequestParam(required = false) String title) {
        return iExamWorkService.queryMyTaskMJSF(page, size, source, currentUserCode, title);
    }

    /**
     * 查询已办
     *
     * @param page            页码
     * @param size            来源
     * @param source          来源
     * @param currentUserCode 当前人OA账号
     * @param title
     * @return
     */
    @ApiOperation(value = "查询我的已办", notes = "查询我的已办")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query",
                    example = "1"),
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query",
                    example = "10"),
            @ApiImplicitParam(name = "source", value = "来源", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "createYear", value = "年度", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/queryMyJoinMJSF", "/api/queryMyJoinMJSF", "/queryMyJoinMJSF/sso",})
    public JsonResponse queryMyJoinMJSF(@RequestParam(required = false, defaultValue = "1") int page,
                                        @RequestParam(required = false, defaultValue = "10") int size,
                                        @RequestParam String source,
                                        @RequestParam(required = false) String currentUserCode,
                                        @RequestParam(required = false) String title) {
        return iExamWorkService.queryMyJoinMJSF(page, size, source, currentUserCode, title);
    }

    /**
     * 查询特定考试待办和已办
     *
     * @param page            页码
     * @param size            来源
     * @param source          来源
     * @param currentUserCode 当前人OA账号
     * @param title           标题
     * @param examCode        考试编码 逗号分隔
     */
    @PostMapping(value = {"/queryMyTask2023", "/api/queryMyTask2023", "/queryMyTask2023/sso",})
    public JsonResponse queryMyTask2023(@RequestParam(required = false, defaultValue = "1") int page,
                                        @RequestParam(required = false, defaultValue = "10") int size,
                                        @RequestParam String source,
                                        @RequestParam(required = false) String currentUserCode,
                                        @RequestParam(required = false) String title,
                                        @RequestParam String examCode) {
        return iExamWorkService.queryMyTask2023(page, size, source, currentUserCode, title, examCode);
    }

    /**
     * 待办办理
     *
     * @param source          来源
     * @param currentUserCode 当前人OA账号
     * @param id              待办id
     * @return
     */
    @ApiOperation(value = "待办办理", notes = "待办办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "source", value = "来源", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "id", value = "待办id", required = true, dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/dealWith", "/api/dealWith", "/dealWith/sso",})
    public JsonResponse dealWith(@RequestParam String source,
                                 @RequestParam(required = false) String currentUserCode,
                                 @RequestParam String id) {
        return iExamWorkService.dealWith(source, currentUserCode, id);

    }

    /**
     * 待办办理 根据username进行办理
     *
     * @param source          来源
     * @param currentUserCode 当前人OA账号
     * @param username        账号
     * @return
     */
    @ApiOperation(value = "待办办理", notes = "待办办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "source", value = "来源", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "username", value = "账号", required = true, dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/dealWithByUsername", "/api/dealWithByUsername", "/dealWithByUsername/sso",})
    public JsonResponse dealWithByUsername(@RequestParam String source,
                                           @RequestParam(required = false) String currentUserCode,
                                           @RequestParam String username,
                                           @RequestParam String examCode) {
        ExamWork byUsername = iExamWorkService.findByUsername(username, examCode);
        if (byUsername == null) {
            return JsonResponse.fail(-1, "获取待办失败，无法办理");
        }
        return iExamWorkService.dealWith(source, currentUserCode, byUsername.getId());
    }

    /**
     * 创建待办
     *
     * @param sumMap 条件集合
     * @return
     */
    @ApiOperation(value = "创建待办", notes = "创建待办")
    @PostMapping(value = {"/createToDo", "/api/createToDo", "/createToDo/sso",})
    public JsonResponse createToDo(@RequestBody(required = false) Map<String, String> sumMap) {
        String company = sumMap.get("company");
        String questionName = sumMap.get("questionName");
        String title = sumMap.get("title");
        String questionType = sumMap.get("questionType");
        boolean toDo = iExamWorkService.createToDo(company, title, questionName, questionType);
        if (toDo) {
            return JsonResponse.defaultSuccessResponse();
        }
        return JsonResponse.defaultErrorResponse();

    }

    @ApiOperation(value = "推送代办", notes = "推送代办")
    @PostMapping(value = "/sendUnifiedToDo")
    public JsonResponse sendUnifiedToDo(@RequestBody(required = false) ExamWork examWork) {
        //获取本次考试参与人员
        List<ExamWork> allCustom = iExamWorkService.findAllCustom(examWork);
        List<ExamWork> newAllCustom = Lists.newArrayList();
        if (allCustom != null) {
            for (ExamWork work : allCustom) {
                //查询是否存在已经核销的统一待办
                UsTodoModel allTypeStatus = iUsTodoModelService.findAllTypeStatus(work.getTransactorCode(), work.getWorkType());
                if (allTypeStatus == null) {
                    //获取当前人是否已经进行过答题操作
                    ExamInfo byExamInfo = iExamInfoService.findByExamInfo(work.getTransactorCode(), "portal");
                    if (byExamInfo == null) {
                        //针对没有进行核销操作的用户，并且没有参与过考试答题，则进行推发统一待办操作
                        newAllCustom.add(work);
                    }
                }
            }
            //推发统一待办
            iExamWorkService.sendUnifiedToDo(newAllCustom);
            log.warn("推送待办完成，总计推送" + newAllCustom.size() + "条");
            return JsonResponse.success(1, "推送待办完成，总计推送" + newAllCustom.size() + "条");
        }
        return JsonResponse.fail(-1, "获取待办数据失败");
    }

    @ApiOperation(value = "短信催办", notes = "短信催办")
    @PostMapping(value = "/urgedToDo")
    public JsonResponse urgedToDo(@RequestBody(required = false) ExamWork examWork) {
        //获取本次参与考试的人员
        List<ExamWork> allCustom = iExamWorkService.findAllCustom(examWork);
        if (allCustom != null) {
            //推发短信消息
            iExamWorkService.urgedToDo(allCustom);
            return JsonResponse.success(1, "短信催办完成，总计重新发送短信" + allCustom.size() + "条");
        }
        return JsonResponse.fail(-1, "获取待办数据失败");
    }

    @ApiOperation(value = "查看个人记录", notes = "查看个人记录")
    @ApiImplicitParams({ //
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称displayOrder）", dataType = "String", //
                    paramType = "query") //
    })
    @PostMapping(value = "/findAllCustom")
    public JsonResponse findAllCustom(@RequestParam(required = false, defaultValue = "1") int page,
                                      @RequestParam(required = false, defaultValue = "10") int size,
                                      @RequestParam(required = false) String direction,
                                      @RequestParam(required = false) String properties,
                                      @RequestBody(required = false) ExamWork examWork) {
        Pageable pageable = iExamWorkService.getPageable(page, size, direction, properties);
        return iExamWorkService.findAllCustom(pageable, examWork);
    }

    @ApiOperation(value = "获取待办App", notes = "获取待办App")
    @PostMapping(value = {"/getWorkTypeApp", "/sso/getWorkTypeApp", "/api/getWorkTypeApp"})
    public JsonResponse getWorkTypeApp(@RequestParam(required = false) String userName) {
        return JsonResponse.success(iExamWorkService.findByUsernameApp(userName), null);
    }

    @ApiOperation(value = "根据用户名，考试编码查询待办信息", notes = "根据用户名，考试编码查询待办信息")
    @PostMapping(value = {"/findByTransactorCodeAndExamCode", "/sso/findByTransactorCodeAndExamCode", "/api/findByTransactorCodeAndExamCode"})
    public JsonResponse findByTransactorCodeAndExamCode(@RequestParam String userName,
                                                        @RequestParam String examCode) {
        return JsonResponse.success(iExamWorkService.findByTransactorCodeAndExamCode(userName, examCode));
    }


//    @PostMapping(value = {"/test"})
//    public JsonResponse test(@RequestParam String name) {
//        List<String> list = new ArrayList<>();
//        List<String> list1 = new ArrayList<>();
//        Arrays.stream(name.split(","))
//                .forEach(v -> {
//                    Optional<SimpleUser> o = Optional.ofNullable(this.getUserByEmployeeNumber(v));
//                    if (!o.isPresent()) list1.add(v);
//                    o.ifPresent(v1 -> {
//                        SimpleUser user = uumsSysUserinfoApi.findByUsernameFromCurrent(v1.getUsername(), Constants.APP_CODE);
//                        String format = String.format("'%s', '%s', '%s');",
//                                user.getAuthOrgs().stream().findFirst().get().getDisplayName(),
//                                user.getTruename(),
//                                user.getUsername());
//                        list.add(format);
//                    });
//                });
//        return JsonResponse.success(list);
//    }
//
//    private SimpleUser getUserByEmployeeNumber(String employeeNumber) {
//        // 去除前缀在查询
//        String replaced = employeeNumber.replaceFirst("E00", "");
//        List<SimpleUser> users = uumsSysUserinfoApi.findAllNoPage(Constants.APP_CODE, new HashMap<String, String>() {{
//            put("employeeNumber", replaced);
//        }});
//        if (users.size() > 1) {
//            log.error("主数据查询到多个用户, 员工编号:" + replaced);
//            throw new IllegalStateException("主数据查询到多个用户, 员工编号:" + replaced);
//        }
//        if (users.size() == 1) return users.get(0);
//
//        // 按原有前缀员工编号查询
//        users = uumsSysUserinfoApi.findAllNoPage(Constants.APP_CODE, new HashMap<String, String>() {{
//            put("employeeNumber", employeeNumber);
//        }});
//        if (users.size() > 1) {
//            log.error("主数据查询到多个用户, 员工编号:" + employeeNumber);
//            throw new IllegalStateException("主数据查询到多个用户, 员工编号:" + employeeNumber);
//        }
//        if (users.size() == 1) return users.get(0);
//
//        return null;
//    }

}
