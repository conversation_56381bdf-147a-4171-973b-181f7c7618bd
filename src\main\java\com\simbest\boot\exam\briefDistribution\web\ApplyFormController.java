package com.simbest.boot.exam.briefDistribution.web;


import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.briefDistribution.model.ApplyForm;
import com.simbest.boot.exam.briefDistribution.model.OrganizationValue;
import com.simbest.boot.exam.briefDistribution.service.ApplyFormService;
import com.simbest.boot.exam.briefDistribution.service.OrganizationValueService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 简报派发主数据模块
 */
@RestController
@RequestMapping(value = "/action/applyForm")
@Slf4j
public class ApplyFormController {

    @Autowired
    private ApplyFormService service;

    @ApiOperation(value = "管理员管理(分页)", notes = "管理员管理(分页)")
    @PostMapping(value = {"/findListByPage", "/sso/findListByPage", "/api/findListByPage"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "页容量", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "direction", value = "排序方向asc/desc", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "properties", value = "排序属性", dataType = "String", paramType = "query"),
    })
    public JsonResponse findListByPage(@RequestParam(required = false, defaultValue = "1") int page,
                                       @RequestParam(required = false, defaultValue = "10") int size,
                                       @RequestParam(required = false, defaultValue = "desc") String direction,
                                       @RequestParam(required = false, defaultValue = "createdTime") String properties,
                                       @RequestBody Map<String,Object> params
    ) {
        try {
            Page<ApplyForm> pageList = service.findListByPage(page, size, direction, properties,params);
            return JsonResponse.success(pageList);
        } catch (Exception e) {
            log.error("简报派发信息查询接口异常，{}", e);
            return JsonResponse.fail("接口异常！");
        }
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = {"/create", "/api/create", "/sso/create"})
    public JsonResponse create(@RequestBody  ApplyForm applyForm){
        return   service.create(applyForm);
    }

    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping(value = {"/update", "/api/update", "/sso/update"})
    public JsonResponse update(@RequestBody  ApplyForm applyForm){
        return   service.updateInfo(applyForm);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping(value = {"/deleteById", "/api/deleteById", "/sso/deleteById"})
    public JsonResponse deleteById(@RequestParam("id") String id){
        return   service.delete(id);
    }

    @ApiOperation(value = "查看", notes = "我的代办-点击办理")
    @PostMapping(value = {"/getById", "/api/getById", "/sso/getById"})
    public JsonResponse getById(@RequestParam("id") String id){
        return   service.getById(id);
    }

    @ApiOperation(value = "查看", notes = "我的代办-点击办理")
    @PostMapping(value = {"/getFormDetail", "/api/getFormDetail", "/sso/getFormDetail"})
    public JsonResponse getFormDetail(@RequestParam("id") String id , @RequestParam(defaultValue = "PC") String source){
        return   service.getFormDetail(id , source);
    }
    @ApiOperation(value = "转阅", notes = "转阅")
    @PostMapping(value = {"/forward", "/api/forward", "/sso/forward"})
    public JsonResponse forward(@RequestParam("id") String id, @RequestParam("userStr")  String userStr, @RequestParam("sendType")  String sendType){
        return   service.forward(id,userStr,sendType);
    }

    @ApiOperation(value = "发送", notes = "发送")
    @PostMapping (value = {"/send", "/api/send", "/sso/send"})
    public JsonResponse send(@RequestParam("id") String id){
        return   service.send(id);
    }

    @ApiOperation(value = "消息明细人员", notes = "消息明细人员")
    @PostMapping(value = {"/findUsersInMsg", "/api/findUsersInMsg", "/sso/findUsersInMsg"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "页容量", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "direction", value = "排序方向asc/desc", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "properties", value = "排序属性", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "id", value = "applyForm表id", dataType = "String", paramType = "query"),
    })
    public JsonResponse findUsersInMsg(
            @RequestParam("id") String id,
            @RequestParam(required = false, defaultValue = "1") int page,
            @RequestParam(required = false, defaultValue = "10") int size,
            @RequestParam(required = false, defaultValue = "asc") String direction,
            @RequestParam(required = false, defaultValue = "createdTime") String properties
    ){
        return   service.findUsersInMsg(page, size, direction, properties,id);
    }

    @ApiOperation(value = "消息明细", notes = "消息明细")
    @PostMapping(value = {"/findMsgByIdNew", "/api/findMsgByIdNew", "/sso/findMsgByIdNew"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", dataType = "String", paramType = "query"),
    })
    public JsonResponse findMsgByIdNew(
            @RequestParam("id") String id){
        return   service.findMsgByIdNew(id);
    }

    @ApiOperation(value = "根据组织类型获取组织列表数据详情", notes = "根据组织类型获取组织列表数据详情")
    @GetMapping(value = {"/getOrgRangePersonData", "/sso/getOrgRangePersonData", "/api/getOrgRangePersonData"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "部门编码", dataType = "string", paramType = "query")
    })    public JsonResponse getOrgRangePersonData(
                                                    @RequestParam(required = false) String orgCode) {
        return  service.getOrgRangePersonData(orgCode);
    }
}
