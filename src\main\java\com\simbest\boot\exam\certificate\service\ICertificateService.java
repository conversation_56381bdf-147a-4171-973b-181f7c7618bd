/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate.service;

import com.simbest.boot.exam.certificate.doc.model.CertificateData;
import com.simbest.boot.exam.certificate.path.model.CertificateTempPaths;
import com.simbest.boot.exam.examOnline.model.CertificateDetail;
import org.apache.xmlbeans.XmlException;

import java.io.File;
import java.io.IOException;

/**
 * <strong>Title : ICertificateService</strong><br>
 * <strong>Description : 证书Service </strong><br>
 * <strong>Create on : 2020/11/16</strong><br>
 * <strong>Modify on : 2020/11/16</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface ICertificateService {

    File callOfficeConvert(File docx);

    CertificateTempPaths generate(CertificateDetail certificateDetail) throws Exception;
}

