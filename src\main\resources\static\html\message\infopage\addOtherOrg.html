<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
    xmlns:th="http://www.thymeleaf.org">
<head>
  <title>组织树</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
  <!--
  <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
  -->
  <link href="../../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
  <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
  <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
  <script src="../../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
  <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<style type="text/css">
  .ztree li ul.line{height:auto;}
  .keyinput{width: 480px;padding: 10px;}
  #key{width: 415px;}
  .clear{position: fixed;top: 10px;right: 10px; display: inline-block;width: 65px;height: 26px;line-height: 26px;text-align: center;background-color: #e34d4d;color:#fff;border-radius: 2px;z-index:9}
  .orgC{padding-top: 35px;} 
</style>
<body class="page_body">
<ul id="orgTree"></ul>
<div class="role orgC"></div>
<script type="text/javascript">
    var gps=getQueryString();
    var hasChoosed = "";


  $(function(){

    treeLoadSuccess();
    initTree();

    $(document).on("click",".role a i",function(){

        var str = $(this).parent("a").attr('id');
        hasChoosed = hasChoosed.replace(str, "");
        $(this).parent("a").remove();
    });

    //全部清除按钮
    $(document).on("click",".clear",function(){
        $(".role").html("");
        hasChoosed = "";
    });

    if(gps.isClear){
    $(".role").html("");
    }
  });

  //初始化
  function initTree() {
      var ajaxopts = {
        url:"exuums/sys/userinfo/findOneOrgStep?appcode="+web.appCode,
        data:{"appCode":web.appCode},
        contentType: "application/json; charset=utf-8",
        success: function (data) {
          userIdzNodes = data.data;
          userTree(userIdzNodes);
        }
      };
    ajaxgeneral(ajaxopts);
  }
  
  //树
  function userTree(userIdzNodes) {
      var datas=toTreeData(userIdzNodes,"id","parentId","id|id,name|text,parentId,id,orgCode");
      $("#orgTree").tree({
        lines:true,//是否显示树控件上的虚线
        treePid:'parentId',
        queryParams:{"appCode":web.appCode},
        contentType: "application/json; charset=utf-8",
        cascadeCheck:false,
        data: datas,
        fileds:'id,parentId,name|text,orgCode',
        animate:true,//节点在展开或折叠的时候是否显示动画效果
        onSelect:function(node){
          console.log(node);
          if(node){
            if(node.children){
              if(node.children.length==0) top.mesAlert("提示信息","该组织无下级组织！", 'info');
            }else{
              ajaxgeneral({
                url:"exuums/sys/userinfo/findOneOrgStep?appcode="+web.appCode+"&orgCode="+node.id,
                data:{"appCode":web.appCode,"orgCode":node.id},
                contentType: "application/json; charset=utf-8",
                success:function(data){
                  if(data.data.length!=0){
                    for(var i in data.data){
                      data.data[i].text=data.data[i].name;
                    }
                    $("#orgTree").tree("append", {
                      parent : node.target,
                      data : data.data
                    });
                  }
                    //判断是否是最后一级组织
                    if (node.children) {
                      return;
                    }
                    //判断是否已经有该成员信息，进行添加操作
                    if(isNotChoosed(node.id)){
                          $(".role").append("<a name='"+node.name+"' id='"+node.id+"' orgCode="+node.orgCode+"positionid="+node.positionid+" orgDisplayName='"+node.orgDisplayName+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                          hasChoosed = hasChoosed + "，" +node.id;
                      } else{
                          top.mesAlert("提示", "成员已存在，无需再次添加！", 'warning');
                    }
                }
              });
            }

          }
        },

        onDblClick:function(node){
          //判断是否已经有该成员信息，进行添加操作
          if(isNotChoosed(node.id)){
                $(".role").append("<a name='"+node.name+"' id='"+node.id+"' orgCode="+node.orgCode+"positionid="+node.positionid+" orgDisplayName='"+node.orgDisplayName+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                hasChoosed = hasChoosed + "，" +node.id;
            } else{
                top.mesAlert("提示", "成员已存在，无需再次添加！", 'warning');
          }
        },

        

        onLoadSuccess:function(node,data){
          $("#orgTree").find("ul >li:not(:has(ul))").find('.tree-title').css({'background':'whith','border':'whith','color':'black'});   // 选择最后一个节点 并添加 float:left
        },


        //选择之前
        onBeforeSelect:function(node){
          if(gps.multi==0){
            var nodes=$("#orgTree").tree("getChecked");
            for(var i in nodes){
              $("#orgTree").tree("uncheck",nodes[i].target);
            }
            $(".role").html("");
          }

        }
      });

  }


  //点击查询
  // function chooseuser() {
  //   initTree($('#key').val());
  // };

//检查对象是否存在
  function getObjects(id){
    var a = true;
    $(".role a").each(function(i,v){
      if($(v).attr('id')==id){
        a = false;
        return;
      }
    });
    return a;
  };
  //判断是否已选择
  function isNotChoosed(id){
      
            if(hasChoosed.indexOf(id)!=-1){
                return false;
            }else{
                return true;
            }
          }
        
  //数据加载成功
  function treeLoadSuccess(){

  }


  //返回数据
  window.getchoosedata=function(){
    var datas=[];
    $(".role a").each(function(i,v){
      var data={};
      data.orgCode=$(v).attr("id");
      data.orgName=$(v).children("font").html();
      datas.push(data);
    });
    return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
  };
</script>

<div class="clear">全部清空</div>
</body>
</html>
