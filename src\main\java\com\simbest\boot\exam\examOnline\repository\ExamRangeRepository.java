package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.SystemRepository;
import com.simbest.boot.exam.examOnline.model.ExamRange;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 用途：考试信息模块--考试范围信息dao
 * 作者：gy
 * 时间: 2021-02-01 10:40
 */
public interface ExamRangeRepository extends SystemRepository<ExamRange, String> {
    /**
     * 根据考试编码和当前登陆人所属公司获取试卷编码
     */
    @Query("select r.examPaperCode " +
            "from us_exam_range r, us_exam_summary s " +
            "where r.summaryId = s.id " +
            "and r.companyCode = :companyCode " +
            "and s.examCode = :examCode")
    String findPaperCodeByExamCode(@Param("examCode") String examCode, @Param("companyCode") String companyCode);
}
