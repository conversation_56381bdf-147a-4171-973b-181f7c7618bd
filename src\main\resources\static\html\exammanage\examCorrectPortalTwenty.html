<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org"  style="height: 100%;">
<head>
    <title>2020年度工会满意度有奖调查问卷</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var timeFlag = 0; // 提交数据的标识
        var remainTimeT; // 保存记录的计时器
        $(function(){
            //获取分辨率

            var fbl = window.screen.height;
            if(fbl<1080){
                $(".wrapper").css({"width": "925px"})
            } else if(fbl>=1080){
                $(".wrapper").css({"width": "1100px"})
            }
            //main()
            $("#Map").click(function () {
                $(".hover").hide()
                $(".wrapper").show()
                main();
            })
            function main() {
                var userAgent = navigator.userAgent;
                /**单点配置**/
                var username = "";
                var truename="";
                var gps = getQueryString();
                if(gps.actionType && gps.actionType=="secrecyJoin"){ // 已办
                    $(".submitBtn").hide();
                }else{
                    $(".submitBtn").show();
                    //禁用鼠标右边
                    document.oncontextmenu = function(){
                        getparent().mesShow("温馨提示","请手动答题",2000,'red');
                        return false;
                    };
                    //禁用ctrl+v功能
                    document.onkeydown = function(){
                        if (event.ctrlKey && window.event.keyCode==86){

                            getparent().mesShow("温馨提示","请手动答题",2000,'red');
                            return false;
                        }
                    };
                }

                if(gps.from=="oa"){
                    ajaxgeneral({
                        url: "getCurrentUser/sso?appcode=exam&loginuser=" + gps.uid,
                        async: false,
                        success: function (ress) {
                            username = ress.data.username;
                            truename=ress.data.truename;
                        }
                    });
                }else {
                    getCurrent();
                    username = web.currentUser.username;
                    truename=web.currentUser.truename;
                }

                $("#truename").val(truename);

                var questionBlankCode = ''; // 题库编码
                var singleLen = 0, multipleLen = 0, judgeLen = 0, shortLen = 0; // 题目数量
                var singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案
                var examData = {}; // 试卷模板
                var examRecordId = null; // 答题记录的id
                var currentAction = "";
                var isFinishExam = false; // 是否完成考试

                var totalSS = 0; // 考试时间

                // 秒数格式化为hh:mm:ss
                function countdown(totalSS){
                    var hh = Math.floor(totalSS/3600).toString().length<2?'0'+Math.floor(totalSS/3600):Math.floor(totalSS/3600);
                    var mm = Math.floor((totalSS%3600)/60).toString().length<2?'0'+Math.floor((totalSS%3600)/60):Math.floor((totalSS%3600)/60);
                    var ss = Math.floor((totalSS%3600)%60).toString().length<2?'0'+Math.floor((totalSS%3600)%60):Math.floor((totalSS%3600)%60);

                    var nowTime = hh+':'+mm+':'+ss;
                    return nowTime
                }



                // 题目序号和答案序号格式化,0题目转为汉字，1选项转为大写英文字母
                function formatNumber(type,num) { // 0题目序号  1选项序号
                    num = parseInt(num);
                    var res = '';
                    if (type == 0) {
                        var arr1 = new Array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九');
                        var arr2 = new Array('', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿');//可继续追加更高位转换值
                        if (!num || isNaN(num)) {
                            return "零";
                        }
                        var english = num.toString().split("")
                        var result = "";
                        for (var i = 0; i < english.length; i++) {
                            var des_i = english.length - 1 - i;//倒序排列设值
                            result = arr2[i] + result;
                            var arr1_index = english[des_i];
                            result = arr1[arr1_index] + result;
                        }
                        //将【零千、零百】换成【零】 【十零】换成【十】
                        result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');
                        //合并中间多个零为一个零
                        result = result.replace(/零+/g, '零');
                        //将【零亿】换成【亿】【零万】换成【万】
                        result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
                        //将【亿万】换成【亿】
                        result = result.replace(/亿万/g, '亿');
                        //移除末尾的零
                        result = result.replace(/零+$/, '')
                        //将【零一十】换成【零十】
                        //result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
                        //将【一十】换成【十】
                        result = result.replace(/^一十/g, '十');
                        res = result;

                    } else if (type == 1) {
                        res = String.fromCharCode((num-1)+65);
                    }
                    return res;
                }

                // 获取试卷模板
                var tempCurrentUserCode = gps.currentUserCode?gps.currentUserCode:""
                ajaxgeneral({
                    url: 'action/examAttribute/constructExamLayout?currentUserCode='+ tempCurrentUserCode,
                    data: {"examAppCode":  gps.examAppCode},
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        questionBlankCode = res.data.questionBankCode;
                        currentAction = "test";

                        // 当前用户是否有未完成试卷
                        ajaxgeneral({
                            url: 'action/examInfo/unfinishedExam',
                            type: "POST",
                            data:{
                                publishUsername: username,
                                examAppCode:gps.examAppCode
                            },
                            contentType: "application/json; charset=utf-8",
                            dataType: "json",
                            success: function (result) {
                                if(result.data!=null) {

                                    var sex=result.data.sex;

                                    if("男"==sex){
                                        $("input:radio[id='sex1']").prop('checked','checked');
                                    }
                                    else {
                                        $("input:radio[id='sex2']").prop('checked','checked');
                                    }

                                    examRecordId = result.data.id; // 未完成试卷id
                                    totalSS = parseInt(result.data.residueTime);

                                    var examRecord = result.data.examRecord.split(','); // 题目编号
                                    var examAnswer = result.data.examAnswer.split(','); // 保存的答案

                                    singleData = [], multipleData = [], judgeData = [], shortData = []; // 答案

                                    // 匹配已选择的答案
                                    function matchAnswer(lists,mark){
                                        for(var i = 0; i < examRecord.length; i++){
                                            for(var n = 0; n < lists.length; n++){
                                                if(examRecord[i] == lists[n].questionCode){
                                                    if(mark != 'shortAnswer'){
                                                        var examAnswerOptions = examAnswer[i].split('/');
                                                        for(var ii = 0; ii < examAnswerOptions.length; ii++){
                                                            for(var nn = 0; nn < lists[n].answerList.length; nn++){
                                                                if(examAnswerOptions[ii] == lists[n].answerList[nn].answerCode){
                                                                    lists[n].answerList[nn].isSelected = true;
                                                                }else {
                                                                    if (!lists[n].answerList[nn].isSelected) {
                                                                        lists[n].answerList[nn].isSelected = false;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }else{
                                                        lists[n].answerCode = examAnswer[i];
                                                    }
                                                }
                                            }
                                        }
                                        return lists;
                                    }
                                    matchAnswer(res.data.singleQuestionList,'single');
                                    matchAnswer(res.data.moreQuestionList,'multiple');
                                    matchAnswer(res.data.judgeQuestionList,'judge');
                                    matchAnswer(res.data.shortAnswerQuestionList,'shortAnswer');
                                }else{
                                    totalSS = parseInt(res.data.setTime)*60;
                                }

                                if(res.data.singleQuestionList && res.data.singleQuestionList.length>0){
                                    singleLen = res.data.singleQuestionList.length;
                                    for(var i = 0;i < res.data.singleQuestionList.length; i++){
                                        for(var j = 0;j < res.data.singleQuestionList[i].answerList.length; j++){
                                            if(res.data.singleQuestionList[i].answerList[j].isSelected){
                                                singleData.push({
                                                    questionCode: res.data.singleQuestionList[i].answerList[j].questionCode,
                                                    examAnswer: res.data.singleQuestionList[i].answerList[j].answerCode
                                                });
                                            }
                                        }
                                    }
                                }
                                if(res.data.moreQuestionList && res.data.moreQuestionList.length>0){
                                    multipleLen = res.data.moreQuestionList.length;
                                    for(var i = 0;i < res.data.moreQuestionList.length; i++){
                                        var options = [];
                                        for(var j = 0;j < res.data.moreQuestionList[i].answerList.length; j++){
                                            if(res.data.moreQuestionList[i].answerList[j].isSelected){
                                                options.push(res.data.moreQuestionList[i].answerList[j].answerCode);
                                            }
                                        }
                                        if(options.length > 0){
                                            multipleData.push({
                                                questionCode: res.data.moreQuestionList[i].questionCode,
                                                examAnswer: options.join("/")
                                            });
                                        }
                                    }
                                }
                                if(res.data.judgeQuestionList && res.data.judgeQuestionList.length>0){
                                    judgeLen = res.data.judgeQuestionList.length;
                                    for(var i = 0; i < res.data.judgeQuestionList.length; i++){
                                        for(var j = 0; j < res.data.judgeQuestionList[i].answerList.length; j++){
                                            if(res.data.judgeQuestionList[i].answerList[j].isSelected){
                                                judgeData.push({
                                                    questionCode: res.data.judgeQuestionList[i].answerList[j].questionCode,
                                                    examAnswer: res.data.judgeQuestionList[i].answerList[j].answerCode
                                                });
                                            }
                                        }
                                    }
                                }
                                if(res.data.shortAnswerQuestionList && res.data.shortAnswerQuestionList.length>0){
                                    shortLen = res.data.shortAnswerQuestionList.length;
                                    for(var i = 0;i < res.data.shortAnswerQuestionList.length; i++){
                                        if(res.data.shortAnswerQuestionList[i].answerCode){
                                            shortData.push({
                                                questionCode: res.data.shortAnswerQuestionList[i].questionCode,
                                                examAnswer: res.data.shortAnswerQuestionList[i].answerCode
                                            });
                                        }
                                    }
                                }

                                examData = res.data;
                                if(result.data && result.data.isFinishExam){ //已完成考试
                                    isFinishExam = true;
                                }else{  //未完成考试
                                    isFinishExam = false;
                                }


                                // $(".explain").html(res.data.examRemark);

                                if(result.data && result.data.isFinishExam){ //已完成考试
                                    showQuestions('reTest', res.data);
                                    $(".submitBtn").hide();
                                    $("#closeDialog").dialog({closed:false});
                                }else{  //未完成考试
                                    showQuestions('test', res.data);
                                    $(".submitBtn").show();
                                    // 设置提交按钮高亮是否显示
                                    if(singleData.length+multipleData.length+judgeData.length+shortData.length == singleLen+multipleLen+judgeLen+shortLen){
                                    // if(singleData.length+multipleData.length+judgeData.length == singleLen+multipleLen+judgeLen){
                                        $("#submit").addClass(" canSubmit");
                                    }else{
                                        $("#submit").removeClass("canSubmit");
                                    }

                                    // if(remainTimeT) clearInterval(remainTimeT);
                                    // remainTimeT = setInterval(ajaxInterval,1000); //每隔5秒保存一次数据
                                }

                            }
                        })
                    }
                });

                // 显示试卷
                function showQuestions(type,data){ // type的值：test测试；reTest重测
                    if(data){
                        var titFlag = 0; // 标题序号
                        var qid=1;
                        if(data.singleQuestionList && data.singleQuestionList.length>0){
                            titFlag += 1;

                            var part = $("<div>").addClass("part singleQues").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、单选题").appendTo(part);
                            for(var i=0;i<data.singleQuestionList.length;i++){
                                var main = $("<div>").addClass("main").appendTo(part);
                                var h6 = $("<h6>").html(qid+"、"+data.singleQuestionList[i].questionName).appendTo(main);
                                var ul = $("<ul>").appendTo(main);
                                if(data.singleQuestionList[i].answerList && data.singleQuestionList[i].answerList.length>0){
                                    for(var j=0;j<data.singleQuestionList[i].answerList.length;j++){
                                        if(type == "test"){ // 测试
                                            if(data.singleQuestionList[i].answerList[j].isSelected){
                                                var li = $("<li>").addClass("active").appendTo(ul);
                                                var input = $("<input>").attr({
                                                    type:'radio',
                                                    id:data.singleQuestionList[i].answerList[j].id,
                                                    name:data.singleQuestionList[i].answerList[j].questionCode,
                                                    value:data.singleQuestionList[i].answerList[j].answerCode,
                                                    checked:true
                                                }).appendTo(li);
                                            }else{
                                                var li = $("<li>").appendTo(ul);
                                                var input = $("<input>").attr({
                                                    type:'radio',
                                                    id:data.singleQuestionList[i].answerList[j].id,
                                                    name: data.singleQuestionList[i].answerList[j].questionCode,
                                                    value:data.singleQuestionList[i].answerList[j].answerCode
                                                }).appendTo(li);
                                            }
                                        }else if(type == "reTest"){ // 重测
                                            var li = $("<li>");
                                            var input = $("<input>").attr({
                                                type:'radio',
                                                id:data.singleQuestionList[i].answerList[j].id,
                                                name:data.singleQuestionList[i].answerList[j].questionCode,
                                                value:data.singleQuestionList[i].answerList[j].answerCode
                                            });
                                            if(data.singleQuestionList[i].answerList[j].isSelected){ // 已填的答案   isSelected
                                                li = $("<li>").addClass("active red");
                                                input = $("<input>").attr({
                                                    type:'radio',
                                                    id:data.singleQuestionList[i].answerList[j].id,
                                                    name:data.singleQuestionList[i].answerList[j].questionCode,
                                                    value:data.singleQuestionList[i].answerList[j].answerCode,
                                                    checked:true
                                                });
                                            }
                                            if(data.singleQuestionList[i].answerList[j].isCorrect){ // 正确答案
                                                li = $("<li>").removeClass("red").addClass(" green");
                                            }

                                            $(input).appendTo(li);
                                            $(li).appendTo(ul);
                                        }
                                        var label = $("<label>").attr("for", data.singleQuestionList[i].answerList[j].id).html(data.singleQuestionList[i].answerList[j].answerCode + "：" + data.singleQuestionList[i].answerList[j].answerContent).appendTo(li);
                                    }
                                }
                                qid++;
                            }
                        }
                        if(data.moreQuestionList && data.moreQuestionList.length>0){
                            titFlag += 1;

                            var part = $("<div>").addClass("part multipleQues").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、多选题").appendTo(part);
                            for(var i=0;i<data.moreQuestionList.length;i++){
                                var main = $("<div>").addClass("main").appendTo(part);
                                var h6 = $("<h6>").html(qid+"、(多选)"+data.moreQuestionList[i].questionName).appendTo(main);
                                var ul = $("<ul>").addClass("clearfix").appendTo(main);

                                for(var j=0;j<data.moreQuestionList[i].answerList.length;j++){
                                    if(type == "test") { // 测试
                                        if (!data.moreQuestionList[i].answerList[j].isSelected) {
                                            if(data.moreQuestionList[i].answerList.length > 4){
                                                var li = $("<li>").addClass("fl w100").appendTo(ul);
                                            }else{
                                                var li = $("<li>").addClass("w100").appendTo(ul);
                                            }
                                            var input = $("<input>").attr({
                                                type:'checkbox',
                                                id:data.moreQuestionList[i].answerList[j].id,
                                                name:data.moreQuestionList[i].answerList[j].questionCode,
                                                value:data.moreQuestionList[i].answerList[j].answerCode
                                            }).appendTo(li);
                                        }else{
                                            if(data.moreQuestionList[i].answerList.length > 4){
                                                var li = $("<li>").addClass("fl w100 active").appendTo(ul);
                                            }else{
                                                var li = $("<li>").addClass("w100 active").appendTo(ul);
                                            }
                                            var input = $("<input>").attr({
                                                type:'checkbox',
                                                id:data.moreQuestionList[i].answerList[j].id,
                                                name:data.moreQuestionList[i].answerList[j].questionCode,
                                                value:data.moreQuestionList[i].answerList[j].answerCode,
                                                checked:true
                                            }).appendTo(li);
                                        }
                                    }else if(type == "reTest") { // 重测
                                        if(data.moreQuestionList[i].answerList.length > 4){
                                            var li = $("<li>").addClass("fl w100");
                                        }else{
                                            var li = $("<li>").addClass("w100");
                                        }
                                        var input = $("<input>").attr({
                                            type:'checkbox',
                                            id:data.moreQuestionList[i].answerList[j].id,
                                            name:data.moreQuestionList[i].answerList[j].questionCode,
                                            value:data.moreQuestionList[i].answerList[j].answerCode
                                        });
                                        if(data.moreQuestionList[i].answerList[j].isSelected){ // 已填的答案
                                            if(data.moreQuestionList[i].answerList.length > 4){
                                                li = $("<li>").addClass("fl w100 active red");
                                            }else{
                                                li = $("<li>").addClass("w100 active red");
                                            }
                                            input = $("<input>").attr({
                                                type:'checkbox',
                                                id:data.moreQuestionList[i].answerList[j].id,
                                                name:data.moreQuestionList[i].answerList[j].questionCode,
                                                value:data.moreQuestionList[i].answerList[j].answerCode,
                                                checked:true
                                            });
                                        }
                                        if(data.moreQuestionList[i].answerList[j].isCorrect){ // 正确答案
                                            if(data.moreQuestionList[i].answerList.length > 4){
                                                li = $("<li>").removeClass("fl w100 red").addClass(" fl w100 green");
                                            }else{
                                                li = $("<li>").removeClass("w100 red").addClass(" w100 green");
                                            }
                                        }
                                        $(input).appendTo(li);
                                        $(li).appendTo(ul);
                                    }
                                    var label = $("<label>").attr("for",data.moreQuestionList[i].answerList[j].id).html(data.moreQuestionList[i].answerList[j].answerCode+"："+data.moreQuestionList[i].answerList[j].answerContent).appendTo(li);
                                }
                                qid++;
                            }
                        }
                        if(data.judgeQuestionList && data.judgeQuestionList.length>0){
                            titFlag += 1;

                            var part = $("<div>").addClass("part judgeQues").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"、判断题").appendTo(part);
                            for(var i=0;i<data.judgeQuestionList.length;i++){
                                var main = $("<div>").addClass("main").appendTo(part);
                                var h6 = $("<h6>").html(qid+"、"+data.judgeQuestionList[i].questionName).appendTo(main);
                                var ul = $("<ul>").addClass("clearfix").appendTo(main);

                                for(var j=0;j<data.judgeQuestionList[i].answerList.length;j++){
                                    if(type == "test") { // 测试
                                        if(data.judgeQuestionList[i].answerList[j].isSelected){
                                            var li = $("<li>").addClass("fl w15 active").appendTo(ul);
                                            var input = $("<input>").attr({
                                                type: 'radio',
                                                id:data.judgeQuestionList[i].answerList[j].id,
                                                name:data.judgeQuestionList[i].answerList[i].questionCode,
                                                value:data.judgeQuestionList[i].answerList[j].answerCode,
                                                checked:true
                                            }).appendTo(li);
                                        }else{
                                            var li = $("<li>").addClass("fl w15").appendTo(ul);
                                            var input = $("<input>").attr({
                                                type:'radio',
                                                id:data.judgeQuestionList[i].answerList[j].id,
                                                name:data.judgeQuestionList[i].answerList[i].questionCode,
                                                value:data.judgeQuestionList[i].answerList[j].answerCode
                                            }).appendTo(li);
                                        }
                                    }else if(type == "reTest"){ // 重测
                                        var li = $("<li>").addClass("fl w15");
                                        var input = $("<input>").attr({
                                            type:'radio',
                                            id:data.judgeQuestionList[i].answerList[j].id,
                                            name:data.judgeQuestionList[i].answerList[i].questionCode,
                                            value:data.judgeQuestionList[i].answerList[j].answerCode
                                        });

                                        if(data.judgeQuestionList[i].answerList[j].isSelected){
                                            li = $("<li>").addClass("fl w15 active red");
                                            input = $("<input>").attr({
                                                type:'radio',
                                                id:data.judgeQuestionList[i].answerList[j].id,
                                                name:data.judgeQuestionList[i].answerList[i].questionCode,
                                                value:data.judgeQuestionList[i].answerList[j].answerCode,
                                                checked:true
                                            });
                                        }
                                        if(data.judgeQuestionList[i].answerList[j].isCorrect){
                                            li = $("<li>").removeClass("fl w15 red").addClass(" fl w15 green");
                                        }
                                        $(input).appendTo(li);
                                        $(li).appendTo(ul);
                                    }

                                    var label = $("<label>").attr("for",data.judgeQuestionList[i].answerList[j].id).appendTo(li);
                                    if (data.judgeQuestionList[i].answerList[j].answerCode == "true") {
                                        var iObj = $("<i>").addClass("iconfont icon-duihao1").appendTo(label);
                                    } else {
                                        var iObj = $("<i>").addClass("iconfont icon-cuo1").appendTo(label);
                                    }
                                    qid++;
                                }
                            }
                        }
                        if(data.shortAnswerQuestionList && data.shortAnswerQuestionList.length>0){
                            titFlag += 1;

                            var part = $("<div>").addClass("part shortAnswer").appendTo($(".questions"));
                            //var h3 = $("<h3>").addClass("questionType").html(formatNumber(0,titFlag)+"：简答题").appendTo(part);
                            for(var i=0;i<data.shortAnswerQuestionList.length;i++){
                                var main = $("<div>").addClass("main shortAnswer").appendTo(part);
                                var h6 = $("<h6>").html(qid+"、"+data.shortAnswerQuestionList[i].questionName).appendTo(main);
                                var pObj = $("<p>").appendTo(main);
                                if(type == "test"){ // 测试
                                    if(data.shortAnswerQuestionList[i].answerCode){
                                        var textarea = $("<textarea>").attr({id:data.shortAnswerQuestionList[i].questionCode}).html(data.shortAnswerQuestionList[i].answerCode).appendTo(pObj);
                                    }else{
                                        var textarea = $("<textarea>").attr({id:data.shortAnswerQuestionList[i].questionCode}).appendTo(pObj);
                                    }
                                }else if(type == "reTest") { // 重测
                                    var textarea = $("<textarea>").attr({id:data.shortAnswerQuestionList[i].questionCode}).html(data.shortAnswerQuestionList[i].answerCode).appendTo(pObj);
                                }
                                qid++;
                            }
                        }

                        // input和textarea的事件
                        $(".questions input").on("click",function() {
                            //判断是否是其他 是其他就让用户输入内容
                            var other=$(this).next().text().substring(2,4)
                            // if((other=="其他"||other=="其它")&&$(this).prop("checked")==true){
                            //     $(this).parent().parent().append('<li class="other">请输入：<input type="text"></li>')
                            // }
                            // if((other=="其他"||other=="其它")&&$(this).prop("checked")==false){
                            //         $(this).parent().next().remove()
                            //     }
                            if ((other=="其他"||other=="其它") && !$(this).parent().siblings("li:last-child").hasClass("other")) {
                                $(this).parent().parent().append('<li class="other">请输入：<textarea type="text"></textarea></li>')
                            }
                            if (other != "其他" && other != "其它" && $(this).parent().siblings("li:last-child").hasClass("other")) {
                                $(this).parent().siblings("li:last-child").remove()
                            }
                            // 单选和判断的高亮、isSelected字段控制
                            if ($(this).attr("type") && $(this).attr("type") == "radio") {
                                if (!$(this).parent("li").hasClass("active")) {
                                    $(this).parent("li").addClass(" active");
                                    $(this).parent("li").siblings().removeClass(" active");

                                    // 重测时isSelected字段表示上次已选择的选项
                                    var partClass = $(this).parents(".part").attr("class").split(" ")[1];
                                    var big_index = $(this).parents(".main").index("." + partClass + " .main"); // 在大题中的索引
                                    var small_index = $(this).parents().index(); // 在小题中的索引
                                    if ($(this).parents(".part").hasClass("singleQues")) {
                                        for (var i = 0; i < examData.singleQuestionList.length; i++) {
                                            for (var j = 0; j < examData.singleQuestionList[i].answerList.length; j++) {
                                                examData.singleQuestionList[i].answerList[j].isSelected = false;
                                            }
                                        }
                                        examData.singleQuestionList[big_index].answerList[small_index].isSelected = true;
                                    } else if ($(this).parents(".part").hasClass("judgeQues")) {
                                        for (var i = 0; i < examData.judgeQuestionList.length; i++) {
                                            for (var j = 0; j < examData.judgeQuestionList[i].answerList.length; j++) {
                                                examData.judgeQuestionList[i].answerList[j].isSelected = false;
                                            }
                                        }
                                        examData.judgeQuestionList[big_index].answerList[small_index].isSelected = true;
                                    }
                                }
                            };

                            // 多选的高亮、isSelected控制
                            if ($(this).attr("type") && $(this).attr("type") == "checkbox") {
                                if (!$(this).parent("li").hasClass("active")) {
                                    //最多只能选3个
                                    if($(this).parents("ul").find("li.active").length >= 3){
                                        $(this).removeAttr("checked");
                                        //如果有其他 去掉显示的请输入框
                                        if($(this).parent().next(".other")){
                                            $(this).parent().next(".other").remove()
                                        }
                                        getparent().mesShow("温馨提示","多选最多只能选择三个！",2000,'orange');
                                    }else{
                                        $(this).parent("li").addClass(" active");
                                    }
                                } else {
                                    $(this).parent("li").removeClass("active");
                                }
                                for (var i = 0; i < examData.moreQuestionList.length; i++) {
                                    for (var j = 0; j < examData.moreQuestionList[i].answerList.length; j++) {
                                        if ($(".multipleQues .main").eq(i).find("li").eq(j).hasClass("active")) {
                                            examData.moreQuestionList[i].answerList[j].isSelected = true;
                                        } else {
                                            examData.moreQuestionList[i].answerList[j].isSelected = false;
                                        }
                                    }
                                }
                            };

                            // 单选
                            if ($(this).parents(".part").hasClass("singleQues")) {
                                singleData = [];
                                for (var i = 0; i < $(".singleQues .main").length; i++) {
                                    if ($(".singleQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                        singleData.push({
                                            questionCode: $(".singleQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                            examAnswer: $(".singleQues .main").eq(i).find("input[type='radio']:checked").val()
                                        });
                                    }
                                }
                            }
                            // 多选
                            if ($(this).parents(".part").hasClass("multipleQues")) {

                                multipleData = [];
                                for (var i = 0; i < $(".multipleQues .main").length; i++) {
                                    var mulAnswer = []; // 每道多选题选中的答案
                                    if ($(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
                                        $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
                                            mulAnswer.push($(this).val());
                                        });
                                        multipleData.push({
                                            questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
                                            examAnswer: mulAnswer.join('/')
                                        });
                                    }
                                }
                            }
                            // 判断
                            if ($(this).parents(".part").hasClass("judgeQues")) {
                                judgeData = [];
                                for (var i = 0; i < $(".judgeQues .main").length; i++) {
                                    if ($(".judgeQues .main").eq(i).find("input[type='radio']:checked").length > 0) {
                                        judgeData.push({
                                            questionCode: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").attr("name"),
                                            examAnswer: $(".judgeQues .main").eq(i).find("input[type='radio']:checked").val()
                                        });
                                    }
                                }
                            }

                            // 设置提交按钮高亮是否显示
                            if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen) {
                            // if (singleData.length + multipleData.length + judgeData.length  == singleLen + multipleLen + judgeLen ) {
                                $("#submit").addClass(" canSubmit");
                            } else {
                                $("#submit").removeClass("canSubmit");
                            }
                        });
                        $("textarea").on("input propertychange",function() {
                            // 简答
                            if ($(this).parents(".part").hasClass("shortAnswer")) {
                                shortData = [];
                                for (var i = 0; i < $(".shortAnswer .main").length; i++) {
                                    if ($(".shortAnswer .main").eq(i).find("textarea").val() != "") {
                                        shortData.push({
                                            questionCode: $(".shortAnswer .main").eq(i).find("textarea").attr("id"),
                                            examAnswer: $(".shortAnswer .main").eq(i).find("textarea").val()
                                        });
                                    }
                                }
                            }

                            // 设置提交按钮高亮是否显示
                            if (singleData.length + multipleData.length + judgeData.length + shortData.length == singleLen + multipleLen + judgeLen + shortLen) {
                                $("#submit").addClass(" canSubmit");
                            } else {
                                $("#submit").removeClass("canSubmit");
                            }
                        });

                        if(isFinishExam){ // 已完成考试禁用表单
                            $(".questions input,.questions textarea").attr("disabled",true);
                            $("#submit").hide();
                        }else{
                            $(".questions input,.questions textarea").attr("disabled",false);
                            $("#submit").show();
                        }

                    }
                }

                // 点“提交”
                $("#submit").click(function(){
                    if(singleData.length+multipleData.length+judgeData.length+shortData.length <= 0){
                        getparent().mesShow("温馨提示","还未开始答题",2000,'red');
                    }else if(singleData.length+multipleData.length+judgeData.length+shortData.length < singleLen+multipleLen+judgeLen+shortLen){
                    // }else if(singleData.length+multipleData.length+judgeData.length < singleLen+multipleLen+judgeLen){
                        getparent().mesShow("温馨提示","试卷未答完，请继续答题！",2000,'red');
                    }else{
                        $("#submitDialog").dialog({closed:false});
                    }
                    //判断其他选项的请输入的值
                    if($(".other")){
                        for(var i=0;i<$(".other").length;i++){
                            var other=$(".other").eq(i).prev().find("input")
                            var prevValue=other.val()
                            var prevValueSubB=prevValue.substring(0,2)
                            var prevValueSub=prevValue.substring(2,prevValue.length)
                            var otherValue=$(".other").eq(i).find("textarea").val()
                            if(prevValue.indexOf(":")==-1){
                                other.val(prevValue+":"+otherValue)
                            }else if(prevValueSub!=otherValue){
                                other.val(prevValueSubB+otherValue)
                            }
                        }
                    }
                    //用户改变其他选项 请输入的值的时候
                    multipleData = [];
                    for (var i = 0; i < $(".multipleQues .main").length; i++) {
                        var mulAnswer = []; // 每道多选题选中的答案
                        if ($(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").length > 0) {
                            $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").each(function (index) {
                                mulAnswer.push($(this).val());
                            });
                            multipleData.push({
                                questionCode: $(".multipleQues .main").eq(i).find("input[type='checkbox']:checked").attr("name"),
                                examAnswer: mulAnswer.join('/')
                            });
                        }
                    }
                });

                $("#sureSubmit").click(function(){
                    submitData();
                });

                // 提交答案
                function submitData(){
                    clearInterval(remainTimeT);

                    var questionCodeArry = [];
                    var examAnswerArry = [];
                    var totalData = singleData.concat(multipleData,judgeData,shortData);

                    for(var i = 0; i < totalData.length; i++){
                        questionCodeArry.push(totalData[i].questionCode);
                        examAnswerArry.push(totalData[i].examAnswer);
                    }

                     var sex= $('input:radio[name=sex]:checked').val();

                    if(sex){
                        ajaxgeneral({
                            url: 'action/examInfo/saveExamInfo',
                            data: {
                                examAppCode: gps.examAppCode,
                                publishUsername: username,
                                sex:sex,
                                //id: examData.id,
                                examRecord: questionCodeArry.join(','),
                                examAnswer: examAnswerArry.join(','),
                                questionBlankCode: questionBlankCode,
                                residueTime: totalSS
                            },
                            contentType: "application/json; charset=utf-8",
                            success: function (res) {
                                if(remainTimeT) clearInterval(remainTimeT);

                                // 禁止答题
                                $(".questions input,.questions textarea").attr("disabled",true);
                                isFinishExam = true;

                                //待办转已办
                                ajaxgeneral({
                                    url: 'action/examWork/dealWithByUsername?username=' + username + "&currentUserCode=" + username + "&source=PC&examCode=ghhy-01",
                                    async:false,
                                    contentType: "application/json; charset=utf-8",
                                    success: function (res) {
                                        if(res.data && res.data!=-1){
                                            // 禁止答题
                                            $(".questions input,.questions textarea").attr("disabled",true);
                                            isFinishExam = true;
                                        }else{
                                            isFinishExam = false;
                                        }
                                    }
                                });
                                $("#scoreDialog h5").html("提交成功！请抽奖");
                                $("#reTry").hide();
                                $("#yes").show();

                                // if(res.data == "true"){
                                //
                                // }
                                // else{ //有错题就重测
                                //     $("#scoreDialog h5").html("问卷未全部答对，请查阅标准答案！");
                                //     $("#yes").hide();
                                //     $("#reTry").show();
                                // }

                                $("#submitDialog").dialog({closed:true});
                                $("#scoreDialog").dialog({closed:false});
                            }
                        });
                    }
                    else {
                        getparent().mesShow("温馨提示","请在最上方选择您的性别",2000,'red');

                    }


                }

                // 全答对时关闭弹框
                $("#yes").click(function(){
                    $(".scoreBtns").css("visibility","hidden")
                    //抽奖
                    ajaxgeneral({
                        url: "action/lottery/isLottery",
                        async:false,
                        contentType: "application/json; charset=utf-8",
                        success: function (res) {
                            $("#scoreDialog").dialog({closed:true});
                            $("#editNumDialog").show();
                            setTimeout(function (){
                                $("#editNumDialog").hide();
                                if(res.data==true){
                                    $("#scoreDialog").dialog({closed:false});
                                    $("#scoreDialog h5").html("恭喜您中奖了！请您填写个人信息");
                                    //成功
                                    setTimeout(function () {
                                        $("#scoreDialog").dialog({closed:true});
                                        $("#LotteryDialog").dialog({closed:false});
                                    },2000)
                                }
                                if(res.data==false){
                                    $("#scoreDialog").dialog({closed:false});
                                    $("#scoreDialog h5").html("很遗憾，没有中奖");
                                    //失败
                                    setTimeout(function () {
                                        $("#scoreDialog").dialog({closed:true});
                                        $("#closeDialog").dialog({closed:false});
                                    },2000)
                                }
                            },2000)
                        }
                    });
                });

                // 返回重测
                $("#reTry").click(function(){
                    $("#scoreDialog").dialog({closed:true});

                    // 匹配以选择的答案
                    function matchAnswerReTest(lists,answer,mark){
                        for(var i = 0; i < answer.length; i++){
                            for(var n = 0; n < lists.length; n++){
                                if(answer[i].questionCode == lists[n].questionCode){
                                    if(mark != 'shortAnswer'){
                                        var examAnswerOptions = answer[i].examAnswer.split('/');
                                        for(var ii = 0; ii < examAnswerOptions.length; ii++){
                                            for(var nn = 0; nn < lists[n].answerList.length; nn++){
                                                if(examAnswerOptions[ii] == lists[n].answerList[nn].answerCode){
                                                    lists[n].answerList[nn].isSelected = true;
                                                }else {
                                                    if (!lists[n].answerList[nn].isSelected) {
                                                        lists[n].answerList[nn].isSelected = false;
                                                    }
                                                }
                                            }
                                        }
                                    }else{
                                        lists[n].answerCode = answer[i].examAnswer;
                                    }
                                }
                            }
                        }
                        return lists
                    }

                    matchAnswerReTest(examData.singleQuestionList,singleData,'single');
                    matchAnswerReTest(examData.moreQuestionList,multipleData,'multiple');
                    matchAnswerReTest(examData.judgeQuestionList,judgeData,'judge');
                    matchAnswerReTest(examData.shortAnswerQuestionList,shortData,'shortAnswer');

                    currentAction = "reTest";
                    totalSS = parseInt(examData.setTime)*60;
                    $(".questions").html("");
                    showQuestions("reTest",examData);
                    // if(remainTimeT) clearInterval(remainTimeT);
                    // remainTimeT = setInterval(ajaxInterval,1000); //每隔5秒保存一次数据
                });

                //中奖
                $("#isWrite").click(function () {
                    var truename=$("input[name='truename']").val();
                    var phone=$("input[name='phone']").val();
                    var address=$("#address").val();
                    var isValid=$("input[name='phone']").validatebox('isValid');


                    if(truename==""){top.mesShow("温馨提示","请填写姓名！",2000,'orange');return false}
                    if(phone==""){top.mesShow("温馨提示","请填写电话号码！",2000,'orange');return false}
                    if(address==""){top.mesShow("温馨提示","请填写收货地址！",2000,'orange');return false}
                    if(isValid==false){top.mesShow("温馨提示","请填写有效的手机号码！",2000,'orange');return false}
                    //保存中奖信息
                    ajaxgeneral({
                        url: "action/lottery/createToJackpotReduce",
                        async:false,
                        data:{
                            username:username,
                            truename:truename,
                            phone:phone,
                            address:address
                        },
                        contentType: "application/json; charset=utf-8",
                        success: function (res) {
                            top.mesShow("温馨提示","恭喜您已填完个人信息！",2000,'orange');
                        }
                    });
                    $("#LotteryDialog").dialog({closed:true});
                    $("#closeDialog").dialog({closed:false});
                });

                // 试卷已完成时，关闭页面
                $("#closeBtns button").click(function(){
                    $('#closeDialog').dialog('close');

                    if(gps.actionType && (gps.actionType=="secrecyJoin" || gps.actionType=="secrecyTask")){
                        top.dialogClose("detail");
                    }else{
                        var userAgent = navigator.userAgent;
                        if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
                            // window.location.href = "about:blank";
                            window.history.back();
                        } else {
                            // window.opener = null;
                            // window.open('', '_self');
                            window.location.replace(window.location.href);
                        }
                        window.close();
                    }

                })
            }
        })
    </script>
    <style type="text/css">
        /*公共样式*/
        .clearfix:after{content:'.';display:block;height:0;line-height:0;clear:both;visibility:hidden;}
        .clearfix{zoom:1;}
        .w15{width:15%;}
        /*页面样式*/
        .wrapper{min-height:1924px;margin:0 auto;background-color:#fff;color:#000;background: url("./exam2.jpg") no-repeat top;}
        .header{text-align: center;height: 555px;}
        .header,.header img{width:100%;}
        .details{width:730px;max-height:1024px;padding:10px;font-size:16px;margin:0 auto;overflow-y: scroll;overflow-x: hidden}
        .other{width: 100%;}
        .other textarea{width: 70%!important;}
        .explain{margin:10px 0;}
        .explain p{line-height:32px;}
        .explain .bold{font-weight: bold;font-family: KaiTi}
        .questions{padding-bottom: 20px}
        .questionType{font-size:20px;font-weight:bold;line-height:1.2;margin-top:20px;}
        .main,.main ul{padding:0 40px;}
        .shortAnswer .main ul{padding:0 10px;}
        .main h6{font-size:16px;line-height:1.5;margin:25px 0;}
        .main li{line-height:1.5;margin:15px 0;}
        .main li.fl{margin-top:0;}
        .main .active{color:orange;}
        .main .green{color:#09DB87;}
        .main .red{color:#E11414;}
        .main input{width:auto;}
        .main label{margin-left:10px;}
        .shortAnswer .main textarea{min-height:160px;font-size:14px;}
        .icon-duihao1{font-size:16px;margin-left:4px;}
        .icon-cuo1{font-size:14px;font-weight:bold;margin-left:4px;}
        .submitBtn{display:block;border:0;outline:0;width:90px;height:36px;background:#B4B4B4;border-radius:4px;font-size:14px;color:#fff;margin:10px 0 0 60px;letter-spacing:2px;}
        .submitBtn:active{opacity:.85;}
        .canSubmit{background-color:#E83333;}

        .dialog h5{font-size:15px;font-weight:bold;text-align:center;margin-top:10px;}
        .forceSubmitDialog p{font-size:14px;font-weight:bold;text-align:center;margin-top:20px;}
        .scoreDialog p{font-size:12px;text-align:center;}

        .submitBtns button{border:0;outline:0;padding:0;margin:0;height:32px;font-size:12px;color:#fff;text-align:center;border-radius:4px;padding:0 20px!important;}
        .submitBtns .gray{background-color:#B4B4B4;}
        .submitBtns .red{background-color:#E11414;}
        .remainTime{font-size:15px;font-weight:bold;margin-top:20px;text-align:right;}
    </style>
</head>
<body style="height: 100%;overflow: scroll;">
<div class="hover" style="width: 1000px;margin: auto;">
    <img src="./hover2021.jpg" style="display: block;max-height: 800px;margin:auto;" usemap="#Map">
</div>
<div class="wrapper" style="display: none;">
    <p class="header"><img src="../../images/exam_head.jpg" style="visibility: hidden"></p>
    <div class="details">
        <div class="explain">
            <p>亲爱的会员:</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;为不断优化工会工作内容，提升工会工作水平，加强与广大员工的联系与沟通，现开展2021年度工会会员问卷调查。</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;请您根据问卷内容如实填写，我们将根据您的意见和建议调整并改进2022年工会工作的内容和重点。</p>
            <p class="bold">&nbsp;&nbsp;&nbsp;&nbsp;您的心声就是我们努力的方向，</p>
            <p class="bold">&nbsp;&nbsp;&nbsp;&nbsp;您的意见就是我们前进的动力，</p>
            <p class="bold">&nbsp;&nbsp;&nbsp;&nbsp;期待您的热心参与……</p>
        </div>

        <div id="information">
            <table>
                <tr>
                    <td>姓名：</td>
                    <td>
                        <input id="truename"  readonly="readonly" class="easyui-validatebox"  style="vertical-align:middle; width: 180px;border-left:none;border-top:none;border-right:none;text-align:left; border-color:black;background: rgb(251,229,182); "/>
                    </td>
                </tr>
                <tr>
                    <td>性别：</td>
                    <td>
                        <input class='wauto'  type="radio" noReset="true" id="sex1" name="sex" value="男" /><label style="margin-right: 10px">男</label>
                        <input class='wauto'  type="radio" noReset="true" id="sex2"  name="sex" value="女" /><label>女</label>
                    </td>
                </tr>
            </table>
        </div>
        <div id="remainTime" class="remainTime"></div>
        <div class="questions">

        </div>
        <!-- 提交 -->
        <button class="submitBtn" id="submit">提交</button>
        <!-- 提交对话框 -->
        <div id="submitDialog" class="easyui-dialog dialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#submitBtns'" style="width:400px;height:200px;padding:10px"><h5>您已答完所有题，确认提交？</h5></div>
        <div class="btns submitBtns" id="submitBtns" style="text-align:center;">
            <button class="easyui-linkbutton gray" onclick="$('#submitDialog').dialog('close')">取消</button>
            <button id="sureSubmit" class="easyui-linkbutton red">是的确认提交！</button>
        </div>

        <!-- 提交成功对话框 -->
        <div id="scoreDialog" class="easyui-dialog dialog scoreDialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#scoreBtns'" style="width:400px;height:200px;padding:10px">
            <h5></h5>
            <!--<p>（注：简答题不计入合格分数，只计入参考分值）</p>-->
        </div>
        <div class="submitBtns scoreBtns" id="scoreBtns" style="text-align:center;">
            <button id="reTry" class="easyui-linkbutton red hide">查看问卷</button>
            <button id="yes" class="easyui-linkbutton red hide">确定</button>
        </div>

        <!-- 打开试卷时，试卷已完成，关闭页面 -->
        <div id="closeDialog" class="easyui-dialog dialog closeDialog" title="温馨提示" closed="true" data-options="modal:true,buttons:'#closeBtns'" style="width:400px;height:200px;padding:10px">
            <h5>您已完成调查问卷，感谢您的参与！</h5>
        </div>
        <div class="submitBtns" id="closeBtns" style="text-align:center;">
            <button class="easyui-linkbutton red">确定</button>
        </div>

        <!-- 抽奖 -->
        <div id="LotteryDialog" class="easyui-dialog dialog dialog" title="请填写个人信息" closed="true" data-options="modal:true,buttons:'#LotteryBtns'" style="width:700px;height:400px;padding:10px">
            <table border="0" cellpadding="0" cellspacing="15" width="100%" style="background-color: #e88484">
                <tr><td align="right" style="font-weight: bold; font-size: 16px;width: 20%;"><i class="iconfont">&#xe6cc;</i>姓名：</td><td><input type="text" name="truename" width="300px"></td></tr>
                <tr><td align="right" style="font-weight: bold; font-size: 16px;width: 20%;"><i class="iconfont">&#xe6bc;</i>联系方式：</td><td><input type="text" name="phone" width="300px"  class="easyui-validatebox" validType="phone"></td></tr>
                <tr><td align="right" style="font-weight: bold; font-size: 16px;width: 20%;"><i class="iconfont">&#xe6d3;</i>收货地址：</td><td><textarea type="text" id="address" name="address" class="easyui-validatebox" style="width: 100%; height: 120px; resize: none;"></textarea></td></tr>
                <tr><td><input type="hidden" name="username"></td></tr>
            </table>
        </div>
        <div class="submitBtns" id="LotteryBtns" style="text-align:center;">
            <button id="isWrite" class="easyui-linkbutton red">提交</button>
        </div>
    </div>
</div>
<map name="Map" id="Map" style="cursor: pointer;">
    <area shape="rect" coords="143,663,308,702" target="_blank">
</map>
<div id="editNumDialog" class="hide" style="position: absolute;top: 50%;left: 50%;width: 500px;height: 231px;margin-left: -250px;margin-top: -115px;z-index: 999">
    <img src="./jpeg.gif" alt="">
</div>
</body>
</html>
