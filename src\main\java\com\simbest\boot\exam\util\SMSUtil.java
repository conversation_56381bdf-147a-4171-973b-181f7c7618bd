package com.simbest.boot.exam.util;

import com.google.common.collect.Sets;
import com.mzlion.easyokhttp.HttpClient;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.cmcc.constants.CmccConstants;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.security.SimpleConfig;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.util.encrypt.Des3Encryptor;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.uums.api.app.UumsSysAppConfigApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @用途: 发送短信
 * @作者：zsf
 * @时间: 2018/12/7
 */
@Slf4j
@Component
public class SMSUtil {
    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private Des3Encryptor des3Encryptor;

    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private UumsSysAppConfigApi uumsSysAppConfigApi;

    /**
     * 准备短信对象
     * @param map 参数 （endUser待发人，短信模板）
     * @return
     */
    public   ShrotMsg readyParams(Map<String,Object> map){
        ShrotMsg shrotMsg = new ShrotMsg();//短信对象
        Content content = new Content();
        Set<Content> contentSet = new HashSet<Content>();
        shrotMsg.setAppCode(  Constants.APP_CODE );
        content.setUsername( map.get("sendUser") != null ? map.get("sendUser").toString():"" );
        content.setMsgContent( map.get("msg") != null ? map.get("msg").toString():""  );
        content.setImmediately( true );
        content.setSmsPriority( 1 );
        contentSet.add( content );
        shrotMsg.setContents( contentSet );
        return shrotMsg;
    }

    /**
     * 发送短信
     * @param shrotMsg      短信发送实体
     * @return
     */
    public Boolean postMsgWithAnddoc( ShrotMsg shrotMsg ){
        Boolean postFlag = false;
        try {
            Boolean isPostMsg = false;   //false 短信不发送     true 发送
            /**查询发送短信开关是否开始**/
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode( Constants.APP_CODE,"hadmin3" );
            if ( simpleApp != null ){
                isPostMsg = simpleApp.getIsSendMsg();
            }
            if ( isPostMsg ) {
                for ( Content content : shrotMsg.getContents( ) ) {
                    Set<String> msgPhones = content.getPhoneNums( );
                    Set<String> resMsgPhones = Sets.newHashSet( );
                    if ( msgPhones != null && !msgPhones.isEmpty( ) ) {
                        break;
                    }
                    SimpleUser simpleUser = uumsSysUserinfoApi.findByUsername( content.getUsername(),shrotMsg.getAppCode() );
                    resMsgPhones.add( simpleUser.getPreferredMobile() );
                    content.setPhoneNums( resMsgPhones );
                }
                String msgJson = JacksonUtils.obj2json( shrotMsg );
                String msgEncyptorJson = des3Encryptor.encrypt( msgJson );
                log.warn( "SMSTool>>>>>>>postMsgWithAnddoc>>>>>短信发送加密串>>>" + msgEncyptorJson );
                SimpleConfig simpleConfig = uumsSysAppConfigApi.findAppConfigByStyle( CmccConstants.UMSC_APP_CODE,"hadmin", CmccConstants.COMMON_APP_CODE);
                JsonResponse response = HttpClient.post( simpleConfig.getAddress( ) + CmccConstants.MSG_POST_INTERFACE )
                        .param( "loginuser", rsaEncryptor.encrypt( "hadmin" ) )
                        .param( "appcode", CmccConstants.COMMON_APP_CODE )
                        .param( "sendMtMsgJson", msgEncyptorJson )
                        .asBean( JsonResponse.class );
                log.warn( "SMSTool>>>>>>>response>>>>>结束>>>" );
                int ret = response.getErrcode( ).intValue( );
                if ( ret == 0 ) {
                    postFlag = true;
                    log.warn( "SMSTool>>>>>>>response>>>>>结束>>>发送成功 ret=" + ret );
                }else {
                    log.warn( "SMSTool>>>>>>>response>>>>>结束>>>发送失败" );
                }
            }
        }catch ( Exception e ){
            Exceptions.printException(e);
            throw e;
        }
        return postFlag;
    }
}
