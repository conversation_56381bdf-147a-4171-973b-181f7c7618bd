<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>人员详情</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../../css/public.css?v=svn.revision" th:href="@{/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var gps = getQueryString();
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#personTable",//table列表的id名称，需加#
                    "querycmd":"network/message/findUsersInOrg?orgId="+gps.orgId,//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass":"noScroll",
                    "hasPagenation":true,
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "姓名", field: "truename",width:80,align: 'center',tooltip: true},
                        { title: "用户名", field: "username",width:80,align: 'center'},
                        { title: "所属部门", field: "orgDisplayName",width:180,align: 'center',tooltip: true},
                    ]],
                }
            };
            //判断是组织还是职位还是分组
            //查看和编辑、新增查看详情接口不一样
            if(gps.type=='read'){
                if(gps.orgId){
                    pageparam.listtable.querycmd = "network/message/findUsersInOrg?orgId="+gps.id;
                }else if(gps.positionId){
                    pageparam.listtable.querycmd = "network/message/findUsersInPosition?positionId="+gps.id;
                }else{
                    pageparam.listtable.querycmd = "network/message/findUsersInGroup?groupId="+gps.id;
                }
            }else{
                if(gps.orgId){
                    pageparam.listtable.querycmd = "network/message/findNewUsersInOrg?orgCode="+gps.orgId;
                    // pageparam.listtable.hasPagenation = true;
                }else if(gps.positionId){
                    pageparam.listtable.querycmd = "network/message/findNewUsersInPosition?positionId="+gps.positionId;
                }else{
                    pageparam.listtable.querycmd = "network/message/findNewUsersInGroup?groupId="+gps.groupId;
                }
            }
            loadGrid(pageparam);
        });
        function loadList(){
            $('#personTable').datagrid('reload');
        }
        function timestampToTime(timestamp) {
            var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
            var Y = date.getFullYear() + '-';
            var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
            var D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
            var h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
            var m = (date.getMinutes() < 10 ? '0'+date.getMinutes() : date.getMinutes()) + ':';
            var s = (date.getSeconds() < 10 ? '0'+date.getSeconds() : date.getSeconds());
            strDate = Y+M+D+h+m+s;
            return strDate;
        }
    </script>
</head>
<body style="padding-top: 0;">
<!--searchform-->
<!--<div class="table_searchD">-->
<!--    <form id="personTableQueryForm">-->
<!--        <table border="0" cellpadding="0" cellspacing="6" width="100%">-->
<!--            <tr>-->
<!--                <td width="70" align="right">标题：</td><td width="180"><input name="title" type="text"  /></td>-->
<!--                &lt;!&ndash;                <td width="80" align="right">内容：</td><td width="250"><input name="content" type="text"  /></td>&ndash;&gt;-->
<!--                <td width="50" align="right">阅读状态：</td>-->
<!--                <td width="150">-->
<!--                    <select class="easyui-combobox" id="readStatus" data-options="editable:false,panelHeight:'auto'" name="readStatus" style="width: 100%; height: 32px;">-->
<!--                        <option value="">&#45;&#45;请选择&#45;&#45;</option>-->
<!--                        <option value="1">已读</option>-->
<!--                        <option value="0">未读</option>-->
<!--                    </select>-->
<!--                </td>-->
<!--                <td colspan="2"></td>-->
<!--            </tr>-->
<!--            <tr>-->
<!--                <td width="70" align="right">发送时间：</td>-->
<!--                <td width="120">-->
<!--                    <input class="easyui-datetimebox" name="startSendTime"-->
<!--                           data-options="showSeconds:true,editable:false" style="width:100%; height: 32px;">-->
<!--                    &lt;!&ndash;                    <span>-</span>&ndash;&gt;-->
<!--                </td>-->
<!--                <td width="5" align="center">-</td>-->
<!--                <td width="120">-->
<!--                    <input class="easyui-datetimebox" name="endSendTime"-->
<!--                           data-options="showSeconds:true,editable:false" style="width:100%; height: 32px;">-->
<!--                </td>-->
<!--                <td>-->
<!--                    <div class="w100">-->
<!--                        <a class="btn fl searchtable"><span>查询</span></a>-->
<!--                        <a class="btn ml10 reset"><span>重置</span></a>-->
<!--                    </div>-->
<!--                </td>-->
<!--            </tr>-->
<!--        </table>-->
<!--    </form>-->
<!--</div>-->

<!--table-->
<div class="personTable" style="margin-top: 20px;"><table id="personTable"></table></div>
</body>
</html>
