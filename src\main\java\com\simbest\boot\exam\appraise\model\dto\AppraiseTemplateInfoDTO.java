package com.simbest.boot.exam.appraise.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * AppraiseTemplateInfoDTO 外部调用获取模板信息封装
 *
 * <AUTHOR>
 * @since 2024/1/24 10:43
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppraiseTemplateInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("模板标题")
    private String title;

    @ApiModelProperty("模板code")
    private String code;


}
