<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>试卷评测-详情</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">
        var data;
        var length;
        var i=0;
        var judgeAnswerList=[];
        $(function () {
            getCurrent();

            ajaxgeneral({
                url:"action/examInfo/markingExam?publishUsername="+gps.publishUsername,
                contentType:"application/json; charset=utf-8",
                success:function(datas){

                    //console.log(datas)
                     data= datas.data;
                     length = data.length;
                     var answrList = data[i].answerList;
                     $("#questionName").val(data[i].questionName);//题目名字
                     $("#questionCode").val(data[i].questionCode);//题目编码
                     $("#publishTruename").val(gps.publishTruename);//答题人
                     $("#doneAnswer").val(data[i].doneAnswer);//答题内容
                     $("#questionScore").val( answrList[i].answerContent);//正确答案


                }
            });
            // 加载表单
            loadForm("examReviewDetailsTableAddForm");
            $("#goesA").show();
            //$("#goesB").show();
        });


        //表单校验
        window.fvalidate = function () {
            return $("#examReviewDetailsTableAddForm").form("validate");
        };

        //对表单中的元素进行校验，返回为0代表检验不成功。可以参考appManagementList.html
        window.getchoosedata = function () {
            formsubmit("examReviewDetailsTableAddForm","action/examQuestion/update");

            return {"data":'',"state":1};
        };

        //创建成功之后
        window.submitcallback = function () {
            top.dialogClose("examReviewDetails");
            //(top.window.insertQuestionshowDialogTopF || top).window.insertQuestionTableLoad();
        };

        function nextStep(){
            i++;
            if(i!=0&&i<length){
                $("#goesC").show();
            }
            if(i==0){
                $("#goesC").hide();
            }

            if(judgeAnswerList[i]==null){
                var map= {};
                for(var j=1;j<=10;j++){
                    $("input[id='judge"+j+"']:checked").each(function () {
                        if($(this).prop('checked')==true){
                            //console.log($(this));
                            var judge=($(this).val());
                            map[$("#questionCode").val()]=judge;
                            judgeAnswerList.push(map);
                        }
                    });
                }
            }else {
                var k= judgeAnswerList[i];
                //console.log(k);
                $("input[id='judge"+k+"']:checked").each(function () {

                    $(this).attr("checked","checked");

                });
            }

            //console.log(judgeAnswerList);

            if(i==length){
                getparent().mesShow("温馨提示","评测完成",2000,'red');
                $("#goesA").hide();
                $("#goesB").show();
            }else {
                for(var j=1;j<=10;j++){
                    $("input[id='judge"+j+"']:checked").each(function () {
                        if($(this).prop('checked')==true){
                            $(this).attr("checked",false);
                        }
                    });
                }
                var answrList = data[i].answerList;
                $("#questionName").val(data[i].questionName);//题目名字
                $("#questionCode").val(data[i].questionCode);//题目编码
                $("#doneAnswer").val(data[i].doneAnswer);//答题内容
                $("#questionScore").val( answrList[0].answerContent);//正确答案
            }

        }

        function lastStep(){
            i--;
            //$("#goesB").show();
            if(i!=0&&i<length){
                $("#goesA").show();
            }
            if(i==0){
                $("#goesC").hide();
            }

            if(judgeAnswerList[i]==null){
                var map= {};

                for(var j=1;j<=10;j++){
                    $("input[id='judge"+j+"']:checked").each(function () {
                        if($(this).prop('checked')==true){
                            //console.log($(this));
                            var judge=($(this).val());
                            map[$("#questionCode").val()]=judge;
                            judgeAnswerList.push(map);
                        }
                    });
                }
            }else {
                var k= judgeAnswerList[i];
                //console.log(k);
                $("input[id='judge"+k+"']:checked").each(function () {

                    $(this).attr("checked","checked");

                });
            }
            //console.log(judgeAnswerList);
            if(i==length){
                getparent().mesShow("温馨提示","评测完成",2000,'red');
                $("#goesA").hide();
                $("#goesB").show();
            }else {

                for(var j=1;j<=10;j++){
                    $("input[id='judge"+j+"']:checked").each(function () {
                        if($(this).prop('checked')==true){
                            $(this).attr("checked",false);
                        }
                    });
                }
                $("#questionName").val(data[i].questionName);//题目名字
                $("#questionCode").val(data[i].questionCode);//题目编码
                var answrList = data[i].answerList;
                $("#doneAnswer").val(data[i].doneAnswer);//答题内容
                $("#questionScore").val( answrList[0].answerContent);//正确答案
            }

        }

        function customSubmit(){
            ajaxgeneral({
                url:"action/examReview/saveExamReview",
                data:{
                    judgeAnswerList: judgeAnswerList,
                    username:gps.publishUsername,
                    truename:gps.publishTruename,
                    reviewUsername:web.currentUser.username,
                    reviewTruename:web.currentUser.truename
                },
                contentType:"application/json; charset=utf-8",
                success:function(datas){
                    var status = datas.status;
                    if(status==200){
                        getparent().mesShow("温馨提示","操作成功",2000);
                        top.dialogClose("examReviewDetails");
                    }else {
                        getparent().mesShow("温馨提示","操作失败", 2000,'red');
                    }


                }
            });

        }


    </script>
</head>
<body>
<!--scontentType获取数据时设置传参方式，不设置默认为kv  cmd-select获取数据命令配置，不配置自动从列表中取   cmd-insert新增  cmd-update修改-->
<div class="basic tab_table">
    <form id="examReviewDetailsTableAddForm" method="post" contentType="application/json; charset=utf-8"
          submitcallback="submitcallback()">
        <input id="id" name="id" type="hidden"/>
        <input id="questionCode" name="questionCode" type="hidden"/>
        <table border="0" cellpadding="0" cellspacing="10">

            <tr>
                <td width="10%" align="right">题目名称：</td>
                <td width="20%" length="100">
                    <input id="questionName" name="questionName" class="easyui-validatebox"  style="width:950px;height:32px;" />

                </td>

            </tr>
            <tr>
                <td width="10%" align="right">答题人：</td>
                <td width="20%" length="100">
                    <input id="publishTruename" name="publishTruename" class="easyui-validatebox"  style="width:950px;height:32px;" />

                </td>
            </tr>
        </table>
        <table border="0" cellpadding="0" cellspacing="10">
            <tr>
                <td width="10%" align="left">正确答案：</td>
                <td width="10%" align="left">回答答案：</td>

            </tr>

            <tr>
                <td width="20%" length="100">
                    <textarea id="questionScore" name="questionScore" class="easyui-validatebox"  style="width:500px;height:300px;resize:both;" ></textarea>

                </td>

                <td width="20%" length="100">
                    <textarea id="doneAnswer" name="doneAnswer" class="easyui-validatebox"  style="width:500px;height:300px;resize:both;" ></textarea>

                </td>
            </tr>

        </table>

        <table border="0" cellpadding="0" cellspacing="10" align="centre">

            <tr>
                <td width="100px" align="center"><input class='wauto' id='judge1' name='judge' noReset="true" value='1' type='radio'/><label for='judge1'>1分</label></td>
                <td width="100px" align="center"><input class='wauto' id='judge2' name='judge' noReset="true" value='2' type='radio'/><label for='judge2'>2分</label></td>
                <td width="100px" align="center"><input class='wauto' id='judge3' name='judge' noReset="true" value='3' type='radio'/><label for='judge3'>3分</label></td>
                <td width="100px" align="center"><input class='wauto' id='judge4' name='judge' noReset="true" value='4' type='radio'/><label for='judge4'>4分</label></td>
                <td width="100px" align="center"><input class='wauto' id='judge5' name='judge' noReset="true" value='5' type='radio'/><label for='judge5'>5分</label></td>
                <td width="100px" align="center"><input class='wauto' id='judge6' name='judge' noReset="true" value='6' type='radio'/><label for='judge6'>6分</label></td>
                <td width="100px" align="center"><input class='wauto' id='judge7' name='judge' noReset="true" value='7' type='radio'/><label for='judge7'>7分</label></td>
                <td width="100px" align="center"><input class='wauto' id='judge8' name='judge' noReset="true" value='8' type='radio'/><label for='judge8'>8分</label></td>
                <td width="100px" align="center"><input class='wauto' id='judge9' name='judge' noReset="true" value='9' type='radio'/><label for='judge9'>9分</label></td>
                <td width="100px" align="center"><input class='wauto' id='judge10' name='judge' noReset="true" value='10' type='radio'/><label for='judge10'>10分</label></td>
            </tr>

        </table>

    </form>
</div>
<div>

    <table align="right" border="0" cellpadding="0" cellspacing="10"  >
        <tr  >
            <td colspan="13">
                <div style="float: right" >
                    <a id="goesA" style="display: none" href="javascript:void(0);" class="btn"  onclick='nextStep()'><font>下道题</font></a>
                    <a id="goesC" style="display: none" href="javascript:void(0);" class="btn"  onclick='lastStep()'><font>上道题</font></a>
                    <a id="goesB" style="display: none" href="javascript:void(0);" class="btn"  onclick='customSubmit()'><font>评测完成</font></a>
                </div>
            </td>
        </tr>
    </table>
</div>
</body>
</html>
