package com.simbest.boot.exam.publicLottery.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.exam.util.BelongInfoTool;
import com.simbest.boot.security.IUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * Lottery
 *
 * <AUTHOR>
 * @since 2024/4/30 15:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "公开摇号人员表")
@Entity(name = "us_public_lottery_person")
@Table(uniqueConstraints = {
        @UniqueConstraint(name = "username_unique", columnNames = {"username"}),
})
public class PublicLotteryPerson extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UPLP") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "用户姓名")
    private String username;

    @Column(length = 40)
    @ApiModelProperty(value = "用户姓名")
    private String truename;

    @Column(length = 20)
    @ApiModelProperty(value = "电话")
    private String preferredMobile;

    public void belongInfoSet(@NotNull IUser user) {
        username = user.getUsername();
        truename = user.getTruename();
        preferredMobile = user.getPreferredMobile();
        BelongInfoTool.setBelongCompanyAndDepartment(this);
    }

}
