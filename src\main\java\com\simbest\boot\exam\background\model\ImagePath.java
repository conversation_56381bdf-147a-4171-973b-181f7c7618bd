package com.simbest.boot.exam.background.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.SystemModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @desc: 试卷背景图片实体
 * @date 2021/7/15  15:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_paper_background")
@ApiModel(value = "试卷属性")
public class ImagePath extends SystemModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "IMG") //主键前缀，此为可选项注解
    private String id;


    @Column(length = 100)
    @ApiModelProperty(value = "系统存在图片时的主键id")
    private String sysId;

    @Column(length = 40)
    @ApiModelProperty(value = "试卷编码")
    private String examAppCode;

    @Column(length = 200)
    @ApiModelProperty(value = "试卷背景图片路径")
    private String paperBackgroundPath;

    @Column(length = 100)
    @ApiModelProperty(value = "服务器保存的试卷背景图片id")
    private String imageId;

    @Column(length = 200)
    @ApiModelProperty(value = "试卷背景图片路径")
    private String mobileFilePath;

    @Column(length = 200)
    @ApiModelProperty(value = "预览图片路径")
    private String downLoadUrl;

    @ApiModelProperty(value = "当前使用图片")
    private Boolean useNow;

}
