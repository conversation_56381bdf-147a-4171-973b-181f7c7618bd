package com.simbest.boot.exam.study.web;

import cn.hutool.core.util.StrUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.study.model.ModifyReport;
import com.simbest.boot.exam.study.service.IModifyReportService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.util.security.LoginUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @ClassName: ModifyReportController
 * @description:
 * @author: ZHAOBO
 * @create: 2025-08-14 11:25
 */
@Slf4j
@RequestMapping("/action/modifyReport")
@RestController
public class ModifyReportController extends LogicController<ModifyReport, String> {

    private IModifyReportService service;

    public ModifyReportController(IModifyReportService service) {
        super(service);
        this.service = service;
    }

    @Autowired
    private LoginUtils loginUtils;

    @PostMapping(value = {"/saveModify" , "/saveModify/api" , "/saveModify/sso"})
    public JsonResponse saveModify(@RequestParam(defaultValue = "PC") String source ,
                                   @RequestParam(required = false ) String currentUserCode ,
                                   @RequestBody Map<String , Object>  map) {
        if (StrUtil.equals(source, Constants.SOURCE_M) && StrUtil.isNotEmpty(currentUserCode)) {
            loginUtils.manualLogin(currentUserCode , Constants.APP_CODE);
        }
        return service.saveModify(map);
    }

    @ApiOperation(value = "查询提交错题信息")
    @PostMapping(value = {"/queryModifyReport" , "/queryModifyReport/api" , "/queryModifyReport/sso"})
    public JsonResponse queryModifyReport(@RequestParam(defaultValue = "1") Integer page ,
                                          @RequestParam(defaultValue = "10") Integer size ,
                                          @RequestParam(defaultValue = "PC") String source ,
                                         @RequestParam(required = false ) String currentUserCode ,
                                         @RequestBody(required = false) ModifyReport report ) {
        if (StrUtil.equals(source, Constants.SOURCE_M) && StrUtil.isNotEmpty(currentUserCode)) {
            loginUtils.manualLogin(currentUserCode , Constants.APP_CODE);
        }
        return service.queryModifyReport(page , size , report);
    }

    @ApiOperation(value = "修改提交错题信息")
    @PostMapping(value = {"/updateStatus" , "/updateStatus/api" , "/updateStatus/sso"})
    public JsonResponse updateStatus(@RequestParam(defaultValue = "PC") String source ,
                                     @RequestParam(required = false ) String currentUserCode ,
                                     @RequestParam String id ,
                                     @RequestParam String status) {
        if (StrUtil.equals(source, Constants.SOURCE_M) && StrUtil.isNotEmpty(currentUserCode)) {
            loginUtils.manualLogin(currentUserCode , Constants.APP_CODE);
        }
        return service.updateStatus(id , status);
    }


    @ApiOperation(value = "修改提交错题信息")
    @PostMapping(value = {"/updateQuestionInfo" , "/updateQuestionInfo/api" , "/updateQuestionInfo/sso"})
    public JsonResponse updateQuestionInfo(@RequestParam(defaultValue = "PC") String source ,
                                     @RequestParam(required = false ) String currentUserCode ,
                                     @RequestBody Map<String , Object> map) {
        if (StrUtil.equals(source, Constants.SOURCE_M) && StrUtil.isNotEmpty(currentUserCode)) {
            loginUtils.manualLogin(currentUserCode , Constants.APP_CODE);
        }
        return service.updateQuestionInfo(map);
    }


    @ApiOperation(value = "查询题目信息")
    @PostMapping(value = {"/findQuestionById" , "/findQuestionById/api" , "/findQuestionById/sso"})
    public JsonResponse findQuestionById(@RequestParam(defaultValue = "PC") String source ,
                                         @RequestParam(required = false ) String currentUserCode ,
                                         @RequestParam String id) {
        if (StrUtil.equals(source, Constants.SOURCE_M) && StrUtil.isNotEmpty(currentUserCode)) {
            loginUtils.manualLogin(currentUserCode , Constants.APP_CODE);
        }
        return service.findQuestionById(id);
    }

}
