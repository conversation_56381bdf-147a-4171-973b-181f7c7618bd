<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>简报转发</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href=" http://************:8088/simbestuihttp://************:8088/simbestui/fonts/iconfont/iconfont.css?v=77778" th:href="@{ http://************:8088/simbestuihttp://************:8088/simbestui/fonts/iconfont/iconfont.css?v=${svn.revision}}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
    th:href="@{/fonts/iconfont/iconfont.css?v=${svn.revision}}" rel="stylesheet" />
    <link href=" http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
        th:href="@{ http://************:8088/simbestui/js/themes/default/easyui.css?v=${svn.revision}}" rel="stylesheet" />
    <link href=" http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
        th:href="@{ http://************:8088/simbestui/js/themes/icon.css?v=${svn.revision}}" rel="stylesheet" />
    <link href=" http://************:8088/simbestui/css/public.css?v=svn.revision"
        th:href="@{ http://************:8088/simbestui/css/public.css?v=${svn.revision}}" rel="stylesheet" />
    <link href=" http://************:8088/simbestui/css/pages.css?v=svn.revision"
        th:href="@{ http://************:8088/simbestui/css/pages.css?v=${svn.revision}}" rel="stylesheet" />
    <script src=" http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{ http://************:8088/simbestui/js/jquery.min.js?v=${svn.revision}}" type="text/javascript"></script>
    <script src=" http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
        th:src="@{ http://************:8088/simbestui/js/jquery.easyui.min.js?v=${svn.revision}}" type="text/javascript"></script>
    <script src=" http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
        th:src="@{ http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=${svn.revision}}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=${svn.revision}}" type="text/javascript"></script>
    <script src=" http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
        th:src="@{ http://************:8088/simbestui/js/jquery.zjsfile.js?v=${svn.revision}}" type="text/javascript"></script>
    <script src=" http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{ http://************:8088/simbestui/js/jquery.aduq.js?v=${svn.revision}}" type="text/javascript"></script>
    <script src=" http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
        th:src="@{ http://************:8088/simbestui/js/jquery.process.js?v=${svn.revision}}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=${svn.revision}}"  type="text/javascript"></script>
    <script type="text/javascript">
        getCurrent()
        $(function () {
            loadForm('applyForm')
            ajaxgeneral({
                url: "action/applyForm/getFormDetail?id="+gps.id,
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    if(gps.actionType == 'secrecyJoin'){
                        $('.doneRead').hide()
                        $('.transpond').hide()
                    }
                    
                    $('.titles').text(res.data.title)
                    $('#myid').val(res.data.id)
                    $('#taskId').val(res.data.taskId)
                    $('.contents').html(htmlDecode(res.data.content))
                }
            });
        });

        // 关闭按钮
        $(document).on('click','a.close',function(){
            if(gps.from){
                window.opener=null;
                window.open('','_self');
                window.close();
            }else{
                top.dialogClose('detail');
            }
        })

        //转发
        $(document).on("click", ".transpond", function () {
           top.orgCode = web.currentUser.belongOrgCode
           top.dialogP('html/message/infopage/addOtherPerson.html?hasChoosed=', window.name, '转发人员', 'OtherPerson', false, 700, 400);
        })

        function OtherPerson(data){
          var datas = data.data
          if(datas.length>0){
            $.messager.confirm('温馨提示', '请确认是否进行转发！', function(r){
              if (r){
                var arr = []
                for(var i in datas){
                  arr.push(datas[i].username)
                }
                var data = {
                  id: $('#taskId').val(),
                  userStr:arr.join(','),
                  sendType:'person'
                }
                ajaxgeneral({
                    url:"action/applyForm/forward?id="+ data.id + '&userStr=' + data.userStr + '&sendType=' + data.sendType,
                    success:function(datas){
                      if(gps.from){
                            window.opener=null;
                            window.open('','_self');
                            window.close();
                        }else{
                            top.dialogClose('detail');
                        }
                    }
                });
                }
            });

          }else{
            return top.mesShow("温馨提示！", "请至少选择一个转发人！", 2000, "red")
          }
        }
         //已阅
         $(document).on("click", ".doneRead", function () {
          $.messager.confirm('温馨提示', '请确认是否已阅！', function(r){
              if (r){
                  ajaxgeneral({
                      url: "action/task/done?taskId=" + $('#taskId').val(),
                      loading:true,
                      contentType: "application/json; charset=utf-8",
                      success: function (res) {
                          if(gps.from){
                              window.opener=null;
                              window.open('','_self');
                              window.close();
                          }else{
                              top.dialogClose('detail');
                          }
                      }
                  })
              }
          });
        })

    </script>
    <style type="text/css">
        /* 页面更新样式修改 */
        .formTable { width: 100%; margin-top: 0px; border-spacing: 0; table-layout: fixed; border-left: 1px solid #e8e8e8 !important; }
        .formTable>tbody>tr>td { border-right: 1px solid #e8e8e8; border-bottom: 1px solid #e8e8e8; font-size: 12px; text-align: left; height: 35px; }
        .formTable>tbody>tr:first-child>td{ height: 0px !important; }
        .formTable>tbody>tr>td input,
        .formTable>tbody>tr>td span,
        .formTable>tbody>tr>td textarea,
        .formTable>tbody>tr>td .textbox .textbox-text { border: none; font-size: 12px; }
        .formTable td.label { padding-left: 10px; background-color: #ddf1fe; }
        .titles { border-top: none; color: #d9001b; font-weight: 600; text-align: center !important; font-size: 18px !important; padding: 8px; }
        .pagetitle{ padding: 0px; margin: 20px 0 12px 0; font-weight: bold; font-size: 14px; }
        /* 背景色不要灰色 */
        .textAndInput_readonly,
        .textAndInput_readonly .validatebox-readonly { background: #fff; }
        /*textarea 字数统计*/
        .applicationContent,
        .applicationContent1,
        .applicationContent2{ padding-left: 7px; }
        /*上传附件位置调整*/
        .cselectorImageUL .btn{ position:relative; float: right; right: 0px; top: 0px; }
        .cselectorImageUL input{ right: 17px; }
        .over_point{ width: auto !important; }


        a.btn:hover, a.a_hover{
          background: #d9001b;
        }
        a.btn{
          background: #d9001b;
        }
        .pageInfoD{
          border-color: #d9001b;
        }
        .contents{
          padding: 20px 5%;
        }
    </style>
</head>
<body class="body_page">
<form id="applyForm" >
    <div class="pageInfo">
        <div class="pageInfoD">
            <a class="btn small fl mr15 doneRead"><i class="iconfont">&#xe688;</i>
              <font>已阅</font>
           </a>
            <a class="btn small fl mr15 transpond"><i class="iconfont">&#xe688;</i>
                <font>转发</font>
            </a>
            <a class="btn small fl mr15 close"><i class="iconfont">&#xe690;</i>
                <font>关闭</font>
            </a>
        </div>
    </div>
    <h4 class="titles" style="margin-top: 85px;"></h4>

    <input type="hidden" id="myid" name="myid" />
    <input type="hidden" id="taskId" name="taskId" />
    <div class="contents"></div>
   
</form>

</body>
</html>
