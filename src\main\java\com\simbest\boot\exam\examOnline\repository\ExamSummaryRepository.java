package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamAnnualQuarterInfo;
import com.simbest.boot.exam.examOnline.model.ExamSummary;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 用途：考试信息模块--考试汇总信息dao
 * 作者：gy
 * 时间: 2021-02-01 10:39
 */
public interface ExamSummaryRepository extends LogicRepository<ExamSummary, String> {

    //根据待办类型查询考试信息
    @Query(value = "select ue.* from us_exam_summary ue where ue.work_type=:workType ",
            nativeQuery = true)
    ExamSummary findByWorkType(@Param("workType") String workType);

    /**
     * 根据试卷编码查询考试配置信息
     * @param examAppCode   考试配置信息
     * @return
     */
    @Query(
            value = "select t.*, t.rowid from US_EXAM_SUMMARY t , us_exam_range_group g where t.enabled = 1 and t.exam_code = g.exam_code and g.exam_app_code = :examAppCode ",
            nativeQuery = true
    )
    List<ExamSummary> findByExamAppCode(@Param("examAppCode") String examAppCode);
}
