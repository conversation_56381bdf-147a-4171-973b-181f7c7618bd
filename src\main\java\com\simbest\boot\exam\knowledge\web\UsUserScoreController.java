/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.web;/**
 * Created by KZH on 2019/10/8 16:09.
 */

import com.google.common.collect.Lists;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.exam.knowledge.model.UsUserScore;
import com.simbest.boot.exam.knowledge.service.IUsUserScoreService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @create 2019-10-08 16:09
 * @desc
 **/
@Api(description = "待办", tags = {"待办相关处理控制器"})
@Slf4j
@RestController
@RequestMapping(value = "/action/usUserScore")
public class UsUserScoreController extends LogicController<UsUserScore, String> {

    private IUsUserScoreService usUserScoreService;

    @Autowired
    public UsUserScoreController(IUsUserScoreService usUserScoreService) {
        super(usUserScoreService);
        this.usUserScoreService = usUserScoreService;
    }

}
