package com.simbest.boot.exam.appraise.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * AppraiseItem
 *
 * <AUTHOR>
 * @since 2024/1/24 9:21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity(name = "us_appraise_item")
@ApiModel(value = "评价工单子项表")
public class AppraiseItem extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UAI") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 1000)
    @ApiModelProperty("评价内容 打分类型时为分数 填写类型时为填写内容")
    private String content;

    @Column(length = 40)
    @ApiModelProperty("评价模板id")
    private String templateId;

    @Column(length = 40)
    @ApiModelProperty("评价工单id")
    private String workId;


}
