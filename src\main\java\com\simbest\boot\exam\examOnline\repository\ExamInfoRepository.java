/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.repository;/**
 * Created by KZH on 2019/10/8 15:07.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:07
 * @desc 答题记录
 **/
public interface ExamInfoRepository extends LogicRepository<ExamInfo,String> {

    /**
     * 根据 publishUsername examCode examAppCode 获取最新的自动保存记录
     */
    @Query(value = "SELECT *\n" +
            "  FROM (SELECT t.*\n" +
            "          FROM us_exam_info t\n" +
            "         WHERE t.publish_username = :publishUsername\n" +
            "           AND t.exam_app_code = :examAppCode\n" +
            "           and t.exam_code = :examCode\n" +
            "           AND t.ENABLED = 1\n" +
            "         ORDER BY t.created_time DESC)\n" +
            " WHERE rownum = 1", nativeQuery = true)
    ExamInfo findAllByNew(@Param("publishUsername") String publishUsername, @Param("examCode") String examCode, @Param("examAppCode") String examAppCode);

    /**
     * 根据 publishUsername 获取答题记录
     * @param publishUsername
     * @return
     */
    @Query(value =  "select * from (select t.*  from us_exam_info t  where t.publish_Username=:publishUsername and t.exam_App_Code=:examAppCode and t.enabled=1 order by t.created_time desc ) where rownum=1 ",
            nativeQuery = true)
    ExamInfo findAllByPublishUsername(@Param("publishUsername")String publishUsername,@Param("examAppCode")String examAppCode);

    /**
     * 根据 publishUsername 获取答题记录
     * @param publishUsername
     * @return
     */
    @Query(value =  "select * from (select t.*  from us_exam_info t  where t.publish_Username=:publishUsername and t.is_finish_exam = 1 and t.exam_App_Code=:examAppCode and t.enabled=1 order by t.created_time desc ) where rownum=1 ",
            nativeQuery = true)
    ExamInfo findFinishedExamByUser(@Param("publishUsername")String publishUsername,@Param("examAppCode")String examAppCode);


    /**
     * 根据 publishUsername 获取未评测过的试卷
     * @param publishUsername
     * @return
     */
    @Query(value =  "select t.*  from us_exam_info t  where t.publish_Username=:publishUsername and t.is_Marking_Exam =0 and t.is_Finish_Exam=1 and t.enabled=1 ",
            nativeQuery = true)
    ExamInfo findAllByIsMarkingExam(@Param("publishUsername")String publishUsername);

    /**
     * 获取未评测过的试卷
     * @return
     */
    @Query(value =  "select t.*  from us_exam_info t  where t.is_Marking_Exam is null and t.enabled=1 ",
            countQuery = "select count(1)from us_exam_info t  where t.is_Marking_Exam is null and t.enabled=1 ",
            nativeQuery = true)
    Page<ExamInfo> findAllByIsMarkingExam(Pageable pageable);

    @Modifying
    @Query(
            value = "update us_exam_info t set t.is_Marking_Exam=1 where t.publish_Username=:publishUsername and t.enabled=1 and t.removed_time is null",
            nativeQuery = true
    )
    int accomplishMarking(@Param("publishUsername") String publishUsername);


    /**
     * 根据 publishUsername 获取答题记录
     * @param publishUsername
     * @return
     */
    @Query(value =  "select t.*  from us_exam_info t  where t.publish_Username=:publishUsername and t.exam_App_Code=:workType and t.is_finish_exam=1 and t.enabled=1 ",
            nativeQuery = true)
    ExamInfo findByExamInfo(@Param("publishUsername")String publishUsername,@Param("workType")String workType);

    /**
     * 统计参与考试人数
     */
    @Query(value = "select count(*) from us_exam_info t where t.exam_code = :examCode and t.IS_FINISH_EXAM = 1 and t.enabled = 1 and t.removed_time is null ", nativeQuery = true)
    Integer countActualExamNum(@Param("examCode") String examCode);
}
