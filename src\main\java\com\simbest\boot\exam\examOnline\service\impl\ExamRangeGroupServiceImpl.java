package com.simbest.boot.exam.examOnline.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.SystemService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.exam.examOnline.model.ExamRangeGroup;
import com.simbest.boot.exam.examOnline.repository.ExamRangeGroupRepository;
import com.simbest.boot.exam.examOnline.service.IExamRangeGroupService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleGroup;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @create 2021-04-23
 * @desc
 **/
@Slf4j
@Service
public class ExamRangeGroupServiceImpl extends SystemService<ExamRangeGroup, String> implements IExamRangeGroupService {
    private ExamRangeGroupRepository repository;

    @Autowired
    public ExamRangeGroupServiceImpl(ExamRangeGroupRepository repository) {
        super(repository);
        this.repository = repository;
    }

    private static final String USER_MAPPING2 = "/action/user/group/";
    private static final String SSO = "sso/";

    @Autowired
    private AppConfig config;
    //private String uumsAddress="http://localhost:8080/uums";
    @Autowired
    private RsaEncryptor encryptor;

    @Autowired
    public ExamRangeUserInfoServiceImpl examRangeUserInfoService;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;


    /**
     * 保存群组信息到考试系统的数据库中
     *
     * @param examRangeGroup
     * @return
     */
    @Override
    public ExamRangeGroup saveExamRangeGroup(ExamRangeGroup examRangeGroup) {
            Assert.notNull(examRangeGroup.getGroupId(), "群组编号不能为空");
            Assert.notNull(examRangeGroup.getGroupName(), "群组名称不能为空");
            Assert.notNull(examRangeGroup.getExamAppCode(), "试卷编号不能为空");
            Assert.notNull(examRangeGroup.getExamName(), "试卷名称不能为空");
            return insert(examRangeGroup);
    }

    /**
     * @desc 校验当前群组编号是否可用
     * <AUTHOR>
     */
    @Override
    public boolean isHaveCode(String groupId) {
        Specification<ExamRangeGroup> spec = Specifications.<ExamRangeGroup>and()
                .eq("groupId", groupId)
                .build();
        List<ExamRangeGroup> allExamRangeGroupNoPage = (List<ExamRangeGroup>) this.findAllNoPage(spec);
        //如果已存在此群组编码则返回false
        if (allExamRangeGroupNoPage.size()==0){
            return true;
        }
        return false;
    }

    /**
      * @desc 新增群组信息 UUMS
      * <AUTHOR>
      */
    @Override
    public SimpleGroup saveUumsSysGroup(ExamRangeGroup examRangeGroup) {
        examRangeGroup.setExamAppCode(examRangeGroup.getExamCode());
        examRangeGroup.setExamName(examRangeGroup.getPaperName());
        //根据用户新增的群组编号判断是否需要拼接约束前缀
        SimpleGroup simpleGroup = new SimpleGroup();
        String groupId = examRangeGroup.getGroupId();
        String newGroupId="";
        if (StringUtils.isNotEmpty(groupId) && !groupId.contains("EXAM")){
            newGroupId="EXAM_"+groupId;
            simpleGroup.setSid(newGroupId);
        }else {
            simpleGroup.setSid(groupId);
        }
        simpleGroup.setName(examRangeGroup.getGroupName());
        //群组与考试以及试卷的关联需要保存到考试管理系统中
        if (StringUtils.isNotEmpty(groupId) && !groupId.contains("EXAM")){
            newGroupId="EXAM_"+groupId;
            examRangeGroup.setGroupId(newGroupId);
        }else {
            examRangeGroup.setGroupId(groupId);
        }
        ExamRangeGroup examRangeGroup1 = saveExamRangeGroup(examRangeGroup);
        Assert.notNull(examRangeGroup1, "创建失败");
        return uumsSysGroupApi.addGroup(Constants.APP_CODE, simpleGroup);
    }

    /**
     * @desc 修改群组信息 UUMS
     * <AUTHOR>
     */
    @Override
    public SimpleGroup updateUumsSysGroup(ExamRangeGroup examRangeGroup) {
        //根据用户修改的群组编号判断是否需要拼接约束前缀
       /* SimpleGroup simpleGroup = new SimpleGroup();
        String groupId = examRangeGroup.getGroupId();
        String newGroupId="";
        if (StringUtils.isNotEmpty(groupId) && !groupId.contains("EXAM")){
            newGroupId="EXAM_"+groupId;
            simpleGroup.setSid(newGroupId);
            examRangeGroup.setGroupId(newGroupId);
        }else {
            simpleGroup.setSid(groupId);
        }
        simpleGroup.setName(examRangeGroup.getGroupName());
        */
      /*  //先查询考试系统中群组信息未修改时的群组id
        ExamRangeGroup rangeGroup = this.findById(examRangeGroup.getId());
        String old= rangeGroup.getGroupId();
        //根据groupId去查uums中该数据的主键id
        Map<String, Object> sysGroupMap = new HashMap<>();
        sysGroupMap.put("sid",old);
        JsonResponse all = uumsSysGroupApi.findAll(1, 10, Sort.Direction.DESC.toString(), "createdTime", Constants.APP_CODE, sysGroupMap);
        Map data = (Map) all.getData();
        List content = (List) data.get("content");
        Map map = (Map) content.get(0);
        String id = (String) map.get("id");
        simpleGroup.setId(id);
        SimpleGroup simpleGroup1 = uumsSysGroupApi.updateGroup(Constants.APP_CODE, simpleGroup);*/
        //uums中的sid不适合修改操作，所以不提供修改群组编码的操作
        ExamRangeGroup updateRangeGroup = this.update(examRangeGroup);
        Assert.notNull(updateRangeGroup, "修改失败");
        return null;
    }

    /**
      * @desc 根据群组编号删除群组信息
      * <AUTHOR>
      */
    @Override
    public Boolean delUumsSysGroup(String groupId) {
        SimpleGroup simpleGroup = new SimpleGroup();
        simpleGroup.setSid(groupId);
        return uumsSysGroupApi.delGroup(Constants.APP_CODE, simpleGroup);
    }

    /**
     * 根据考试编号查询参考人员
     *
     * @param examAppCode
     * @return
     */
    @Override
    public List<Map<String, Object>> findUserByExamAppCode(String examAppCode) {
        List<Map<String, Object>> userByExamAppCode = repository.findUserByExamAppCode(examAppCode);
        return userByExamAppCode;
    }

    /**
     * 根据examCode查询群组名称
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> findExamDetailAndGroup(String examCode) {
        return repository.findExamDetailAndGroup(examCode);

    }

    /**
     * 根据当前登陆人获取其参加考试的试卷编号
     *
     * @param userName
     * @return
     */
    @Override
    public String findExamAppCodeByCurrentUser(String userName) {
        return repository.findExamAppCodeByCurrentUser(userName);
    }


    /**
     * 分页查询群组信息可根据分组名称、分组编号进行模糊查询
     *
     * @param
     * @return
     */
    @Override
    public Page<ExamRangeGroup> findExamGroupInfoByGroupName(int page,int size, ExamRangeGroup o) {
        IUser currentUser = SecurityUtils.getCurrentUser();
        Pageable pageable = this.getPageable(page, size);
            Page<ExamRangeGroup> allExamGroupInfo = null;
            // 这里对【梁洁】用户特殊处理-》该用户只能操作洛阳分公司满意度参评人员群组
            if (Constants.USERNAME_MANAGER1.equals(currentUser.getUsername())) {
                Specification<ExamRangeGroup> build = Specifications.<ExamRangeGroup>and()
                        .like(null != o.getGroupName(), "groupName", "%" + o.getGroupName() + "%")
                        .like(null != o.getGroupId(), "groupId", "%" + o.getGroupId() + "%")
                        .predicate(Specifications.<ExamRangeGroup>or()
                                .eq("examCode", Constants.EXAM_CODE_OFFICE_LY)
                                .eq("examCode", Constants.EXAM_CODE_BRANCH_LY)
                                .build())
                        .build();
                allExamGroupInfo = repository.findAll(build, pageable);
            } else {
                Specification<ExamRangeGroup> spec = Specifications.<ExamRangeGroup>and()
                        .like(null != o.getGroupName(), "groupName", "%" + o.getGroupName() + "%")
                        .like(null != o.getGroupId(), "groupId", "%" + o.getGroupId() + "%")
                        .eq("examCode", o.getExamCode())
                        .build();
                allExamGroupInfo = repository.findAll(spec, pageable);
            }
            String examGroup=o.getExamCode();
            //如果考试为洛阳满意度考试则统计群组人员数从本考试系统的群组人员关联表中统计
            if (examGroup.equals(Constants.EXAM_CODE_BRANCH_LY) || examGroup.equals(Constants.EXAM_CODE_OFFICE_LY)){
                for (ExamRangeGroup examRangeGroup : allExamGroupInfo) {
                    String groupId1 = examRangeGroup.getGroupId();
                    Integer CountGroupUser = examRangeUserInfoService.countGroupUser(groupId1);
                    examRangeGroup.setCountGroupUser(CountGroupUser);
                }
            }else {
                //如果是其它的考试则从uums中的sys_user_group中统计群组人员数
                for (ExamRangeGroup examRangeGroup : allExamGroupInfo) {
                    String groupId1 = examRangeGroup.getGroupId();
                    Map<String, Object> hashMap = new HashMap<>();
                    hashMap.put("groupId",groupId1);
                    hashMap.put("enabled",true);
                    int examGroupInfoByUUMS = findExamGroupInfoByUUMS(page, size, hashMap);
                    examRangeGroup.setCountGroupUser(examGroupInfoByUUMS);
                }
            }
            return allExamGroupInfo;
    }

    /**
      * @desc 从UUMS中统计对应群组中的人员数
      * <AUTHOR>
      */
    @Override
    public int findExamGroupInfoByUUMS(int page,int size, Map<String,Object> userGroupMap) {
        String loginUser = SecurityUtils.getCurrentUserName();
        log.debug("Http remote request user by loginUser: {}", loginUser);
        String json0= JacksonUtils.obj2json(userGroupMap);
        String username1=encryptor.encrypt(loginUser);
        String username2=username1.replace("+","%2B");
        JsonResponse response= HttpClient.textBody(config.getUumsAddress() + USER_MAPPING2 +SSO +"findAllNoPage"+"?loginuser="+username2+"&appcode="+Constants.APP_CODE)
                .json( json0 )
                .asBean(JsonResponse.class );
        //获取该群组的人数
        List data = (List) response.getData();
        return data.size();
    }

    /**
      * @desc 查询所有群组的考试信息
      * <AUTHOR>
      */
    @Override
    public List<ExamRangeGroup> listExamRangeGroup() {
        Specification<ExamRangeGroup> spec = Specifications.<ExamRangeGroup>and()
                .like("groupId", "%" + "EXAM" + "%")
                .build();
        List<ExamRangeGroup> allExamRangeGroupNoPage = (List<ExamRangeGroup>) this.findAllNoPage(spec);
        return allExamRangeGroupNoPage;
    }

    /**
      * @desc 根据考试编码查询试卷编码
      * <AUTHOR>
      */
    @Override
    public Iterable<ExamRangeGroup> findExamRangeGroupByExamCode(String examCode) {
        Specification<ExamRangeGroup> sepc = Specifications.<ExamRangeGroup>and()
                .eq("examCode", examCode)
                .build();

       return this.findAllNoPage(sepc);
    }

    /**
      * @desc 根据考试名称查询试卷编码
      * <AUTHOR>
      */
    @Override
    public ExamRangeGroup findByExamName(String examName) {
        //构建查询条件
        Specification<ExamRangeGroup> spec = Specifications.<ExamRangeGroup>and()
                .eq("examName", examName)
                .build();
        return   this.findOne(spec);
    }

    /**
     * 根据考试编码获取试卷编码
     *
     * @param examCode
     * @return
     */
    @Override
    public ExamRangeGroup findByExamCode(String examCode) {
        Specification<ExamRangeGroup> build = Specifications.<ExamRangeGroup>and()
                .eq("examCode", examCode)
                .build();
        return this.findOne(build);
    }
}
