package com.simbest.boot.exam.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class RoleDeleteEvent extends ApplicationEvent {

    //注册用户对象 用户id以“,”分隔
    private String Ids;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public RoleDeleteEvent(Object source, String Ids) {
        super(source);
        this.Ids=Ids;
    }
}
