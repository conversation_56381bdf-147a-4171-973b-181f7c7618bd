package com.simbest.boot.exam.examOnline.constants;


/**
 * 用途：考试信息模块--常量类
 * 作者：gy
 * 时间: 2021-02-03 9:57
 */
public class ExamConstants {
    /**
     * 考试编码
     */
    public static final String EXAM_CODE_XUN_CHA = "xczg";//巡察整改满意度测评
    public static final String EXAM_CODE_SHANG_QIU = "xczg-shangqiu";//商丘分公司巡察整改满意度测评
    public static final String EXAM_CODE_HE_BI = "xczg-hebi";//鹤壁分公司巡察整改满意度测评
    public static final String EXAM_CODE_AN_YANG = "xczg-anyang";//安阳分公司巡察整改满意度测评
    public static final String EXAM_CODE_KAI_FENG = "xczg-kaifeng";//开封分公司巡察整改满意度测评

    /**
     * 特殊考试范围公司编码
     */
    public static final String EXAM_RANGE_SHANG_QIU = "4772190764623477349";//商丘分公司编码
    public static final String EXAM_RANGE_HE_BI = "4772356884662698724";//鹤壁分公司编码
    public static final String EXAM_RANGE_AN_YANG = "4772319337553908874";//安阳分公司编码
    public static final String EXAM_RANGE_KAI_FENG = "4772385069455370245";//开封分公司编码

    /**
     * 模块名称
     */
    public static final String EXAM_NAME = "问卷调查";

    /**
     * 巡查整改excel报表常量
     */
    public static final String EXAM_TITLE = "2020年内部巡察后评估线上调查问卷结果";// excel导出标题
    public static final String EXAM_COLUMN_COMPANY = "单位";// excel导出表头
    public static final String EXAM_COLUMN_JOIN_NUM = "应参与人员数量";// excel导出表头
    public static final String EXAM_COLUMN_ACTUAL_NUM = "实际参与人员数量";// excel导出表头
    public static final String EXAM_COLUMN_COMPANY_SATISFACTION = "四个分公司问卷满意度：（成效显著+比较显著+成效一般+不了解）/参与人员总数";// excel导出表头
    public static final String EXAM_COLUMN_LEADER_SATISFACTION = "领导班子调查问卷满意度：（成效显著+比较显著+成效一般+不了解）/参与人员总数";// excel导出表头




}
