package com.simbest.boot.exam.examOnline.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: 洛阳市机关部门互评各维度满意度平均分统计结果的导出
 * @projectName exam
 * @description:
 * @date 2021/6/10  13:58
 */
@Data
public class ExamDimensionAvgJgExcel {
    @ExcelVOAttribute(name = "维度", column = "A")
    @Excel(name =  "维度",width = 50)
    private String department;


    @ExcelVOAttribute(name = "协作态度", column = "B")
    @Excel(name =  "协作态度",width = 50)
    private BigDecimal PL;

    @ExcelVOAttribute(name = "责任担当", column = "C")
    @Excel(name =  "责任担当",width = 50)
    private BigDecimal PS;

    @ExcelVOAttribute(name = "沟通协调", column = "D")
    @Excel(name =  "沟通协调",width = 50)
    private BigDecimal TF;

    @ExcelVOAttribute(name = "协作质量", column = "E")
    @Excel(name =  "协作质量",width = 50)
    private BigDecimal WE;

    @ExcelVOAttribute(name = "协作效率", column = "F")
    @Excel(name =  "协作效率",width = 50)
    private BigDecimal CA;

    @ExcelVOAttribute(name = "工作对接", column = "G")
    @Excel(name =  "工作对接",width = 50)
    private BigDecimal SC;

    @ExcelVOAttribute(name = "部门成绩", column = "H")
    @Excel(name =  "部门成绩",width = 50)
    private BigDecimal departmentScore;
}
