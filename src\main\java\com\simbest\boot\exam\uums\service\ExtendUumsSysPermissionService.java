package com.simbest.boot.exam.uums.service;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.security.SimplePermission;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc: 考试管理系统菜单权限控制service接口
 * @date 2021/7/3  9:55
 */
public interface ExtendUumsSysPermissionService {

   /**
     * @desc  根据应用查询其下的所有权限不分页
     * <AUTHOR>
     */
    List<SimplePermission> findAllPermissionNoPage();

    /**
      * @desc
      * 用于主数据管理后台获取角色或用户修改应用权限的列表
      * 先根据应用查所有权限列表
      * 再查角色或用户已关联的角色
      * <AUTHOR>
      */
    JsonResponse findAllNoPageForKey( String key);


    /**
      * @desc  修改角色权限信息，先删除角色所有权限，再重新添加
      * <AUTHOR>
      */
    JsonResponse updateListByRoleId(String permissionIds, String roleId);


}
