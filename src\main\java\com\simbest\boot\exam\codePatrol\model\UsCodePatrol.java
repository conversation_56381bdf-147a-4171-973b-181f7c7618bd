package com.simbest.boot.exam.codePatrol.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;



@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity(name = "us_code_patrol")
@ApiModel(value = "码上巡实体类")
public class UsCodePatrol extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "UAI") //主键前缀，此为可选项注解
    private String id;


    @Column(length = 40)
    @ApiModelProperty("被巡查对象")
    private String patrolObject;


    @Column(length = 40)
    @ApiModelProperty("被巡查对象code")
    private String patrolObjectCode;

    @Column(length = 500)
    @ApiModelProperty("描述问题")
    private String problemDescription;

    @Column(length = 40)
    @ApiModelProperty("反应人")
    private String reflectingPeople;

    @Column(length = 40)
    @ApiModelProperty("联系方式")
    private String contactInformation;

    @Column(length = 40)
    @ApiModelProperty("试卷类型")
    private String paperType;


    @Column(length = 40)
    @ApiModelProperty("邮箱附件密码")
    private String password;

    @Column(length = 40)
    @ApiModelProperty("短信邮箱关联code")
    private String code;



    @Transient
    private List<SysFile> files;//佐证材料




}
