package com.simbest.boot.exam.examStatistics.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParticipationRatioVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "公司")
    private String company;

    @Excel(name =  "参加人数")
    private Integer participation;

    @Excel(name =  "未参加人数")
    private Integer noParticipation;

    @Excel(name =  "参加人数百分比")
    private String participationRatio;

    @Excel(name =  "未参加人数百分比")
    private String noParticipationRatio;

}
