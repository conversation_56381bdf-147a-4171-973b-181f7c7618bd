package com.simbest.boot.exam.test.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Exam
 *
 * <AUTHOR>
 * @since 2023/10/13 15:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Exam {
    /**
     * 考试名称 明纪守法_复赛_法律组
     */
    private String exam_name;
    /**
     * 考试题库编码 2023mjsf_fs_flz
     */
    private String question_bank_code;
    /**
     * 考试时间(分钟) 30
     */
    private String set_time;
    /**
     * 考试出题类型 original 全题库
     */
    private String topic_style;
    /**
     * 考试开始时间 2023-09-19 00:00:00
     */
    private String exam_start_time;
    /**
     * 考试结束时间 2023-10-19 00:00:00
     */
    private String exam_end_time;
    /**
     * 工单类型 H 通用
     */
    private String work_type;
    /**
     * 权重(必须不同) 95
     */
    private Integer weight_value;
    /**
     * 考试人员
     */
    private List<String> users = new ArrayList<>();
    /**
     * 原考试题库编码
     */
    private String old_question_bank_code;
}
