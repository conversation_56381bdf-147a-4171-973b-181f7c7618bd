<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>添加人员</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <link href="../../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .search{width:50px;height: 30px;line-height: 30px;border-radius: 2px;background: #E9A238;text-align: center;color:#fff;}
    </style>
    <script type="text/javascript">
        window.fvalidate=function(){
            return formValidate("infoTableAddForm");
        };
        window.initsystem=function(){
            loadForm("infoTableAddForm");
        };
        window.bindval=function(data){
            formval(data,"infoTableAddForm");
        };
        window.formReady=function(data){
            formReadonly("infoTableAddForm");
        };
        $(function (){
            loadForm("infoTableAddForm");
            $(".receivePhoneNum").hide();
            //领导层手机号隐藏
            $("#preferredMobileleader").val('***********');
            $(".leader").hide();
            if(gps.type=='update'){
                formval(gps,"infoTableAddForm");
                $("#username").attr('disabled','disabled');
                $(".updateHide").hide();
                ajaxgeneral({
                    url: "exuums/sys/userinfo/findUserByUsernameFromView?username="+gps.username,
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        if(res.data){
                            formval(res.data,"infoTableAddForm");
                            if(!gps.receivePhoneNum||gps.receivePhoneNum=='undefined'){
                                $("#receivePhoneNum").val(res.data.preferredMobile);
                            }
                            if(res.data.belongDepartmentCode=='4772338661636601428'){
                                $(".leader").show();
                                $(".noleader").hide();
                            }else{
                                $(".receivePhoneNum").show();
                            }
                        }else {
                            top.mesAlert("提示", "查询用户账号【"+username+"】信息为空", 'warning');
                        }
                    }
                });
            }
            // 输入OA账号查询事件
            $(".search").on("click", function () {
                var username=$('#username').val();
                if (username==""){
                    top.mesAlert("提示", "请输入用户账号再点击查询按钮", 'warning');
                }else {
                    ajaxgeneral({
                        url: "exuums/sys/userinfo/findUserByUsernameFromView?username="+username,
                        contentType: "application/json; charset=utf-8",
                        success: function (res) {
                            if(res.data){
                                formval(res.data,"infoTableAddForm");
                                $("#receivePhoneNum").val(res.data.preferredMobile);
                                if(res.data.belongDepartmentCode=='4772338661636601428'){
                                    $(".leader").show();
                                    $(".noleader").hide();
                                    $(".receivePhoneNum").hide();
                                }else{
                                    $(".receivePhoneNum").show();
                                    $(".leader").hide();
                                    $(".noleader").show();
                                }
                            }else {
                                top.mesAlert("提示", "查询用户账号【"+username+"】信息为空", 'warning');
                            }
                        }
                    })
                }
            });
        })

        window.getchoosedata = function () {
            // 校验数据是否合法
            var username = $('#username').val();
            var truename = $('#truename').val();
            var preferredMobile = $('#preferredMobile').val();
            var orgDisplayName = $('#orgDisplayName').val();
            var belongDepartmentCode = $('#belongDepartmentCode').val();
            var receivePhoneNum = $("#receivePhoneNum").val();
            if (username==null || username==""){
                top.mesAlert("提示", "用户账号不能为空", 'warning');
                return {"state":0};
            }
            if (preferredMobile==null || preferredMobile==""){
                top.mesAlert("提示", "用户手机号不能为空", 'warning');
                return {"state":0};
            }
            var data = {
                username:username,
                truename:truename,
                preferredMobile:preferredMobile,
                orgDisplayName:orgDisplayName,
                belongDepartmentCode:belongDepartmentCode,
                parentName:'临时添加',
                receivePhoneNum:receivePhoneNum,
                type:gps.type,
                displayOrder:gps.displayOrder
            };
            var datas = [data];
            return {"data":datas,"state":1};
        };
    </script>
</head>
<body class="body_page">
<form id="infoTableAddForm">
    <input id="id" name="id" type="hidden" />
    <table border="0" cellpadding="0" cellspacing="10" width="100%">
        <tr>
            <td width="120" align="right"><font class="col_r">*</font>用户账号：</td>
            <td width="300">
                <input class="easyui-validatebox username" name="username" id="username" required/>
            </td>
            <td class="updateHide"><a class="btn small a_warning search"  title="查询"><i class="iconfont">&#xe634;</i></a></td>
        </tr>
        <tr>
            <td width="120" align="right">用户姓名：</td>
            <td width="300">
                <input class="easyui-validatebox truename textAndInput_readonly" disabled="disabled" name="truename" id="truename"/>
            </td>
            <td></td>
        </tr>
        <tr class="leader">
            <td width="120" align="right">用户手机号：</td>
            <td width="300">
                <input class="easyui-validatebox preferredMobileleader textAndInput_readonly" disabled="disabled" name="preferredMobileleader" id="preferredMobileleader"/>
            </td>
            <td></td>
        </tr>
        <tr class="noleader">
            <td width="120" align="right">用户手机号：</td>
            <td width="300">
                <input class="easyui-validatebox preferredMobile textAndInput_readonly" disabled="disabled" name="preferredMobile" id="preferredMobile"/>
            </td>
            <td></td>
        </tr>
        <tr>
            <td width="120" align="right">所属组织：</td>
            <td width="300">
                <input class="easyui-validatebox orgDisplayName textAndInput_readonly" disabled="disabled"
                       name="orgDisplayName" id="orgDisplayName"/>
            </td>
            <td></td>
        </tr>
        <tr hidden>
            <td width="120" align="right">所属组织：</td>
            <td width="300">
                <input class="easyui-validatebox belongDepartmentCode textAndInput_readonly" disabled="disabled"
                       name="belongDepartmentCode" id="belongDepartmentCode"/>
            </td>
            <td></td>
        </tr>
        <tr class="receivePhoneNum">
            <td width="120" align="right">接收手机号：</td>
            <td width="300">
                <input class="easyui-validatebox receivePhoneNum" name="receivePhoneNum" id="receivePhoneNum" validType="phone"/>
            </td>
            <td></td>
        </tr>
    </table>
</form>
</body>
</html>