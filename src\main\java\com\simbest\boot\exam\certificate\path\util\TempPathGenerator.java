/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate.path.util;

import com.google.common.collect.Lists;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.exam.certificate.ConstantsOffice;
import com.simbest.boot.exam.certificate.path.model.TempFilePath;

import java.util.List;
import java.util.UUID;

/**
 * <strong>Title : TempPathGenerator</strong><br>
 * <strong>Description : 生成临时文件 </strong><br>
 * <strong>Create on : 2020/11/10</strong><br>
 * <strong>Modify on : 2020/11/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR> kou<PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public class TempPathGenerator {
    //public static String tempFilePath = "/src/main/resources/tmp/";

    public static TempFilePath generateTempPath(String fileExt)  {
        // todo 替换为username
        try {
            //System.getProperty("user.dir") +
            String path = ConstantsOffice.TEMP_FILE_PATH + fileExt + "/" + System.currentTimeMillis();
            String name = getId() + "." + fileExt;
            return new TempFilePath(path, name);
        }catch (Exception e){
            Exceptions.printException( e );
        }
        return  null;

    }

    public static List<TempFilePath> generateTempPaths(String... fileExts)   {
        List<TempFilePath> tempFilePaths = Lists.newArrayList();

        for (String fileExt : fileExts) {
            try {
                tempFilePaths.add(generateTempPath(fileExt));
            }catch (Exception e){
                Exceptions.printException( e );
            }

        }

        return tempFilePaths;
    }

    public static String getId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }
}
