/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.web;/**
 * Created by KZH on 2019/10/8 16:09.
 */

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.knowledge.service.IUsChallengeQuestionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:09
 * @desc
 **/
@Api(description = "每日挑战", tags = {"每日挑战控制器"})
@Slf4j
@RestController
@RequestMapping(value = "/action/usChallengeQuestions")
public class UsChallengeQuestionsController {

    @Autowired
    private IUsChallengeQuestionsService usChallengeQuestionsService;

    @ApiOperation(value = "获取答题题目列表接口", notes = "获取答题题目列表接口")
    @PostMapping(value = {"/getAnswersList", "/sso/getAnswersList", "/api/getAnswersList"})
    public JsonResponse getAnswersList(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                       @RequestParam(value = "currentUserCode",required = false) String currentUserCode ) {
        //return usChallengeQuestionsService.getAnswersList(source,currentUserCode);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }

    @ApiOperation(value = "温馨提示（答题次数剩余次数）接口", notes = "温馨提示（答题次数剩余次数）接口")
    @PostMapping(value = {"/answersRecordCheck", "/sso/answersRecordCheck", "/api/answersRecordCheck"})
    public JsonResponse answersRecordCheck(@RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                           @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
//        return usChallengeQuestionsService.answersRecordCheck(source,currentUserCode);
         return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }

    @ApiOperation(value = "开始答题接口", notes = "开始答题接口")
    @PostMapping(value = {"/saveRecord", "/sso/saveRecord", "/api/saveRecord"})
    public JsonResponse saveRecord(@RequestParam(value = "workType" ,required = true) String workType,
                                   @RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                   @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
       // return usChallengeQuestionsService.saveRecord(workType,source,currentUserCode);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }

    @ApiOperation(value = "提交答案接口", notes = "提交答案接口")
    @PostMapping(value = {"/saveAnswer", "/sso/saveAnswer", "/api/saveAnswer"})
    public JsonResponse saveQuestion(@RequestBody Map<String,Object> requestParam,
                                     @RequestParam(value = "source" ,defaultValue = "PC",required = false) String source,
                                     @RequestParam(value = "currentUserCode",required = false) String currentUserCode) {
       // return usChallengeQuestionsService.saveAnswer(requestParam,source,currentUserCode);
        return JsonResponse.fail("本次知识竞赛已结束，感谢您的参与");
    }

}
