/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.certificate.doc.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <strong>Title : CertificateField</strong><br>
 * <strong>Description : 模板中字段 </strong><br>
 * <strong>Create on : 2020/11/10</strong><br>
 * <strong>Modify on : 2020/11/10</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR> kou<PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Data
public class CertificateField {

    private String key;

    private String content;

    private Integer fontSize;

    private String fontFamily;

    private Boolean isBold = false;

    public CertificateField() {
        this(null, null);
    }

    public CertificateField(String key, String content) {
        this(key, content, null, null);
    }

    public CertificateField(String key, String content, Integer fontSize) {
        this(key, content, fontSize, null);
    }

    public CertificateField(String key, String content, Integer fontSize, String fontFamily) {
        this(key, content, fontSize, fontFamily, false);
    }

    public CertificateField(String key, String content, Integer fontSize, String fontFamily, Boolean isBold) {
        super();
        this.key = key;
        this.content = (StringUtils.isEmpty(content) ? "" : content);
        this.fontSize = (fontSize == null ? 24 : fontSize);
        this.fontFamily = (StringUtils.isEmpty(fontFamily) ? "" : fontFamily);
        this.isBold = (isBold == null ? false : isBold);
    }


}
