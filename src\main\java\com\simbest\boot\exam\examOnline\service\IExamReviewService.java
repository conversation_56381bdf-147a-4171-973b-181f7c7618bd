/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service;/**
 * Created by KZH on 2019/10/17 16:47.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamReview;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-17 16:47
 * @desc 评测记录
 **/
public interface IExamReviewService extends ILogicService<ExamReview,String> {

    /**
     * 保存评测记录
     * @param sumMap
     * @return
     */
    JsonResponse saveExamReview(Map<String,Object> sumMap);
}
