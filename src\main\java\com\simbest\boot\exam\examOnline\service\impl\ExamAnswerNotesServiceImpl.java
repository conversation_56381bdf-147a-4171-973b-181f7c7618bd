package com.simbest.boot.exam.examOnline.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.examOnline.dto.AnswerStatusDto;
import com.simbest.boot.exam.examOnline.model.ExamAnnualQuarterInfo;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;
import com.simbest.boot.exam.examOnline.model.ExamSummary;
import com.simbest.boot.exam.examOnline.repository.ExamAnswerNotesRepository;
import com.simbest.boot.exam.examOnline.repository.ExamQuestionRepository;
import com.simbest.boot.exam.examOnline.repository.ExamQuestionUserRepository;
import com.simbest.boot.exam.examOnline.service.IExamAnnualQuarterInfoService;
import com.simbest.boot.exam.examOnline.service.IExamAnswerNotesService;
import com.simbest.boot.exam.examOnline.service.IExamSummaryService;
import com.simbest.boot.exam.examOnline.service.ISummaryStatisticsService;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.simbest.boot.exam.util.Constants.*;

/**
 * <AUTHOR>
 * @create 2021-04-25
 * @desc 单选问卷调查答题记录与统计service实现类
 **/
@Slf4j
@Service
public class ExamAnswerNotesServiceImpl extends LogicService<ExamQuestionUser, String> implements IExamAnswerNotesService {


    private ExamAnswerNotesRepository examAnswerNotesRepository;

    @Autowired
    private CustomDynamicWhere dynamicWhere;

    @Autowired
    public ExamAnswerNotesServiceImpl(ExamAnswerNotesRepository repository) {
        super(repository);
        this.examAnswerNotesRepository = repository;
    }

    @Autowired
    private ExamQuestionUserRepository examQuestionUserRepository;

    @Autowired
    private ExamQuestionRepository examQuestionRepository;

    @Autowired
    private IExamSummaryService examSummaryService;

    @Autowired
    @Lazy
    private IExamWorkService examWorkService;

    @Autowired
    private ISummaryStatisticsService iSummaryStatisticsService;

    @Autowired
    private IExamAnnualQuarterInfoService iExamAnnualQuarterInfoService;


    /**
     * 保存提交的试卷
     *
     * @param
     * @return
     */
    @Override
    public List<ExamQuestionUser> saveAnswerNotes(String currentUserCode, String source, AnswerStatusDto o, String annualQuarterCode) {
        //获取当前登录人userName
        String username = SecurityUtils.getCurrentUser().getUsername();
        Specification<ExamQuestionUser> creator = Specifications.<ExamQuestionUser>and()
                .eq("creator", username)
                .eq("annualQuarterCode", annualQuarterCode)
                .eq("answerStatus", 0)//0代表是仅仅保存的
                .build();

        /** 判断是否已过考试时间  过了考试时间则无法提交试卷**/
        String examCode1 = o.getQuestionList().get(0).getExamCode();
        ExamSummary examSummaryByCode = examSummaryService.findExamSummaryByCode(examCode1);
        LocalDateTime examEndTime = examSummaryByCode.getExamEndTime();
        LocalDateTime nowTime = LocalDateTime.now();
        boolean before = nowTime.isBefore(examEndTime);
        Assert.isTrue(before, "考试时间已过！");

        //查询仅保存的答题记录，并将其清空
        List<ExamQuestionUser> allSave = examAnswerNotesRepository.findAll(creator);
        if (!allSave.isEmpty()) examAnswerNotesRepository.deleteAll(allSave);
        Specification<ExamQuestionUser> creator2 = Specifications.<ExamQuestionUser>and()
                .eq("creator", username)
                .eq("annualQuarterCode", annualQuarterCode)
                .eq("answerStatus", 1)//1代表已提交过本季度的答卷
                .build();
        List<ExamQuestionUser> allSaveQuestion = examAnswerNotesRepository.findAll(creator2);
        Assert.isTrue(allSaveQuestion.size() == 0, "已提交");

        if (o.getAnswerStatus() == Constants.answerStatus_1) {
            String examCode = o.getQuestionList().get(0).getExamCode();

            ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode(examCode);
            Assert.notNull(summaryInfo, "考试信息不存在!");
            String workType = summaryInfo.getWorkType();
            Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
            // 根据提交人和待办类型获取待办工作并进行核销操作
            List<ExamWork> workInfo = examWorkService.findByUsernameAndWorkType(username, workType);
            for (ExamWork examWork : workInfo) {
                if (null != examWork) {
                    examWorkService.dealWith(source, currentUserCode, examWork.getId());
                } else {
                    // 提交问卷操作时待办一定存在
                    log.error("根据username【{}】、workType【{}】未能成功核销统一待办!", username, workType);
                }
            }
        }


        List<ExamQuestionUser> questionList = o.getQuestionList();
        List<ExamQuestionUser> examQuestionUsers = new ArrayList<>();
        for (ExamQuestionUser examQuestionUser : questionList) {
            examQuestionUser.setAnswerStatus(o.getAnswerStatus());
            examQuestionUser.setCreator(username);
            examQuestionUser.setModifier(username);
            examQuestionUser.setEnabled(true);

            //将考试的年度季度编码保存到答题记录表中，以实现各年度季度的满意度评价统计功能
            examQuestionUser.setAnnualQuarterCode(annualQuarterCode);
            examQuestionUsers.add(examQuestionUser);
        }

        return examAnswerNotesRepository.saveAll(examQuestionUsers);
    }

    /**
     * 保存提交的试卷(短信小程序端)
     *
     * @param
     * @return
     */
    @Override
    public List<ExamQuestionUser> saveAnswerNotesMB(String currentUserCode, String source, AnswerStatusDto o, String annualQuarterCode) {
        //获取当前登录人userName
        String username = SecurityUtils.getCurrentUser().getUsername();
        Specification<ExamQuestionUser> creator = Specifications.<ExamQuestionUser>and()
                .eq("creator", username)
                .eq("annualQuarterCode", annualQuarterCode)
                .eq("answerStatus", 0)//0代表是仅仅保存的
                .build();


        /** 判断是否已过考试时间  过了考试时间则无法提交试卷**/
        String examCode1 = o.getQuestionList().get(0).getExamCode();
        ExamSummary examSummaryByCode = examSummaryService.findExamSummaryByCode(examCode1);
        LocalDateTime examEndTime = examSummaryByCode.getExamEndTime();
        LocalDateTime nowTime = LocalDateTime.now();
        boolean before = nowTime.isBefore(examEndTime);
        Assert.isTrue(before, "考试时间已过！");
        ;

        //查询仅保存的答题记录，并将其清空
        List<ExamQuestionUser> allSave = examAnswerNotesRepository.findAll(creator);
        if (!allSave.isEmpty()) examAnswerNotesRepository.deleteAll(allSave);
        Specification<ExamQuestionUser> creator2 = Specifications.<ExamQuestionUser>and()
                .eq("creator", username)
                .eq("annualQuarterCode", annualQuarterCode)
                .eq("answerStatus", 1)//1代表已提交过本季度的答卷
                .build();
        List<ExamQuestionUser> allSaveQuestion = examAnswerNotesRepository.findAll(creator2);
        Assert.isTrue(allSaveQuestion.size() == 0, "已提交");

        if (o.getAnswerStatus() == Constants.answerStatus_1) {
            String examCode = o.getQuestionList().get(0).getExamCode();
            ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode(examCode);
            Assert.notNull(summaryInfo, "考试信息不存在!");
            String workType = summaryInfo.getWorkType();
            Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
            // 根据提交人和待办类型获取待办工作并进行核销操作
            List<ExamWork> workInfo = examWorkService.findByUsernameAndWorkType(username, workType);
            for (ExamWork examWork : workInfo) {
                if (null != examWork) {
                    examWorkService.dealWith(source, currentUserCode, examWork.getId());
                } else {
                    // 提交问卷操作时待办一定存在
                    log.error("根据username【{}】、workType【{}】未能成功核销统一待办!", username, workType);
                }
            }
        }


        List<ExamQuestionUser> questionList = o.getQuestionList();
        List<ExamQuestionUser> examQuestionUsers = new ArrayList<>();
        for (ExamQuestionUser examQuestionUser : questionList) {
            examQuestionUser.setAnswerStatus(o.getAnswerStatus());
            examQuestionUser.setCreator(username);
            examQuestionUser.setModifier(username);
            examQuestionUser.setEnabled(true);

            //将考试的年度季度编码保存到答题记录表中，以实现各年度季度的满意度评价统计功能
            examQuestionUser.setAnnualQuarterCode(annualQuarterCode);
            examQuestionUsers.add(examQuestionUser);
        }

        return examAnswerNotesRepository.saveAll(examQuestionUsers);
    }

    /**
     * 查询用户的答题记录     根据人、考试的年度季度信息编码
     *
     * @param
     * @return
     */
    @Override
    public List<ExamQuestionUser> findAnswerNotesByCreator(String annualQuarterCode) {
        Specification<ExamQuestionUser> condition = Specifications.<ExamQuestionUser>and()
                .eq("creator", SecurityUtils.getCurrentUser().getUsername())
                .eq("annualQuarterCode", annualQuarterCode)
                .build();
        return this.findAllNoPage(condition, Sort.by(Sort.Direction.ASC, "modifiedTime"));

    }

    /**
     * 统计机关各部门对各部门的满意度评价结果
     * 1-6为财务部
     * 7-12为党委办公室
     * 13-18为人力资源部
     * 19-24为市场经营部
     * 25-30为网络部
     * 31-36为政企客户部
     * 37-42为综合部
     * 43-48为工会
     * 49-54为客户服务部
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> statisticsDepartments(String annualQuarterCode) {
        String questionBankCode = Constants.LYJG_QUESTION_BANK_CODE;
        List<ExamQuestion> allNoPage = examQuestionRepository.findAllByQuestionBankCode(questionBankCode);
        List<Map<String, Object>> endResult = Lists.newArrayList();
        //List<String> codeList = Lists.newArrayList();
        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数格式

        Map<String, Object> mapCwb = new HashMap<>();
        Map<String, Object> mapDqgh = new HashMap<>();
        Map<String, Object> mapRlzy = new HashMap<>();
        Map<String, Object> mapScjy = new HashMap<>();
        Map<String, Object> mapKhfwzx = new HashMap<>();
        Map<String, Object> mapInternet = new HashMap<>();
        Map<String, Object> mapZqkehb = new HashMap<>();
        Map<String, Object> mapZhb = new HashMap<>();
        Map<String, Object> mapGh = new HashMap<>();
        Map<String, Object> mapKhfwb = new HashMap<>();
        Map<String, Object> mapAvg = new HashMap<>();//各维度的部门平均分

        List<Double> questionScoreList = new ArrayList<>();

        for (ExamQuestion examQuestion : allNoPage) {
            // 获取题目编号
            String questionCode = examQuestion.getQuestionCode();

            // 19-24 对市场经营部

            if ("A-2222-19".equals(questionCode)
                    || "A-2222-20".equals(questionCode)
                    || "A-2222-21".equals(questionCode)
                    || "A-2222-22".equals(questionCode)
                    || "A-2222-23".equals(questionCode)
                    || "A-2222-24".equals(questionCode)
            ) {
                //
                Double questionScore = examQuestionUserRepository.findQuestionScoreProportion(questionCode, annualQuarterCode, "政企客户部", "网络部", "客户服务部");
                questionScoreList.add(questionScore);
            }
            //31-36 对政企客户部
            else if ("A-2222-31".equals(questionCode)
                    || "A-2222-32".equals(questionCode)
                    || "A-2222-33".equals(questionCode)
                    || "A-2222-34".equals(questionCode)
                    || "A-2222-35".equals(questionCode)
                    || "A-2222-36".equals(questionCode)
            ) {
                Double questionScore = examQuestionUserRepository.findQuestionScoreProportion(questionCode, annualQuarterCode, "市场经营部", "网络部", "客户服务部");
                questionScoreList.add(questionScore);
            }
            //25-30 对网络部
            else if ("A-2222-25".equals(questionCode)
                    || "A-2222-26".equals(questionCode)
                    || "A-2222-27".equals(questionCode)
                    || "A-2222-28".equals(questionCode)
                    || "A-2222-29".equals(questionCode)
                    || "A-2222-30".equals(questionCode)
            ) {
                Double questionScore = examQuestionUserRepository.findQuestionScoreProportion(questionCode, annualQuarterCode, "政企客户部", "市场经营部", "客户服务部");
                questionScoreList.add(questionScore);
            } else {
                // 计算每道题的平均分
                Double questionScore = examQuestionUserRepository.findQuestionScore(questionCode, annualQuarterCode);
                questionScoreList.add(questionScore);
            }


        }

    /*        Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("questionCode", code);
            String dataSql = "select round(avg (equ.answer_score),2) as \"average\" from us_exam_question_user equ " +
                    "where equ.question_code =:questionCode " +
                    "and equ.annual_quarter_code=:annualQuarterCode "+
                    "and equ.answer_score is not null";
            Set<String> resultList = dynamicWhere.queryNamedParameterForList(dataSql, paramMap);*/
        /**
         * 财务部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Cwb = questionScoreList.subList(0, 6);//财务部的各维度平均值
        mapCwb.put("department", "财务部");
        Double CPL = Cwb.get(0) == null ? 0.0 : Cwb.get(0);
        Double CPS = Cwb.get(1) == null ? 0.0 : Cwb.get(1);
        Double CTF = Cwb.get(2) == null ? 0.0 : Cwb.get(2);
        Double CWE = Cwb.get(3) == null ? 0.0 : Cwb.get(3);
        Double CCA = Cwb.get(4) == null ? 0.0 : Cwb.get(4);
        Double CSC = Cwb.get(5) == null ? 0.0 : Cwb.get(5);
        mapCwb.put(DIMENSION_PL2, CPL);//协作态度
        mapCwb.put(DIMENSION_PS2, CPS);//责任担当
        mapCwb.put(DIMENSION_TF2, CTF);//沟通协调
        mapCwb.put(DIMENSION_WE2, CWE);//协作质量
        mapCwb.put(DIMENSION_CA2, CCA);//协作效率
        mapCwb.put(DIMENSION_SC2, CSC);//工作对接
        //部门成绩等于各维度之和平均值
        Double CWBScore;
        CWBScore = (CPL + CPS + CTF + CWE + CCA + CSC) / Cwb.size();
        mapCwb.put(DEPARTMENT_SCORE, df.format(CWBScore));//部门成绩
        /**
         * 党委办公室的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Dqgh = questionScoreList.subList(6, 12);//党委办公室的各维度平均值
        mapDqgh.put("department", "党委办公室");
        Double DPL = Dqgh.get(0) == null ? 0.0 : Dqgh.get(0);
        Double DPS = Dqgh.get(1) == null ? 0.0 : Dqgh.get(1);
        Double DTF = Dqgh.get(2) == null ? 0.0 : Dqgh.get(2);
        Double DWE = Dqgh.get(3) == null ? 0.0 : Dqgh.get(3);
        Double DCA = Dqgh.get(4) == null ? 0.0 : Dqgh.get(4);
        Double DSC = Dqgh.get(5) == null ? 0.0 : Dqgh.get(5);
        mapDqgh.put(DIMENSION_PL2, DPL);
        mapDqgh.put(DIMENSION_PS2, DPS);
        mapDqgh.put(DIMENSION_TF2, DTF);
        mapDqgh.put(DIMENSION_WE2, DWE);
        mapDqgh.put(DIMENSION_CA2, DCA);
        mapDqgh.put(DIMENSION_SC2, DSC);
        //部门成绩等于各维度之和平均值
        Double DQGHScore;
        DQGHScore = (DPL + DPS + DTF + DWE + DCA + DSC) / Dqgh.size();
        mapDqgh.put(DEPARTMENT_SCORE, df.format(DQGHScore));//部门成绩

        /**
         * 人力资源部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Rlzy = questionScoreList.subList(12, 18);//人力资源部的各维度平均值
        mapRlzy.put("department", "人力资源部");
        Double RPL = Rlzy.get(0) == null ? 0.0 : Rlzy.get(0);
        Double RPS = Rlzy.get(1) == null ? 0.0 : Rlzy.get(1);
        Double RTF = Rlzy.get(2) == null ? 0.0 : Rlzy.get(2);
        Double RWE = Rlzy.get(3) == null ? 0.0 : Rlzy.get(3);
        Double RCA = Rlzy.get(4) == null ? 0.0 : Rlzy.get(4);
        Double RSC = Rlzy.get(5) == null ? 0.0 : Rlzy.get(5);
        mapRlzy.put(DIMENSION_PL2, RPL);
        mapRlzy.put(DIMENSION_PS2, RPS);
        mapRlzy.put(DIMENSION_TF2, RTF);
        mapRlzy.put(DIMENSION_WE2, RWE);
        mapRlzy.put(DIMENSION_CA2, RCA);
        mapRlzy.put(DIMENSION_SC2, RSC);
        //部门成绩等于各维度之和平均值
        Double HRScore;
        HRScore = (RPL + RPS + RTF + RWE + RCA + RSC) / Rlzy.size();
        mapRlzy.put(DEPARTMENT_SCORE, df.format(HRScore));//部门成绩

        /**
         * 市场经营部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Scjy = questionScoreList.subList(18, 24);//市场经营部的各维度平均值
        mapScjy.put("department", "市场经营部");
        Double SPL = Scjy.get(0) == null ? 0.0 : Scjy.get(0);
        Double SPS = Scjy.get(1) == null ? 0.0 : Scjy.get(1);
        Double STF = Scjy.get(2) == null ? 0.0 : Scjy.get(2);
        Double SWE = Scjy.get(3) == null ? 0.0 : Scjy.get(3);
        Double SCA = Scjy.get(4) == null ? 0.0 : Scjy.get(4);
        Double SSC = Scjy.get(5) == null ? 0.0 : Scjy.get(5);
        mapScjy.put(DIMENSION_PL2, SPL);
        mapScjy.put(DIMENSION_PS2, SPS);
        mapScjy.put(DIMENSION_TF2, STF);
        mapScjy.put(DIMENSION_WE2, SWE);
        mapScjy.put(DIMENSION_CA2, SCA);
        mapScjy.put(DIMENSION_SC2, SSC);
        //部门成绩等于各维度之和平均值
        Double MKScore;
        MKScore = (SPL + SPS + STF + SWE + SCA + SSC) / Scjy.size();
        mapScjy.put(DEPARTMENT_SCORE, df.format(MKScore));//部门成绩

//            /**
//             * 客户服务中心的满意度评价统计结果
//             * <AUTHOR>
//             */
//            List<Double> Khfwzx = questionScoreList.subList(24, 30);//客户服务中心的各维度平均值
//            mapKhfwzx.put("department", "客户服务中心");
//            Double KPL=Khfwzx.get(0)==null? 0.0:Khfwzx.get(0);
//            Double KPS=Khfwzx.get(1)==null? 0.0:Khfwzx.get(1);
//            Double KTF=Khfwzx.get(2)==null? 0.0:Khfwzx.get(2);
//            Double KWE=Khfwzx.get(3)==null? 0.0:Khfwzx.get(3);
//            Double KCA=Khfwzx.get(4)==null? 0.0:Khfwzx.get(4);
//            Double KSC=Khfwzx.get(5)==null? 0.0:Khfwzx.get(5);
//            mapKhfwzx.put(DIMENSION_PL2, KPL);
//            mapKhfwzx.put(DIMENSION_PS2, KPS);
//            mapKhfwzx.put(DIMENSION_TF2, KTF);
//            mapKhfwzx.put(DIMENSION_WE2, KWE);
//            mapKhfwzx.put(DIMENSION_CA2, KCA);
//            mapKhfwzx.put(DIMENSION_SC2, KSC);
//            //部门成绩等于各维度之和平均值
//            Double KHScore;
//            KHScore = (KPL + KPS + KTF + KWE + KCA + KSC) / Khfwzx.size();
//            mapKhfwzx.put(DEPARTMENT_SCORE, df.format(KHScore));//部门成绩


        /**
         * 网络部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Internet = questionScoreList.subList(24, 30);//网络部的各维度平均值
        mapInternet.put("department", "网络部");
        Double IPL = Internet.get(0) == null ? 0.0 : Internet.get(0);
        Double IPS = Internet.get(1) == null ? 0.0 : Internet.get(1);
        Double ITF = Internet.get(2) == null ? 0.0 : Internet.get(2);
        Double IWE = Internet.get(3) == null ? 0.0 : Internet.get(3);
        Double ICA = Internet.get(4) == null ? 0.0 : Internet.get(4);
        Double ISC = Internet.get(5) == null ? 0.0 : Internet.get(5);
        mapInternet.put(DIMENSION_PL2, IPL);
        mapInternet.put(DIMENSION_PS2, IPS);
        mapInternet.put(DIMENSION_TF2, ITF);
        mapInternet.put(DIMENSION_WE2, IWE);
        mapInternet.put(DIMENSION_CA2, ICA);
        mapInternet.put(DIMENSION_SC2, ISC);
        //部门成绩等于各维度之和平均值
        Double ITScore;
        ITScore = (IPL + IPS + ITF + IWE + ICA + ISC) / Internet.size();
        mapInternet.put(DEPARTMENT_SCORE, df.format(ITScore));//部门成绩

        /**
         * 政企客户部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Zqkehb = questionScoreList.subList(30, 36);//政企客户部的各维度平均值
        mapZqkehb.put("department", "政企客户部");
        Double ZPL = Zqkehb.get(0) == null ? 0.0 : Zqkehb.get(0);
        Double ZPS = Zqkehb.get(1) == null ? 0.0 : Zqkehb.get(1);
        Double ZTF = Zqkehb.get(2) == null ? 0.0 : Zqkehb.get(2);
        Double ZWE = Zqkehb.get(3) == null ? 0.0 : Zqkehb.get(3);
        Double ZCA = Zqkehb.get(4) == null ? 0.0 : Zqkehb.get(4);
        Double ZSC = Zqkehb.get(5) == null ? 0.0 : Zqkehb.get(5);
        mapZqkehb.put(DIMENSION_PL2, ZPL);
        mapZqkehb.put(DIMENSION_PS2, ZPS);
        mapZqkehb.put(DIMENSION_TF2, ZTF);
        mapZqkehb.put(DIMENSION_WE2, ZWE);
        mapZqkehb.put(DIMENSION_CA2, ZCA);
        mapZqkehb.put(DIMENSION_SC2, ZSC);
        //部门成绩等于各维度之和平均值
        Double ZQScore;
        ZQScore = (ZPL + ZPS + ZTF + ZWE + ZCA + ZSC) / Zqkehb.size();
        mapZqkehb.put(DEPARTMENT_SCORE, df.format(ZQScore));//部门成绩

        /**
         * 综合部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Zhb = questionScoreList.subList(36, 42);//综合部的各维度平均值
        mapZhb.put("department", "综合部");
        Double BPL = Zhb.get(0) == null ? 0.0 : Zhb.get(0);
        Double BPS = Zhb.get(1) == null ? 0.0 : Zhb.get(1);
        Double BTF = Zhb.get(2) == null ? 0.0 : Zhb.get(2);
        Double BWE = Zhb.get(3) == null ? 0.0 : Zhb.get(3);
        Double BCA = Zhb.get(4) == null ? 0.0 : Zhb.get(4);
        Double BSC = Zhb.get(5) == null ? 0.0 : Zhb.get(5);
        mapZhb.put(DIMENSION_PL2, BPL);
        mapZhb.put(DIMENSION_PS2, BPS);
        mapZhb.put(DIMENSION_TF2, BTF);
        mapZhb.put(DIMENSION_WE2, BWE);
        mapZhb.put(DIMENSION_CA2, BCA);
        mapZhb.put(DIMENSION_SC2, BSC);
        //部门成绩等于各维度之和平均值
        Double ZHScore;
        ZHScore = (BPL + BPS + BTF + BWE + BCA + BSC) / Zhb.size();
        mapZhb.put(DEPARTMENT_SCORE, df.format(ZHScore));//部门成绩
        /**
         * 工会的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Gh = questionScoreList.subList(42, 48);//工会的各维度平均值
        mapGh.put("department", "工会");
        Double GPL = Gh.get(0) == null ? 0.0 : Gh.get(0);
        Double GPS = Gh.get(1) == null ? 0.0 : Gh.get(1);
        Double GTF = Gh.get(2) == null ? 0.0 : Gh.get(2);
        Double GWE = Gh.get(3) == null ? 0.0 : Gh.get(3);
        Double GCA = Gh.get(4) == null ? 0.0 : Gh.get(4);
        Double GSC = Gh.get(5) == null ? 0.0 : Gh.get(5);
        mapGh.put(DIMENSION_PL2, GPL);
        mapGh.put(DIMENSION_PS2, GPS);
        mapGh.put(DIMENSION_TF2, GTF);
        mapGh.put(DIMENSION_WE2, GWE);
        mapGh.put(DIMENSION_CA2, GCA);
        mapGh.put(DIMENSION_SC2, GSC);
        //部门成绩等于各维度之和平均值
        Double GHScore;
        GHScore = (GPL + GPS + GTF + GWE + GCA + GSC) / Gh.size();
        mapGh.put(DEPARTMENT_SCORE, df.format(GHScore));//部门成绩
        /**
         * 客户服务部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Khfub = questionScoreList.subList(48, 54);//客户服务部的各维度平均值
        mapKhfwb.put("department", "客户服务部");
        Double KPL = Khfub.get(0) == null ? 0.0 : Khfub.get(0);
        Double KPS = Khfub.get(1) == null ? 0.0 : Khfub.get(1);
        Double KTF = Khfub.get(2) == null ? 0.0 : Khfub.get(2);
        Double KWE = Khfub.get(3) == null ? 0.0 : Khfub.get(3);
        Double KCA = Khfub.get(4) == null ? 0.0 : Khfub.get(4);
        Double KSC = Khfub.get(5) == null ? 0.0 : Khfub.get(5);
        mapKhfwb.put(DIMENSION_PL2, KPL);
        mapKhfwb.put(DIMENSION_PS2, KPS);
        mapKhfwb.put(DIMENSION_TF2, KTF);
        mapKhfwb.put(DIMENSION_WE2, KWE);
        mapKhfwb.put(DIMENSION_CA2, KCA);
        mapKhfwb.put(DIMENSION_SC2, KSC);
        //部门成绩等于各维度之和平均值
        Double KHFWBScore;
        KHFWBScore = (KPL + KPS + KTF + KWE + KCA + KSC) / Khfub.size();
        mapKhfwb.put(DEPARTMENT_SCORE, df.format(KHFWBScore));//部门成绩
        /**
         * 各维度的部门平均分
         * <AUTHOR>
         */

        mapAvg.put("department", "平均分");
//            mapAvg.put(DIMENSION_PL2, df.format((CPL + DPL + RPL + SPL + KPL + IPL + ZPL + BPL) / 8));//专业水平的部门平均分
//            mapAvg.put(DIMENSION_PS2, df.format((CPS + CPS + RPS + SPS + KPS + IPS + ZPS + BPS) / 8));//问题解决的部门平均分
//            mapAvg.put(DIMENSION_TF2, df.format((CTF + DTF + RTF + STF + KTF + ITF + ZTF + BTF) / 8));//及时反馈的部门平均分
//            mapAvg.put(DIMENSION_WE2, df.format((CWE + DWE + RWE + SWE + KWE + IWE + ZWE + BWE) / 8));//工作效率的部门平均分
//            mapAvg.put(DIMENSION_CA2, df.format((CCA + DCA + RCA + SCA + KCA + ICA + ZCA + BCA) / 8));
//            mapAvg.put(DIMENSION_SC2, df.format((CSC + DCA + RCA + SCA + KCA + ICA + ZCA + BCA) / 8));

        mapAvg.put(DIMENSION_PL2, df.format((CPL + DPL + RPL + SPL + IPL + ZPL + BPL + GPL + KPL) / 9));//专业水平的部门平均分
        mapAvg.put(DIMENSION_PS2, df.format((CPS + DPS + RPS + SPS + IPS + ZPS + BPS + GPS + KPS) / 9));//问题解决的部门平均分
        mapAvg.put(DIMENSION_TF2, df.format((CTF + DTF + RTF + STF + ITF + ZTF + BTF + GTF + KTF) / 9));//及时反馈的部门平均分
        mapAvg.put(DIMENSION_WE2, df.format((CWE + DWE + RWE + SWE + IWE + ZWE + BWE + GWE + KWE) / 9));//工作效率的部门平均分
        mapAvg.put(DIMENSION_CA2, df.format((CCA + DCA + RCA + SCA + ICA + ZCA + BCA + GCA + KCA) / 9));
        mapAvg.put(DIMENSION_SC2, df.format((CSC + DSC + RSC + SSC + ISC + ZSC + BSC + GSC + KSC) / 9));
        Double allAvg;
        allAvg = (CWBScore + DQGHScore + HRScore + MKScore + ZHScore + ZQScore + ITScore + GHScore + KHFWBScore) / 9;
        mapAvg.put(DEPARTMENT_SCORE, df.format(allAvg));//部门成绩平均分
        endResult.add(mapCwb);
        endResult.add(mapDqgh);
        endResult.add(mapRlzy);
        endResult.add(mapScjy);
        //endResult.add(mapKhfwzx);
        endResult.add(mapInternet);
        endResult.add(mapZqkehb);
        endResult.add(mapZhb);
        endResult.add(mapGh);
        endResult.add(mapKhfwb);
        endResult.add(mapAvg);

        return endResult;
    }


    public static BigDecimal comparingByKpScore(Map map) {
        return new BigDecimal(map.get("Centennial").toString());

    }

    /**
     * 统计机关各部门对机关各部门的满意度评价总平均分以及百分制分值
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> statisticsDepartmentsAvg(String annualQuarterCode) {
        List<Map<String, Object>> avgAndPercentageValue = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数格式
        //获取所有部门平均成绩
        List<Map<String, Object>> statisticsDepartments = this.statisticsDepartments(annualQuarterCode);

        BigDecimal zero = new BigDecimal(0);
        //统计各部门的平均值以及其百分值
        //财务部
        Map<String, Object> mapCwb = statisticsDepartments.get(0);
        HashMap<String, Object> financeDepartment = new HashMap<>();
        financeDepartment.put("Avgs", mapCwb.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialCwb = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapCwb, DEPARTMENT_SCORE));
        financeDepartment.put("Centennial", calculateCentennialCwb.get("Centennial") == null ? zero : calculateCentennialCwb.get("Centennial"));
        financeDepartment.put("department", mapCwb.get("department"));
        //获取百分制的分数
        BigDecimal financeCentennial = (BigDecimal) financeDepartment.get("Centennial");

        //党委办公室
        Map<String, Object> mapDqgh = statisticsDepartments.get(1);
        Map<String, Object> partyTradeUnion = new HashMap<>();
        partyTradeUnion.put("Avgs", mapDqgh.get(DEPARTMENT_SCORE) == null ? zero : mapDqgh.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialDqgh = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapDqgh, DEPARTMENT_SCORE));
        partyTradeUnion.put("Centennial", calculateCentennialDqgh.get("Centennial") == null ? zero : calculateCentennialDqgh.get("Centennial"));
        partyTradeUnion.put("department", mapDqgh.get("department"));
        //获取百分制的分数
        BigDecimal partyCentennial = (BigDecimal) partyTradeUnion.get("Centennial");

        //人力资源部
        Map<String, Object> mapRlzy = statisticsDepartments.get(2);
        Map<String, Object> humanResourcesDepartment = new HashMap<>();
        humanResourcesDepartment.put("Avgs", mapRlzy.get(DEPARTMENT_SCORE) == null ? zero : mapRlzy.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialRlzy = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapRlzy, DEPARTMENT_SCORE));
        humanResourcesDepartment.put("Centennial", calculateCentennialRlzy.get("Centennial") == null ? zero : calculateCentennialRlzy.get("Centennial"));
        humanResourcesDepartment.put("department",  mapRlzy.get("department"));
        //获取百分制的分数
        BigDecimal humanResourcesCentennial = (BigDecimal) humanResourcesDepartment.get("Centennial");

        //市场经营部
        Map<String, Object> mapScjy = statisticsDepartments.get(3);
        Map<String, Object> marketingDepartment = new HashMap<>();
        marketingDepartment.put("Avgs", mapScjy.get(DEPARTMENT_SCORE) == null ? zero : mapScjy.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialScjy = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapScjy, DEPARTMENT_SCORE));
        marketingDepartment.put("Centennial", calculateCentennialScjy.get("Centennial") == null ? zero : calculateCentennialScjy.get("Centennial"));
        marketingDepartment.put("department", mapScjy.get("department"));
        //获取百分制的分数
        BigDecimal marketingCentennial = (BigDecimal) marketingDepartment.get("Centennial");
//        Map<String, Object> customer = examAnswerNotesRepository.customerServiceCenter(annualQuarterCode);
//        Map<String, Object> customerServiceCenter = new HashMap<>();
//        customerServiceCenter.put("Avgs",customer.get("Avgs")==null ? zero : customer.get("Avgs"));
//        customerServiceCenter.put("Centennial",customer.get("Centennial")==null ? zero : customer.get("Centennial"));
//        customerServiceCenter.put("department","客户服务中心");
        //获取百分制的分数
        //BigDecimal customerCentennial = (BigDecimal) customerServiceCenter.get("Centennial");

        //网络部
        Map<String, Object> mapInternet = statisticsDepartments.get(4);
        Map<String, Object> networkDepartment = new HashMap<>();
        networkDepartment.put("Avgs", mapInternet.get(DEPARTMENT_SCORE) == null ? zero : mapInternet.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialInternet = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapInternet, DEPARTMENT_SCORE));
        networkDepartment.put("Centennial", calculateCentennialInternet.get("Centennial") == null ? zero : calculateCentennialInternet.get("Centennial"));
        networkDepartment.put("department", mapInternet.get("department"));
        //获取百分制的分数
        BigDecimal networkCentennial = (BigDecimal) networkDepartment.get("Centennial");

        //政企客服部
        Map<String, Object> mapZqkehb = statisticsDepartments.get(5);
        Map<String, Object> governmentEnterpriseCustomerDepartment = new HashMap<>();
        governmentEnterpriseCustomerDepartment.put("Avgs", mapZqkehb.get(DEPARTMENT_SCORE) == null ? zero : mapZqkehb.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialZqkehb = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapZqkehb, DEPARTMENT_SCORE));
        governmentEnterpriseCustomerDepartment.put("Centennial", calculateCentennialZqkehb.get("Centennial") == null ? zero : calculateCentennialZqkehb.get("Centennial"));
        governmentEnterpriseCustomerDepartment.put("department", mapZqkehb.get("department"));
        //获取百分制的分数
        BigDecimal governmentCentennial = (BigDecimal) governmentEnterpriseCustomerDepartment.get("Centennial");

        //综合部
        Map<String, Object> mapZhb = statisticsDepartments.get(6);
//        Map<String, Object> general = examAnswerNotesRepository.generalDepartment(annualQuarterCode);
        Map<String, Object> generalDepartment = new HashMap<>();
        generalDepartment.put("Avgs", mapZhb.get(DEPARTMENT_SCORE) == null ? zero : mapZhb.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialZhb = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapZhb, DEPARTMENT_SCORE));
        generalDepartment.put("Centennial", calculateCentennialZhb.get("Centennial") == null ? zero : calculateCentennialZhb.get("Centennial"));
        generalDepartment.put("department", mapZhb.get("department"));
        //获取百分制的分数
        BigDecimal generalCentennial = (BigDecimal) generalDepartment.get("Centennial");

        //工会
        Map<String, Object> mapGh = statisticsDepartments.get(7);
//        Map<String, Object> laborUnion = examAnswerNotesRepository.laborUnionDepartment(annualQuarterCode);

        Map<String, Object> laborUnionDepartment = new HashMap<>();
        laborUnionDepartment.put("Avgs", mapGh.get(DEPARTMENT_SCORE) == null ? zero : mapGh.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialGh = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapGh, DEPARTMENT_SCORE));
        laborUnionDepartment.put("Centennial", calculateCentennialGh.get("Centennial") == null ? zero : calculateCentennialGh.get("Centennial"));
        laborUnionDepartment.put("department", mapGh.get("department"));
        //获取百分制的分数
        BigDecimal laborUnionCentennial = (BigDecimal) laborUnionDepartment.get("Centennial");

        //客户服务部
        Map<String, Object> mapKhfwb = statisticsDepartments.get(8);
//        Map<String, Object> clientele = examAnswerNotesRepository.clienteleDepartment(annualQuarterCode);
        Map<String, Object> clienteleDepartment = new HashMap<>();
        clienteleDepartment.put("Avgs", mapKhfwb.get(DEPARTMENT_SCORE) == null ? zero : mapKhfwb.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialKhfwb = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapKhfwb, DEPARTMENT_SCORE));
        clienteleDepartment.put("Centennial", calculateCentennialKhfwb.get("Centennial") == null ? zero : calculateCentennialKhfwb.get("Centennial"));
        clienteleDepartment.put("department", mapKhfwb.get("department"));
        //获取百分制的分数
        BigDecimal clienteleCentennial = (BigDecimal) clienteleDepartment.get("Centennial");

        HashMap<String, Object> allAvg = new HashMap<>();//用于存储各部门平均分的平均分
        allAvg.put("department", "平均成绩");
        //获取部门平均分
//        BigDecimal financeAvgs = (BigDecimal) financeDepartment.get("Avgs");
//        BigDecimal partyAvgs = (BigDecimal) partyTradeUnion.get("Avgs");
//        BigDecimal humanAvgs = (BigDecimal) humanResourcesDepartment.get("Avgs");
//        BigDecimal marketingAvgs = (BigDecimal) marketingDepartment.get("Avgs");
        // BigDecimal customerAvgs = (BigDecimal) customerServiceCenter.get("Avgs");
//        BigDecimal networkAvgs = (BigDecimal) networkDepartment.get("Avgs");
//        BigDecimal governmentAvgs = (BigDecimal) governmentEnterpriseCustomerDepartment.get("Avgs");
//        BigDecimal generalAvgs = (BigDecimal) generalDepartment.get("Avgs");
//        BigDecimal laborUnionAvgs = (BigDecimal) laborUnionDepartment.get("Avgs");
//        BigDecimal clienteleAvgs = (BigDecimal) clienteleDepartment.get("Avgs");

//        BigDecimal financeAvgs = new BigDecimal(MapUtil.getStr(financeDepartment,"Avgs"));
//        BigDecimal partyAvgs = new BigDecimal(MapUtil.getStr(partyTradeUnion,"Avgs"));
//        BigDecimal humanAvgs = new BigDecimal(MapUtil.getStr(humanResourcesDepartment,"Avgs"));
//        BigDecimal marketingAvgs = new BigDecimal(MapUtil.getStr(marketingDepartment,"Avgs"));
//        BigDecimal networkAvgs = new BigDecimal(MapUtil.getStr(networkDepartment,"Avgs"));
//        BigDecimal governmentAvgs = new BigDecimal(MapUtil.getStr(governmentEnterpriseCustomerDepartment,"Avgs"));
//        BigDecimal generalAvgs = new BigDecimal(MapUtil.getStr(generalDepartment,"Avgs"));
//        BigDecimal laborUnionAvgs = new BigDecimal(MapUtil.getStr(laborUnionDepartment,"Avgs"));
//        BigDecimal clienteleAvgs = new BigDecimal(MapUtil.getStr(clienteleDepartment,"Avgs"));
        BigDecimal bigDecimalCentennial = new BigDecimal(BigDecimal.ROUND_UP);

        BigDecimal AllScore = bigDecimalCentennial.add(financeCentennial)
                .add(partyCentennial)
                .add(humanResourcesCentennial)
                .add(marketingCentennial)
                //.add(customerCentennial)
                .add(networkCentennial)
                .add(governmentCentennial)
                .add(generalCentennial)
                .add(laborUnionCentennial)
                .add(clienteleCentennial);
        int i = 9;
        BigDecimal departmentCount = new BigDecimal(i);
        BigDecimal CentennialAvg = AllScore.divide(departmentCount,BigDecimal.ROUND_UP); //百分值的部门成绩的平均分
        String Centennial = df.format(CentennialAvg);

//        BigDecimal bigDecimalAvgs = new BigDecimal(BigDecimal.ROUND_UP);
//        BigDecimal allAvgs = bigDecimalAvgs.add(financeAvgs)
//                .add(partyAvgs)
//                .add(humanAvgs)
//                .add(marketingAvgs)
//                //.add(customerAvgs)
//                .add(networkAvgs)
//                .add(governmentAvgs)
//                .add(generalAvgs)
//                .add(laborUnionAvgs)
//                .add(clienteleAvgs);
//
//        BigDecimal Avgs = allAvgs.divide(departmentCount,BigDecimal.ROUND_UP);
//        String Avg2 = df.format(Avgs);
//        BigDecimal Avg = new BigDecimal(Avg2);
//        //    avgs=(financeAvgs+partyAvgs+humanAvgs+marketingAvgs+customerAvgs+networkAvgs+generalAvgs+governmentAvgs)/8;//各部门平均分的平均分
        Map<String, Object> avgMap = statisticsDepartments.get(9);
        allAvg.put("Avgs", MapUtil.getStr(avgMap,DEPARTMENT_SCORE));

        //各部门百分值的平均值
        //  Centennial=(financeCentennial+partyCentennial+humanResourcesCentennial+marketingCentennial+customerCentennial+networkCentennial+governmentCentennial+generalCentennial)/8;
        allAvg.put("Centennial", Centennial);

        avgAndPercentageValue.add(financeDepartment);
        avgAndPercentageValue.add(partyTradeUnion);
        //avgAndPercentageValue.add(customerServiceCenter);
        avgAndPercentageValue.add(humanResourcesDepartment);
        avgAndPercentageValue.add(marketingDepartment);
        avgAndPercentageValue.add(networkDepartment);
        avgAndPercentageValue.add(governmentEnterpriseCustomerDepartment);
        avgAndPercentageValue.add(generalDepartment);
        avgAndPercentageValue.add(laborUnionDepartment);
        avgAndPercentageValue.add(clienteleDepartment);


        /**
         * 根据百分值分数进行排名
         * <AUTHOR>
         * @date 2021/6/10
         */
        List<Map<String, Object>> collect = avgAndPercentageValue.stream().sorted(Comparator.comparing(ExamAnswerNotesServiceImpl::comparingByKpScore).reversed()).collect(Collectors.toList());

        int j = 1;
        for (Map<String, Object> map : collect) {
            if (j > collect.size()) {
                break;
            } else {
                map.put("rank", j);
                j++;
            }
        }
        avgAndPercentageValue.add(allAvg);


        return avgAndPercentageValue;
    }

    /**
     * 统计县分公司对各部门的满意度评价结果
     * 1-9为财务部
     * 10-18为党群工会
     * 19-27为人力资源部
     * 28-36为市场经营部
     * 37-45为网络部
     * 56-54为政企客户部
     * 55-63为综合部
     * 64-72为工会
     * 73-81为客户服务部
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> countyBranchStatistics(String annualQuarterCode) {
        String questionBankCode = Constants.LYXF_QUESTION_BANK_CODE;
        List<ExamQuestion> allNoPage = examQuestionRepository.findAllByQuestionBankCode(questionBankCode);
        List<Map<String, Object>> endResult = Lists.newArrayList();
        List<String> codeList = Lists.newArrayList();

        Map<String, Object> mapCwb = new HashMap<>();
        Map<String, Object> mapDqgh = new HashMap<>();
        Map<String, Object> mapRlzy = new HashMap<>();
        Map<String, Object> mapScjy = new HashMap<>();
        Map<String, Object> mapKhfwzx = new HashMap<>();
        Map<String, Object> mapInternet = new HashMap<>();
        Map<String, Object> mapZqkehb = new HashMap<>();
        Map<String, Object> mapZhb = new HashMap<>();
        Map<String, Object> mapGh = new HashMap<>();
        Map<String, Object> mapKhfwb = new HashMap<>();
        Map<String, Object> mapAvg = new HashMap<>();//各维度的部门平均分

        List<Double> questionScoreList = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数格式

        for (ExamQuestion examQuestion : allNoPage) {
            String questionCode = examQuestion.getQuestionCode();
            codeList.add(questionCode);
        }

        for (int i = 0; i < codeList.size(); i++) {
            Double questionScore = examQuestionUserRepository.findQuestionScore(codeList.get(i), annualQuarterCode);
            questionScoreList.add(questionScore);
            // Double.parseDouble()
        }
    /*        Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("questionCode", code);
            String dataSql = "select round(avg (equ.answer_score),2) as \"average\" from us_exam_question_user equ " +
                    "where equ.question_code =:questionCode " +
                    "and equ.annual_quarter_code=:annualQuarterCode "+
                    "and equ.answer_score is not null";
            Set<String> resultList = dynamicWhere.queryNamedParameterForList(dataSql, paramMap);*/
        /**
         * 财务部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Cwb = questionScoreList.subList(0, 9);//财务部的各维度平均值
        mapCwb.put("department", "财务部");
        Double CPL = Cwb.get(0) == null ? 0.0 : Cwb.get(0);
        Double CPS = Cwb.get(1) == null ? 0.0 : Cwb.get(1);
        Double CTF = Cwb.get(2) == null ? 0.0 : Cwb.get(2);
        Double CWE = Cwb.get(3) == null ? 0.0 : Cwb.get(3);
        Double CCA = Cwb.get(4) == null ? 0.0 : Cwb.get(4);
        Double CSC = Cwb.get(5) == null ? 0.0 : Cwb.get(5);
        Double CPG = Cwb.get(6) == null ? 0.0 : Cwb.get(6);
        Double CPN = Cwb.get(7) == null ? 0.0 : Cwb.get(7);
        Double COE = Cwb.get(8) == null ? 0.0 : Cwb.get(8);
        mapCwb.put(DIMENSION_PL, CPL);//专业水平
        mapCwb.put(DIMENSION_PS, CPS);//问题解决
        mapCwb.put(DIMENSION_TF, CTF);//及时反馈
        mapCwb.put(DIMENSION_WE, CWE);//工作效率
        mapCwb.put(DIMENSION_CA, CCA);//咨询解答
        mapCwb.put(DIMENSION_SC, CSC);//服务意识
        mapCwb.put(DIMENSION_PG, CPG);//政策指导
        mapCwb.put(DIMENSION_PN, CPN);//计划性
        mapCwb.put(DIMENSION_OE, COE);//总体评价
        //部门成绩等于各维度之和平均值
        Double CWBScore;
        CWBScore = (CPL + CPS + CTF + CWE + CCA + CSC + CPG + CPN + COE) / Cwb.size();
        mapCwb.put(DEPARTMENT_SCORE, df.format(CWBScore));//部门成绩

        /**
         * 党委办公室的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Dqgh = questionScoreList.subList(9, 18);//党委办公室的各维度平均值
        mapDqgh.put("department", "党委办公室");
        Double DPL = Dqgh.get(0) == null ? 0.0 : Dqgh.get(0);
        Double DPS = Dqgh.get(1) == null ? 0.0 : Dqgh.get(1);
        Double DTF = Dqgh.get(2) == null ? 0.0 : Dqgh.get(2);
        Double DWE = Dqgh.get(3) == null ? 0.0 : Dqgh.get(3);
        Double DCA = Dqgh.get(4) == null ? 0.0 : Dqgh.get(4);
        Double DSC = Dqgh.get(5) == null ? 0.0 : Dqgh.get(5);
        Double DPG = Dqgh.get(6) == null ? 0.0 : Dqgh.get(6);
        Double DPN = Dqgh.get(7) == null ? 0.0 : Dqgh.get(7);
        Double DOE = Dqgh.get(8) == null ? 0.0 : Dqgh.get(8);
        mapDqgh.put(DIMENSION_PL, DPL);//专业水平
        mapDqgh.put(DIMENSION_PS, DPS);//问题解决
        mapDqgh.put(DIMENSION_TF, DTF);//及时反馈
        mapDqgh.put(DIMENSION_WE, DWE);//工作效率
        mapDqgh.put(DIMENSION_CA, DCA);//咨询解答
        mapDqgh.put(DIMENSION_SC, DSC);//服务意识
        mapDqgh.put(DIMENSION_PG, DPG);//政策指导
        mapDqgh.put(DIMENSION_PN, DPN);//计划性
        mapDqgh.put(DIMENSION_OE, DOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double DQGHScore;
        DQGHScore = (DPL + DPS + DTF + DWE + DCA + DSC + DPG + DPN + DOE) / Dqgh.size();
        mapDqgh.put(DEPARTMENT_SCORE, df.format(DQGHScore));//部门成绩

        /**
         * 人力资源部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Rlzy = questionScoreList.subList(18, 27);//人力资源部的各维度平均值
        mapRlzy.put("department", "人力资源部");
        Double RPL = Rlzy.get(0) == null ? 0.0 : Rlzy.get(0);
        Double RPS = Rlzy.get(1) == null ? 0.0 : Rlzy.get(1);
        Double RTF = Rlzy.get(2) == null ? 0.0 : Rlzy.get(2);
        Double RWE = Rlzy.get(3) == null ? 0.0 : Rlzy.get(3);
        Double RCA = Rlzy.get(4) == null ? 0.0 : Rlzy.get(4);
        Double RSC = Rlzy.get(5) == null ? 0.0 : Rlzy.get(5);
        Double RPG = Rlzy.get(6) == null ? 0.0 : Rlzy.get(6);
        Double RPN = Rlzy.get(7) == null ? 0.0 : Rlzy.get(7);
        Double ROE = Rlzy.get(8) == null ? 0.0 : Rlzy.get(8);
        mapRlzy.put(DIMENSION_PL, RPL);//专业水平
        mapRlzy.put(DIMENSION_PS, RPS);//问题解决
        mapRlzy.put(DIMENSION_TF, RTF);//及时反馈
        mapRlzy.put(DIMENSION_WE, RWE);//工作效率
        mapRlzy.put(DIMENSION_CA, RCA);//咨询解答
        mapRlzy.put(DIMENSION_SC, RSC);//服务意识
        mapRlzy.put(DIMENSION_PG, RPG);//政策指导
        mapRlzy.put(DIMENSION_PN, RPN);//计划性
        mapRlzy.put(DIMENSION_OE, ROE);//总体评价
        //部门成绩等于各维度之和平均值
        Double HRScore;
        HRScore = (RPL + RPS + RTF + RWE + RCA + RSC + RPG + RPN + ROE) / Rlzy.size();
        mapRlzy.put(DEPARTMENT_SCORE, df.format(HRScore));//部门成绩

        /**
         * 市场经营部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Scjy = questionScoreList.subList(27, 36);//市场经营部的各维度平均值
        mapScjy.put("department", "市场经营部");
        Double SPL = Scjy.get(0) == null ? 0.0 : Scjy.get(0);
        Double SPS = Scjy.get(1) == null ? 0.0 : Scjy.get(1);
        Double STF = Scjy.get(2) == null ? 0.0 : Scjy.get(2);
        Double SWE = Scjy.get(3) == null ? 0.0 : Scjy.get(3);
        Double SCA = Scjy.get(4) == null ? 0.0 : Scjy.get(4);
        Double SSC = Scjy.get(5) == null ? 0.0 : Scjy.get(5);
        Double SPG = Scjy.get(6) == null ? 0.0 : Scjy.get(6);
        Double SPN = Scjy.get(7) == null ? 0.0 : Scjy.get(7);
        Double SOE = Scjy.get(8) == null ? 0.0 : Scjy.get(8);
        mapScjy.put(DIMENSION_PL, SPL);//专业水平
        mapScjy.put(DIMENSION_PS, SPS);//问题解决
        mapScjy.put(DIMENSION_TF, STF);//及时反馈
        mapScjy.put(DIMENSION_WE, SWE);//工作效率
        mapScjy.put(DIMENSION_CA, SCA);//咨询解答
        mapScjy.put(DIMENSION_SC, SSC);//服务意识
        mapScjy.put(DIMENSION_PG, SPG);//政策指导
        mapScjy.put(DIMENSION_PN, SPN);//计划性
        mapScjy.put(DIMENSION_OE, SOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double MKScore;
        MKScore = (SPL + SPS + STF + SWE + SCA + SSC + SPG + SPN + SOE) / Scjy.size();
        mapScjy.put(DEPARTMENT_SCORE, df.format(MKScore));//部门成绩

//        /**
//         * 客户服务中心的满意度评价统计结果
//         * <AUTHOR>
//         */
//        List<Double> Khfwzx = questionScoreList.subList(36, 45);//客户服务中心的各维度平均值
//        mapKhfwzx.put("department","客户服务中心");
//        Double KPL=Khfwzx.get(0)==null? 0.0:Khfwzx.get(0);
//        Double KPS=Khfwzx.get(1)==null? 0.0:Khfwzx.get(1);
//        Double KTF=Khfwzx.get(2)==null? 0.0:Khfwzx.get(2);
//        Double KWE=Khfwzx.get(3)==null? 0.0:Khfwzx.get(3);
//        Double KCA=Khfwzx.get(4)==null? 0.0:Khfwzx.get(4);
//        Double KSC=Khfwzx.get(5)==null? 0.0:Khfwzx.get(5);
//        Double KPG=Khfwzx.get(6)==null? 0.0:Khfwzx.get(6);
//        Double KPN=Khfwzx.get(7)==null? 0.0:Khfwzx.get(7);
//        Double KOE=Khfwzx.get(8)==null? 0.0:Khfwzx.get(8);
//        mapKhfwzx.put(DIMENSION_PL,KPL);//专业水平
//        mapKhfwzx.put(DIMENSION_PS,KPS);//问题解决
//        mapKhfwzx.put(DIMENSION_TF,KTF);//及时反馈
//        mapKhfwzx.put(DIMENSION_WE,KWE);//工作效率
//        mapKhfwzx.put(DIMENSION_CA,KCA);//咨询解答
//        mapKhfwzx.put(DIMENSION_SC,KSC);//服务意识
//        mapKhfwzx.put(DIMENSION_PG,KPG);//政策指导
//        mapKhfwzx.put(DIMENSION_PN,KPN);//计划性
//        mapKhfwzx.put(DIMENSION_OE,KOE);//总体评价
//        //部门成绩等于各维度之和平均值
//        Double KHScore;
//        KHScore=(KPL+KPS+KTF+KWE+KCA+KSC+KPG+KPN+KOE)/Khfwzx.size();
//        mapKhfwzx.put(DEPARTMENT_SCORE,df.format(KHScore));//部门成绩


        /**
         * 网络部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Internet = questionScoreList.subList(36, 45);//网络部的各维度平均值
        mapInternet.put("department", "网络部");
        Double IPL = Internet.get(0) == null ? 0.0 : Internet.get(0);
        Double IPS = Internet.get(1) == null ? 0.0 : Internet.get(1);
        Double ITF = Internet.get(2) == null ? 0.0 : Internet.get(2);
        Double IWE = Internet.get(3) == null ? 0.0 : Internet.get(3);
        Double ICA = Internet.get(4) == null ? 0.0 : Internet.get(4);
        Double ISC = Internet.get(5) == null ? 0.0 : Internet.get(5);
        Double IPG = Internet.get(6) == null ? 0.0 : Internet.get(6);
        Double IPN = Internet.get(7) == null ? 0.0 : Internet.get(7);
        Double IOE = Internet.get(8) == null ? 0.0 : Internet.get(8);
        mapInternet.put(DIMENSION_PL, IPL);//专业水平
        mapInternet.put(DIMENSION_PS, IPS);//问题解决
        mapInternet.put(DIMENSION_TF, ITF);//及时反馈
        mapInternet.put(DIMENSION_WE, IWE);//工作效率
        mapInternet.put(DIMENSION_CA, ICA);//咨询解答
        mapInternet.put(DIMENSION_SC, ISC);//服务意识
        mapInternet.put(DIMENSION_PG, IPG);//政策指导
        mapInternet.put(DIMENSION_PN, IPN);//计划性
        mapInternet.put(DIMENSION_OE, IOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double ITScore;
        ITScore = (IPL + IPS + ITF + IWE + ICA + ISC + IPG + IPN + IOE) / Internet.size();
        mapInternet.put(DEPARTMENT_SCORE, df.format(ITScore));//部门成绩

        /**
         * 政企客户部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Zqkehb = questionScoreList.subList(45, 54);//政企客户部的各维度平均值
        mapZqkehb.put("department", "政企客户部");
        Double ZPL = Zqkehb.get(0) == null ? 0.0 : Zqkehb.get(0);
        Double ZPS = Zqkehb.get(1) == null ? 0.0 : Zqkehb.get(1);
        Double ZTF = Zqkehb.get(2) == null ? 0.0 : Zqkehb.get(2);
        Double ZWE = Zqkehb.get(3) == null ? 0.0 : Zqkehb.get(3);
        Double ZCA = Zqkehb.get(4) == null ? 0.0 : Zqkehb.get(4);
        Double ZSC = Zqkehb.get(5) == null ? 0.0 : Zqkehb.get(5);
        Double ZPG = Zqkehb.get(6) == null ? 0.0 : Zqkehb.get(6);
        Double ZPN = Zqkehb.get(7) == null ? 0.0 : Zqkehb.get(7);
        Double ZOE = Zqkehb.get(8) == null ? 0.0 : Zqkehb.get(8);
        mapZqkehb.put(DIMENSION_PL, ZPL);//专业水平
        mapZqkehb.put(DIMENSION_PS, ZPS);//问题解决
        mapZqkehb.put(DIMENSION_TF, ZTF);//及时反馈
        mapZqkehb.put(DIMENSION_WE, ZWE);//工作效率
        mapZqkehb.put(DIMENSION_CA, ZCA);//咨询解答
        mapZqkehb.put(DIMENSION_SC, ZSC);//服务意识
        mapZqkehb.put(DIMENSION_PG, ZPG);//政策指导
        mapZqkehb.put(DIMENSION_PN, ZPN);//计划性
        mapZqkehb.put(DIMENSION_OE, ZOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double ZQScore;
        ZQScore = (ZPL + ZPS + ZTF + ZWE + ZCA + ZSC + ZPG + ZPN + ZOE) / Zqkehb.size();
        mapZqkehb.put(DEPARTMENT_SCORE, df.format(ZQScore));//部门成绩

        /**
         * 综合部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Zhb = questionScoreList.subList(54, 63);//综合部的各维度平均值
        mapZhb.put("department", "综合部");
        Double BPL = Zhb.get(0) == null ? 0.0 : Zhb.get(0);
        Double BPS = Zhb.get(1) == null ? 0.0 : Zhb.get(1);
        Double BTF = Zhb.get(2) == null ? 0.0 : Zhb.get(2);
        Double BWE = Zhb.get(3) == null ? 0.0 : Zhb.get(3);
        Double BCA = Zhb.get(4) == null ? 0.0 : Zhb.get(4);
        Double BSC = Zhb.get(5) == null ? 0.0 : Zhb.get(5);
        Double BPG = Zhb.get(6) == null ? 0.0 : Zhb.get(6);
        Double BPN = Zhb.get(7) == null ? 0.0 : Zhb.get(7);
        Double BOE = Zhb.get(8) == null ? 0.0 : Zhb.get(8);
        mapZhb.put(DIMENSION_PL, BPL);//专业水平
        mapZhb.put(DIMENSION_PS, BPS);//问题解决
        mapZhb.put(DIMENSION_TF, BTF);//及时反馈
        mapZhb.put(DIMENSION_WE, BWE);//工作效率
        mapZhb.put(DIMENSION_CA, BCA);//咨询解答
        mapZhb.put(DIMENSION_SC, BSC);//服务意识
        mapZhb.put(DIMENSION_PG, BPG);//政策指导
        mapZhb.put(DIMENSION_PN, BPN);//计划性
        mapZhb.put(DIMENSION_OE, BOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double ZHScore;
        ZHScore = (BPL + BPS + BTF + BWE + BCA + BSC + BPG + BPN + BOE) / Zhb.size();
        mapZhb.put(DEPARTMENT_SCORE, df.format(ZHScore));//部门成绩

        /**
         * 工会的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Gh = questionScoreList.subList(63, 72);//工会的各维度平均值
        mapGh.put("department", "工会");
        Double GPL = Gh.get(0) == null ? 0.0 : Gh.get(0);
        Double GPS = Gh.get(1) == null ? 0.0 : Gh.get(1);
        Double GTF = Gh.get(2) == null ? 0.0 : Gh.get(2);
        Double GWE = Gh.get(3) == null ? 0.0 : Gh.get(3);
        Double GCA = Gh.get(4) == null ? 0.0 : Gh.get(4);
        Double GSC = Gh.get(5) == null ? 0.0 : Gh.get(5);
        Double GPG = Gh.get(5) == null ? 0.0 : Gh.get(6);
        Double GPN = Gh.get(7) == null ? 0.0 : Gh.get(7);
        Double GOE = Gh.get(8) == null ? 0.0 : Gh.get(8);
        mapGh.put(DIMENSION_PL, GPL);//专业水平
        mapGh.put(DIMENSION_PS, GPS);//问题解决
        mapGh.put(DIMENSION_TF, GTF);//及时反馈
        mapGh.put(DIMENSION_WE, GWE);//工作效率
        mapGh.put(DIMENSION_CA, GCA);//咨询解答
        mapGh.put(DIMENSION_SC, GSC);//服务意识
        mapGh.put(DIMENSION_PG, GPG);//政策指导
        mapGh.put(DIMENSION_PN, GPN);//计划性
        mapGh.put(DIMENSION_OE, GOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double GHScore;
        GHScore = (GPL + GPS + GTF + GWE + GCA + GSC + GPN + GPG + GOE) / Gh.size();
        mapGh.put(DEPARTMENT_SCORE, df.format(GHScore));//部门成绩
        /**
         * 客户服务部的满意度评价统计结果
         * <AUTHOR>
         */
        List<Double> Khfub = questionScoreList.subList(72, 81);//客户服务部的各维度平均值
        mapKhfwb.put("department", "客户服务部");
        Double KPL = Khfub.get(0) == null ? 0.0 : Khfub.get(0);
        Double KPS = Khfub.get(1) == null ? 0.0 : Khfub.get(1);
        Double KTF = Khfub.get(2) == null ? 0.0 : Khfub.get(2);
        Double KWE = Khfub.get(3) == null ? 0.0 : Khfub.get(3);
        Double KCA = Khfub.get(4) == null ? 0.0 : Khfub.get(4);
        Double KSC = Khfub.get(5) == null ? 0.0 : Khfub.get(5);
        Double KPG = Khfub.get(6) == null ? 0.0 : Khfub.get(6);
        Double KPN = Khfub.get(7) == null ? 0.0 : Khfub.get(7);
        Double KOE = Khfub.get(8) == null ? 0.0 : Khfub.get(8);
        mapKhfwb.put(DIMENSION_PL, KPL);//专业水平
        mapKhfwb.put(DIMENSION_PS, KPS);//问题解决
        mapKhfwb.put(DIMENSION_TF, KTF);//及时反馈
        mapKhfwb.put(DIMENSION_WE, KWE);//工作效率
        mapKhfwb.put(DIMENSION_CA, KCA);//咨询解答
        mapKhfwb.put(DIMENSION_SC, KSC);//服务意识
        mapKhfwb.put(DIMENSION_PG, KPN);//政策指导
        mapKhfwb.put(DIMENSION_PN, KOE);//计划性
        mapKhfwb.put(DIMENSION_OE, KOE);//总体评价
        //部门成绩等于各维度之和平均值
        Double KHFWBScore;
        KHFWBScore = (KPL + KPS + KTF + KWE + KCA + KSC + KPG + KPN + KOE) / Khfub.size();
        mapKhfwb.put(DEPARTMENT_SCORE, df.format(KHFWBScore));//部门成绩
//        /**
//         * 各维度的部门平均分
//         * <AUTHOR>
//         */
//        mapAvg.put("department","平均分");
//        mapAvg.put(DIMENSION_PL,df.format((CPL+DPL+RPL+SPL+KPL+IPL+ZPL+BPL)/8));//专业水平的部门平均分
//        mapAvg.put(DIMENSION_PS,df.format((CPS+DPS+RPS+SPS+KPS+IPS+ZPS+BPS)/8));//问题解决的部门平均分
//        mapAvg.put(DIMENSION_TF,df.format((CTF+DTF+RTF+STF+KTF+ITF+ZTF+BTF)/8));//及时反馈的部门平均分
//        mapAvg.put(DIMENSION_WE,df.format((CWE+DWE+RWE+SWE+KWE+IWE+ZWE+BWE)/8));//工作效率的部门平均分
//        mapAvg.put(DIMENSION_CA,df.format((CCA+DCA+RCA+SCA+KCA+ICA+ZCA+BCA)/8));//咨询解答的部门平均分
//        mapAvg.put(DIMENSION_SC,df.format((CSC+DSC+RSC+SSC+KSC+ISC+ZSC+BSC)/8));//服务意识的部门平均分
//        mapAvg.put(DIMENSION_PG,df.format((CPG+DPG+RPG+SPG+KPG+IPG+ZPG+BPG)/8));//政策指导的部门平均分
//        mapAvg.put(DIMENSION_PN,df.format((CPN+DPN+RPN+SPN+KPN+IPN+ZPN+BPN)/8));//计划性的部门平均分
//        mapAvg.put(DIMENSION_OE,df.format((COE+DOE+ROE+SOE+KOE+IOE+ZOE+BOE)/8));//总体评价的部门平均分
//        //所有部门成绩的平均分
//        Double allAvg;
//        allAvg=(CWBScore+DQGHScore+HRScore+MKScore+KHScore+ZHScore+ZQScore+ITScore)/8;
//        mapAvg.put(DEPARTMENT_SCORE,df.format(allAvg));//部门成绩的平均分

        /**
         * 各维度的部门平均分
         * <AUTHOR>
         */
        mapAvg.put("department", "平均分");
        mapAvg.put(DIMENSION_PL, df.format((CPL + DPL + RPL + SPL + IPL + ZPL + BPL + GPL + KPL) / 9));//专业水平的部门平均分
        mapAvg.put(DIMENSION_PS, df.format((CPS + DPS + RPS + SPS + IPS + ZPS + BPS + GPS + KPS) / 9));//问题解决的部门平均分
        mapAvg.put(DIMENSION_TF, df.format((CTF + DTF + RTF + STF + ITF + ZTF + BTF + GTF + KTF) / 9));//及时反馈的部门平均分
        mapAvg.put(DIMENSION_WE, df.format((CWE + DWE + RWE + SWE + IWE + ZWE + BWE + GWE + KWE) / 9));//工作效率的部门平均分
        mapAvg.put(DIMENSION_CA, df.format((CCA + DCA + RCA + SCA + ICA + ZCA + BCA + GCA + KCA) / 9));//咨询解答的部门平均分
        mapAvg.put(DIMENSION_SC, df.format((CSC + DSC + RSC + SSC + ISC + ZSC + BSC + GSC + KSC) / 9));//服务意识的部门平均分
        mapAvg.put(DIMENSION_PG, df.format((CPG + DPG + RPG + SPG + IPG + ZPG + BPG + GPG + KPG) / 9));//政策指导的部门平均分
        mapAvg.put(DIMENSION_PN, df.format((CPN + DPN + RPN + SPN + IPN + ZPN + BPN + GPN + KPN) / 9));//计划性的部门平均分
        mapAvg.put(DIMENSION_OE, df.format((COE + DOE + ROE + SOE + IOE + ZOE + BOE + GOE + KOE) / 9));//总体评价的部门平均分
        //所有部门成绩的平均分
        Double allAvg;
        allAvg = (CWBScore + DQGHScore + HRScore + MKScore + ZHScore + ZQScore + ITScore + GHScore + KHFWBScore) / 9;
        mapAvg.put(DEPARTMENT_SCORE, df.format(allAvg));//部门成绩的平均分

        endResult.add(mapCwb);
        endResult.add(mapDqgh);
        endResult.add(mapRlzy);
        endResult.add(mapScjy);
        //endResult.add(mapKhfwzx);
        endResult.add(mapInternet);
        endResult.add(mapZqkehb);
        endResult.add(mapZhb);
        endResult.add(mapGh);
        endResult.add(mapKhfwb);
        endResult.add(mapAvg);

        return endResult;
       /* List<Map<String, Object>> endResult = Lists.newArrayList();
        List<String> codeList = Lists.newArrayList();
        for (ExamQuestion examQuestion : allNoPage) {
            String questionCode = examQuestion.getQuestionCode();
            codeList.add(questionCode);
        }
        for (String code : codeList) {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("questionCode", code);
            String dataSql = "select round(avg (equ.answer_score),2) as \"average\" from us_exam_question_user equ " +
                    "where equ.question_code =:questionCode " +
                    "and equ.annual_quarter_code=:annualQuarterCode "+
                    "and equ.answer_score is not null";

            List<Map<String, Object>> resultList = dynamicWhere.queryNamedParameterForList(dataSql, paramMap);
            for (Map<String, Object> result : resultList) {
                result.put("questionCode", code);
            }
            endResult.addAll(resultList);
        }
        return endResult;*/
    }

    /**
     * 功能描述  县分公司的部门平均分以及其百分值统计
     *
     * @date 2021/6/9
     */
    @Override
    public List<Map<String, Object>> countyBranchStatisticsDepartmentsAvg(String annualQuarterCode) {
        List<Map<String, Object>> avgAndPercentageValue = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数格式
        List<Map<String, Object>> countyBranchStatistics = this.countyBranchStatistics(annualQuarterCode);
        BigDecimal zero = new BigDecimal(0);
        //统计各部门的平均值以及其百分值

        //财务部
        Map<String, Object> mapCwb = countyBranchStatistics.get(0);
        Map<String, Object> financeDepartment = new HashMap<>();
        financeDepartment.put("Avgs", mapCwb.get(DEPARTMENT_SCORE) == null ? zero : mapCwb.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialCwb = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapCwb, DEPARTMENT_SCORE));
        financeDepartment.put("Centennial", calculateCentennialCwb.get("Centennial") == null ? zero : calculateCentennialCwb.get("Centennial"));
        financeDepartment.put("department", mapCwb.get("department"));
        //获取百分制的分数
        BigDecimal financeCentennial = (BigDecimal) financeDepartment.get("Centennial");

        //党委办公室
        Map<String, Object> mapDqgh = countyBranchStatistics.get(1);
        Map<String, Object> partyTradeUnion = new HashMap<>();
        partyTradeUnion.put("Avgs", mapDqgh.get(DEPARTMENT_SCORE) == null ? zero : mapDqgh.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialDqgh = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapDqgh, DEPARTMENT_SCORE));
        partyTradeUnion.put("Centennial", calculateCentennialDqgh.get("Centennial") == null ? zero : calculateCentennialDqgh.get("Centennial"));
        partyTradeUnion.put("department", mapDqgh.get("department"));
        //获取百分制的分数
        BigDecimal partyCentennial = (BigDecimal) partyTradeUnion.get("Centennial");

        //人力资源部
        Map<String, Object> mapRlzy = countyBranchStatistics.get(2);
        Map<String, Object> humanResourcesDepartment = new HashMap<>();
        humanResourcesDepartment.put("Avgs", mapRlzy.get(DEPARTMENT_SCORE) == null ? zero : mapRlzy.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialRlzy = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapRlzy, DEPARTMENT_SCORE));
        humanResourcesDepartment.put("Centennial", calculateCentennialRlzy.get("Centennial") == null ? zero : calculateCentennialRlzy.get("Centennial"));
        humanResourcesDepartment.put("department",  mapRlzy.get("department"));
        //获取百分制的分数
        BigDecimal humanResourcesCentennial = (BigDecimal) humanResourcesDepartment.get("Centennial");

        //市场经营部
        Map<String, Object> mapScjy = countyBranchStatistics.get(3);
        Map<String, Object> marketingDepartment = new HashMap<>();
        marketingDepartment.put("Avgs", mapScjy.get(DEPARTMENT_SCORE) == null ? zero : mapScjy.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialScjy = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapScjy, DEPARTMENT_SCORE));
        marketingDepartment.put("Centennial", calculateCentennialScjy.get("Centennial") == null ? zero : calculateCentennialScjy.get("Centennial"));
        marketingDepartment.put("department", mapScjy.get("department"));
        //获取百分制的分数
        BigDecimal marketingCentennial = (BigDecimal) marketingDepartment.get("Centennial");

        //网络部
        Map<String, Object> mapInternet = countyBranchStatistics.get(4);
        Map<String, Object> networkDepartment = new HashMap<>();
        networkDepartment.put("Avgs", mapInternet.get(DEPARTMENT_SCORE) == null ? zero : mapInternet.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialInternet = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapInternet, DEPARTMENT_SCORE));
        networkDepartment.put("Centennial", calculateCentennialInternet.get("Centennial") == null ? zero : calculateCentennialInternet.get("Centennial"));
        networkDepartment.put("department", mapInternet.get("department"));
        //获取百分制的分数
        BigDecimal networkCentennial = (BigDecimal) networkDepartment.get("Centennial");

        //政企客服部
        Map<String, Object> mapZqkehb = countyBranchStatistics.get(5);
        Map<String, Object> governmentEnterpriseCustomerDepartment = new HashMap<>();
        governmentEnterpriseCustomerDepartment.put("Avgs", mapZqkehb.get(DEPARTMENT_SCORE) == null ? zero : mapZqkehb.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialZqkehb = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapZqkehb, DEPARTMENT_SCORE));
        governmentEnterpriseCustomerDepartment.put("Centennial", calculateCentennialZqkehb.get("Centennial") == null ? zero : calculateCentennialZqkehb.get("Centennial"));
        governmentEnterpriseCustomerDepartment.put("department", mapZqkehb.get("department"));
        //获取百分制的分数
        BigDecimal governmentCentennial = (BigDecimal) governmentEnterpriseCustomerDepartment.get("Centennial");

        //综合部
        Map<String, Object> mapZhb = countyBranchStatistics.get(6);
//        Map<String, Object> general = examAnswerNotesRepository.generalDepartment(annualQuarterCode);
        Map<String, Object> generalDepartment = new HashMap<>();
        generalDepartment.put("Avgs", mapZhb.get(DEPARTMENT_SCORE) == null ? zero : mapZhb.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialZhb = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapZhb, DEPARTMENT_SCORE));
        generalDepartment.put("Centennial", calculateCentennialZhb.get("Centennial") == null ? zero : calculateCentennialZhb.get("Centennial"));
        generalDepartment.put("department", mapZhb.get("department"));
        //获取百分制的分数
        BigDecimal generalCentennial = (BigDecimal) generalDepartment.get("Centennial");

        //工会
        Map<String, Object> mapGh = countyBranchStatistics.get(7);
        Map<String, Object> laborUnionDepartment = new HashMap<>();
        laborUnionDepartment.put("Avgs", mapGh.get(DEPARTMENT_SCORE) == null ? zero : mapGh.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialGh = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapGh, DEPARTMENT_SCORE));
        laborUnionDepartment.put("Centennial", calculateCentennialGh.get("Centennial") == null ? zero : calculateCentennialGh.get("Centennial"));
        laborUnionDepartment.put("department", mapGh.get("department"));
        //获取百分制的分数
        BigDecimal laborUnionCentennial = (BigDecimal) laborUnionDepartment.get("Centennial");

        //客户服务部
        Map<String, Object> mapKhfwb = countyBranchStatistics.get(8);
//        Map<String, Object> clientele = examAnswerNotesRepository.clienteleDepartment(annualQuarterCode);
        Map<String, Object> clienteleDepartment = new HashMap<>();
        clienteleDepartment.put("Avgs", mapKhfwb.get(DEPARTMENT_SCORE) == null ? zero : mapKhfwb.get(DEPARTMENT_SCORE));
        Map<String, Object> calculateCentennialKhfwb = examAnswerNotesRepository.calculateCentennial(MapUtil.getStr(mapKhfwb, DEPARTMENT_SCORE));
        clienteleDepartment.put("Centennial", calculateCentennialKhfwb.get("Centennial") == null ? zero : calculateCentennialKhfwb.get("Centennial"));
        clienteleDepartment.put("department", mapKhfwb.get("department"));
        //获取百分制的分数
        BigDecimal clienteleCentennial = (BigDecimal) clienteleDepartment.get("Centennial");

        HashMap<String, Object> allAvg = new HashMap<>();//用于存储各部门平均分的平均分
        allAvg.put("department", "平均成绩");
        BigDecimal avgs;
        //获取各部门的平均分
//        BigDecimal financeAvgs = (BigDecimal) financeDepartment.get("Avgs");
//        BigDecimal partyAvgs = (BigDecimal) partyTradeUnion.get("Avgs");
//        BigDecimal humanAvgs = (BigDecimal) humanResourcesDepartment.get("Avgs");
//        BigDecimal marketingAvgs = (BigDecimal) marketingDepartment.get("Avgs");
//        BigDecimal customerAvgs = (BigDecimal) customerServiceCenter.get("Avgs");
//        BigDecimal networkAvgs = (BigDecimal) networkDepartment.get("Avgs");
//        BigDecimal governmentAvgs = (BigDecimal) governmentEnterpriseCustomerDepartment.get("Avgs");
//        BigDecimal generalAvgs = (BigDecimal) generalDepartment.get("Avgs");
//        BigDecimal laborUnionAvgs = (BigDecimal) laborUnionDepartment.get("Avgs");
//        BigDecimal clienteleAvgs = (BigDecimal) clienteleDepartment.get("Avgs");
        BigDecimal bigDecimalCentennial = new BigDecimal(BigDecimal.ROUND_UP);

        BigDecimal AllScore = bigDecimalCentennial.add(financeCentennial)
                .add(partyCentennial)
                .add(humanResourcesCentennial)
                .add(marketingCentennial)
//                .add(customerCentennial)
                .add(networkCentennial)
                .add(governmentCentennial)
                .add(generalCentennial)
                .add(laborUnionCentennial)
                .add(clienteleCentennial);
        int i = 9;
        BigDecimal departmentCount = new BigDecimal(i);
        BigDecimal CentennialAvg = AllScore.divide(departmentCount,BigDecimal.ROUND_UP); //百分值的部门成绩的平均分
        String Centennial = df.format(CentennialAvg);

//        BigDecimal bigDecimalAvgs = new BigDecimal(BigDecimal.ROUND_UP);
//        BigDecimal allAvgs = bigDecimalAvgs.add(financeAvgs)
//                .add(partyAvgs)
//                .add(humanAvgs)
//                .add(marketingAvgs)
////                .add(customerAvgs)
//                .add(networkAvgs)
//                .add(governmentAvgs)
//                .add(generalAvgs)
//                .add(laborUnionAvgs)
//                .add(clienteleAvgs);
//
//        BigDecimal Avgs = allAvgs.divide(departmentCount,BigDecimal.ROUND_UP);
//        String Avg1 = df.format(Avgs);
//        BigDecimal Avg = new BigDecimal(Avg1);
//        allAvg.put("Avgs", Avg);
        Map<String, Object> avgMap = countyBranchStatistics.get(9);
        allAvg.put("Avgs", MapUtil.getStr(avgMap,DEPARTMENT_SCORE));

        //    avgs=(financeAvgs+partyAvgs+humanAvgs+marketingAvgs+customerAvgs+networkAvgs+generalAvgs+governmentAvgs)/8;//各部门平均分的平均分

        //各部门百分值的平均值
        //  Centennial=(financeCentennial+partyCentennial+humanResourcesCentennial+marketingCentennial+customerCentennial+networkCentennial+governmentCentennial+generalCentennial)/8;
        allAvg.put("Centennial", Centennial);

        avgAndPercentageValue.add(financeDepartment);
        avgAndPercentageValue.add(partyTradeUnion);
//        avgAndPercentageValue.add(customerServiceCenter);
        avgAndPercentageValue.add(humanResourcesDepartment);
        avgAndPercentageValue.add(marketingDepartment);
        avgAndPercentageValue.add(networkDepartment);
        avgAndPercentageValue.add(governmentEnterpriseCustomerDepartment);
        avgAndPercentageValue.add(generalDepartment);
        avgAndPercentageValue.add(laborUnionDepartment);
        avgAndPercentageValue.add(clienteleDepartment);
        /**
         * 根据百分值分数进行排名
         * <AUTHOR>
         * @date 2021/6/10
         */
        List<Map<String, Object>> collect = avgAndPercentageValue.stream().sorted(Comparator.comparing(ExamAnswerNotesServiceImpl::comparingByKpScore).reversed()).collect(Collectors.toList());

        int j = 1;
        for (Map<String, Object> map : collect) {
            if (j > collect.size()) {
                break;
            } else {
                map.put("rank", j);
                j++;
            }
        }
        avgAndPercentageValue.add(allAvg);
        return avgAndPercentageValue;
    }

    /**
     * 功能描述 统计汇总表表头
     *
     * @param
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 2021/7/1
     */
    @Override
    public List<Map<String, Object>> statisticalSummaryTitle(String annualQuarterCode) {
        //根据洛阳下拉框选择的机关考试年度季度编码来获取县分公司考试的年度季度编码
        String subAnnualQuarterCode = annualQuarterCode.substring(4, 14);
        ExamAnnualQuarterInfo lYJGAnnualQuarterInfo = iExamAnnualQuarterInfoService.findAnnualQuarterInfo3("%" + annualQuarterCode + "%");
        //获取年度季度信息字段
        String annualQuarterInfo = lYJGAnnualQuarterInfo.getAnnualQuarterInfo();
        String LYXFAnnualQuarterCode = "lyxf" + subAnnualQuarterCode;
        ExamAnnualQuarterInfo LYXFAnnualQuarterInfo = iExamAnnualQuarterInfoService.findAnnualQuarterInfo3("%" + LYXFAnnualQuarterCode + "%");
        String LYXFAnnualQuarterCode2 = LYXFAnnualQuarterInfo.getAnnualQuarterCode();
        String lYXFAnnualQuarterInfo = LYXFAnnualQuarterInfo.getAnnualQuarterInfo();
        //获取机关与县分的统计结果

        List<Map<String, Object>> titleArrayList = new ArrayList<>();
        Map<String, Object> hashMap = new HashMap<>();
        String substring = annualQuarterInfo.substring(0, 9);

        String annualQuarterInfoLYJG = substring + "服务支撑满意度得分(县区评价)";
        String annualQuarterInfoLYXF = substring + "协同满意度得分(机关互评)";
        String annualQuarter = substring + "协作满意度总得分";
        hashMap.put("department", "部门");
        hashMap.put("lyxfAvgs", annualQuarterInfoLYJG);
        hashMap.put("lyjgAvgs", annualQuarterInfoLYXF);
        hashMap.put("Quarter", annualQuarter);
        hashMap.put("Centennial", "百分制成绩");
        hashMap.put("rank", "排名");
        titleArrayList.add(hashMap);
        return titleArrayList;
    }

    /**
     * 功能描述 统计汇总表
     *
     * @param
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 2021/7/1
     */
    @Override
    public List<Map<String, Object>> statisticalSummary(String annualQuarterCode) {
        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数格式
        BigDecimal two = new BigDecimal(2);
        BigDecimal twenty = new BigDecimal(20);
        //根据洛阳下拉框选择的机关考试年度季度编码来获取县分公司考试的年度季度编码
        String subAnnualQuarterCode = annualQuarterCode.substring(4, 14);
        ExamAnnualQuarterInfo lYJGAnnualQuarterInfo = iExamAnnualQuarterInfoService.findAnnualQuarterInfo3("%" + annualQuarterCode + "%");
        //获取年度季度信息字段
        String annualQuarterInfo = lYJGAnnualQuarterInfo.getAnnualQuarterInfo();
        String LYXFAnnualQuarterCode = "lyxf" + subAnnualQuarterCode;
        ExamAnnualQuarterInfo LYXFAnnualQuarterInfo = iExamAnnualQuarterInfoService.findAnnualQuarterInfo3("%" + LYXFAnnualQuarterCode + "%");
        String LYXFAnnualQuarterCode2 = LYXFAnnualQuarterInfo.getAnnualQuarterCode();
        String lYXFAnnualQuarterInfo = LYXFAnnualQuarterInfo.getAnnualQuarterInfo();
        //获取机关与县分的统计结果
        List<Map<String, Object>> LYJGList = statisticsDepartmentsAvg(annualQuarterCode);
            List<Map<String, Object>> LYXFList = countyBranchStatisticsDepartmentsAvg(LYXFAnnualQuarterCode2);

        List<Map<String, Object>> mapArrayList = new ArrayList<>();
        //  List<Map<String, Object>> titleArrayList = new ArrayList<>();
        Map<String, Object> hashMap = new HashMap<>();
        String substring = annualQuarterInfo.substring(0, 9);

      /*  String annualQuarterInfoLYJG=substring+"服务支撑满意度得分(县区评价)";
        String annualQuarterInfoLYXF = substring + "协同满意度得分(机关互评)";
        String annualQuarter = substring + "协作满意度总得分";
        hashMap.put("department","部门");
        hashMap.put("lyxfAvgs",annualQuarterInfoLYJG);
        hashMap.put("lyjgAvgs",annualQuarterInfoLYXF);
        hashMap.put("Quarter",annualQuarter);
        hashMap.put("Centennial","百分制成绩");
        hashMap.put("rank","排名");
        titleArrayList.add(hashMap);*/

        List<Map<String, Object>> list = new ArrayList<>();
        list.addAll(LYXFList);

        list.addAll(LYJGList);

        Map<String, Object> CWBMap = Maps.newHashMap();//存储财务部的相关数据
        Map<String, Object> lyxfCWBmap = list.get(0);//县分财务部数据
        Map<String, Object> lyjgCWBmap = list.get(10);//机关财务部数据
//        BigDecimal lyxfCWBavgs = (BigDecimal) lyxfCWBmap.get("Avgs");
//        BigDecimal lyjgCWBavgs = (BigDecimal) lyjgCWBmap.get("Avgs");

        BigDecimal lyxfCWBavgs = new BigDecimal(MapUtil.getStr(lyxfCWBmap, "Avgs"));
        BigDecimal lyjgCWBavgs = new BigDecimal(MapUtil.getStr(lyjgCWBmap, "Avgs"));
        CWBMap.put("department", lyxfCWBmap.get("department"));
        CWBMap.put("lyxfAvgs", lyxfCWBavgs);
        CWBMap.put("lyjgAvgs", lyjgCWBavgs);
        BigDecimal decimalCWB = lyxfCWBavgs.add(lyjgCWBavgs);
        BigDecimal bigDecimalCWB = decimalCWB.divide(two);//县分财务部得分与机关财务部得分的平均分
        String bigDecimalCWB2 = df.format(bigDecimalCWB);
        BigDecimal CentennialCWB = bigDecimalCWB.multiply(twenty);//县分财务部得分与机关财务部得分的平均分的百分制得分
        CWBMap.put("Quarter", bigDecimalCWB2);
        String CentennialCWB2 = df.format(CentennialCWB);
        CWBMap.put("Centennial", CentennialCWB2);

        Map<String, Object> DQGHMap = new HashMap<>();//存储党群工会的相关数据
        Map<String, Object> lyxfDQGHmap = list.get(1);//县分党群工会数据
        Map<String, Object> lyjgDQGHmap = list.get(11);//机关党群工会数据
//        BigDecimal lyxfDQGHavgs = (BigDecimal) lyxfDQGHmap.get("Avgs");
//        BigDecimal lyjgDQGHavgs = (BigDecimal) lyjgDQGHmap.get("Avgs");
        BigDecimal lyxfDQGHavgs = new BigDecimal(MapUtil.getStr(lyxfDQGHmap, "Avgs"));
        BigDecimal lyjgDQGHavgs = new BigDecimal(MapUtil.getStr(lyjgDQGHmap, "Avgs"));
        DQGHMap.put("department", lyxfDQGHmap.get("department"));
        DQGHMap.put("lyxfAvgs", lyxfDQGHavgs);
        DQGHMap.put("lyjgAvgs", lyjgDQGHavgs);
        BigDecimal decimalDQGH = lyxfDQGHavgs.add(lyjgDQGHavgs);
        BigDecimal bigDecimalDQGH = decimalDQGH.divide(two);//县分党群工会得分与党群工会得分的平均分
        String bigDecimalDQGH2 = df.format(bigDecimalDQGH);
        BigDecimal CentennialDQGH = bigDecimalDQGH.multiply(twenty);
        DQGHMap.put("Quarter", bigDecimalDQGH2);
        String CentennialDQGH2 = df.format(CentennialDQGH);
        DQGHMap.put("Centennial", CentennialDQGH2);

//        Map<String, Object> KHFUMap = new HashMap<>();//存储客户服务中心的相关数据
//        Map<String, Object> lyxfKHFUmap = list.get(2);//县分客户服务中心数据
//        Map<String, Object> lyjgKHFUmap = list.get(11);//机关客户服务中心数据
//        BigDecimal lyxfKHFUavgs = (BigDecimal) lyxfKHFUmap.get("Avgs");
//        BigDecimal lyjgKHFUavgs = (BigDecimal) lyjgKHFUmap.get("Avgs");
//        KHFUMap.put("department",lyxfKHFUmap.get("department"));
//        KHFUMap.put("lyxfAvgs",lyxfKHFUavgs);
//        KHFUMap.put("lyjgAvgs",lyjgKHFUavgs);
//        BigDecimal decimalKHFU = lyxfKHFUavgs.add(lyjgKHFUavgs);
//        BigDecimal bigDecimalKHFU = decimalKHFU.divide(two);
//        String bigDecimalKHFU2 = df.format(bigDecimalKHFU);
//        BigDecimal CentennialKHFU = bigDecimalKHFU.multiply(twenty);
//        KHFUMap.put("Quarter",bigDecimalKHFU2);
//        String CentennialKHFU2 = df.format(CentennialKHFU);
//        KHFUMap.put("Centennial",CentennialKHFU2);

        Map<String, Object> HRMap = new HashMap<>();//存储人力资源部的相关数据
        Map<String, Object> lyxfHRmap = list.get(2);//县分人力资源部数据
        Map<String, Object> lyjgHRmap = list.get(12);//机关人力资源部数据
//        BigDecimal lyxfHRavgs = (BigDecimal) lyxfHRmap.get("Avgs");
//        BigDecimal lyjgHRavgs = (BigDecimal) lyjgHRmap.get("Avgs");
        BigDecimal lyxfHRavgs = new BigDecimal(MapUtil.getStr(lyxfHRmap, "Avgs"));
        BigDecimal lyjgHRavgs = new BigDecimal(MapUtil.getStr(lyjgHRmap, "Avgs"));
        HRMap.put("department", lyxfHRmap.get("department"));
        HRMap.put("lyxfAvgs", lyxfHRavgs);
        HRMap.put("lyjgAvgs", lyjgHRavgs);
        BigDecimal decimalHR = lyxfHRavgs.add(lyjgHRavgs);
        BigDecimal QuarterHR = decimalHR.divide(two);
        String QuarterHR2 = df.format(QuarterHR);
        BigDecimal CentennialHR = QuarterHR.multiply(twenty);
        HRMap.put("Quarter", QuarterHR2);
        String CentennialHR2 = df.format(CentennialHR);
        HRMap.put("Centennial", CentennialHR2);

        Map<String, Object> MKMap = new HashMap<>();//存储市场部的相关数据
        Map<String, Object> lyxfMKmap = list.get(3);//县分市场部数据
        Map<String, Object> lyjgMKmap = list.get(13);//机关市场部数据
//        BigDecimal lyxfMKavgs = (BigDecimal) lyxfMKmap.get("Avgs");
//        BigDecimal lyjgMKavgs = (BigDecimal) lyjgMKmap.get("Avgs");
        BigDecimal lyxfMKavgs = new BigDecimal(MapUtil.getStr(lyxfMKmap, "Avgs"));
        BigDecimal lyjgMKavgs = new BigDecimal(MapUtil.getStr(lyjgMKmap, "Avgs"));
        MKMap.put("department", lyxfMKmap.get("department"));
        MKMap.put("lyxfAvgs", lyxfMKavgs);
        MKMap.put("lyjgAvgs", lyjgMKavgs);
        BigDecimal decimalMK = lyxfMKavgs.add(lyjgMKavgs);
        BigDecimal QuarterMK = decimalMK.divide(two);
        String QuarterMK2 = df.format(QuarterMK);
        BigDecimal CentennialMK = QuarterMK.multiply(twenty);
        MKMap.put("Quarter", QuarterMK2);
        String CentennialMK2 = df.format(CentennialMK);
        MKMap.put("Centennial", CentennialMK2);

        Map<String, Object> ITMap = new HashMap<>();//存储网络部的相关数据
        Map<String, Object> lyxfITmap = list.get(4);//县分网络部数据
        Map<String, Object> lyjgITmap = list.get(14);//机关网络部数据
//        BigDecimal lyxfITavgs = (BigDecimal) lyxfITmap.get("Avgs");
//        BigDecimal lyjgITavgs = (BigDecimal) lyjgITmap.get("Avgs");
        BigDecimal lyxfITavgs = new BigDecimal(MapUtil.getStr(lyxfITmap, "Avgs"));
        BigDecimal lyjgITavgs = new BigDecimal(MapUtil.getStr(lyjgITmap, "Avgs"));
        ITMap.put("department", lyxfITmap.get("department"));
        ITMap.put("lyxfAvgs", lyxfITavgs);
        ITMap.put("lyjgAvgs", lyjgITavgs);
        BigDecimal decimalIT = lyxfITavgs.add(lyjgITavgs);
        BigDecimal QuarterIT = decimalIT.divide(two);
        String QuarterIT2 = df.format(QuarterIT);
        BigDecimal CentennialIT = QuarterIT.multiply(twenty);
        ITMap.put("Quarter", QuarterIT2);
        String CentennialIT2 = df.format(CentennialIT);
        ITMap.put("Centennial", CentennialIT2);

        Map<String, Object> ZQMap = new HashMap<>();//存储政企部的相关数据
        Map<String, Object> lyxfZQmap = list.get(5);//县分政企部数据
        Map<String, Object> lyjgZQmap = list.get(15);//机关政企部数据
//        BigDecimal lyxfZQavgs = (BigDecimal) lyxfZQmap.get("Avgs");
//        BigDecimal lyjgZQavgs = (BigDecimal) lyjgZQmap.get("Avgs");
        BigDecimal lyxfZQavgs = new BigDecimal(MapUtil.getStr(lyxfZQmap, "Avgs"));
        BigDecimal lyjgZQavgs = new BigDecimal(MapUtil.getStr(lyjgZQmap, "Avgs"));
        ZQMap.put("department", lyxfZQmap.get("department"));
        ZQMap.put("lyxfAvgs", lyxfZQavgs);
        ZQMap.put("lyjgAvgs", lyjgZQavgs);
        BigDecimal decimalZQ = lyxfZQavgs.add(lyjgZQavgs);
        BigDecimal QuarterZQ = decimalZQ.divide(two);
        String QuarterZQ2 = df.format(QuarterZQ);
        BigDecimal CentennialZQ = QuarterZQ.multiply(twenty);
        ZQMap.put("Quarter", QuarterZQ2);
        String CentennialZQ2 = df.format(CentennialZQ);
        ZQMap.put("Centennial", CentennialZQ2);

        Map<String, Object> ZHMap = new HashMap<>();//存储综合部的相关数据
        Map<String, Object> lyxfZHmap = list.get(6);//县分综合部数据
        Map<String, Object> lyjgZHmap = list.get(16);//机关综合部数据
//        BigDecimal lyxfZHavgs = (BigDecimal) lyxfZHmap.get("Avgs");
//        BigDecimal lyjgZHavgs = (BigDecimal) lyjgZHmap.get("Avgs");
        BigDecimal lyxfZHavgs = new BigDecimal(MapUtil.getStr(lyxfZHmap, "Avgs"));
        BigDecimal lyjgZHavgs = new BigDecimal(MapUtil.getStr(lyjgZHmap, "Avgs"));
        ZHMap.put("department", lyxfZHmap.get("department"));
        ZHMap.put("lyxfAvgs", lyxfZHavgs);
        ZHMap.put("lyjgAvgs", lyjgZHavgs);
        BigDecimal decimalZH = lyxfZHavgs.add(lyjgZHavgs);
        BigDecimal QuarterZH = decimalZH.divide(two);
        String QuarterZH2 = df.format(QuarterZH);
        BigDecimal CentennialZH = QuarterZH.multiply(twenty);
        ZHMap.put("Quarter", QuarterZH2);
        String CentennialZH2 = df.format(CentennialZH);
        ZHMap.put("Centennial", CentennialZH2);

        Map<String, Object> GHMap = new HashMap<>();//存储工会的相关数据
        Map<String, Object> lyxfGHmap = list.get(7);//县分人力资源部数据
        Map<String, Object> lyjgGHmap = list.get(17);//机关人力资源部数据
//        BigDecimal lyxfGHavgs = (BigDecimal) lyxfGHmap.get("Avgs");
//        BigDecimal lyjgGHavgs = (BigDecimal) lyjgGHmap.get("Avgs");
        BigDecimal lyxfGHavgs = new BigDecimal(MapUtil.getStr(lyxfGHmap, "Avgs"));
        BigDecimal lyjgGHavgs = new BigDecimal(MapUtil.getStr(lyjgGHmap, "Avgs"));
        GHMap.put("department", lyxfGHmap.get("department"));
        GHMap.put("lyxfAvgs", lyxfGHavgs);
        GHMap.put("lyjgAvgs", lyjgGHavgs);
        BigDecimal decimalGH = lyxfGHavgs.add(lyjgGHavgs);
        BigDecimal QuarterGH = decimalGH.divide(two);
        String QuarterGH2 = df.format(QuarterGH);
        BigDecimal CentennialGH = QuarterGH.multiply(twenty);
        GHMap.put("Quarter", QuarterGH2);
        String CentennialGH2 = df.format(CentennialGH);
        GHMap.put("Centennial", CentennialGH2);

        Map<String, Object> KHFWBMap = new HashMap<>();//存储工会的相关数据
        Map<String, Object> lyxfKHFWBmap = list.get(8);//县分人力资源部数据
        Map<String, Object> lyjgKHFWBmap = list.get(18);//机关人力资源部数据
//        BigDecimal lyxfKHFWBavgs = (BigDecimal) lyxfKHFWBmap.get("Avgs");
//        BigDecimal lyjgKHFWBavgs = (BigDecimal) lyjgKHFWBmap.get("Avgs");
        BigDecimal lyxfKHFWBavgs = new BigDecimal(MapUtil.getStr(lyxfKHFWBmap, "Avgs"));
        BigDecimal lyjgKHFWBavgs = new BigDecimal(MapUtil.getStr(lyjgKHFWBmap, "Avgs"));
        KHFWBMap.put("department", lyxfKHFWBmap.get("department"));
        KHFWBMap.put("lyxfAvgs", lyxfKHFWBavgs);
        KHFWBMap.put("lyjgAvgs", lyjgKHFWBavgs);
        BigDecimal decimalKHFWB = lyxfKHFWBavgs.add(lyjgKHFWBavgs);
        BigDecimal QuarterKHFWB = decimalKHFWB.divide(two);
        String QuarterKHFWB2 = df.format(QuarterKHFWB);
        BigDecimal CentennialKHFWB = QuarterKHFWB.multiply(twenty);
        KHFWBMap.put("Quarter", QuarterKHFWB2);
        String CentennialKHFWB2 = df.format(CentennialKHFWB);
        KHFWBMap.put("Centennial", CentennialKHFWB2);

        Map<String, Object> AVGMap = new HashMap<>();//存储人力资源部的相关数据
        Map<String, Object> lyxfAVGmap = list.get(9);//县分人力资源部数据
        Map<String, Object> lyjgAVGmap = list.get(19);//机关人力资源部数据
//        BigDecimal lyxfAVGavgs = (BigDecimal) lyxfAVGmap.get("Avgs");
//        BigDecimal lyjgAVGavgs = (BigDecimal) lyjgAVGmap.get("Avgs");
        BigDecimal lyxfAVGavgs = new BigDecimal(MapUtil.getStr(lyxfAVGmap, "Avgs"));
        BigDecimal lyjgAVGavgs = new BigDecimal(MapUtil.getStr(lyjgAVGmap, "Avgs"));
        AVGMap.put("department",lyxfAVGmap.get("department"));
        AVGMap.put("lyxfAvgs",lyxfAVGavgs);
        AVGMap.put("lyjgAvgs",lyjgAVGavgs);
        BigDecimal decimalAVG = lyxfAVGavgs.add(lyjgAVGavgs);
        BigDecimal QuarterAVG = decimalAVG.divide(two);
        String QuarterAVG2 = df.format(QuarterAVG);
        BigDecimal CentennialAVG = QuarterAVG.multiply(twenty);
        AVGMap.put("Quarter",QuarterAVG2);
        String CentennialAVG2 = df.format(CentennialAVG);
        AVGMap.put("Centennial",CentennialAVG2);

        mapArrayList.add(CWBMap);
        mapArrayList.add(DQGHMap);
        //mapArrayList.add(KHFUMap);
        mapArrayList.add(HRMap);
        mapArrayList.add(MKMap);
        mapArrayList.add(ITMap);
        mapArrayList.add(ZQMap);
        mapArrayList.add(ZHMap);
        mapArrayList.add(GHMap);
        mapArrayList.add(KHFWBMap);

        List<Map<String, Object>> collect = mapArrayList.stream().sorted(Comparator.comparing(ExamAnswerNotesServiceImpl::comparingByKpScore).reversed()).collect(Collectors.toList());

        int j = 1;
        for (Map<String, Object> map : collect) {
            if (j > collect.size()) {
                break;
            } else {
                map.put("rank", j);
                j++;
            }
        }

        mapArrayList.add(AVGMap);

    /*    for (Map<String, Object> map : mapArrayList) {
            titleArrayList.add(map);
        }
        return titleArrayList;*/
        return mapArrayList;
    }

    /**
     * 根据当前人判断是否答过题目，去判断是否需要推送统一待办
     */
    @Override
    public ExamQuestionUser findByCreator(String creator, String annualQuarterCode) {
        return examAnswerNotesRepository.findByCreator(creator, annualQuarterCode);
    }
}
