package com.simbest.boot.exam.examOnline.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: ExamStatisticsExcel
 * @projectName exam
 * @description:
 * @date 2021/7/1  10:27
 */

@Data
public class ExamStatisticsExcel {

    @ExcelVOAttribute( name = "部门", column = "A" )
    @Excel(name =  "部门",width = 50)
    private String department;

    @ExcelVOAttribute(name = "服务支撑满意度得分（县区评价）",column = "B" )
    @Excel(name =  "服务支撑满意度得分（县区评价）",width = 80)
    private BigDecimal lyxfAvgs;

    @ExcelVOAttribute(name = "协同满意度得分（机关互评）",column = "C" )
    @Excel(name =  "协同满意度得分（机关互评）",width = 45)
    private BigDecimal lyjgAvgs;

    @ExcelVOAttribute(name = "协作满意度总得分",column = "D" )
    @Excel(name =  "协作满意度总得分",width = 45)
    private BigDecimal Quarter;

    @ExcelVOAttribute(name = "百分制成绩",column = "E" )
    @Excel(name =  "百分制成绩",width = 35)
    private String Centennial;

    @ExcelVOAttribute(name = "排名",column = "F" )
    @Excel(name =  "排名",width = 30)
    private Integer rank;
}
