<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>简报派发</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <style>
        .panel.combo-p {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #e6e6e6;
        }
        .combo-panel.panel-body.panel-body-noheader {
            border: none;
        }
    </style>
    <script type="text/javascript">
        $(function () {
            ajaxgeneral({
                url:"action/organizationValue/findList",
                contentType:"application/json; charset=utf-8",
                success:function(datas){
                    // formval(datas.data,"#updateDistributionTableAddForm");

                    console.log("返回的组织数据"+datas.data[0].userName);
                    $("#sender").val(datas.data[0].userName+"等"+(datas.data.length-1)+"人");
                }
            });
            // 加载表单
            loadForm("updateDistributionTableAddForm");
        });


        //表单校验
        window.fvalidate = function () {
            return $("#updateDistributionTableAddForm").form("validate");
        };

        //对表单中的元素进行校验，返回为0代表检验不成功。可以参考appManagementList.html
        window.getchoosedata = function () {
            formsubmit("updateDistributionTableAddForm","action/examDistribution/update");

            return {"data":'',"state":1};
        };

        //创建成功之后
        window.submitcallback = function () {
            top.dialogClose("updateDistribution");
            //(top.window.insertDistributionshowDialogTopF || top).window.insertDistributionTableLoad();
        };


    </script>
</head>
<body>



<!--scontentType获取数据时设置传参方式，不设置默认为kv  cmd-select获取数据命令配置，不配置自动从列表中取   cmd-insert新增  cmd-update修改-->
<div class="basic tab_table">

    <form id="updateDistributionTableAddForm" method="post" contentType="application/json; charset=utf-8"
          submitcallback="submitcallback()" initsystem="initsystem()">
        <input id="id" name="id" type="hidden"/>
        <fieldset class="title">
            <font class="">
                接收组织人员： <input  id="sender" name="sender"  />
            </font>
        </fieldset>
        <table border="0" cellpadding="0" cellspacing="10">
            <tr>
                <td width="10%" align="left">标题：</td>
                <td width="20%" length="100">
                    <input id="title" name="title" type="text" value="" class="easyui-validatebox"  style="width:  350px; height: 32px;"/>
                </td>
            </tr>

            <tr>
                <td width="10%" align="left">内容：</td>
                <td width="20%" length="100">
                    <textarea id="content" name="content"  class="easyui-validatebox">
                    </textarea>
                </td>
            </tr>
        </table>
    </form>
</div>
</body>
</html>
