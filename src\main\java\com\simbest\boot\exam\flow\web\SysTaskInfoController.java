package com.simbest.boot.exam.flow.web;

import cn.hutool.core.map.MapUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;

import com.simbest.boot.exam.flow.model.SysTaskInfo;
import com.simbest.boot.exam.flow.service.ISysTaskInfoService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: SysTaskInfoController
 * @description:
 * @author: ZHAOBO
 * @create: 2024-07-09 17:40
 */
@RestController
@RequestMapping(value = "/action/task")
@Slf4j
public class SysTaskInfoController extends LogicController<SysTaskInfo, String> {

    private ISysTaskInfoService service;

    public SysTaskInfoController(ISysTaskInfoService service) {
        super(service);
        this.service = service;
    }

    @ApiOperation(value = "查询我的待办")
    @PostMapping(value = {"/myTaskToDo", "/myTaskToDo/api", "/myTaskToDo/sso"})
    public JsonResponse myTaskToDo(@RequestParam(defaultValue = "1") Integer page,
                                   @RequestParam(defaultValue = "10") Integer rows,
                                   @RequestParam(defaultValue = "PC") String source,
                                   @RequestParam(required = false) String currentUserCode,
                                   @RequestBody(required = false) Map<String, Object> condition) {
        String title = MapUtil.getStr(condition, "title");
        String pmInsType = MapUtil.getStr(condition, "pmInsType");
        String pmInsId = MapUtil.getStr(condition, "pmInsId");
        String applyNumber = MapUtil.getStr(condition, "applyNumber");
        return service.myTaskToDo(page, rows, source, title, pmInsType, applyNumber, currentUserCode, pmInsId);
    }

    @ApiOperation(value = "查询我的已办")
    @PostMapping(value = {"/myJoin", "/myJoin/api", "/myJoin/sso"})
    public JsonResponse myJoin(@RequestParam(defaultValue = "1") Integer page,
                               @RequestParam(defaultValue = "10") Integer rows,
                               @RequestParam(defaultValue = "PC") String source,
                               @RequestParam(required = false) String currentUserCode,
                               @RequestBody(required = false) Map<String, Object> condition) {
        String title = MapUtil.getStr(condition, "title");
        String pmInsType = MapUtil.getStr(condition, "pmInsType");
        String applyNumber = MapUtil.getStr(condition, "applyNumber");
        return service.myJoin(page, rows, source, title, pmInsType, applyNumber, currentUserCode);
    }

    @ApiOperation(value = "已阅")
    @PostMapping(value = {"/done", "/done/api", "/done/sso"})
    public JsonResponse done(@RequestParam String taskId) {
        return service.done(taskId);
    }

    @ApiOperation(value = "根据工单id查询工单数量")
    @PostMapping(value = {"/findByPmInsId", "/findByPmInsId/api", "/findByPmInsId/sso"})
    public JsonResponse findByPmInsId(@RequestParam String pmInsId, @RequestParam String type) {
        List<SysTaskInfo> sysTaskInfos = service.findByPmInsId(pmInsId, type);
        return JsonResponse.success(sysTaskInfos);
    }
}
