/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.model.ExamSurvey;
import com.simbest.boot.exam.examOnline.model.ExamTask;
import org.springframework.data.repository.query.Param;

public interface IExamSurveyService extends ILogicService<ExamSurvey,String> {

    /**
     * 新增
     * @param examSurvey
     * @return
     */
    JsonResponse createExamSurvey(ExamSurvey examSurvey);

    /**
     * 修改
     * @param examSurvey
     * @return
     */
    JsonResponse updateExamSurvey(ExamSurvey examSurvey);

    /**
     * 删除
     * @param id
     * @return
     */
    JsonResponse deleteExamSurvey(String id);

    /**
     * 查询
     * @param id
     * @return
     */
    JsonResponse findByExamSurvey(String id);

    /**
     * 查询用户是否办理过
     * @param userName
     * @return
     */
    ExamSurvey findExamSurvey(String userName);


    /**
     * 提交接口
     * @param currentUserCode
     * @param source
     * @param examSurvey
     * @return
     */
    ExamInfo submitExamNew(String currentUserCode, String source,ExamSurvey examSurvey);

    /**
     *
     * @param pmInsId
     * @return
     */
    ExamSurvey findExamSurveyByPmInsId(String pmInsId);


    /**
     *查询一遍接口
     * @param workId
     * @return
     */
    ExamSurvey findJoinDetail(String workId);

}
