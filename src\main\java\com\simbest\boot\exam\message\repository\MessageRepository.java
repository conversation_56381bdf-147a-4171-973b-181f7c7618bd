package com.simbest.boot.exam.message.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.message.model.MessageModel;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface MessageRepository extends LogicRepository<MessageModel,String> {

    @Query(value="select t.message_content from US_MESSAGE_MODEL t  where t.exam_code =:examCode and t.enabled =1"
            ,nativeQuery = true)
    String findMessageContentByExamCode(@Param("examCode") String examCode);
}
