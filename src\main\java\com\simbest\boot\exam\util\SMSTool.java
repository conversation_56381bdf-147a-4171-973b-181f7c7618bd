package com.simbest.boot.exam.util;

import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @用途: 发送短信
 * @作者：zsf
 * @时间: 2018/12/7
 */
@Slf4j
@Component
public class SMSTool {

    @Autowired
    private MsgPostOperatorService msgPostOperatorService;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    /**
     * 发送短信
     * @param map 准备发送参数
     * @return
     */
    public Boolean sendSMS(Map<String,Object> map ){
        Boolean isPostMsgOK = false; //是否发送成功标志
        /**发送短信操作**/
        try {
            /**参数不为空情况下**/
            if ( map != null && map.size() > 0 ){
                String sendUser = map.get("sendUser") != null ? map.get("sendUser").toString(): "";
                /**发送人不为空**/
                if ( StringUtils.isNotEmpty( sendUser) ){
                    /**准备审批短信模板数据**/
                    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("appName", Constants.APP_NAME);
                    paramMap.put("fromUser", map.get("fromUser") != null ?  map.get("fromUser").toString() : "");
                    paramMap.put("itemSubject", map.get("itemSubject") != null ? map.get("itemSubject").toString() : "" );
                    //String msg = MessageEnum.MT000001.getMessage(paramMap);
                    //TODO 临时自定义短信模板
//                    String msg="问卷调查提醒：您好，请您参加公司工会组织的2019年度工会会员有奖问卷调查。" +
//                            "参与方式：登录OA系统进入“工会之家”，点击漂浮窗“2019年度中国移动河南公司工会会员有奖问卷调查”填写问卷并参与抽奖。" +
//                            "活动结束时间为2020年1月10日24:00，逾期系统将自动关闭，请您及时完成问卷调查，感谢您的大力支持和配合！";

//                    String msg="问卷调查提醒：您好，请您参加公司工会组织的2020年度工会会员有奖问卷调查。" +
//                            "参与方式：登录OA系统进入“工会之家”，点击漂浮窗“2020年度中国移动河南公司工会会员有奖问卷调查”填写问卷并参与抽奖。" +
//                            "活动结束时间为2020年1月10日24:00，逾期系统将自动关闭，请您及时完成问卷调查，感谢您的大力支持和配合！";
                    String msg="问卷调查提醒：您好，请您参加公司工会组织的2021年度工会会员问卷调查活动。参与方式：登录OA系统进入“工会之家”，" +
                            "点击漂浮窗“2021年度中国移动河南公司工会会员问卷调查”填写问卷。活动结束时间为2022年1月16日23:59，届时系统将自动关闭，" +
                            "请您及时完成问卷调查，感谢您的大力支持和配合！";
                    /**准备参数**/
                    Map<String,Object> mapParam = Maps.newHashMap();
                    mapParam.put("sendUser", sendUser);
                    mapParam.put("msg", msg);
                    log.debug("短信参数----------------------->>>>>>>>>>>>>>>>>>mapParam"+mapParam.toString());
                    isPostMsgOK = msgPostOperatorService.postMsg( readyParams( mapParam ));
                }
            }
        }catch ( Exception e ){
            Exceptions.printException( e );
        }
        return isPostMsgOK;
    }

    public boolean sendShortMessage( ExamWork returnWork ){
        log.debug("sendShortMessage--------------------->>>>>>>>>>>>推送短信开始");
        boolean flag = false;
        try {
            /**设置变量查找短信开关，是否具有发送资格**/
            boolean isPostMsg = false;   //短信开关 false 短信不发送 true 发送
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode( Constants.APP_CODE, "hadmin" );
            if ( simpleApp != null ){
                isPostMsg = simpleApp.getIsSendMsg();
            }
            /**如果具有发送资格准备发送短信参数**/
            boolean isPostMsgOK = false;
            if ( isPostMsg ){
                /**准备发送短息**/
                log.debug("isPostMsg开关--------------------->>>>>>>>>>>>"+isPostMsg);
                Map<String,Object> paramMap = Maps.newHashMap();
                paramMap.put("sendUser", returnWork.getTransactorCode());//returnWork.getTransactorCode()
                paramMap.put("fromUser", Constants.APP_NAME);
                paramMap.put("itemSubject", returnWork.getTitle());
                /**调用发送短信方法**/
                isPostMsgOK = sendSMS( paramMap );
                flag = isPostMsgOK;
            }
            /**发送失败记录**/
            if( !isPostMsgOK ){
                LocalDateTime time = LocalDateTime.now();
                SysOperateLog operateLog = new SysOperateLog();
                operateLog.setOperateFlag("MSG");
                operateLog.setBussinessKey( returnWork.getId() );
                operateLog.setInterfaceParam( returnWork.toString() );
                operateLog.setOperateInterface("sendShortMessage");
                operateLog.setCreator( returnWork.getCreator());
                operateLog.setCreatedTime( time );
                operateLog.setModifier( returnWork.getModifier() );
                operateLog.setModifiedTime( time );
                operateLogService.saveLog( operateLog );
                log.debug( "sendShortMessage  MSG Fialure>>>>" + operateLog.toString() );
            }
            log.debug("sendShortMessage--------------------->>>>>>>>>>>>推送短信结束");
        }catch ( Exception e ){
            Exceptions.printException( e );
        }
        return flag;
    }

    /**
     * 准备短信对象
     * @param map 参数 （endUser待发人，短信模板）
     * @return
     */
    private  ShrotMsg readyParams(Map<String,Object> map){
        ShrotMsg shrotMsg = new ShrotMsg();//短信对象
        Content content = new Content();
        Set<Content> contentSet = new HashSet<Content>();
        shrotMsg.setAppCode( Constants.APP_CODE );
        content.setUsername( map.get("sendUser") != null ? map.get("sendUser").toString():"" );
        content.setMsgContent( map.get("msg") != null ? map.get("msg").toString():""  );
        content.setImmediately( true );
        content.setSmsPriority( 1 );
        contentSet.add( content );
        shrotMsg.setContents( contentSet );
        return shrotMsg;
    }

    public Boolean postMsg(ShrotMsg shrotMsg) {
        Boolean f=false;
        try {
           f=   msgPostOperatorService.postMsg(shrotMsg);

        } catch (Exception var8) {
            log.warn("发送短信失败：【{}】", JacksonUtils.obj2json(shrotMsg));
        }finally {
            return f;
        }

    }
}
