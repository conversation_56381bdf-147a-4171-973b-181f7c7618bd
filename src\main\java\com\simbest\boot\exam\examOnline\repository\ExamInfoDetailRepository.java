package com.simbest.boot.exam.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.exam.examOnline.model.ExamInfoDetail;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 用途：考试管理模块--考试答题明细dao
 * 作者：gy
 * 时间: 2021-02-23 20:00
 */
public interface ExamInfoDetailRepository extends LogicRepository<ExamInfoDetail, String> {

    @Modifying
    @Query(
            value = "update US_EXAM_INFO_DETAIL t set t.enabled = 0 , t.removed_time = sysdate where t.exam_info_id = :examInfoId and t.enabled = 1  " ,
            nativeQuery = true
    )
    void deleteAllByExamInfoId(@Param("examInfoId") String examInfoId);
}
