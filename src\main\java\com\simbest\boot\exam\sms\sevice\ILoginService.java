package com.simbest.boot.exam.sms.sevice;

import java.util.Map;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2020/8/12</strong><br>
 * <strong>Modify on : 2020/8/12</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface ILoginService {

    /**
     * 发送验证码
     *
     * @param phone
     * @return
     */
    int sendMsg(String phone);

    int sendMsgForOA(String phone);

    String getShortUrl(String url);

    /**
     * 远程调用转短链接接口
     */
    String ConvertShortUrl(String url);

    /**
     * 自动登录
     * @param token
     * @return
     */
    Map<String, Object> getPhone(String token);


    boolean checkVerifyCode(String phone, String code);

}
