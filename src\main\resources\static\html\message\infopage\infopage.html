<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>消息管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../../css/public.css?v=svn.revision" th:href="@{/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../../js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#infoTable",//table列表的id名称，需加#
                    "querycmd":"network/message/findAllMessage",//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass":"noScroll",
                    "frozenColumns":[[
                        { title: "标题", field: "title",width:300,align: 'center',fixed:true,
                            formatter:function (value,row,index) {
                                var g="<a href='#' class='operateBlue read titleTooltipA' id='"+row.id+"'>"+value+"</a>";
                                // var g="<a href='#' class='readDialogTop operateBlue' openlayer=\"html/message/infopage/infopageAdd.html?type=read&id="+row.id+"\" listPageName=\"infopage\" width=\"1000\" height=\"700\" readDialogindex='" + index + "'>"+value+"</a>";
                                return g;
                            }
                        }
                    ]],//固定在左侧的列
                    "columns":[[//列
                        // { title: "内容", field: "content",width:80,align: 'center',tooltip: true},
                        { title: "状态", field: "sendStatus",width:120,align: 'center',fixed:false,
                            formatter:function (value,row,index) {
                                if(row.sendStatus==true){
                                    return '已发送';
                                }else{
                                    return '待发送';
                                }
                            }
                        },
                        { title: "创建人", field: "createUsername",width:120,align: 'center',fixed:false},
                        { title: "创建时间", field: "createdTime",width:340,align: 'center',fixed:true,
                            formatter:function (value,row,index) {
                                if(!value){
                                    return '--';
                                }else{
                                    return "<span class='titleTooltipA'>"+timestampToTime(value)+"</span>";
                                }
                            }
                        },
                        { title: "发送人", field: "sendUsername",width:80,align: 'center',fixed:true,
                            formatter:function (value,row,index) {
                                if(!value){
                                    return '--';
                                }else{
                                    return value;
                                }
                            }
                        },
                        { title: "发送时间", field: "sendTime",width:140,align: 'center',fixed:true,
                            formatter:function (value,row,index) {
                                if(!value){
                                    return '--';
                                }else{
                                    return "<span class='titleTooltipA'>"+timestampToTime(value)+"</span>";
                                }
                            }
                        },
                        { title: "接收人数", field: "receiveNum",width:80,align: 'center',fixed:true},
                        { title: "已阅人数", field: "haveReadNum",width:80,align: 'center',fixed:true},
                        {title: "操作", field: "mm",width:170, align: 'center',fixed:true,
                            formatter:function (value,row,index) {
                                // var gsend=
                                //     "<a href='#' class='showDialogTop operateBlue' openlayer=\"html/message/infopage/infopageAdd.html?id="+row.id+"\" listPageName=\"infopage\" width=\"1000\" height=\"700\" showDialogindex='" + index + "'>编辑</a>"
                                //     +"<span class='operateLine'>|</span>"
                                //     +"<a  href='#' class='del operateRed' delete='network/message/deleteMsgById' deleteid='" + row.id + "'>删除</a>"
                                //     +"<span class='operateLine'>|</span>"
                                //     +"<a  href='#' class='send operateBlue send' id='"+row.id+"'>发送</a>";
                                // var g=
                                //     "<a href='#' class='showDialogTop operateBlue msgDetail' openlayer=\"html/message/infopage/infopageDetail.html?id="+row.id+"\" listPageName=\"infopage\" width=\"1000\" height=\"700\" showDialogindex='" + index + "'>消息明细</a>"
                                //     +"<span class='operateLine'>|</span>"
                                //     +"<a  href='#' class='showDialogTop operateBlue transpond' openlayer=\"html/message/infopage/infopageAdd.html?type=transpond&id="+row.id+"\" listPageName=\"infopage\" width=\"1000\" height=\"700\" showDialogindex='" + index + "'>转发</a>";
                                var gsend=
                                    "<a href='#' class='operateBlue update' id='"+row.id+"'>编辑</a>"
                                    +"<span class='operateLine'>|</span>"
                                    +"<a  href='#' class='del operateRed' delete='network/message/deleteMsgById' deleteid='" + row.id + "'>删除</a>"
                                    +"<span class='operateLine'>|</span>"
                                    +"<a  href='#' class='send operateBlue send' id='"+row.id+"'>发送</a>";
                                var g=
                                    "<a href='#' class='readDialogTop operateBlue msgDetail' openlayer=\"html/message/infopage/infopageDetail.html?nobutton=true&type=read&id="+row.id+"\" listPageName=\"infopage\" width=\"820\" height=\"600\" readDialogindex='" + index + "' title='消息明细'>消息明细</a>"
                                    +"<span class='operateLine'>|</span>"
                                    +"<a  href='#' class='operateBlue transpond' id='"+row.id+"'>转发</a>";
                                if(row.sendStatus){
                                    return g;
                                }else{
                                    return gsend;
                                }
                            }
                        },
                    ]]
                },
                "dialogform": {
                    "dialogid":'infopage',
                    "formname": "#infoTableAddForm"//新增或修改对话框的formid需加#
                }
            };
            loadGrid(pageparam);
            //发送
            $(document).on('click','.send',function (e) {
                top.mesConfirm("提示", '确定下发吗？', function (){
                    ajaxgeneral({
                        url: "network/message/sendMsgById?id="+e.target.id,
                        contentType: "application/json; charset=utf-8",
                        success: function (res) {
                            $('#infoTable').datagrid('reload');
                        }
                    })
                },function (){});
            })
            // //消息明细
            // $(document).on('click','.msgDetail',function () {
            //     top.dialogP('html/group/groupAdd.html?id='+target.id+'&type=read',window.name,'详情','groupUpdate',true,'680','400');
            // })
            //新建
            $(document).on('click','#add',function () {
                winOpen('html/message/infopage/infopageAdd.html?type=add', window.name, '新建消息', 'infopageAdd',null,null,loadList);
            })
            //修改
            $(document).on('click','.update',function (e) {
                var event = e || window.event;
                var target = event.target||event .srcElement;
                winOpen('html/message/infopage/infopageAdd.html?id='+target.id, window.name, '编辑消息', 'infopageAdd',null,null,loadList);
            })
            //查看
            $(document).on('click',".read",function (e) {
                var event = e || window.event;
                var target = event.target||event .srcElement;
                winOpen('html/message/infopage/infopageAdd.html?type=read&id='+target.id, window.name, '查看', 'infopageAdd',null,null,loadList);
            })
            //转发
            $(document).on('click','.transpond',function (e) {
                var event = e || window.event;
                var target = event.target||event .srcElement;
                winOpen('html/message/infopage/infopageAdd.html?type=transpond&id='+target.id, window.name, '转发', 'infopageAdd',null,null,loadList);
            })
            $(document).on('click','.reset',function (){
                formreset('infoTableQueryForm');
            });
        });
        function loadList(){
            $('#infoTable').datagrid('reload');
        }
        function timestampToTime(timestamp) {
            var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
            var Y = date.getFullYear() + '-';
            var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
            var D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
            var h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
            var m = (date.getMinutes() < 10 ? '0'+date.getMinutes() : date.getMinutes()) + ':';
            var s = (date.getSeconds() < 10 ? '0'+date.getSeconds() : date.getSeconds());
            strDate = Y+M+D+h+m+s;
            return strDate;
        }
    </script>
</head>
<body style="padding-top: 0;">
<!--searchform-->
<div class="table_searchD">
    <form id="infoTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="50" align="right">标题：</td><td width="140"><input name="title" type="text" /></td>
                <td width="50" align="right">状态：</td>
                <td width="140">
                    <select class="easyui-combobox" id="sendStatus" data-options="editable:false,panelHeight:'auto'" name="sendStatus" style="width: 100%; height: 32px;">
                        <option value="">--请选择--</option>
                        <option value="true">已发送</option>
                        <option value="false">待发送</option>
                    </select>
                </td>
<!--                <td width="50" align="right">内容：</td><td width="150"><input name="content" type="text"  /></td>-->
            </tr>
            <tr>
                <td width="70" align="right">发送时间：</td>
                <td width="140">
                    <input class="easyui-datetimebox" name="startSendTime"
                           data-options="showSeconds:true,editable:false" style="width:148px; height: 32px;">
<!--                    <span>-</span>-->
                </td>
                <td width="20" align="center">-</td>
                <td width="140">
                    <input class="easyui-datetimebox" name="endSendTime"
                           data-options="showSeconds:true,editable:false" style="width:148px; height: 32px;">
                </td>
                <td>
                    <div class="w100">
                        <a class="btn fl searchtable"><span>查询</span></a>
                        <a class="btn ml10 reset"><span>重置</span></a>
                        <!--                        <a class="btn a_green fr showDialogTop" openlayer="html/message/infopage/infopageAdd.html" listPageName="infopage" width="1000" height="700">新建</a>-->
                        <a class="btn a_green ml10" id="add" width="880">新建</a>
                        <!--                        <a class="btn a_green fr add showDialogWinopen" openlayer="html/message/infopage/infopageAdd.html" listPageName="infopage">新建</a>-->
                    </div>
                </td>
            </tr>
        </table>
    </form>
</div>

<!--table-->
<div class="infoTable" style="margin-top: 20px;"><table id="infoTable"></table></div>
</body>
</html>
