/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.knowledge.service.Impl;/**
 * Created by KZH on 2019/10/8 16:07.
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.mzlion.core.lang.Assert;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamRangeUserInfo;
import com.simbest.boot.exam.examOnline.model.ExamSummary;
import com.simbest.boot.exam.examOnline.service.IExamRangeUserInfoService;
import com.simbest.boot.exam.examOnline.service.IExamSummaryService;
import com.simbest.boot.exam.knowledge.model.UsUserScore;
import com.simbest.boot.exam.knowledge.repository.UsUserScoreRepository;
import com.simbest.boot.exam.knowledge.service.IUsUserScoreService;
import com.simbest.boot.exam.mainbills.model.UsPmInstence;
import com.simbest.boot.exam.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.exam.todo.service.IUsTodoModelService;
import com.simbest.boot.exam.util.*;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2019-10-08 16:07
 * @desc
 **/
@Slf4j
@Service
public class UsUserScoreServiceImpl extends LogicService<UsUserScore, String> implements IUsUserScoreService {

    private UsUserScoreRepository usUserScoreRepository;

    private String param1 = "/acton/usUserScore";

    @Autowired
    public UsUserScoreServiceImpl(UsUserScoreRepository usUserScoreRepository) {
        super(usUserScoreRepository);
        this.usUserScoreRepository = usUserScoreRepository;

    }

}
