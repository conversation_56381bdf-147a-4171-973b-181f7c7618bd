/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examStatistics.service.impl;/**
 * Created by KZH on 2019/11/26 9:46.
 */

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.model.ExamQuestion;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;
import com.simbest.boot.exam.examOnline.service.IExamInfoService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionService;
import com.simbest.boot.exam.examOnline.service.IExamQuestionUserService;
import com.simbest.boot.exam.examStatistics.model.AnalyzeUnIon;
import com.simbest.boot.exam.examStatistics.model.ProblemsAnalyze;
import com.simbest.boot.exam.examStatistics.model.Temporary;
import com.simbest.boot.exam.examStatistics.model.vo.*;
import com.simbest.boot.exam.examStatistics.service.IStatisticExamInfoService;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.test.util.MyUtils;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.DictValueTool;
import com.simbest.boot.exam.util.ExcelUtils;
import com.simbest.boot.exam.util.FormatConversion;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.thymeleaf.util.ListUtils;
import org.thymeleaf.util.MapUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @create 2019-11-26 9:46
 * @desc
 **/
@Slf4j
@Service
public class StatisticExamInfoServiceImpl implements IStatisticExamInfoService {

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private DictValueTool dictValueTool;

    @Autowired
    private IExamQuestionService iExamQuestionService;

    @Autowired
    private IExamQuestionAnswerService iExamQuestionAnswerService;

    @Autowired
    private IExamQuestionUserService examQuestionUserService;
    @Autowired
    private IExamWorkService examWorkService;
    @Autowired
    private IExamInfoService iExamInfoService;
    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    /**
     * 获取到各个公司的答题情况 带答案
     *
     * @param companyName 公司名称
     * @param examAppCode 公司名称
     * @return 公司的答题情况 固定排除职位为一线员工
     */
    @Override
    public List<Temporary> getRawExamConditionInfo(String companyName, String examAppCode) {

        Map<String, Object> map = Maps.newHashMap();

        map.put("companyName", companyName);
        map.put("examAppCode", examAppCode);
//        String sql = "select distinct jg.*" +
//                "  from (SELECT distinct up.truename        truename," +
//                "                        up.username        username," +
//                "                        ei.department_name     companyName," +
//                "                        ei.position_name   positionName," +
//                "                        ei.exam_record    examRecord," +
//                "                        ei.exam_answer    examAnswer" +
//                "          FROM uums.V_USER_ORG_POSITION up," +
//                "               US_EXAM_INFO             ei" +
//                "         WHERE up.username = ei.publish_username" +
//                "           AND ei.is_finish_exam=1" +
//                "           AND up.displayName LIKE concat(:companyName,'%')" +
//                //"           AND  to_char(ei.created_time,'yyyy-MM-dd HH:mm:ss') <= '2020-01-13 09:50:00'" +
//                //"           AND  ei.created_time <= '2020-01-13 09:50:00'" +
//                //"           AND up.positionName != '一线员工'" +
//                "           AND ei.exam_app_code=:examAppCode" +
//                //"           AND up.positionName != '资深经理'" +
//                "           AND up.userType = '1'" +
//                "         ORDER BY up.departmentName, up.username ASC) jg";
        //" where jg.username not in (select username from TEMP)";
        String sql = "SELECT distinct up.truename  truename, " +
                "                        up.username  username, " +
                "                        ei.department_name companyName, " +
                "                        ei.position_name positionName, " +
                "                        ei.exam_record  examRecord, " +
                "                        ei.exam_answer examAnswer " +
                "          FROM uums.V_USER_ORG_POSITION up, US_EXAM_INFO ei " +
                "         WHERE up.username = ei.publish_username " +
                "           AND ei.is_finish_exam = 1 " +
                "           AND up.displayName LIKE concat(:companyName,'%')" +
                "           AND ei.exam_app_code =:examAppCode " +
                "           AND up.positionName != '一线员工' " +
                "           AND up.userType = '1' " +
                "         ORDER BY  ei.department_name, up.username ASC ";

        List<Map<String, Object>> maps = customDynamicWhere.queryNamedParameterForList(sql, map);

        return transition(maps);
    }

    /**
     * 获取到各个公司中应完成问卷人数
     *
     * @param companyName 公司名称
     * @return 返回集合为指定公司答题人员信息
     */
    @Override
    public int getCompanyShouldFinishPeople(String companyName) {
        Map<String, Object> map = Maps.newHashMap();

        map.put("companyName", companyName);
        String sql = " select distinct jg.*" +
                "  from (SELECT distinct up.truename        truename," +
                "                        up.username        username" +
                "          FROM uums.V_USER_ORG_POSITION up" +
                "         WHERE  up.displayName LIKE concat(:companyName,'%')" +
                "           AND up.positionName != '一线员工'" +
                //"           AND up.positionName != '资深经理'" +
                "           AND up.userType = '1'" +
                "         ORDER BY up.username ASC) jg";
        //" where jg.username not in (select username from TEMP)";
        List<Map<String, Object>> maps = customDynamicWhere.queryNamedParameterForList(sql, map);

        return transition(maps).size();
    }

    /**
     * 获取到各个公司中应完成问卷人数,排除某些职位
     *
     * @param companyName 公司名称
     * @param paramList   职位
     * @return
     */
    @Override
    public List<Temporary> getCompanyShouldFinishPeopleExclude(String companyName, String department, List<String> paramList) {
        Map<String, Object> map = Maps.newHashMap();

        map.put("companyName", companyName);
        map.put("paramList", paramList);
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct jg.*");
        sql.append("  from (SELECT distinct up.truename        truename,");
        sql.append("                        up.username        username,");
        sql.append("                        ei.department_name     companyName,");
        sql.append("                        ei.position_name   positionName,");
        sql.append("                        ei.is_finish_exam isFinish");
        sql.append("          FROM uums.V_USER_ORG_POSITION up,");
        sql.append("               US_EXAM_INFO             ei");
        sql.append("         WHERE up.username = ei.publish_username");
        sql.append("           AND up.displayName LIKE concat(:companyName,'%')");
        sql.append("           AND up.positionName not in (:paramList)");
        sql.append("           AND up.userType = '1'");
        //部门不为空
        if (StringUtils.isNotEmpty(department)) {
            map.put("department", department);
            sql.append("           AND up.departmentName =:department");
        }
        sql.append("         ORDER BY up.departmentName, up.username ASC) jg");
        sql.append(" where jg.username not in (select username from TEMP)");
        List<Map<String, Object>> maps = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);

        return transition(maps);
    }

    /**
     * 获取到各个公司部门的答题情况 带答案 根据指定职位
     *
     * @param companyName  公司名称
     * @param department   部门
     * @param positionName 职位名称
     * @return 返回结果带答案
     */
    @Override
    public List<Temporary> getRawExamInfoByPositionName(String companyName, String department, String[] positionName) {

        if (StringUtils.isEmpty(companyName)) {
            return null;
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("companyName", companyName);
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct jg.*");
        sql.append("  from (SELECT distinct up.truename        truename,");
        sql.append("                        up.username        username,");
        sql.append("                        ei.department_name     companyName,");
        sql.append("                        ei.position_name   positionName,");
        sql.append("                        ei.is_finish_exam isFinish,");
        sql.append("                        ei.exam_record    examRecord,");
        sql.append("                        ei.exam_answer    examAnswer");
        sql.append("          FROM uums.V_USER_ORG_POSITION up,");
        sql.append("               US_EXAM_INFO             ei");
        sql.append("         WHERE up.username = ei.publish_username");
        sql.append("           AND ei.is_finish_exam='1'");
        sql.append("           AND up.displayName LIKE concat(:companyName,'%')");
        sql.append("           AND to_char(ei.created_time,'yyyy-MM-dd HH:mm:ss') <= '2020-01-13 09:50:00'");
        //sql.append("           AND  ei.created_time <= '2020-01-13 09:50:00'");

        //职位不为空
        if (positionName != null) {
            map.put("positionName", positionName);
            sql.append("           AND up.positionName in (:positionName)");
        } else {
            sql.append("           AND up.positionName != '一线员工'");
            sql.append("           AND up.positionName != '资深经理'");
        }
        //部门不为空
        if (StringUtils.isNotEmpty(department)) {
            map.put("department", department);
            sql.append("           AND up.departmentName =:department");
        }
        //sql.append("           AND up.userType = '1'" );
        sql.append("         ORDER BY up.departmentName, up.username ASC) jg");
        //sql.append(" where jg.username not in (select username from TEMP)");

        List<Map<String, Object>> maps = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);

        return transition(maps);
    }

    /**
     * 获取到各个公司部门的答题人数  根据指定职位
     *
     * @param companyName  公司名称
     * @param department   部门
     * @param positionName 职位名称
     * @return 返回结果int
     */
    @Override
    public int getFinishPeopleByPositionName(String companyName, String department, String[] positionName) {
        if (StringUtils.isEmpty(companyName)) {
            return 0;
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("companyName", companyName);
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct jg.*");
        sql.append("  from (SELECT distinct up.truename        truename,");
        sql.append("                        up.username        username");
        sql.append("          FROM uums.V_USER_ORG_POSITION up");
        sql.append("         WHERE  up.displayName LIKE concat(:companyName,'%')");
        //职位不为空
        if (positionName != null) {
            map.put("positionName", positionName);
            sql.append("           AND up.positionName in (:positionName)");
        } else {
            sql.append("           AND up.positionName != '一线员工'");
            sql.append("           AND up.positionName != '资深经理'");
        }
        //部门不为空
        if (StringUtils.isNotEmpty(department)) {
            map.put("department", department);
            sql.append("           AND up.departmentName =:department");
        }
        sql.append("           AND up.userType = '1'");
        sql.append("         ORDER BY  up.username ASC) jg");
        //sql.append(" where jg.username not in (select username from TEMP)");

        List<Map<String, Object>> maps = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);

        return transition(maps).size();
    }

    /**
     * 通用考试结果导出
     *
     * @param request  请求
     * @param response 响应
     */
    @Override
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, String examCode) {
        //从数据字典中获取到19公司
        List<Map<String, Object>> departmentType = dictValueTool.getByType(Constants.DEPARTMENT_TYPE);
        //设置导出Excel的名称
        String targetFileName = String.format("%s_考试结果通用信息导出.xls", examCode);

        StopWatch watch = new StopWatch();
        watch.start("getExamInfo");
        // 获取到考试信息
        List<ExamInfoVO> list = this.getExamInfoByExamCode(examCode);
        watch.stop();
        log.info("通用导出耗时1： {}, {}", watch.prettyPrint(), watch.getTotalTimeMillis());
        watch.start("getRatioInfo");
        // 参与率统计
        List<ParticipationRatioVO> data = this.getParticipationRatioVOList(departmentType, list);
        watch.stop();
        log.info("通用导出耗时2： {}, {}", watch.prettyPrint(), watch.getTotalTimeMillis());
        watch.start("getInfo");
        // 通用考试信息统计
        List<InfoVO> objects = getInfoVOList(list);
        watch.stop();
        log.info("通用导出耗时3： {}, {}", watch.prettyPrint(), watch.getTotalTimeMillis());

        // 构造sheet页
        List<Map<String, Object>> sheetsList = Lists.newArrayList();
        Map<String, Object> sheetMap = Maps.newHashMap();
        sheetMap.put("title", new ExportParams("参与率", "参与率"));
        sheetMap.put("entity", ParticipationRatioVO.class);
        sheetMap.put("data", data);
        sheetsList.add(sheetMap);
        sheetMap = Maps.newHashMap();
        sheetMap.put("title", new ExportParams("通用统计", "通用统计"));
        sheetMap.put("entity", InfoVO.class);
        sheetMap.put("data", objects);
        sheetsList.add(sheetMap);

        watch.start("exportInfo");
        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        ExcelUtils.downLoadExcel(targetFileName, response, workbook);
        watch.stop();
        log.info("通用导出耗时4： {}, {}", watch.prettyPrint(), watch.getTotalTimeMillis());
    }

    /**
     * 获取参与率统计数据
     *
     * @param departmentType 19个公司名称
     * @param list           考试信息
     * @return 返回结果
     */
    private List<ParticipationRatioVO> getParticipationRatioVOList(List<Map<String, Object>> departmentType, List<ExamInfoVO> list) {
        //获取格式化对象
        NumberFormat nt = NumberFormat.getPercentInstance();
        //设置百分数精确度2即保留两位小数
        nt.setMinimumFractionDigits(2);
        List<ParticipationRatioVO> data = departmentType.stream().map(v -> ParticipationRatioVO.builder().company((String) v.get("VALUE")).participation(0).noParticipation(0).build()).collect(Collectors.toList());
        list.parallelStream().forEach(v -> data.stream().filter(vo -> Objects.equals(vo.getCompany(), v.getCompanyName())).findFirst().ifPresent(vo -> {
            if (StringUtils.isNotBlank(v.getExamAnswer())) {
                vo.setParticipation(vo.getParticipation() + 1);
            } else {
                vo.setNoParticipation(vo.getNoParticipation() + 1);
            }
        }));
        data.forEach(v -> {
            if (v.getParticipation() == 0) {
                v.setParticipationRatio("0.00%");
                v.setNoParticipationRatio("100.00%");
            }
            if (v.getNoParticipation() == 0) {
                v.setParticipationRatio("100.00%");
                v.setNoParticipationRatio("0.00%");
            }
            if (v.getParticipation() == 0 && v.getNoParticipation() == 0) {
                v.setParticipationRatio("0.00%");
                v.setNoParticipationRatio("0.00%");
            }
            if (StringUtils.isBlank(v.getParticipationRatio())) {
                double num = v.getParticipation() / ((v.getParticipation() + v.getNoParticipation()) * 1.0);
                v.setParticipationRatio(nt.format(num));
            }
            if (StringUtils.isBlank(v.getNoParticipationRatio())) {
                double num = v.getNoParticipation() / ((v.getParticipation() + v.getNoParticipation()) * 1.0);
                v.setNoParticipationRatio(nt.format(num));
            }
        });
        ParticipationRatioVO ratioVO = ParticipationRatioVO.builder()
                .company("总计")
                .participation(data.stream().map(ParticipationRatioVO::getParticipation).reduce(0, Integer::sum))
                .noParticipation(data.stream().map(ParticipationRatioVO::getNoParticipation).reduce(0, Integer::sum))
                .build();
        if (ratioVO.getParticipation() == 0) {
            ratioVO.setParticipationRatio("0.00%");
            ratioVO.setNoParticipationRatio("100.00%");
        }
        if (ratioVO.getNoParticipation() == 0) {
            ratioVO.setParticipationRatio("100.00%");
            ratioVO.setNoParticipationRatio("0.00%");
        }
        if (StringUtils.isBlank(ratioVO.getParticipationRatio())) {
            double num = ratioVO.getParticipation() / ((ratioVO.getParticipation() + ratioVO.getNoParticipation()) * 1.0);
            ratioVO.setParticipationRatio(nt.format(num));
        }
        if (StringUtils.isBlank(ratioVO.getNoParticipationRatio())) {
            double num = ratioVO.getNoParticipation() / ((ratioVO.getParticipation() + ratioVO.getNoParticipation()) * 1.0);
            ratioVO.setNoParticipationRatio(nt.format(num));
        }
        data.add(ratioVO);
        return data;
    }

    /**
     * 获取通用考试信息统计数据
     *
     * @param list 考试信息
     * @return 返回结果
     */
    private List<InfoVO> getInfoVOList(List<ExamInfoVO> list) {
        Optional<ExamInfoVO> examInfoVO = list.parallelStream().filter(v -> StringUtils.isNotBlank(v.getExamRecord())).findFirst();
        if (!examInfoVO.isPresent()) return Lists.newArrayList();

        //获取题目 和 答案 信息
        List<String> examRecord = Arrays.asList(examInfoVO.get().getExamRecord().split(","));
        List<ExamQuestion> examQuestions = iExamQuestionService.findAllNoPage(Specifications.<ExamQuestion>and().in("questionCode", examRecord).build(), Sort.by(Sort.Direction.ASC, "questionOrder"));
        List<ExamQuestionAnswer> examQuestionAnswers = iExamQuestionAnswerService.findAllNoPage(Specifications.<ExamQuestionAnswer>and().in("questionCode", examRecord).build(), Sort.by(Sort.Direction.ASC, "answerCode"));

        // 构造excel 动态列名
        List<Field> declaredFields = Arrays.stream(InfoVO.class.getSuperclass().getDeclaredFields()).collect(Collectors.toList());
        this.initExcelAnnotation(examQuestions, examQuestionAnswers, declaredFields);

        return list.parallelStream().map(v -> {
            InfoVO infoVO = new InfoVO();
            BeanUtils.copyProperties(v, infoVO);
            if (StringUtils.isBlank(v.getExamAnswer())) return infoVO;
            // 赋值答案
            String[] examAnswer = v.getExamAnswer().split(",");
            int min = Math.min(examAnswer.length, declaredFields.size());
            IntStream.range(0, min).forEach(i -> {
                try {
                    Field field = declaredFields.get(i);
                    field.setAccessible(true);
                    field.set(infoVO, examAnswer[i]);
                } catch (IllegalAccessException e) {
                    throw new IllegalStateException(e);
                }
            });
            return infoVO;
        }).collect(Collectors.toList());
    }

    /**
     * 初始化 excel 注解 构造excel动态列名
     *
     * @param examQuestions       题目信息
     * @param examQuestionAnswers 答案信息
     */
    private void initExcelAnnotation(List<ExamQuestion> examQuestions, List<ExamQuestionAnswer> examQuestionAnswers, List<Field> declaredFields) {
        // 这里最大支持60道题目 如需扩展，修改 InfoVO 的父类 Option类 扩展字段即可
        List<String> records = examQuestions.stream().map(v -> {
            String reduce = examQuestionAnswers.stream().filter(v1 -> Objects.equals(v1.getQuestionCode(), v.getQuestionCode()))
                    .map(v1 -> String.format("%s %s", v1.getAnswerCode(), v1.getAnswerContent())).reduce("", (a, b) -> a + "\n" + b);
            String flag = "";
            Optional<ExamQuestionAnswer> first = examQuestionAnswers.stream().filter(v1 -> BooleanUtils.isTrue(v1.getIsCorrect())).findFirst();
            if (first.isPresent()) flag = String.format("答案: %s", first.get().getAnswerCode());
            // 题号 题目 选项 答案
            return String.format("%s\n%s\n%s\n%s", v.getQuestionCode(), v.getQuestionName(), reduce, flag).replaceAll("null", "").replaceAll("[\n]{2}", "\n");
        }).collect(Collectors.toList());
        int min = Math.min(declaredFields.size(), records.size());
        IntStream.range(0, min).forEach(i -> {
            Field field = declaredFields.get(i);
            String str = records.get(i);
            try {
                field.setAccessible(true);
                Excel annotation = field.getAnnotation(Excel.class);
                if (annotation != null) {
                    modifyAnnotation(annotation, "name", str);
                }
            } catch (Exception e) {
                throw new IllegalStateException(e);
            }
        });
    }

    /**
     * 通过反射修改注解的值
     *
     * @param annotation 注解
     * @param key        属性名
     * @param value      属性值
     */
    @SuppressWarnings("all")
    private static void modifyAnnotation(Annotation annotation, String key, String value) {
        try {
            InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
            Field memberValuesField = invocationHandler.getClass().getDeclaredField("memberValues");
            memberValuesField.setAccessible(true);
            Map<String, Object> memberValues = (Map<String, Object>) memberValuesField.get(invocationHandler);
            memberValues.put(key, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new IllegalStateException(e);
        }
    }

    /**
     * 获取考试信息
     *
     * @param examCode 考试编号
     * @return 本次考试人员信息
     */
    private List<ExamInfoVO> getExamInfoByExamCode(String examCode) {
        String sql = "SELECT a.transactor_code username,\n" +
                "       a.transactor truename,\n" +
                "       a.exam_app_code,\n" +
                "       a.exam_code,\n" +
                "       b.POSITION_NAME,\n" +
                "       b.residue_time,\n" +
                "       b.score,\n" +
                "       b.exam_record,\n" +
                "       b.exam_answer\n" +
                "  FROM us_exam_work a, us_exam_info b\n" +
                " WHERE 1 = 1\n" +
                "   AND a.transactor_code != 'sbjg'\n" +
                "   AND a.enabled = 1\n" +
                "   AND a.enabled = b.enabled(+)\n" +
                "   AND a.transactor_code = b.publish_username(+)\n" +
                "   AND a.exam_code = b.exam_code(+)\n" +
                "   AND b.is_finish_exam(+) = 1\n" +
                "   AND a.exam_code = :examCode\n" +
                " ORDER BY a.transactor_code";
        Map<String, Object> map = Maps.newHashMap();
        map.put("examCode", examCode);
        List<Map<String, Object>> maps = customDynamicWhere.queryNamedParameterForList(sql, map);
        if (CollectionUtils.isEmpty(maps)) {
            // 问卷类型结果导出(无工单)
            sql = "SELECT b.publish_username username,\n" +
                    "       b.publish_truename truename,\n" +
                    "       b.POSITION_NAME,\n" +
                    "       b.exam_app_code,\n" +
                    "       b.exam_code,\n" +
                    "       b.residue_time,\n" +
                    "       b.score,\n" +
                    "       b.exam_record,\n" +
                    "       b.exam_answer\n" +
                    "  FROM us_exam_info b\n" +
                    " WHERE 1 = 1\n" +
                    "   AND b.enabled = 1\n" +
                    "   AND b.is_finish_exam = 1\n" +
                    "   AND b.publish_username != 'sbjg'\n" +
                    "   AND b.exam_code = :examCode\n" +
                    " ORDER BY b.publish_username\n";
            maps = customDynamicWhere.queryNamedParameterForList(sql, map);
        }
        List<ExamInfoVO> collect = FormatConversion.formatConversion(maps)
                .parallelStream().map(v -> BeanUtil.toBean(v, ExamInfoVO.class)).collect(Collectors.toList());

        // 获取用户信息
        String sqlId = "exam_073ED14BF4D4FF0AE0605C0AA1521A77";
        List<Map<String, Object>> list = MyUtils.selectBySqlSelectType(sqlId, Collections.emptyList());
        Map<String, SimpleUser> userMap = FormatConversion.formatConversion(list).parallelStream()
                .map(v -> SimpleUser.builder()
                        .username(MapUtil.getStr(v, "username"))
                        .truename(MapUtil.getStr(v, "truename"))
                        .preferredMobile(MapUtil.getStr(v, "preferredmobile"))
                        .belongCompanyName(MapUtil.getStr(v, "companyname"))
                        .belongDepartmentName(MapUtil.getStr(v, "departmentname"))
                        .belongOrgName(MapUtil.getStr(v, "orgname"))
                        .build()).collect(Collectors.toMap(SimpleUser::getUsername, v -> v));

        // 完善用户信息
        collect.parallelStream().forEach(v -> {
            SimpleUser user = userMap.getOrDefault(v.getUsername(), new SimpleUser());
            v.setCompanyName(user.getBelongCompanyName());
            v.setDepartmentName(user.getBelongDepartmentName());
            v.setOrgName(user.getBelongOrgName());
            v.setPhone(user.getPreferredMobile());
        });
        return collect;
    }

    /**
     * 题目分析数据导出
     *
     * @param request  请求
     * @param response 响应
     * @param paramMap 条件参数集合
     */
    @Override
//    public void exportExcel2(HttpServletRequest request, HttpServletResponse response, Map<String, Object> paramMap) {
//        /*初始化参数*/
//        //从字典中获取到19公司
//        List<Map<String, Object>> departmentType = dictValueTool.getByType("departmentType");
//        //获取格式化对象
//        NumberFormat nt = NumberFormat.getPercentInstance();
//        //设置百分数精确度2即保留两位小数
//        nt.setMinimumFractionDigits(2);
//        //设置导出Excel的名称
//        //String targetFileName= "2019嵌入式廉洁风险防控机制建设认知情况调查问卷-题目分析.xls";
//        String targetFileName = "2019TMFX.xls";
//
//        //获取全部职务
//        //List<Map<String, Object>> allPosition = findAllPosition();
//
//        /*处理条件参数*/
//        //分析题目数量
//        String questionSizeStr = String.valueOf(paramMap.get("questionSize"));
//        int questionSize;
//        if (Constants.NULL.equals(questionSizeStr)) {
//            questionSize = 0;
//        } else {
//            questionSize = Integer.parseInt(questionSizeStr);
//        }
//        //题库编码
//        //String questionBankCode=(String) paramMap.get("questionBankCode");
//        String questionBankCode = "A-333";
//        //部门
//        String department = (String) paramMap.get("department");
//        //职位名称集合
//        String[] positionName = (String[]) paramMap.get("positionName");
//
//        if (StringUtils.isEmpty(questionBankCode)) {
//            return;
//        }
//
//        //获取题目信息
//        List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);
//
//        int size = examQuestionList.size();
//        List<Map<String, Object>> sheetsList = Lists.newArrayList();
//
//        /*开始创建sheet页 一个sheet页19家公司 一共10个sheet页 对应10道题 每道题有不固定选项 统计选择数和比例*/
//        for (int i = 0; i < size; i++) {
//
//            // 创建sheet使用得map
//            Map<String, Object> sheetMap = Maps.newHashMap();
//
//            List<ProblemsAnalyze> problemsAnalyzeList = Lists.newArrayList();
//            String questionName = null;
//            String questionCode = null;
//            int answerSize = 4;
//            if (ListUtils.isEmpty(problemsAnalyzeList)) {
//                ExamQuestion examQuestion = examQuestionList.get(i);
//                questionCode = examQuestion.getQuestionCode();
//                questionName = examQuestion.getQuestionName();
//                //获取到对应题目的答案信息
//                List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(questionCode);
//                answerSize = examQuestionAnswerList.size();
//                //初始化sheet页的表头数据
//                ProblemsAnalyze problemsAnalyze = initProblemsAnalyzeList(examQuestionAnswerList);
//                problemsAnalyzeList.add(problemsAnalyze);
//            }
//            int departmentSize = 0;
//            //处理sheet内的数据
//            for (Map<String, Object> companyMap : departmentType) {
//                departmentSize++;
//                //处理人数和题目分析处理
//                //ProblemsAnalyze problemsAnalyzes = problemsAnalyze(allPosition,departmentSize,companyMap,questionCode,questionSize,department,positionName);
//                ProblemsAnalyze problemsAnalyzes = problemsAnalyze(departmentSize, companyMap, questionCode, questionSize, department, positionName);
//                //处理百分比
//                problemsAnalyzeList.add(disposePercentage(problemsAnalyzes, answerSize - 1));
//            }
//            sheetMap.put("title", new ExportParams(questionName, "第" + i + "题"));
//            sheetMap.put("entity", ProblemsAnalyze.class);
//            // sheet中要填充得数据
//            sheetMap.put("data", problemsAnalyzeList);
//            // 将循环出的sheet页使用得map进行包装
//            sheetsList.add(sheetMap);
//        }
//
//        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
//
//        ExcelUtils.downLoadExcel(targetFileName, response, workbook);
//
//    }
    public void exportExcel2(HttpServletRequest request, HttpServletResponse response, Map<String, Object> paramMap) {
        String targetFileName = "2023lyxf.xls";
        String questionBankCode = "A-111";
        String examCode = "lyxf";
        exprotExcel22(response, targetFileName, questionBankCode, examCode);
    }

    private void exprotExcel22(HttpServletResponse response, String targetFileName, String questionBankCode, String examCode) {
        //获取题目信息
        List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);
        int size = examQuestionList.size();
        List<Map<String, Object>> sheetsList = Lists.newArrayList();
        List<ExamQuestionUser> questionUsers = examQuestionUserService.findAllNoPage(Specifications.<ExamQuestionUser>and().eq("examCode", examCode).build());

        for (int i = 0; i < size; i++) {
            // 创建sheet使用得map
            Map<String, Object> sheetMap = Maps.newHashMap();

            List<ProblemsAnalyze> problemsAnalyzeList = Lists.newArrayList();
            String questionName = null;
            String questionCode = null;
            int answerSize = 4;
            ExamQuestion examQuestion = examQuestionList.get(i);
            if (ListUtils.isEmpty(problemsAnalyzeList)) {
                questionCode = examQuestion.getQuestionCode();
                questionName = examQuestion.getQuestionName();
                //获取到对应题目的答案信息
                List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(questionCode);
                answerSize = examQuestionAnswerList.size();
                //初始化sheet页的表头数据
                ProblemsAnalyze problemsAnalyze = new ProblemsAnalyze();
                problemsAnalyze.setCompanyName("用户名");
                for (ExamQuestionAnswer examQuestionAnswer : examQuestionAnswerList) {
                    String answerContent = examQuestionAnswer.getAnswerContent();
                    if (StringUtils.isEmpty(problemsAnalyze.getOptionA())) {
                        problemsAnalyze.setOptionA("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionB())) {
                        problemsAnalyze.setOptionB("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionC())) {
                        problemsAnalyze.setOptionC("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionD())) {
                        problemsAnalyze.setOptionD("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionE())) {
                        problemsAnalyze.setOptionE("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionF())) {
                        problemsAnalyze.setOptionF("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionG())) {
                        problemsAnalyze.setOptionG("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionH())) {
                        problemsAnalyze.setOptionH("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionI())) {
                        problemsAnalyze.setOptionI("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionJ())) {
                        problemsAnalyze.setOptionJ("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionK())) {
                        problemsAnalyze.setOptionK("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionL())) {
                        problemsAnalyze.setOptionL("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionM())) {
                        problemsAnalyze.setOptionM("计数项：" + answerContent);
                    } else if (StringUtils.isEmpty(problemsAnalyze.getOptionN())) {
                        problemsAnalyze.setOptionN("计数项：" + answerContent);
                    }
                }
                problemsAnalyzeList.add(problemsAnalyze);
            }
            int departmentSize = 0;

            List<ExamQuestionUser> collect = questionUsers.stream().filter(v -> Objects.equals(examQuestion.getQuestionCode(), v.getQuestionCode())).collect(Collectors.toList());
            for (int j = 0; j < collect.size(); j++) {
                ExamQuestionUser user = collect.get(j);
                ProblemsAnalyze analyze = new ProblemsAnalyze();
                analyze.setCompanyName(user.getCreator());
                String answer = user.getUsernameAnswer();
                switch (answer) {
                    case "A":
                        analyze.setOptionA(String.valueOf(answer));
                        break;
                    case "B":
                        analyze.setOptionB(String.valueOf(answer));
                        break;
                    case "C":
                        analyze.setOptionC(String.valueOf(answer));
                        break;
                    case "D":
                        analyze.setOptionD(String.valueOf(answer));
                        break;
                    case "E":
                        analyze.setOptionE(String.valueOf(answer));
                        break;
                    case "F":
                        analyze.setOptionF(String.valueOf(answer));
                        break;
                    case "G":
                        analyze.setOptionG(String.valueOf(answer));
                        break;
                    case "H":
                        analyze.setOptionH(String.valueOf(answer));
                        break;
                    case "I":
                        analyze.setOptionI(String.valueOf(answer));
                        break;
                    case "J":
                        analyze.setOptionJ(String.valueOf(answer));
                        break;
                    case "K":
                        analyze.setOptionK(String.valueOf(answer));
                        break;
                    case "L":
                        analyze.setOptionL(String.valueOf(answer));
                        break;
                    default:
                        break;
                }
                problemsAnalyzeList.add(analyze);
            }
            sheetMap.put("title", new ExportParams(questionName, "第" + i + "题"));
            sheetMap.put("entity", ProblemsAnalyze.class);
            // sheet中要填充得数据
            sheetMap.put("data", problemsAnalyzeList);
            // 将循环出的sheet页使用得map进行包装
            sheetsList.add(sheetMap);
        }
        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);

        ExcelUtils.downLoadExcel(targetFileName, response, workbook);
    }

    @Override
    public void exportExcel3(HttpServletRequest request, HttpServletResponse response) {
        /*初始化参数*/
        //从字典中获取到19公司
        List<Map<String, Object>> departmentType = dictValueTool.getByType("departmentType");
        List<Map<String, Object>> statisticalType = dictValueTool.getByType("statisticalType");
        //获取格式化对象
        NumberFormat nt = NumberFormat.getPercentInstance();
        //设置百分数精确度2即保留两位小数
        nt.setMinimumFractionDigits(2);
        //设置导出Excel的名称
        //String targetFileName= "2019年度河南移动工会会员调查问卷数据明细表.xls";
        String targetFileName = MapUtil.getStr(statisticalType.get(0), "VALUE") + ".xls";
        String questionBankCode = MapUtil.getStr(statisticalType.get(1), "VALUE");
        String examAppCode = MapUtil.getStr(statisticalType.get(2), "VALUE");

        if (StringUtils.isEmpty(targetFileName) || StringUtils.isEmpty(questionBankCode)) {
            return;
        }

        //获取题目信息
        List<ExamQuestion> examQuestionList = iExamQuestionService.findAllByQuestionBankCode(questionBankCode);
        int size = examQuestionList.size();
        List<Map<String, Object>> sheetsList = Lists.newArrayList();

        /*开始创建sheet页 一个sheet页19家公司 一共10个sheet页 对应10道题 每道题有不固定选项 统计选择数和比例*/
        for (int i = 0; i < size; i++) {

            // 创建sheet使用得map
            Map<String, Object> sheetMap = Maps.newHashMap();

            List<AnalyzeUnIon> problemsAnalyzeList = Lists.newArrayList();
            String questionName = null;
            String questionCode = null;

            int answerSize = 4;
            if (ListUtils.isEmpty(problemsAnalyzeList)) {
                ExamQuestion examQuestion = examQuestionList.get(i);
                questionCode = examQuestion.getQuestionCode();
                questionName = examQuestion.getQuestionName();
                //获取到对应题目的答案信息
                List<ExamQuestionAnswer> examQuestionAnswerList = iExamQuestionAnswerService.findAllByQuestionCode(questionCode);
                answerSize = examQuestionAnswerList.size();
                //初始化sheet页的表头数据
                AnalyzeUnIon analyzeUnIon = initProblemsAnalyzeList2(examQuestionAnswerList);
                problemsAnalyzeList.add(analyzeUnIon);
            }
            int departmentSize = 0;
            //处理sheet内的数据
            for (Map<String, Object> companyMap : departmentType) {
                departmentSize++;
                //处理人数和题目分析处理
                //ProblemsAnalyze problemsAnalyzes = problemsAnalyze(allPosition,departmentSize,companyMap,questionCode,questionSize,department,positionName);
                AnalyzeUnIon analyzeUnIon = analyzeUnIon(departmentSize, companyMap, questionCode, 0, examAppCode);
                //处理百分比
                problemsAnalyzeList.add(disposePercentage(analyzeUnIon, answerSize - 1));
            }
            int j = i + 1;
            sheetMap.put("title", new ExportParams(questionName, "第" + j + "题"));
            sheetMap.put("entity", AnalyzeUnIon.class);
            // sheet中要填充得数据
            sheetMap.put("data", problemsAnalyzeList);
            // 将循环出的sheet页使用得map进行包装
            sheetsList.add(sheetMap);
        }

        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);

        ExcelUtils.downLoadExcel(targetFileName, response, workbook);
    }

    @Override
    public JsonResponse exportExcel4() {
        /*初始化参数*/
        //获取格式化对象
        NumberFormat nt = NumberFormat.getPercentInstance();
        //设置百分数精确度2即保留两位小数
        nt.setMinimumFractionDigits(2);
        //获取到32道题目带正确答案

        List<ExamQuestionAnswer> examQuestionAnswers = iExamQuestionAnswerService.findAllByQuestionCodeAndCorrect("A-017");

        //转换为map key为code value为答案
        Map<String, String> answerMap = Maps.newHashMap();
        for (ExamQuestionAnswer examQuestionAnswer : examQuestionAnswers) {
            answerMap.put(examQuestionAnswer.getQuestionCode(), examQuestionAnswer.getAnswerCode());
        }

        List<ExamInfo> llds = iExamInfoService.findExamInfo("llds");

        int sumCorrect1 = 0, sumError1 = 0;
        int sumCorrect2 = 0, sumError2 = 0;
        int sumCorrect3 = 0, sumError3 = 0;

        int sumCorrect4 = 0, sumError4 = 0;
        int sumCorrect5 = 0, sumError5 = 0;

        int sumCorrect6 = 0, sumError6 = 0;
        int sumCorrect7 = 0, sumError7 = 0;

        int sumCorrect8 = 0, sumError8 = 0;
        int sumCorrect9 = 0, sumError9 = 0;
        int sumCorrect10 = 0, sumError10 = 0;
        int sumCorrect11 = 0, sumError11 = 0;

        int sumCorrect12 = 0, sumError12 = 0;
        int sumCorrect13 = 0, sumError13 = 0;
        int sumCorrect14 = 0, sumError14 = 0;
        int sumCorrect15 = 0, sumError15 = 0;
        int sumCorrect16 = 0, sumError16 = 0;

        int sumCorrect17 = 0, sumError17 = 0;
        int sumCorrect18 = 0, sumError18 = 0;
        int sumCorrect19 = 0, sumError19 = 0;
        int sumCorrect20 = 0, sumError20 = 0;

        int sumCorrect21 = 0, sumError21 = 0;
        int sumCorrect22 = 0, sumError22 = 0;
        int sumCorrect23 = 0, sumError23 = 0;

        int sumCorrect24 = 0, sumError24 = 0;
        int sumCorrect25 = 0, sumError25 = 0;
        int sumCorrect26 = 0, sumError26 = 0;

        int sumCorrect27 = 0, sumError27 = 0;
        int sumCorrect28 = 0, sumError28 = 0;
        int sumCorrect29 = 0, sumError29 = 0;

        int sumCorrect30 = 0, sumError30 = 0;
        int sumCorrect31 = 0, sumError31 = 0;
        int sumCorrect32 = 0, sumError32 = 0;

        for (ExamInfo lld : llds) {

            String examRecord = lld.getExamRecord();
            String examAnswer = lld.getExamAnswer();

            //获取答案编号
            String[] splitRecord = examRecord.split(",");
            //获取回答答案
            String[] splitAnswer = examAnswer.split(",");

            for (int i = 0; i < splitRecord.length; i++) {
                String s = splitRecord[i];
                String a = splitAnswer[i];
                String answer = MapUtil.getStr(answerMap, s);

                if (a.equals(answer)) {
                    switch (s) {
                        case "A-017-1":
                            sumCorrect1 = sumCorrect1 + 1;
                            break;
                        case "A-017-2":
                            sumCorrect2 = sumCorrect2 + 1;
                            break;
                        case "A-017-3":
                            sumCorrect3 = sumCorrect3 + 1;
                            break;
                        case "A-017-4":
                            sumCorrect4 = sumCorrect4 + 1;
                            break;
                        case "A-017-5":
                            sumCorrect5 = sumCorrect5 + 1;
                            break;
                        case "A-017-6":
                            sumCorrect6 = sumCorrect6 + 1;
                            break;
                        case "A-017-7":
                            sumCorrect7 = sumCorrect7 + 1;
                            break;
                        case "A-017-8":
                            sumCorrect8 = sumCorrect8 + 1;
                            break;
                        case "A-017-9":
                            sumCorrect9 = sumCorrect9 + 1;
                            break;
                        case "A-017-10":
                            sumCorrect10 = sumCorrect10 + 1;
                            break;
                        case "A-017-11":
                            sumCorrect11 = sumCorrect11 + 1;
                            break;
                        case "A-017-12":
                            sumCorrect12 = sumCorrect12 + 1;
                            break;
                        case "A-017-13":
                            sumCorrect13 = sumCorrect13 + 1;
                            break;
                        case "A-017-14":
                            sumCorrect14 = sumCorrect14 + 1;
                            break;
                        case "A-017-15":
                            sumCorrect15 = sumCorrect15 + 1;
                            break;
                        case "A-017-16":
                            sumCorrect16 = sumCorrect16 + 1;
                            break;
                        case "A-017-17":
                            sumCorrect17 = sumCorrect17 + 1;
                            break;
                        case "A-017-18":
                            sumCorrect18 = sumCorrect18 + 1;
                            break;
                        case "A-017-19":
                            sumCorrect19 = sumCorrect19 + 1;
                            break;
                        case "A-017-20":
                            sumCorrect20 = sumCorrect20 + 1;
                            break;
                        case "A-017-21":
                            sumCorrect21 = sumCorrect21 + 1;
                            break;
                        case "A-017-22":
                            sumCorrect22 = sumCorrect22 + 1;
                            break;
                        case "A-017-23":
                            sumCorrect23 = sumCorrect23 + 1;
                            break;
                        case "A-017-24":
                            sumCorrect24 = sumCorrect24 + 1;
                            break;
                        case "A-017-25":
                            sumCorrect25 = sumCorrect25 + 1;
                            break;
                        case "A-017-26":
                            sumCorrect26 = sumCorrect26 + 1;
                            break;
                        case "A-017-27":
                            sumCorrect27 = sumCorrect27 + 1;
                            break;
                        case "A-017-28":
                            sumCorrect28 = sumCorrect28 + 1;
                            break;
                        case "A-017-29":
                            sumCorrect29 = sumCorrect29 + 1;
                            break;
                        case "A-017-30":
                            sumCorrect30 = sumCorrect30 + 1;
                            break;
                        case "A-017-31":
                            sumCorrect31 = sumCorrect31 + 1;
                            break;
                        case "A-017-32":
                            sumCorrect32 = sumCorrect32 + 1;
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (s) {
                        case "A-017-1":
                            sumError1 = sumError1 + 1;
                            break;
                        case "A-017-2":
                            sumError2 = sumError2 + 1;
                            break;
                        case "A-017-3":
                            sumError3 = sumError3 + 1;
                            break;
                        case "A-017-4":
                            sumError4 = sumError4 + 1;
                            break;
                        case "A-017-5":
                            sumError5 = sumError5 + 1;
                            break;
                        case "A-017-6":
                            sumError6 = sumError6 + 1;
                            break;
                        case "A-017-7":
                            sumError7 = sumError7 + 1;
                            break;
                        case "A-017-8":
                            sumError8 = sumError8 + 1;
                            break;
                        case "A-017-9":
                            sumError9 = sumError9 + 1;
                            break;
                        case "A-017-10":
                            sumError10 = sumError10 + 1;
                            break;
                        case "A-017-11":
                            sumError11 = sumError11 + 1;
                            break;
                        case "A-017-12":
                            sumError12 = sumError12 + 1;
                            break;
                        case "A-017-13":
                            sumError13 = sumError13 + 1;
                            break;
                        case "A-017-14":
                            sumError14 = sumError14 + 1;
                            break;
                        case "A-017-15":
                            sumError15 = sumError15 + 1;
                            break;
                        case "A-017-16":
                            sumError16 = sumError16 + 1;
                            break;
                        case "A-017-17":
                            sumError17 = sumError17 + 1;
                            break;
                        case "A-017-18":
                            sumError18 = sumError18 + 1;
                            break;
                        case "A-017-19":
                            sumError19 = sumError19 + 1;
                            break;
                        case "A-017-20":
                            sumError20 = sumError20 + 1;
                            break;
                        case "A-017-21":
                            sumError21 = sumError21 + 1;
                            break;
                        case "A-017-22":
                            sumError22 = sumError22 + 1;
                            break;
                        case "A-017-23":
                            sumError23 = sumError23 + 1;
                            break;
                        case "A-017-24":
                            sumError24 = sumError24 + 1;
                            break;
                        case "A-017-25":
                            sumError25 = sumError25 + 1;
                            break;
                        case "A-017-26":
                            sumError26 = sumError26 + 1;
                            break;
                        case "A-017-27":
                            sumError27 = sumError27 + 1;
                            break;
                        case "A-017-28":
                            sumError28 = sumError28 + 1;
                            break;
                        case "A-017-29":
                            sumError29 = sumError29 + 1;
                            break;
                        case "A-017-30":
                            sumError30 = sumError30 + 1;
                            break;
                        case "A-017-31":
                            sumError31 = sumError31 + 1;
                            break;
                        case "A-017-32":
                            sumError32 = sumError32 + 1;
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        int totalCorrect1 = sumCorrect1 + sumCorrect2 + sumCorrect3;
        int totalCorrect2 = sumCorrect4 + sumCorrect5;
        int totalCorrect3 = sumCorrect6 + sumCorrect7;
        int totalCorrect4 = sumCorrect8 + sumCorrect9 + sumCorrect10 + sumCorrect11;
        int totalCorrect5 = sumCorrect12 + sumCorrect13 + sumCorrect14 + sumCorrect15 + sumCorrect16;
        int totalCorrect6 = sumCorrect17 + sumCorrect18 + sumCorrect18 + sumCorrect20;
        int totalCorrect7 = sumCorrect21 + sumCorrect22 + sumCorrect23;
        int totalCorrect8 = sumCorrect24 + sumCorrect25 + sumCorrect26;
        int totalCorrect9 = sumCorrect27 + sumCorrect28 + sumCorrect29;
        int totalCorrect10 = sumCorrect30 + sumCorrect31 + sumCorrect32;

        int totalError1 = sumError1 + sumError2 + sumError3;
        int totalError2 = sumError4 + sumError5;
        int totalError3 = sumError6 + sumError7;
        int totalError4 = sumError8 + sumError9 + sumError10 + sumError11;
        int totalError5 = sumError12 + sumError13 + sumError14 + sumError15 + sumError16;
        int totalError6 = sumError17 + sumError18 + sumError18 + sumError20;
        int totalError7 = sumError21 + sumError22 + sumError23;
        int totalError8 = sumError24 + sumError25 + sumError26;
        int totalError9 = sumError27 + sumError28 + sumError29;
        int totalError10 = sumError30 + sumError31 + sumError32;

        int sum1 = totalCorrect1 + totalError1;
        int sum2 = totalCorrect2 + totalError2;
        int sum3 = totalCorrect3 + totalError3;
        int sum4 = totalCorrect4 + totalError5;
        int sum5 = totalCorrect5 + totalError5;
        int sum6 = totalCorrect6 + totalError6;
        int sum7 = totalCorrect7 + totalError7;
        int sum8 = totalCorrect8 + totalError8;
        int sum9 = totalCorrect9 + totalError9;
        int sum10 = totalCorrect10 + totalError10;


        List<Map<String, Object>> dataList = Lists.newArrayList();

        Map<String, Object> valMap1 = Maps.newHashMap();
        double ratio1 = (double) totalCorrect1 / (double) sum1;
        String format1 = nt.format(ratio1);
        valMap1.put("一", format1);
        valMap1.put("总数", sum1);
        valMap1.put("正确总数", totalCorrect1);
        valMap1.put("错误总数", totalError1);
        dataList.add(valMap1);

        Map<String, Object> valMap2 = Maps.newHashMap();
        double ratio2 = (double) totalCorrect2 / (double) sum2;
        String format2 = nt.format(ratio2);
        valMap2.put("二", format2);
        valMap2.put("总数", sum2);
        valMap2.put("正确总数", totalCorrect2);
        valMap2.put("错误总数", totalError2);
        dataList.add(valMap2);

        Map<String, Object> valMap3 = Maps.newHashMap();
        double ratio3 = (double) totalCorrect3 / (double) sum3;
        String format3 = nt.format(ratio3);
        valMap3.put("三", format3);
        valMap3.put("总数", sum3);
        valMap3.put("正确总数", totalCorrect3);
        valMap3.put("错误总数", totalError3);
        dataList.add(valMap3);

        Map<String, Object> valMap4 = Maps.newHashMap();
        double ratio4 = (double) totalCorrect4 / (double) sum4;
        String format4 = nt.format(ratio4);
        valMap4.put("四", format4);
        valMap4.put("总数", sum4);
        valMap4.put("正确总数", totalCorrect4);
        valMap4.put("错误总数", totalError4);
        dataList.add(valMap4);

        Map<String, Object> valMap5 = Maps.newHashMap();
        double ratio5 = (double) totalCorrect5 / (double) sum5;
        String format5 = nt.format(ratio5);
        valMap5.put("五", format5);
        valMap5.put("总数", sum5);
        valMap5.put("正确总数", totalCorrect5);
        valMap5.put("错误总数", totalError5);
        dataList.add(valMap5);

        Map<String, Object> valMap6 = Maps.newHashMap();
        double ratio6 = (double) totalCorrect6 / (double) sum6;
        String format6 = nt.format(ratio6);
        valMap6.put("六", format6);
        valMap6.put("总数", sum6);
        valMap6.put("正确总数", totalCorrect6);
        valMap6.put("错误总数", totalError6);
        dataList.add(valMap6);

        Map<String, Object> valMap7 = Maps.newHashMap();
        double ratio7 = (double) totalCorrect7 / (double) sum7;
        String format7 = nt.format(ratio7);
        valMap7.put("七", format7);
        valMap7.put("总数", sum7);
        valMap7.put("正确总数", totalCorrect7);
        valMap7.put("错误总数", totalError7);
        dataList.add(valMap7);

        Map<String, Object> valMap8 = Maps.newHashMap();
        double ratio8 = (double) totalCorrect8 / (double) sum8;
        String format8 = nt.format(ratio8);
        valMap8.put("八", format8);
        valMap8.put("总数", sum8);
        valMap8.put("正确总数", totalCorrect8);
        valMap8.put("错误总数", totalError8);
        dataList.add(valMap8);

        Map<String, Object> valMap9 = Maps.newHashMap();
        double ratio9 = (double) totalCorrect9 / (double) sum9;
        String format9 = nt.format(ratio9);
        valMap9.put("九", format9);
        valMap9.put("总数", sum9);
        valMap9.put("正确总数", totalCorrect9);
        valMap9.put("错误总数", totalError9);
        dataList.add(valMap9);

        Map<String, Object> valMap10 = Maps.newHashMap();
        double ratio10 = (double) totalCorrect10 / (double) sum10;
        String format10 = nt.format(ratio10);
        valMap10.put("十", format10);
        valMap10.put("总数", sum10);
        valMap10.put("正确总数", totalCorrect10);
        valMap10.put("错误总数", totalError10);
        dataList.add(valMap10);

        return JsonResponse.success(dataList);
    }

    /**
     * 实体转换
     *
     * @param examInfoFullList 答题情况信息集合
     * @return 返回List<Temporary>
     */
    private List<Temporary> transition(List<Map<String, Object>> examInfoFullList) {
        List<Temporary> temporaryList = Lists.newArrayList();

        for (Map<String, Object> map : examInfoFullList) {
            Temporary temporary = new Temporary();
            if (StringUtils.isNotEmpty((String) map.get("USERNAME"))) {
                temporary.setUsername((String) map.get("USERNAME"));
            }
            if (StringUtils.isNotEmpty((String) map.get("TRUENAME"))) {
                temporary.setTruename((String) map.get("TRUENAME"));
            }
            if (StringUtils.isNotEmpty((String) map.get("PHONE"))) {
                temporary.setPhone((String) map.get("PHONE"));
            }
            if (StringUtils.isNotEmpty((String) map.get("COMPANYNAME"))) {
                temporary.setCompanyName((String) map.get("COMPANYNAME"));
            }
            if (StringUtils.isNotEmpty((String) map.get("DEPARTMENTNAME"))) {
                temporary.setDepartmentName((String) map.get("DEPARTMENTNAME"));
            }
            if (StringUtils.isNotEmpty((String) map.get("DISPLAYNAME"))) {
                temporary.setDisplayName((String) map.get("DISPLAYNAME"));
            }
            Object isfinish = map.get("ISFINISH");
            temporary.setIsFinish(isfinish);
            if (StringUtils.isNotEmpty((String) map.get("EXAMRECORD"))) {
                temporary.setExamRecord((String) map.get("EXAMRECORD"));
            }
            if (StringUtils.isNotEmpty((String) map.get("EXAMANSWER"))) {
                temporary.setExamAnswer((String) map.get("EXAMANSWER"));
            }
            if (StringUtils.isNotEmpty((String) map.get("POSITIONNAME"))) {
                temporary.setPositionName((String) map.get("POSITIONNAME"));
            }
            if (StringUtils.isNotEmpty((String) map.get("RESIDUETIME"))) {
                temporary.setPositionName((String) map.get("RESIDUETIME"));
            }
            if (StringUtils.isNotEmpty((String) map.get("SCORE"))) {
                temporary.setPositionName((String) map.get("SCORE"));
            }
            if (StringUtils.isNotEmpty((String) map.get("TITLE"))) {
                temporary.setPositionName((String) map.get("TITLE"));
            }

            temporaryList.add(temporary);


        }

        return temporaryList;
    }

    /**
     * 题目分析处理
     *
     * @param i            18个公司限制
     * @param problemsMap  公司答题情况集合
     * @param questionCode 题目编码
     * @param questionSize 题目数量
     * @param department   部门
     * @param positionName 职位集合
     * @return 返回ProblemsAnalyze实体
     */
    private ProblemsAnalyze problemsAnalyze(int i, Map<String, Object> problemsMap, String questionCode, int questionSize, String department, String[] positionName) {
        //初始化返回数据
        ProblemsAnalyze problemsAnalyze = new ProblemsAnalyze();

        //获取格式化对象
        NumberFormat nt = NumberFormat.getPercentInstance();
        //设置百分数精确度2即保留两位小数
        nt.setMinimumFractionDigits(2);

        //获取到各个公司的答题情况 带答案
        String company = (String) problemsMap.get("VALUE");
        //这里需要根据条件来处理
        List<Temporary> temporaryList = null;

        //全部人数
        int allSize = 0;
        //提前设置公司，避免计算时影响
        problemsAnalyze.setCompanyName(company);
        if (i <= Constants.EIGHTEEN) {
            if (StringUtils.isNotEmpty(department) || positionName != null) {
                temporaryList = getRawExamInfoByPositionName(company, department, positionName);
                allSize = getFinishPeopleByPositionName(company, department, positionName);
            }

            if (ListUtils.isEmpty(temporaryList)) {
                temporaryList = getRawExamConditionInfo(company, "hnjjwz");
                allSize = getCompanyShouldFinishPeople(company);
            }
        } else {
            //计算21部门的答题情况 ，固定公司为省公司，计算完部门整体答题情况后 重新将公司名称设置为省公司（初始为部门名称）
            temporaryList = getRawExamInfoByPositionName(Constants.COMPANY_S, company, positionName);
            allSize = getFinishPeopleByPositionName(Constants.COMPANY_S, company, positionName);
            //company=Constants.COMPANY_S;
        }

        //经理应答题人数
        int shouldSize = 0;

        //涉及县公司数量
        problemsAnalyze.setCountyQuantity(String.valueOf(getPrefecturesSize(company)));

        shouldSize = getTwoPrefecturesSize(company);

        //全部实际人数
        int rawSize = temporaryList.size();
        //int rawSize = 0;

        //经理实际完成人数
        int managerSize = 0;


        Map<String, Object> sumMap = Maps.newHashMap();
        Map<String, Integer> initCountMap = initCountMap();
        //处理答案中选项
        for (Temporary temporary : temporaryList) {

            String positionNames = temporary.getPositionName();
            //String username = temporary.getUsername();
            //String departmentName = temporary.getDepartmentName();
            Object isFinish = temporary.getIsFinish();
            String companyName = temporary.getCompanyName();


            //分公司实际二级处理
            if (i <= Constants.EIGHTEEN) {
                //获取分公司二级应完成人员
                if (judgePositionName2(positionNames)) {
                    shouldSize++;
                    //rawSize--;
                    //allSize--;
                }
                if (judgePositionName2(positionNames) && isFinish != null && Constants.SIGN_O.equals(isFinish.toString())) {
                    managerSize++;
                }

                if (judgePositionName(positionNames, companyName)) {
                    rawSize--;
                    allSize--;
                }
            }
            //省公司实际二级处理
            else {
                //获取省公司二级应完成人员
//                if(judgePositionName2(positionNames)){
//                    shouldSize++;
//
//                }
                if (judgePositionName2(positionNames) && Constants.SIGN_O.equals(isFinish.toString())) {
                    managerSize++;
                }
            }

            //获取答案编号
            String examRecord = temporary.getExamRecord();
            String[] splitRecord = examRecord.split(",");
            //获取回答答案
            String examAnswer = temporary.getExamAnswer();
            String[] splitAnswer = examAnswer.split(",");

            if (questionSize == 0) {
                for (int j = 0; j < splitRecord.length; j++) {
                    String answer = splitAnswer[j];
                    String record = splitRecord[j];
                    if (questionCode.equals(record)) {
                        Map<String, Integer> stringIntegerMap;
                        if (answer.contains("/")) {
                            stringIntegerMap = addUpMore(initCountMap, answer);
                        } else {
                            stringIntegerMap = addUpSingle(initCountMap, answer);
                        }
                        sumMap.put(questionCode, stringIntegerMap);
                    }
                }
            } else {
                for (int j = 0; j < questionSize; j++) {
                    String answer = splitAnswer[j];
                    String record = splitRecord[j];
                    if (questionCode.equals(record)) {
                        Map<String, Integer> stringIntegerMap;
                        if (answer.contains("/")) {
                            stringIntegerMap = addUpMore(initCountMap, answer);
                        } else {
                            stringIntegerMap = addUpSingle(initCountMap, answer);
                        }
                        sumMap.put(questionCode, stringIntegerMap);
                    }
                }
            }


        }

        int poor = shouldSize - managerSize;

        problemsAnalyze.setSecondManager(String.valueOf(managerSize));
        problemsAnalyze.setNoSecondManager(String.valueOf(poor));

        //计算答题比例三级比例
        double ratio1 = (double) managerSize / (double) shouldSize;
        double ratio2 = (double) poor / (double) shouldSize;
        problemsAnalyze.setSecondManagerRatio(nt.format(ratio1));
        problemsAnalyze.setNoSecondManagerRatio(nt.format(ratio2));

        //未完成人数
        int difference = allSize - rawSize;

        //计算全部答题比例
        double ratio = (double) rawSize / (double) allSize;
        double noRatio = (double) difference / (double) allSize;

        problemsAnalyze.setAllpation(String.valueOf(allSize));
        problemsAnalyze.setParticipation(String.valueOf(rawSize));
        problemsAnalyze.setNoParticipation(String.valueOf(difference));
        problemsAnalyze.setParticipationRatio(nt.format(ratio));
        problemsAnalyze.setNoParticipationRatio(nt.format(noRatio));

        //problemsAnalyze= disposeManagerPercentage(company,department,temporaryList,problemsAnalyze);

        return disposeProblemsAnalyze(sumMap, problemsAnalyze, questionCode);
    }

    /**
     * 题目分析处理
     *
     * @param i            18个公司限制
     * @param problemsMap  公司答题情况集合
     * @param questionCode 题目编码
     * @param questionSize 题目数量
     * @return 返回ProblemsAnalyze实体
     */
    private AnalyzeUnIon analyzeUnIon(int i, Map<String, Object> problemsMap, String questionCode, int questionSize, String examAppCode) {
        //初始化返回数据
        AnalyzeUnIon analyzeUnIon = new AnalyzeUnIon();

        //获取格式化对象
        NumberFormat nt = NumberFormat.getPercentInstance();
        //设置百分数精确度2即保留两位小数
        nt.setMinimumFractionDigits(2);

        //获取到各个公司的答题情况 带答案
        String company = (String) problemsMap.get("VALUE");
        //这里需要根据条件来处理
        List<Temporary> temporaryList;

        //全部人数
        int allSize = 0;
        //提前设置公司，避免计算时影响
        analyzeUnIon.setCompanyName(company);
//        if(i<Constants.EIGHTEEN){
//            temporaryList=getRawExamConditionInfo(company,examAppCode);
//            allSize= getCompanyShouldFinishPeople(company);
//        }
//        else {
//            //计算21部门的答题情况 ，固定公司为省公司，计算完部门整体答题情况后 重新将公司名称设置为省公司（初始为部门名称）
//            temporaryList= getRawExamInfoByPositionName(Constants.COMPANY_S,company,null);
//            allSize=getFinishPeopleByPositionName(Constants.COMPANY_S,company,null);
//        }

        temporaryList = getRawExamConditionInfo(company, examAppCode);
        allSize = getCompanyShouldFinishPeople(company);

        //全部实际人数
        int rawSize = temporaryList.size();

        Map<String, Object> sumMap = Maps.newHashMap();
        Map<String, Integer> initCountMap = initCountMap();
        //处理答案中选项
        for (Temporary temporary : temporaryList) {

            //获取答案编号
            String examRecord = temporary.getExamRecord();
            String[] splitRecord = examRecord.split(",");
            //获取回答答案
            String examAnswer = temporary.getExamAnswer();
            String[] splitAnswer = examAnswer.split(",");

            if (questionSize == 0) {
                for (int j = 0; j < splitRecord.length; j++) {
                    String answer = splitAnswer[j];
                    String record = splitRecord[j];
                    if (questionCode.equals(record)) {
                        Map<String, Integer> stringIntegerMap;
                        if (answer.contains("/")) {
                            stringIntegerMap = addUpMore(initCountMap, answer);
                        } else {
                            stringIntegerMap = addUpSingle(initCountMap, answer);
                        }
                        sumMap.put(questionCode, stringIntegerMap);
                    }
                }
            }

        }

        //未完成人数
        int difference = allSize - rawSize;

        //计算全部答题比例
        double ratio = (double) rawSize / (double) allSize;
        double noRatio = (double) difference / (double) allSize;

        analyzeUnIon.setAllpation(String.valueOf(allSize));
        analyzeUnIon.setParticipation(String.valueOf(rawSize));
        analyzeUnIon.setNoParticipation(String.valueOf(difference));
        analyzeUnIon.setParticipationRatio(nt.format(ratio));
        analyzeUnIon.setNoParticipationRatio(nt.format(noRatio));

        return disposeAnalyzeUnIon(sumMap, analyzeUnIon, questionCode);
    }


    /**
     * 单选累计增加
     *
     * @param counter
     * @param answer
     * @return
     */
    private Map<String, Integer> addUpSingle(Map<String, Integer> counter, String answer) {

        switch (answer) {
            case "A":
                int a = counter.get("A");
                a++;
                counter.put("A", a);
                break;
            case "B":
                int b = counter.get("B");
                b++;
                counter.put("B", b);
                break;
            case "C":
                int c = counter.get("C");
                c++;
                counter.put("C", c);
                break;
            case "D":
                int d = counter.get("D");
                d++;
                counter.put("D", d);
                break;
            case "E":
                int e = counter.get("E");
                e++;
                counter.put("E", e);
                break;
            case "F":
                int f = counter.get("F");
                f++;
                counter.put("F", f);
                break;
            case "G":
                int g = counter.get("G");
                g++;
                counter.put("G", g);
                break;
            case "H":
                int h = counter.get("H");
                h++;
                counter.put("H", h);
                break;
            case "I":
                int i = counter.get("I");
                i++;
                counter.put("I", i);
                break;
            case "J":
                int j = counter.get("J");
                j++;
                counter.put("J", j);
                break;
            case "K":
                int k = counter.get("K");
                k++;
                counter.put("K", k);
                break;
            case "L":
                int l = counter.get("L");
                l++;
                counter.put("L", l);
                break;
            default:
                break;

        }
        return counter;

    }

    /**
     * 多选累计增加
     *
     * @param counter
     * @param answer
     * @return
     */
    private Map<String, Integer> addUpMore(Map<String, Integer> counter, String answer) {

        String[] answerList = answer.split("/");

        for (String s : answerList) {
            switch (s) {
                case "A":
                    int a = counter.get("A");
                    a++;
                    counter.put("A", a);
                    break;
                case "B":
                    int b = counter.get("B");
                    b++;
                    counter.put("B", b);
                    break;
                case "C":
                    int c = counter.get("C");
                    c++;
                    counter.put("C", c);
                    break;
                case "D":
                    int d = counter.get("D");
                    d++;
                    counter.put("D", d);
                    break;
                case "E":
                    int e = counter.get("E");
                    e++;
                    counter.put("E", e);
                    break;
                case "F":
                    int f = counter.get("F");
                    f++;
                    counter.put("F", f);
                    break;
                case "G":
                    int g = counter.get("G");
                    g++;
                    counter.put("G", g);
                    break;
                case "H":
                    int h = counter.get("H");
                    h++;
                    counter.put("H", h);
                    break;
                case "I":
                    int i = counter.get("I");
                    i++;
                    counter.put("I", i);
                    break;
                case "J":
                    int j = counter.get("J");
                    j++;
                    counter.put("J", j);
                    break;
                case "K":
                    int k = counter.get("K");
                    k++;
                    counter.put("K", k);
                    break;
                case "L":
                    int l = counter.get("L");
                    l++;
                    counter.put("L", l);
                    break;
                default:
                    break;
            }
        }

        return counter;

    }

    /**
     * 初始化 map中数据
     *
     * @return
     */
    private Map<String, Integer> initCountMap() {
        Map<String, Integer> countMap = Maps.newHashMap();
        countMap.put("A", 0);
        countMap.put("B", 0);
        countMap.put("C", 0);
        countMap.put("D", 0);
        countMap.put("E", 0);
        countMap.put("F", 0);
        countMap.put("G", 0);
        countMap.put("H", 0);
        countMap.put("I", 0);
        countMap.put("J", 0);
        countMap.put("K", 0);
        countMap.put("L", 0);
        return countMap;
    }

    /**
     * 初始化sheet页的表头数据
     *
     * @param examQuestionAnswerList 答案集合
     * @return
     */
    private ProblemsAnalyze initProblemsAnalyzeList(List<ExamQuestionAnswer> examQuestionAnswerList) {
        ProblemsAnalyze problemsAnalyze = new ProblemsAnalyze();
        problemsAnalyze.setCompanyName("所属单位/部门");

        problemsAnalyze.setAllpation("全部人数(全部)");
        problemsAnalyze.setParticipation("参与人数(全部)");
        problemsAnalyze.setNoParticipation("未参与人数(全部)");

        problemsAnalyze.setParticipationRatio("参与比例(全部)");
        problemsAnalyze.setNoParticipationRatio("未参与比例(全部)");

        problemsAnalyze.setSecondManager("二级经理参与人数(全部)");
        problemsAnalyze.setNoSecondManager("二级经理未参与人数(全部)");

        problemsAnalyze.setSecondManagerRatio("二级经理参与比例(全部)");
        problemsAnalyze.setNoSecondManagerRatio("二级经理未参与比例(全部)");

        problemsAnalyze.setCountyQuantity("涉及县公司数量(个)");

        for (ExamQuestionAnswer examQuestionAnswer : examQuestionAnswerList) {
            String answerContent = examQuestionAnswer.getAnswerContent();
            if (StringUtils.isEmpty(problemsAnalyze.getOptionA())) {
                problemsAnalyze.setOptionA("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionB())) {
                problemsAnalyze.setOptionB("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionC())) {
                problemsAnalyze.setOptionC("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionD())) {
                problemsAnalyze.setOptionD("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionE())) {
                problemsAnalyze.setOptionE("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionF())) {
                problemsAnalyze.setOptionF("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionG())) {
                problemsAnalyze.setOptionG("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionH())) {
                problemsAnalyze.setOptionH("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionI())) {
                problemsAnalyze.setOptionI("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionJ())) {
                problemsAnalyze.setOptionJ("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionK())) {
                problemsAnalyze.setOptionK("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionL())) {
                problemsAnalyze.setOptionL("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionM())) {
                problemsAnalyze.setOptionM("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionN())) {
                problemsAnalyze.setOptionN("计数项：" + answerContent);
            }
        }

        for (ExamQuestionAnswer examQuestionAnswer : examQuestionAnswerList) {
            String answerContent = examQuestionAnswer.getAnswerContent();
            if (StringUtils.isEmpty(problemsAnalyze.getOptionA())) {
                problemsAnalyze.setOptionA("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionB())) {
                problemsAnalyze.setOptionB("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionC())) {
                problemsAnalyze.setOptionC("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionD())) {
                problemsAnalyze.setOptionD("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionE())) {
                problemsAnalyze.setOptionE("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionF())) {
                problemsAnalyze.setOptionF("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionG())) {
                problemsAnalyze.setOptionG("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionH())) {
                problemsAnalyze.setOptionH("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionI())) {
                problemsAnalyze.setOptionI("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionJ())) {
                problemsAnalyze.setOptionJ("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionK())) {
                problemsAnalyze.setOptionK("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionL())) {
                problemsAnalyze.setOptionL("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionM())) {
                problemsAnalyze.setOptionM("占比：" + answerContent);
            } else if (StringUtils.isEmpty(problemsAnalyze.getOptionN())) {
                problemsAnalyze.setOptionN("占比：" + answerContent);
            }
        }

        return problemsAnalyze;
    }


    /**
     * 初始化sheet页的表头数据
     *
     * @param examQuestionAnswerList 答案集合
     * @return
     */
    private AnalyzeUnIon initProblemsAnalyzeList2(List<ExamQuestionAnswer> examQuestionAnswerList) {
        AnalyzeUnIon analyzeUnIon = new AnalyzeUnIon();
        analyzeUnIon.setCompanyName("所属单位/部门");

        analyzeUnIon.setAllpation("全部人数(全部)");
        analyzeUnIon.setParticipation("参与人数(全部)");
        analyzeUnIon.setNoParticipation("未参与人数(全部)");

        analyzeUnIon.setParticipationRatio("参与比例(全部)");
        analyzeUnIon.setNoParticipationRatio("未参与比例(全部)");


        for (ExamQuestionAnswer examQuestionAnswer : examQuestionAnswerList) {
            String answerContent = examQuestionAnswer.getAnswerContent();
            if (StringUtils.isEmpty(analyzeUnIon.getOptionA())) {
                analyzeUnIon.setOptionA("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionB())) {
                analyzeUnIon.setOptionB("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionC())) {
                analyzeUnIon.setOptionC("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionD())) {
                analyzeUnIon.setOptionD("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionE())) {
                analyzeUnIon.setOptionE("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionF())) {
                analyzeUnIon.setOptionF("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionG())) {
                analyzeUnIon.setOptionG("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionH())) {
                analyzeUnIon.setOptionH("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionI())) {
                analyzeUnIon.setOptionI("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionJ())) {
                analyzeUnIon.setOptionJ("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionK())) {
                analyzeUnIon.setOptionK("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionL())) {
                analyzeUnIon.setOptionL("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionM())) {
                analyzeUnIon.setOptionM("计数项：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionN())) {
                analyzeUnIon.setOptionN("计数项：" + answerContent);
            }
        }

        for (ExamQuestionAnswer examQuestionAnswer : examQuestionAnswerList) {
            String answerContent = examQuestionAnswer.getAnswerContent();
            if (StringUtils.isEmpty(analyzeUnIon.getOptionA())) {
                analyzeUnIon.setOptionA("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionB())) {
                analyzeUnIon.setOptionB("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionC())) {
                analyzeUnIon.setOptionC("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionD())) {
                analyzeUnIon.setOptionD("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionE())) {
                analyzeUnIon.setOptionE("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionF())) {
                analyzeUnIon.setOptionF("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionG())) {
                analyzeUnIon.setOptionG("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionH())) {
                analyzeUnIon.setOptionH("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionI())) {
                analyzeUnIon.setOptionI("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionJ())) {
                analyzeUnIon.setOptionJ("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionK())) {
                analyzeUnIon.setOptionK("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionL())) {
                analyzeUnIon.setOptionL("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionM())) {
                analyzeUnIon.setOptionM("占比：" + answerContent);
            } else if (StringUtils.isEmpty(analyzeUnIon.getOptionN())) {
                analyzeUnIon.setOptionN("占比：" + answerContent);
            }
        }

        return analyzeUnIon;
    }

    /**
     * 对最后处理过的题目进行放人实体中
     *
     * @param sumMap          题目集合 带每个选项多少人选
     * @param problemsAnalyze 返回的数据
     * @param questionCode    题目编号
     * @return 返回实体
     */
    private ProblemsAnalyze disposeProblemsAnalyze(Map<String, Object> sumMap, ProblemsAnalyze problemsAnalyze, String questionCode) {


        Map<String, Integer> stringIntegerMap = (Map<String, Integer>) sumMap.get(questionCode);

        if (!MapUtils.isEmpty(stringIntegerMap)) {

            Iterator<String> iterator2 = stringIntegerMap.keySet().iterator();
            while (iterator2.hasNext()) {
                String key = iterator2.next();
                Integer integer = stringIntegerMap.get(key);
                switch (key) {
                    case "A":
                        problemsAnalyze.setOptionA(String.valueOf(integer));
                        break;
                    case "B":
                        problemsAnalyze.setOptionB(String.valueOf(integer));
                        break;
                    case "C":
                        problemsAnalyze.setOptionC(String.valueOf(integer));
                        break;
                    case "D":
                        problemsAnalyze.setOptionD(String.valueOf(integer));
                        break;
                    case "E":
                        problemsAnalyze.setOptionE(String.valueOf(integer));
                        break;
                    case "F":
                        problemsAnalyze.setOptionF(String.valueOf(integer));
                        break;
                    case "G":
                        problemsAnalyze.setOptionG(String.valueOf(integer));
                        break;
                    case "H":
                        problemsAnalyze.setOptionH(String.valueOf(integer));
                        break;
                    case "I":
                        problemsAnalyze.setOptionI(String.valueOf(integer));
                        break;
                    case "J":
                        problemsAnalyze.setOptionJ(String.valueOf(integer));
                        break;
                    case "K":
                        problemsAnalyze.setOptionK(String.valueOf(integer));
                        break;
                    case "L":
                        problemsAnalyze.setOptionL(String.valueOf(integer));
                        break;
                    default:
                        break;
                }
            }
        }

        return problemsAnalyze;
    }


    /**
     * 对最后处理过的题目进行放人实体中
     *
     * @param sumMap       题目集合 带每个选项多少人选
     * @param analyzeUnIon 返回的数据
     * @param questionCode 题目编号
     * @return 返回实体
     */
    private AnalyzeUnIon disposeAnalyzeUnIon(Map<String, Object> sumMap, AnalyzeUnIon analyzeUnIon, String questionCode) {


        Map<String, Integer> stringIntegerMap = (Map<String, Integer>) sumMap.get(questionCode);

        if (!MapUtils.isEmpty(stringIntegerMap)) {

            Iterator<String> iterator2 = stringIntegerMap.keySet().iterator();
            while (iterator2.hasNext()) {
                String key = iterator2.next();
                Integer integer = stringIntegerMap.get(key);
                switch (key) {
                    case "A":
                        analyzeUnIon.setOptionA(String.valueOf(integer));
                        break;
                    case "B":
                        analyzeUnIon.setOptionB(String.valueOf(integer));
                        break;
                    case "C":
                        analyzeUnIon.setOptionC(String.valueOf(integer));
                        break;
                    case "D":
                        analyzeUnIon.setOptionD(String.valueOf(integer));
                        break;
                    case "E":
                        analyzeUnIon.setOptionE(String.valueOf(integer));
                        break;
                    case "F":
                        analyzeUnIon.setOptionF(String.valueOf(integer));
                        break;
                    case "G":
                        analyzeUnIon.setOptionG(String.valueOf(integer));
                        break;
                    case "H":
                        analyzeUnIon.setOptionH(String.valueOf(integer));
                        break;
                    case "I":
                        analyzeUnIon.setOptionI(String.valueOf(integer));
                        break;
                    case "J":
                        analyzeUnIon.setOptionJ(String.valueOf(integer));
                        break;
                    case "K":
                        analyzeUnIon.setOptionK(String.valueOf(integer));
                        break;
                    case "L":
                        analyzeUnIon.setOptionL(String.valueOf(integer));
                        break;
                    default:
                        break;
                }
            }
        }

        return analyzeUnIon;
    }


    /**
     * 处理各项占总数的百分比
     *
     * @param problemsAnalyze
     * @return
     */
    private ProblemsAnalyze disposePercentage(ProblemsAnalyze problemsAnalyze, int answerSize) {

        //获取格式化对象
        NumberFormat nt = NumberFormat.getPercentInstance();
        //设置百分数精确度2即保留两位小数
        nt.setMinimumFractionDigits(2);
        int participation = Integer.parseInt(problemsAnalyze.getParticipation());

        List<String> stringList = traverseProblemsAnalyze(problemsAnalyze);
        List<String> newList = Lists.newArrayList();
        for (String s : stringList) {
            if (!StringUtils.isEmpty(s)) {

                int i = Integer.parseInt(s);
                //计算答题比例
                double ratio = (double) i / (double) participation;
                newList.add(nt.format(ratio));

            }
        }

        return traverseProblemsAnalyze(newList, problemsAnalyze, answerSize);
    }

    /**
     * 处理各项占总数的百分比
     *
     * @param analyzeUnIon
     * @return
     */
    private AnalyzeUnIon disposePercentage(AnalyzeUnIon analyzeUnIon, int answerSize) {

        //获取格式化对象
        NumberFormat nt = NumberFormat.getPercentInstance();
        //设置百分数精确度2即保留两位小数
        nt.setMinimumFractionDigits(2);
        int participation = Integer.parseInt(analyzeUnIon.getParticipation());

        List<String> stringList = traverseProblemsAnalyze(analyzeUnIon);
        List<String> newList = Lists.newArrayList();
        for (String s : stringList) {
            if (!StringUtils.isEmpty(s)) {

                int i = Integer.parseInt(s);
                //计算答题比例
                double ratio = (double) i / (double) participation;
                newList.add(nt.format(ratio));

            }
        }

        return traverseProblemsAnalyze(newList, analyzeUnIon, answerSize);
    }

    /**
     * 将选项转换为list
     *
     * @param problemsAnalyze 需要转换的对象
     * @return 返回list
     */
    private List<String> traverseProblemsAnalyze(ProblemsAnalyze problemsAnalyze) {

        List<String> stringList = Lists.newArrayList();
        stringList.add(problemsAnalyze.getOptionA());
        stringList.add(problemsAnalyze.getOptionB());
        stringList.add(problemsAnalyze.getOptionC());
        stringList.add(problemsAnalyze.getOptionD());
        stringList.add(problemsAnalyze.getOptionE());
        stringList.add(problemsAnalyze.getOptionF());
        stringList.add(problemsAnalyze.getOptionG());
        stringList.add(problemsAnalyze.getOptionH());
        stringList.add(problemsAnalyze.getOptionI());
        stringList.add(problemsAnalyze.getOptionJ());
        stringList.add(problemsAnalyze.getOptionK());
        stringList.add(problemsAnalyze.getOptionL());
        stringList.add(problemsAnalyze.getOptionM());
        stringList.add(problemsAnalyze.getOptionN());
        stringList.add(problemsAnalyze.getOptionO());
        stringList.add(problemsAnalyze.getOptionP());
        stringList.add(problemsAnalyze.getOptionQ());
        stringList.add(problemsAnalyze.getOptionR());
        stringList.add(problemsAnalyze.getOptionS());
        stringList.add(problemsAnalyze.getOptionT());
        stringList.add(problemsAnalyze.getOptionU());
        stringList.add(problemsAnalyze.getOptionV());
        stringList.add(problemsAnalyze.getOptionW());
        stringList.add(problemsAnalyze.getOptionX());
        stringList.add(problemsAnalyze.getOptionY());
        stringList.add(problemsAnalyze.getOptionZ());

        return stringList;
    }

    /**
     * 将选项转换为list
     *
     * @param analyzeUnIon 需要转换的对象
     * @return 返回list
     */
    private List<String> traverseProblemsAnalyze(AnalyzeUnIon analyzeUnIon) {

        List<String> stringList = Lists.newArrayList();
        stringList.add(analyzeUnIon.getOptionA());
        stringList.add(analyzeUnIon.getOptionB());
        stringList.add(analyzeUnIon.getOptionC());
        stringList.add(analyzeUnIon.getOptionD());
        stringList.add(analyzeUnIon.getOptionE());
        stringList.add(analyzeUnIon.getOptionF());
        stringList.add(analyzeUnIon.getOptionG());
        stringList.add(analyzeUnIon.getOptionH());
        stringList.add(analyzeUnIon.getOptionI());
        stringList.add(analyzeUnIon.getOptionJ());
        stringList.add(analyzeUnIon.getOptionK());
        stringList.add(analyzeUnIon.getOptionL());
        stringList.add(analyzeUnIon.getOptionM());
        stringList.add(analyzeUnIon.getOptionN());
        stringList.add(analyzeUnIon.getOptionO());
        stringList.add(analyzeUnIon.getOptionP());
        stringList.add(analyzeUnIon.getOptionQ());
        stringList.add(analyzeUnIon.getOptionR());
        stringList.add(analyzeUnIon.getOptionS());
        stringList.add(analyzeUnIon.getOptionT());
        stringList.add(analyzeUnIon.getOptionU());
        stringList.add(analyzeUnIon.getOptionV());
        stringList.add(analyzeUnIon.getOptionW());
        stringList.add(analyzeUnIon.getOptionX());
        stringList.add(analyzeUnIon.getOptionY());
        stringList.add(analyzeUnIon.getOptionZ());

        return stringList;
    }

    /**
     * 百分比处理过的list 重新赋值进数据中
     *
     * @param stringList      百分比处理后的集合
     * @param problemsAnalyze 对象
     * @return 返回转换完成的对象
     */
    private ProblemsAnalyze traverseProblemsAnalyze(List<String> stringList, ProblemsAnalyze problemsAnalyze, int answerSize) {

        int size = stringList.size();

        int index = 0;
        if (0 < size && 0 > answerSize) {
            problemsAnalyze.setOptionA(stringList.get(index));
            index++;
        }
        if (1 < size && 1 > answerSize) {
            problemsAnalyze.setOptionB(stringList.get(index));
            index++;
        }
        if (2 < size && 2 > answerSize) {
            problemsAnalyze.setOptionC(stringList.get(index));
            index++;
        }
        if (3 < size && 3 > answerSize) {
            problemsAnalyze.setOptionD(stringList.get(index));
            index++;
        }
        if (4 < size && 4 > answerSize) {
            problemsAnalyze.setOptionE(stringList.get(index));
            index++;
        }
        if (5 < size && 5 > answerSize) {
            problemsAnalyze.setOptionF(stringList.get(index));
            index++;
        }
        if (6 < size && 6 > answerSize) {
            problemsAnalyze.setOptionG(stringList.get(index));
            index++;
        }
        if (7 < size && 7 > answerSize) {
            problemsAnalyze.setOptionH(stringList.get(index));
            index++;
        }
        if (8 < size && 8 > answerSize) {
            problemsAnalyze.setOptionI(stringList.get(index));
            index++;
        }
        if (9 < size && 9 > answerSize) {
            problemsAnalyze.setOptionJ(stringList.get(index));
            index++;
        }
        if (10 < size && 10 > answerSize) {
            problemsAnalyze.setOptionK(stringList.get(index));
            index++;
        }
        if (11 < size && 11 > answerSize) {
            problemsAnalyze.setOptionL(stringList.get(index));
            index++;
        }
        if (12 < size && 12 > answerSize) {
            problemsAnalyze.setOptionM(stringList.get(index));
            index++;
        }
        if (13 < size && 13 > answerSize) {
            problemsAnalyze.setOptionN(stringList.get(index));
            index++;
        }
        if (14 < size && 14 > answerSize) {
            problemsAnalyze.setOptionO(stringList.get(index));
            index++;
        }
        if (15 < size && 15 > answerSize) {
            problemsAnalyze.setOptionP(stringList.get(index));
            index++;
        }
        if (16 < size && 16 > answerSize) {
            problemsAnalyze.setOptionQ(stringList.get(index));
            index++;
        }
        if (17 < size && 17 > answerSize) {
            problemsAnalyze.setOptionR(stringList.get(index));
            index++;
        }
        if (18 < size && 18 > answerSize) {
            problemsAnalyze.setOptionS(stringList.get(index));
            index++;
        }
        if (19 < size && 19 > answerSize) {
            problemsAnalyze.setOptionT(stringList.get(index));
            index++;
        }
        if (20 < size && 20 > answerSize) {
            problemsAnalyze.setOptionU(stringList.get(index));
            index++;
        }
        if (21 < size && 21 > answerSize) {
            problemsAnalyze.setOptionV(stringList.get(index));
            index++;
        }
        if (22 < size && 22 > answerSize) {
            problemsAnalyze.setOptionW(stringList.get(index));
            index++;
        }
        if (23 < size && 23 > answerSize) {
            problemsAnalyze.setOptionX(stringList.get(index));
            index++;
        }
        if (24 < size && 24 > answerSize) {
            problemsAnalyze.setOptionY(stringList.get(index));
            index++;
        }
        if (25 <= size && 25 > answerSize) {
            problemsAnalyze.setOptionZ(stringList.get(index));
        }
        return problemsAnalyze;
    }


    /**
     * 百分比处理过的list 重新赋值进数据中
     *
     * @param stringList   百分比处理后的集合
     * @param analyzeUnIon 对象
     * @return 返回转换完成的对象
     */
    private AnalyzeUnIon traverseProblemsAnalyze(List<String> stringList, AnalyzeUnIon analyzeUnIon, int answerSize) {

        int size = stringList.size();

        int index = 0;
        if (0 < size && 0 > answerSize) {
            analyzeUnIon.setOptionA(stringList.get(index));
            index++;
        }
        if (1 < size && 1 > answerSize) {
            analyzeUnIon.setOptionB(stringList.get(index));
            index++;
        }
        if (2 < size && 2 > answerSize) {
            analyzeUnIon.setOptionC(stringList.get(index));
            index++;
        }
        if (3 < size && 3 > answerSize) {
            analyzeUnIon.setOptionD(stringList.get(index));
            index++;
        }
        if (4 < size && 4 > answerSize) {
            analyzeUnIon.setOptionE(stringList.get(index));
            index++;
        }
        if (5 < size && 5 > answerSize) {
            analyzeUnIon.setOptionF(stringList.get(index));
            index++;
        }
        if (6 < size && 6 > answerSize) {
            analyzeUnIon.setOptionG(stringList.get(index));
            index++;
        }
        if (7 < size && 7 > answerSize) {
            analyzeUnIon.setOptionH(stringList.get(index));
            index++;
        }
        if (8 < size && 8 > answerSize) {
            analyzeUnIon.setOptionI(stringList.get(index));
            index++;
        }
        if (9 < size && 9 > answerSize) {
            analyzeUnIon.setOptionJ(stringList.get(index));
            index++;
        }
        if (10 < size && 10 > answerSize) {
            analyzeUnIon.setOptionK(stringList.get(index));
            index++;
        }
        if (11 < size && 11 > answerSize) {
            analyzeUnIon.setOptionL(stringList.get(index));
            index++;
        }
        if (12 < size && 12 > answerSize) {
            analyzeUnIon.setOptionM(stringList.get(index));
            index++;
        }
        if (13 < size && 13 > answerSize) {
            analyzeUnIon.setOptionN(stringList.get(index));
            index++;
        }
        if (14 < size && 14 > answerSize) {
            analyzeUnIon.setOptionO(stringList.get(index));
            index++;
        }
        if (15 < size && 15 > answerSize) {
            analyzeUnIon.setOptionP(stringList.get(index));
            index++;
        }
        if (16 < size && 16 > answerSize) {
            analyzeUnIon.setOptionQ(stringList.get(index));
            index++;
        }
        if (17 < size && 17 > answerSize) {
            analyzeUnIon.setOptionR(stringList.get(index));
            index++;
        }
        if (18 < size && 18 > answerSize) {
            analyzeUnIon.setOptionS(stringList.get(index));
            index++;
        }
        if (19 < size && 19 > answerSize) {
            analyzeUnIon.setOptionT(stringList.get(index));
            index++;
        }
        if (20 < size && 20 > answerSize) {
            analyzeUnIon.setOptionU(stringList.get(index));
            index++;
        }
        if (21 < size && 21 > answerSize) {
            analyzeUnIon.setOptionV(stringList.get(index));
            index++;
        }
        if (22 < size && 22 > answerSize) {
            analyzeUnIon.setOptionW(stringList.get(index));
            index++;
        }
        if (23 < size && 23 > answerSize) {
            analyzeUnIon.setOptionX(stringList.get(index));
            index++;
        }
        if (24 < size && 24 > answerSize) {
            analyzeUnIon.setOptionY(stringList.get(index));
            index++;
        }
        if (25 <= size && 25 > answerSize) {
            analyzeUnIon.setOptionZ(stringList.get(index));
        }
        return analyzeUnIon;
    }

    /**
     * 获取县公司数量
     *
     * @param companyName
     * @return
     */
    private int getPrefecturesSize(String companyName) {

        Map<String, Object> map = Maps.newHashMap();

        map.put("companyName", companyName);
        String sql = " select distinct jg.companyName" +
                "  from (SELECT distinct up.truename        truename," +
                "                        up.username        username," +
                //"                        up.displayname     displayName," +
                "                        up.companyName     companyName," +
                "                        up.departmentName  departmentName" +
                //"                        ei.position_name    positionName" +
                "          FROM uums.V_USER_ORG_POSITION up" +
                "         WHERE up.displayName LIKE concat(:companyName, '%')" +
                "           AND up.positionName != '一线员工'" +
                "           AND up.companyName!=:companyName" +
                "           AND up.userType = '1'" +
                " ORDER BY up.departmentName, up.username ASC) jg" +
                " where jg.username not in (select username from TEMP)";
        List<Map<String, Object>> maps = customDynamicWhere.queryNamedParameterForList(sql, map);

        return transition(maps).size();

    }


    /**
     * 获取省公司二级以上人员
     *
     * @param companyName
     * @return
     */
    private int getTwoPrefecturesSize(String companyName) {

        Map<String, Object> map = Maps.newHashMap();

        map.put("companyName", companyName);
        String sql = " select distinct jg.*" +
                "          from (SELECT distinct up.username,up.truename " +
                "                  FROM uums.V_USER_ORG_POSITION up " +
                "                 WHERE  up.displayName LIKE concat('省公司','%')" +
                "                   AND up.positionName in('副总经理','总经理')" +
                "                   AND up.departmentName =:companyName" +
                "                   AND up.userType = '1') jg" +
                "         where jg.username not in (select username from TEMP) ORDER BY jg.username ASC   ";
        List<Map<String, Object>> maps = customDynamicWhere.queryNamedParameterForList(sql, map);

        return transition(maps).size();

    }

    private boolean judgePositionName(String positionName, String companyName) {

        if (!positionName.contains("资深经理")) {

            return false;
        }
        if (positionName.contains("资深经理")) {
            if (companyName.contains("部") || companyName.contains("中心") || companyName.contains("办公室")) {
                return false;
            }
        }

        return true;


    }

    private boolean judgePositionName2(String positionName) {

        if (positionName.contains("总经理") || positionName.contains("副总经理")) {

            return true;
        }


        return false;

    }

    /**
     * 2024xszg_ks 定制化导出
     */
    @Override
    public void exportExcelBy2024xszg_ks(HttpServletRequest request, HttpServletResponse response) {
        String examCode = "2024xszg_ks";
        String examAppCode = "2024xszg_sj";

        //设置导出Excel的名称
        String targetFileName = String.format("%s_考试结果信息导出.xls", examCode);
        // 获取到考试信息
        List<ExamInfoVO> infos = this.getExamInfoByExamCode(examCode);
        // 通过map去重
        infos = new ArrayList<>(infos.stream().collect(Collectors.toMap(
                ExamInfoVO::getUsername,
                v -> v,
                (source, target) -> source // Resolve duplicates by keeping the first encountered object
        )).values());

        // 通用考试信息统计
        List<InfoVO> infoVOS = getInfoVOList(infos);

        // 获取题目与答案信息
        List<SimpleQuestion> sqs = this.getSimpleQuestionList(infos);

        infos = infos.parallelStream().filter(v -> StringUtils.isNotBlank(v.getExamRecord())).collect(Collectors.toList());

        // 定制化赋值分数
        HashMap<String, Object> groupMap = dealCustomizeQuestion2024xszg_ks(sqs);

        // 获取部门得分
        List<DeptInfoBy2024xszg_ks> deptInfo = this.getDeptInfo(infos, sqs);


        // 构造sheet页
        List<Map<String, Object>> sheetsList = Lists.newArrayList();
        Map<String, Object> sheetMap = Maps.newHashMap();
        sheetMap.put("title", new ExportParams("通用统计", "通用统计"));
        sheetMap.put("entity", InfoVO.class);
        sheetMap.put("data", infoVOS);
        sheetsList.add(sheetMap);
        sheetMap = Maps.newHashMap();
        sheetMap.put("title", new ExportParams("得分统计", "得分统计"));
        sheetMap.put("entity", DeptInfoBy2024xszg_ks.class);
        sheetMap.put("data", deptInfo);
        sheetsList.add(sheetMap);

        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        ExcelUtils.downLoadExcel(targetFileName, response, workbook);
    }

    /**
     * 数据结构转换
     */
    private List<DeptInfoBy2024xszg_ks> getDeptInfo(List<ExamInfoVO> infos, List<SimpleQuestion> sqs) {
        // 获取部门得分
        Map<String, Double> scoreMap = computeScoreBy2024xszg_ks(infos, sqs);

        DecimalFormat df = new DecimalFormat("#.00");
        List<DeptInfoBy2024xszg_ks> deptInfo = new ArrayList<>();
        scoreMap.forEach((k, v) -> {
            DeptInfoBy2024xszg_ks info = new DeptInfoBy2024xszg_ks();
            info.setDepartmentName(k);
            info.setScore(df.format(v));
            deptInfo.add(info);
        });
        deptInfo.sort(Comparator.comparing(DeptInfoBy2024xszg_ks::getDepartmentName));
        return deptInfo;
    }

    /**
     * 计算部门得分
     */
    private static Map<String, Double> computeScoreBy2024xszg_ks(List<ExamInfoVO> infos, List<SimpleQuestion> sqs) {
        // 1. 计算 认可度得分
        infos.forEach(v -> {
            // 消除数据影响
            List<String> answers = Arrays.stream(v.getExamAnswer().substring(14).split(",")).collect(Collectors.toList());
            String score = "10";
            for (int i = 0; i < sqs.size(); i++) {
                String answer = answers.get(i);
                String reduce = sqs.get(i).getAnswerList().stream()
                        .filter(a -> answer.contains(a.getAnswerCode()))
                        .map(SimpleQuestionAnswer::getAnswerScore)
                        .reduce("0", (a, b) -> String.valueOf(new BigDecimal(a).add(new BigDecimal(b))));
                log.info("题号：{} 扣分值：{}", i + 1, reduce);
                score = String.valueOf(new BigDecimal(score).subtract(new BigDecimal(reduce)));
            }
            v.setScore(score);
            log.info("答题人 {} 认可度得分：{}", v.getUsername(), score);
        });
        // 2. 计算总得分 问卷得分=认可度得分-扣分值
        // 按部门分组
        Map<String, List<ExamInfoVO>> deptMap = infos.stream()
                .collect(Collectors.groupingBy(ExamInfoVO::getDepartmentName, Collectors.toList()));
        // 计算每个部门平均分
        Map<String, Double> averageMap = infos.stream()
                .collect(Collectors.groupingBy(ExamInfoVO::getDepartmentName, Collectors.averagingDouble(v -> Double.parseDouble(v.getScore()))));
        // 部门 扣分项计算
        deptMap.forEach((k, v) -> {
            // 判断第一题选C的数量是否大于40%
            long count = v.stream().filter(info -> Objects.equals(info.getExamAnswer().substring(14, 15), "C")).count();
            BigDecimal ratio = BigDecimal.valueOf(count).divide(BigDecimal.valueOf(v.size()), 2, RoundingMode.HALF_UP); // 保留两位小数，四舍五入
            boolean isMoreThan40Percent = ratio.compareTo(BigDecimal.valueOf(0.4)) >= 0;
            log.info("部门 {} 第一题选C的数量 {} 比例 {}", k, count, ratio);
            if (isMoreThan40Percent) {
                log.info("部门平均分 {} 按比例扣分项分数 {} 最终得分 {}", averageMap.get(k), 2, averageMap.get(k) - 2);
                averageMap.put(k, averageMap.get(k) - 2);
            } else {
                // 0-2线性扣分
                BigDecimal decimal = ratio.multiply(BigDecimal.valueOf(5)).min(BigDecimal.valueOf(2));
                log.info("部门平均分 {} 按线性扣分项分数 {} 最终得分 {}", averageMap.get(k), decimal, averageMap.get(k) - decimal.doubleValue());
                averageMap.put(k, averageMap.get(k) - decimal.doubleValue());
            }
        });
        return averageMap;
    }

    /**
     * 定制扣分项
     */
    private static HashMap<String, Object> dealCustomizeQuestion2024xszg_ks(List<SimpleQuestion> sqs) {
        HashMap<String, Object> groupMap = Maps.newHashMap();
        //        组织领导
        groupMap.put("1", Arrays.asList(sqs.get(1), sqs.get(2), sqs.get(7), sqs.get(8)));
        //        日常推动
        groupMap.put("2", Arrays.asList(sqs.get(3), sqs.get(9), sqs.get(12)));
        //        向下延伸
        groupMap.put("3", Arrays.asList(sqs.get(4), sqs.get(5), sqs.get(10)));
        //        长效机制建设
        groupMap.put("4", Arrays.asList(sqs.get(11)));
        //        整改效果
        groupMap.put("5", Arrays.asList(sqs.get(6)));
        sqs.forEach(v -> v.setQuestionScore("2"));
        sqs.get(0).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
        });
        sqs.get(1).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("B", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
            if (Objects.equals("C", v.getAnswerCode())) {
                v.setAnswerScore("0.2");
            }
        });
        sqs.get(2).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("D", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
            if (Objects.equals("C", v.getAnswerCode())) {
                v.setAnswerScore("0.2");
            }
        });
        sqs.get(3).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("C", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
            if (Objects.equals("D", v.getAnswerCode())) {
                v.setAnswerScore("0.2");
            }
        });
        sqs.get(4).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("B", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
        });
        sqs.get(5).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("C", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
            if (Objects.equals("D", v.getAnswerCode())) {
                v.setAnswerScore("0.2");
            }
        });
        sqs.get(6).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("B", v.getAnswerCode())) {
                v.setAnswerScore("0.4");
            }
            if (Objects.equals("C", v.getAnswerCode())) {
                v.setAnswerScore("0.8");
            }
            if (Objects.equals("D", v.getAnswerCode())) {
                v.setAnswerScore("2");
            }
        });
        sqs.get(7).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("D", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
        });
        sqs.get(8).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("C", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
            if (Objects.equals("D", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
        });
        sqs.get(9).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("A", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
            if (Objects.equals("B", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
            if (Objects.equals("C", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
            if (Objects.equals("D", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
        });
        sqs.get(10).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("E", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
        });
        sqs.get(11).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("D", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
        });
        sqs.get(12).getAnswerList().forEach(v -> {
            v.setAnswerScore("0");
            if (Objects.equals("A", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
            if (Objects.equals("C", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
            if (Objects.equals("B", v.getAnswerCode())) {
                v.setAnswerScore("0.1");
            }
        });
        return groupMap;
    }

    /**
     * 获取题目与答案信息
     *
     * @param infos 用户答题信息
     */
    private List<SimpleQuestion> getSimpleQuestionList(List<ExamInfoVO> infos) {
        // 检查合法性
        Optional<ExamInfoVO> examInfoVO = infos.parallelStream().filter(v -> StringUtils.isNotBlank(v.getExamRecord())).findFirst();
        if (!examInfoVO.isPresent()) return Lists.newArrayList();

        //获取题目 和 答案 信息
        List<String> examRecord = Arrays.asList(examInfoVO.get().getExamRecord().split(","));
        List<ExamQuestion> examQuestions = iExamQuestionService.findAllNoPage(Specifications.<ExamQuestion>and().in("questionCode", examRecord).build(), Sort.by(Sort.Direction.ASC, "questionOrder"));
        List<ExamQuestionAnswer> examQuestionAnswers = iExamQuestionAnswerService.findAllNoPage(Specifications.<ExamQuestionAnswer>and().in("questionCode", examRecord).build(), Sort.by(Sort.Direction.ASC, "answerCode"));
        // 简化bean
        List<SimpleQuestion> simpleQuestions = examQuestions.stream().map(v -> BeanUtil.copyProperties(v, SimpleQuestion.class)).collect(Collectors.toList());
        List<SimpleQuestionAnswer> simpleQuestionAnswers = examQuestionAnswers.stream().map(v -> BeanUtil.copyProperties(v, SimpleQuestionAnswer.class)).collect(Collectors.toList());
        simpleQuestions.forEach(q -> q.setAnswerList(
                simpleQuestionAnswers.stream()
                        .filter(a -> Objects.equals(q.getQuestionCode(), a.getQuestionCode()))
                        .collect(Collectors.toList())
        ));
        return simpleQuestions;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private class DeptInfoBy2024xszg_ks implements Serializable {

        private static final long serialVersionUID = 1L;

        @Excel(name = "部门")
        private String departmentName;

        @Excel(name = "得分")
        private String score;
    }

}
