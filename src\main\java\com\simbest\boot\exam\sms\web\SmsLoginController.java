package com.simbest.boot.exam.sms.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.sms.constant.SmsConstants;
import com.simbest.boot.exam.sms.sevice.ILoginService;
import com.simbest.boot.util.security.LoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2020/8/12</strong><br>
 * <strong>Modify on : 2020/8/12</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@RequestMapping(value = "/action/logic")
@RestController
@Slf4j
@Api(description = "短信小程序-通用Controller", tags = "短信小程序-通用模块")
public class SmsLoginController {

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private ILoginService loginService;

    @ApiOperation(value = "发送短信验证码", notes = "发送短信验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号码", dataType = "string", paramType = "query"),
    })
    @PostMapping(value = {"/sendMsg", "/api/sendMsg", "/sendMsg/sso", "/sendMsg/anonymous"})
    public JsonResponse sendMsg(@RequestParam String phone) {
        int i = loginService.sendMsg(phone);
        JsonResponse jsonResponse = new JsonResponse();
        if (i == 1) {
            jsonResponse = JsonResponse.success(1, SmsConstants.SEND_MSG_SUCCESS);
        } else if (i == 0) {
            jsonResponse = JsonResponse.fail(0, SmsConstants.SEND_MSG_NO_PERMISSION);
        } else if (i == 2) {
            jsonResponse = JsonResponse.success(2, SmsConstants.SEND_MSG_REPEAT);
        } else {
            jsonResponse = JsonResponse.fail(SmsConstants.SEND_MSG_FAILED);
        }
        return jsonResponse;
    }

    @ApiOperation(value = "发送短信验证码 仅限oa手机号", notes = "发送短信验证码 仅限oa手机号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号码", dataType = "string", paramType = "query"),
    })
    @PostMapping(value = {"/sendMsgForOA", "/api/sendMsgForOA", "/sendMsgForOA/sso", "/sendMsgForOA/anonymous"})
    public JsonResponse sendMsgForOA(@RequestParam String phone) {
        int i = loginService.sendMsgForOA(phone);
        JsonResponse jsonResponse = new JsonResponse();
        if (i == 1) {
            jsonResponse = JsonResponse.success(1, SmsConstants.SEND_MSG_SUCCESS);
        } else if (i == 0) {
            jsonResponse = JsonResponse.fail(0, "没有摇号权限！");
        } else if (i == 2) {
            jsonResponse = JsonResponse.success(2, SmsConstants.SEND_MSG_REPEAT);
        } else {
            jsonResponse = JsonResponse.fail(SmsConstants.SEND_MSG_FAILED);
        }
        return jsonResponse;
    }

    /**
     * 获取电话号码给前端进行自动登录
     *
     * @param
     * @return
     */
    @PostMapping(value = {"/getPhone", "/getPhone/sso", "/api/getPhone", "/getPhone/anonymous"})
    public JsonResponse getPhone(@RequestParam String token) {
        loginUtils.adminLogin();
        Map<String, Object> map = loginService.getPhone(token);
        return JsonResponse.success(map);
    }

    /**
     * 长链接转短链接
     */
    @PostMapping(value = {"/convertshorturl", "/convertshorturl/sso", "/api/convertshorturl", "/convertshorturl/anonymous"})
    public JsonResponse convertshorturl(@RequestBody Map<String, String> map) {
        String url = map.get("url");
        Assert.notNull(url, "[url]不能为空, 检查参数！");
        loginUtils.adminLogin();
        String shortUrl = loginService.ConvertShortUrl(url);
        return JsonResponse.success(shortUrl, null);
    }

}
