package com.simbest.boot.exam.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * 用途：考试管理模块--考试答题明细
 * 作者：gy
 * 时间: 2021-02-23 19:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_info_detail")
@ApiModel(value = "考试答题明细")
@Builder
public class ExamInfoDetail extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EID") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @ApiModelProperty(value = "答题人")
    private String publishUsername;

    @Column(length = 250)
    @ApiModelProperty(value = "答题人")
    private String publishTruename;

    @Column(length = 40)
    @ApiModelProperty(value = "考试编码")
    private String examCode;

    @ApiModelProperty(value = "汇总数据id")
    @Column(length = 40)
    private String examInfoId;

    @Column(length = 2000)
    @ApiModelProperty(value = "答题记录",name = "examRecord",example = "A-001-1,A-001-2,A-001-3,A-001-4")
    private String examQuestionCode;

    @Column(length = 2000)
    @ApiModelProperty(value = "答案记录",name = "examAnswer",example = "A/B/C/B,C/A,B,C/")
    private String examAnswer;

    @Column(length = 40)
    @ApiModelProperty(value = "得分",name = "score",example = "100",required = true)
    private String score;

}
